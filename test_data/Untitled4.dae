<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 4.5.0 Alpha commit date:2025-05-07, commit time:23:18, hash:6a3cd8f1470b</authoring_tool>
    </contributor>
    <created>2025-07-14T22:07:00</created>
    <modified>2025-07-14T22:07:00</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_images/>
  <library_geometries>
    <geometry id="Cube-mesh" name="Cube">
      <mesh>
        <source id="Cube-mesh-positions">
          <float_array id="Cube-mesh-positions-array" count="294">-1 -1 -1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 1 1 1 -1 1 1 1 -1 0 -1 -1 -1 0 -1 0 1 -1 1 0 0 1 -1 0 1 1 1 1 0 1 0 -1 1 0 1 1 -1 0 0 -1 -1 0 -1 1 0 0 1 0 0 -1 0 -1 0 1 0 0 0 1 0 -1 0 0 -1 -0.5 -1 -1 -1 0.5 -1 0.5 1 -1 1 -0.5 -0.5 1 -1 0.5 1 1 1 1 -0.5 1 0.5 -1 1 -0.5 1 1 -1 -0.5 0.5 -1 -1 -0.5 -1 1 -1 0.5 -1 -1 -1 -0.5 -1 -0.5 1 -1 1 0.5 0.5 1 -1 -0.5 1 1 1 1 0.5 1 -0.5 -1 1 0.5 1 1 -1 0.5 -0.5 -1 -1 0.5 -1 1 0 -0.5 1 0 0.5 1 0.5 0 1 -0.5 0 1 0 -0.5 -1 0 0.5 -1 -0.5 0 -1 0.5 0 -1 -0.5 -1 0 0.5 -1 0 0 -1 -0.5 0 -1 0.5 1 -0.5 0 1 0.5 0 1 0 -0.5 1 0 0.5 0.5 1 0 -0.5 1 0 0 1 -0.5 0 1 0.5 -1 0.5 0 -1 -0.5 0 -1 0 -0.5 -1 0 0.5 -1 -0.5 0.5 -1 -0.5 -0.5 -1 0.5 -0.5 -0.5 1 0.5 -0.5 1 -0.5 0.5 1 -0.5 1 0.5 0.5 1 0.5 -0.5 1 -0.5 -0.5 0.5 -1 0.5 0.5 -1 -0.5 -0.5 -1 -0.5 0.5 0.5 -1 -0.5 0.5 -1 -0.5 -0.5 -1 -0.5 0.5 1 0.5 0.5 1 0.5 -0.5 1 -0.5 -0.5 1 0.5 -0.5 -1 -0.5 -1 0.5 1 -0.5 0.5 0.5 1 0.5 -1 0.5 0.5</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-positions-array" count="98" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube-mesh-normals">
          <float_array id="Cube-mesh-normals-array" count="576">-1 0 0 0 1 0 1 0 0 0 -1 0 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 -1 0 0 -1 0 0 -1 0 1 0 0 1 0 0 1 0 0 0 1 0 0 1 0 0 1 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 -1 0 0 -1 0 0 -1 0 1 0 0 1 0 0 1 0 0 0 1 0 0 1 0 0 1 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 0 1 0 1 0 0 0 -1 0 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 -1 0 0 -1 0 0 -1 0 1 0 0 1 0 0 1 0 0 0 1 0 0 1 0 0 1 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 -1 0 0 -1 0 0 -1 0 1 0 0 1 0 0 1 0 0 0 1 0 0 1 0 0 1 0 -1 0 0 -1 0 0 -1 0 0</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-normals-array" count="192" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube-mesh-map-0">
          <float_array id="Cube-mesh-map-0-array" count="1152">0.625 0.1875 0.5625 0.25 0.5625 0.1875 0.625 0.4375 0.5625 0.5 0.5625 0.4375 0.625 0.6875 0.5625 0.75 0.5625 0.6875 0.625 0.9375 0.5625 1 0.5625 0.9375 0.375 0.6875 0.3125 0.75 0.3125 0.6875 0.875 0.6875 0.8125 0.75 0.8125 0.6875 0.75 0.6875 0.6875 0.75 0.6875 0.6875 0.75 0.5625 0.6875 0.625 0.6875 0.5625 0.875 0.5625 0.8125 0.625 0.8125 0.5625 0.25 0.6875 0.1875 0.75 0.1875 0.6875 0.25 0.5625 0.1875 0.625 0.1875 0.5625 0.375 0.5625 0.3125 0.625 0.3125 0.5625 0.5 0.9375 0.4375 1 0.4375 0.9375 0.5 0.8125 0.4375 0.875 0.4375 0.8125 0.625 0.8125 0.5625 0.875 0.5625 0.8125 0.5 0.6875 0.4375 0.75 0.4375 0.6875 0.5 0.5625 0.4375 0.625 0.4375 0.5625 0.625 0.5625 0.5625 0.625 0.5625 0.5625 0.5 0.4375 0.4375 0.5 0.4375 0.4375 0.5 0.3125 0.4375 0.375 0.4375 0.3125 0.625 0.3125 0.5625 0.375 0.5625 0.3125 0.5 0.1875 0.4375 0.25 0.4375 0.1875 0.5 0.0625 0.4375 0.125 0.4375 0.0625 0.625 0.0625 0.5625 0.125 0.5625 0.0625 0.5625 0.0625 0.5 0.125 0.5 0.0625 0.5625 0 0.5 0.0625 0.5 0 0.625 0 0.5625 0.0625 0.5625 0 0.4375 0.0625 0.375 0.125 0.375 0.0625 0.4375 0 0.375 0.0625 0.375 0 0.5 0 0.4375 0.0625 0.4375 0 0.4375 0.1875 0.375 0.25 0.375 0.1875 0.4375 0.125 0.375 0.1875 0.375 0.125 0.5 0.125 0.4375 0.1875 0.4375 0.125 0.5625 0.3125 0.5 0.375 0.5 0.3125 0.5625 0.25 0.5 0.3125 0.5 0.25 0.625 0.25 0.5625 0.3125 0.5625 0.25 0.4375 0.3125 0.375 0.375 0.375 0.3125 0.4375 0.25 0.375 0.3125 0.375 0.25 0.5 0.25 0.4375 0.3125 0.4375 0.25 0.4375 0.4375 0.375 0.5 0.375 0.4375 0.4375 0.375 0.375 0.4375 0.375 0.375 0.5 0.375 0.4375 0.4375 0.4375 0.375 0.5625 0.5625 0.5 0.625 0.5 0.5625 0.5625 0.5 0.5 0.5625 0.5 0.5 0.625 0.5 0.5625 0.5625 0.5625 0.5 0.4375 0.5625 0.375 0.625 0.375 0.5625 0.4375 0.5 0.375 0.5625 0.375 0.5 0.5 0.5 0.4375 0.5625 0.4375 0.5 0.4375 0.6875 0.375 0.75 0.375 0.6875 0.4375 0.625 0.375 0.6875 0.375 0.625 0.5 0.625 0.4375 0.6875 0.4375 0.625 0.5625 0.8125 0.5 0.875 0.5 0.8125 0.5625 0.75 0.5 0.8125 0.5 0.75 0.625 0.75 0.5625 0.8125 0.5625 0.75 0.4375 0.8125 0.375 0.875 0.375 0.8125 0.4375 0.75 0.375 0.8125 0.375 0.75 0.5 0.75 0.4375 0.8125 0.4375 0.75 0.4375 0.9375 0.375 1 0.375 0.9375 0.4375 0.875 0.375 0.9375 0.375 0.875 0.5 0.875 0.4375 0.9375 0.4375 0.875 0.3125 0.5625 0.25 0.625 0.25 0.5625 0.3125 0.5 0.25 0.5625 0.25 0.5 0.375 0.5 0.3125 0.5625 0.3125 0.5 0.1875 0.5625 0.125 0.625 0.125 0.5625 0.1875 0.5 0.125 0.5625 0.125 0.5 0.25 0.5 0.1875 0.5625 0.1875 0.5 0.1875 0.6875 0.125 0.75 0.125 0.6875 0.1875 0.625 0.125 0.6875 0.125 0.625 0.25 0.625 0.1875 0.6875 0.1875 0.625 0.8125 0.5625 0.75 0.625 0.75 0.5625 0.8125 0.5 0.75 0.5625 0.75 0.5 0.875 0.5 0.8125 0.5625 0.8125 0.5 0.6875 0.5625 0.625 0.625 0.625 0.5625 0.6875 0.5 0.625 0.5625 0.625 0.5 0.75 0.5 0.6875 0.5625 0.6875 0.5 0.6875 0.6875 0.625 0.75 0.625 0.6875 0.6875 0.625 0.625 0.6875 0.625 0.625 0.75 0.625 0.6875 0.6875 0.6875 0.625 0.8125 0.6875 0.75 0.75 0.75 0.6875 0.8125 0.625 0.75 0.6875 0.75 0.625 0.875 0.625 0.8125 0.6875 0.8125 0.625 0.3125 0.6875 0.25 0.75 0.25 0.6875 0.3125 0.625 0.25 0.6875 0.25 0.625 0.375 0.625 0.3125 0.6875 0.3125 0.625 0.5625 0.9375 0.5 1 0.5 0.9375 0.5625 0.875 0.5 0.9375 0.5 0.875 0.625 0.875 0.5625 0.9375 0.5625 0.875 0.5625 0.6875 0.5 0.75 0.5 0.6875 0.5625 0.625 0.5 0.6875 0.5 0.625 0.625 0.625 0.5625 0.6875 0.5625 0.625 0.5625 0.4375 0.5 0.5 0.5 0.4375 0.5625 0.375 0.5 0.4375 0.5 0.375 0.625 0.375 0.5625 0.4375 0.5625 0.375 0.5625 0.1875 0.5 0.25 0.5 0.1875 0.5625 0.125 0.5 0.1875 0.5 0.125 0.625 0.125 0.5625 0.1875 0.5625 0.125 0.625 0.1875 0.625 0.25 0.5625 0.25 0.625 0.4375 0.625 0.5 0.5625 0.5 0.625 0.6875 0.625 0.75 0.5625 0.75 0.625 0.9375 0.625 1 0.5625 1 0.375 0.6875 0.375 0.75 0.3125 0.75 0.875 0.6875 0.875 0.75 0.8125 0.75 0.75 0.6875 0.75 0.75 0.6875 0.75 0.75 0.5625 0.75 0.625 0.6875 0.625 0.875 0.5625 0.875 0.625 0.8125 0.625 0.25 0.6875 0.25 0.75 0.1875 0.75 0.25 0.5625 0.25 0.625 0.1875 0.625 0.375 0.5625 0.375 0.625 0.3125 0.625 0.5 0.9375 0.5 1 0.4375 1 0.5 0.8125 0.5 0.875 0.4375 0.875 0.625 0.8125 0.625 0.875 0.5625 0.875 0.5 0.6875 0.5 0.75 0.4375 0.75 0.5 0.5625 0.5 0.625 0.4375 0.625 0.625 0.5625 0.625 0.625 0.5625 0.625 0.5 0.4375 0.5 0.5 0.4375 0.5 0.5 0.3125 0.5 0.375 0.4375 0.375 0.625 0.3125 0.625 0.375 0.5625 0.375 0.5 0.1875 0.5 0.25 0.4375 0.25 0.5 0.0625 0.5 0.125 0.4375 0.125 0.625 0.0625 0.625 0.125 0.5625 0.125 0.5625 0.0625 0.5625 0.125 0.5 0.125 0.5625 0 0.5625 0.0625 0.5 0.0625 0.625 0 0.625 0.0625 0.5625 0.0625 0.4375 0.0625 0.4375 0.125 0.375 0.125 0.4375 0 0.4375 0.0625 0.375 0.0625 0.5 0 0.5 0.0625 0.4375 0.0625 0.4375 0.1875 0.4375 0.25 0.375 0.25 0.4375 0.125 0.4375 0.1875 0.375 0.1875 0.5 0.125 0.5 0.1875 0.4375 0.1875 0.5625 0.3125 0.5625 0.375 0.5 0.375 0.5625 0.25 0.5625 0.3125 0.5 0.3125 0.625 0.25 0.625 0.3125 0.5625 0.3125 0.4375 0.3125 0.4375 0.375 0.375 0.375 0.4375 0.25 0.4375 0.3125 0.375 0.3125 0.5 0.25 0.5 0.3125 0.4375 0.3125 0.4375 0.4375 0.4375 0.5 0.375 0.5 0.4375 0.375 0.4375 0.4375 0.375 0.4375 0.5 0.375 0.5 0.4375 0.4375 0.4375 0.5625 0.5625 0.5625 0.625 0.5 0.625 0.5625 0.5 0.5625 0.5625 0.5 0.5625 0.625 0.5 0.625 0.5625 0.5625 0.5625 0.4375 0.5625 0.4375 0.625 0.375 0.625 0.4375 0.5 0.4375 0.5625 0.375 0.5625 0.5 0.5 0.5 0.5625 0.4375 0.5625 0.4375 0.6875 0.4375 0.75 0.375 0.75 0.4375 0.625 0.4375 0.6875 0.375 0.6875 0.5 0.625 0.5 0.6875 0.4375 0.6875 0.5625 0.8125 0.5625 0.875 0.5 0.875 0.5625 0.75 0.5625 0.8125 0.5 0.8125 0.625 0.75 0.625 0.8125 0.5625 0.8125 0.4375 0.8125 0.4375 0.875 0.375 0.875 0.4375 0.75 0.4375 0.8125 0.375 0.8125 0.5 0.75 0.5 0.8125 0.4375 0.8125 0.4375 0.9375 0.4375 1 0.375 1 0.4375 0.875 0.4375 0.9375 0.375 0.9375 0.5 0.875 0.5 0.9375 0.4375 0.9375 0.3125 0.5625 0.3125 0.625 0.25 0.625 0.3125 0.5 0.3125 0.5625 0.25 0.5625 0.375 0.5 0.375 0.5625 0.3125 0.5625 0.1875 0.5625 0.1875 0.625 0.125 0.625 0.1875 0.5 0.1875 0.5625 0.125 0.5625 0.25 0.5 0.25 0.5625 0.1875 0.5625 0.1875 0.6875 0.1875 0.75 0.125 0.75 0.1875 0.625 0.1875 0.6875 0.125 0.6875 0.25 0.625 0.25 0.6875 0.1875 0.6875 0.8125 0.5625 0.8125 0.625 0.75 0.625 0.8125 0.5 0.8125 0.5625 0.75 0.5625 0.875 0.5 0.875 0.5625 0.8125 0.5625 0.6875 0.5625 0.6875 0.625 0.625 0.625 0.6875 0.5 0.6875 0.5625 0.625 0.5625 0.75 0.5 0.75 0.5625 0.6875 0.5625 0.6875 0.6875 0.6875 0.75 0.625 0.75 0.6875 0.625 0.6875 0.6875 0.625 0.6875 0.75 0.625 0.75 0.6875 0.6875 0.6875 0.8125 0.6875 0.8125 0.75 0.75 0.75 0.8125 0.625 0.8125 0.6875 0.75 0.6875 0.875 0.625 0.875 0.6875 0.8125 0.6875 0.3125 0.6875 0.3125 0.75 0.25 0.75 0.3125 0.625 0.3125 0.6875 0.25 0.6875 0.375 0.625 0.375 0.6875 0.3125 0.6875 0.5625 0.9375 0.5625 1 0.5 1 0.5625 0.875 0.5625 0.9375 0.5 0.9375 0.625 0.875 0.625 0.9375 0.5625 0.9375 0.5625 0.6875 0.5625 0.75 0.5 0.75 0.5625 0.625 0.5625 0.6875 0.5 0.6875 0.625 0.625 0.625 0.6875 0.5625 0.6875 0.5625 0.4375 0.5625 0.5 0.5 0.5 0.5625 0.375 0.5625 0.4375 0.5 0.4375 0.625 0.375 0.625 0.4375 0.5625 0.4375 0.5625 0.1875 0.5625 0.25 0.5 0.25 0.5625 0.125 0.5625 0.1875 0.5 0.1875 0.625 0.125 0.625 0.1875 0.5625 0.1875</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-map-0-array" count="576" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube-mesh-colors-Attribute" name="Attribute">
          <float_array id="Cube-mesh-colors-Attribute-array" count="2304">1 0 0 1 1 0 0 1 1 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0.4980392 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 1 0 0 0 1 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 1 0 0 1 1 0 0 1 1 0 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 0 0 1 1 0 0 1 1 0 0 1 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 0 0 1 1 0 0 1 1 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 0 0 0 1 0 0 0 1 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 1 0 0 0 1 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0.4980392 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0.4980392 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 1 0 0 0 1 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0.4980392 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 1 0 0 0 1 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 1 0 0 1 1 0 0 1 1 0 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 0 0 1 1 0 0 1 1 0 0 1 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0 1 0 0 1 1 0 0 1 1 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 0 0 0 1 0 0 0 1 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 1 0 0 0 1 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0.4980392 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0 1 1 1 0 1 1 1 0 1 1 1 0.4980392 0 1 1 0.4980392 0 1 1 0.4980392 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 1 0 0 0 1 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 0.4352941 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 1 0 0 1 1 0 0 1 1 0 0 1</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-colors-Attribute-array" count="576" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube-mesh-vertices">
          <input semantic="POSITION" source="#Cube-mesh-positions"/>
        </vertices>
        <triangles count="192">
          <input semantic="VERTEX" source="#Cube-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#Cube-mesh-colors-Attribute" offset="3" set="0"/>
          <p>28 0 0 0 41 0 1 1 97 0 2 2 31 1 3 3 44 1 4 4 96 1 5 5 34 2 6 6 47 2 7 7 95 2 8 8 37 3 9 9 27 3 10 10 94 3 11 11 45 4 12 12 36 4 13 13 93 4 14 14 40 5 15 15 37 5 16 16 92 5 17 17 50 6 18 18 49 6 19 19 91 6 20 20 51 7 21 21 52 7 22 22 90 7 23 23 28 8 24 24 53 8 25 25 89 8 26 26 54 9 27 27 48 9 28 28 88 9 29 29 55 10 30 30 56 10 31 31 87 10 32 32 33 11 33 33 57 11 34 34 86 11 35 35 58 12 36 36 39 12 37 37 85 12 38 38 59 13 39 39 60 13 40 40 84 13 41 41 49 14 42 42 61 14 43 43 83 14 44 44 62 15 45 45 35 15 46 46 82 15 47 47 63 16 48 48 64 16 49 49 81 16 50 50 46 17 51 51 65 17 52 52 80 17 53 53 66 18 54 54 32 18 55 55 79 18 56 56 67 19 57 57 68 19 58 58 78 19 59 59 43 20 60 60 69 20 61 61 77 20 62 62 70 21 63 63 29 21 64 64 76 21 65 65 71 22 66 66 72 22 67 67 75 22 68 68 40 23 69 69 73 23 70 70 74 23 71 71 74 24 72 72 25 24 73 73 71 24 74 74 27 25 75 75 71 25 76 76 9 25 77 77 1 26 78 78 74 26 79 79 27 26 80 80 75 27 81 81 8 27 82 82 26 27 83 83 39 28 84 84 26 28 85 85 0 28 86 86 9 29 87 87 75 29 88 88 39 29 89 89 76 30 90 90 2 30 91 91 38 30 92 92 72 31 93 93 38 31 94 94 8 31 95 95 25 32 96 96 76 32 97 97 72 32 98 98 77 33 99 99 24 33 100 100 67 33 101 101 41 34 102 102 67 34 103 103 11 34 104 104 3 35 105 105 77 35 106 106 41 35 107 107 78 36 108 108 12 36 109 109 30 36 110 110 29 37 111 111 30 37 112 112 2 37 113 113 11 38 114 114 78 38 115 115 29 38 116 116 79 39 117 117 6 39 118 118 42 39 119 119 68 40 120 120 42 40 121 121 12 40 122 122 24 41 123 123 79 41 124 124 68 41 125 125 80 42 126 126 23 42 127 127 63 42 128 128 44 43 129 129 63 43 130 130 14 43 131 131 7 44 132 132 80 44 133 133 44 44 134 134 81 45 135 135 15 45 136 136 33 45 137 137 32 46 138 138 33 46 139 139 6 46 140 140 14 47 141 141 81 47 142 142 32 47 143 143 82 48 144 144 4 48 145 145 45 48 146 146 64 49 147 147 45 49 148 148 15 49 149 149 23 50 150 150 82 50 151 151 64 50 152 152 83 51 153 153 22 51 154 154 59 51 155 155 47 52 156 156 59 52 157 157 17 52 158 158 5 53 159 159 83 53 160 160 47 53 161 161 84 54 162 162 18 54 163 163 36 54 164 164 35 55 165 165 36 55 166 166 4 55 167 167 17 56 168 168 84 56 169 169 35 56 170 170 85 57 171 171 0 57 172 172 48 57 173 173 60 58 174 174 48 58 175 175 18 58 176 176 22 59 177 177 85 59 178 178 60 59 179 179 86 60 180 180 21 60 181 181 55 60 182 182 42 61 183 183 55 61 184 184 12 61 185 185 6 62 186 186 86 62 187 187 42 62 188 188 87 63 189 189 8 63 190 190 38 63 191 191 30 64 192 192 38 64 193 193 2 64 194 194 12 65 195 195 87 65 196 196 30 65 197 197 88 66 198 198 0 66 199 199 26 66 200 200 56 67 201 201 26 67 202 202 8 67 203 203 21 68 204 204 88 68 205 205 56 68 206 206 89 69 207 207 20 69 208 208 51 69 209 209 43 70 210 210 51 70 211 211 13 70 212 212 3 71 213 213 89 71 214 214 43 71 215 215 90 72 216 216 16 72 217 217 46 72 218 218 31 73 219 219 46 73 220 220 7 73 221 221 13 74 222 222 90 74 223 223 31 74 224 224 91 75 225 225 5 75 226 226 34 75 227 227 52 76 228 228 34 76 229 229 16 76 230 230 20 77 231 231 91 77 232 232 52 77 233 233 92 78 234 234 19 78 235 235 50 78 236 236 53 79 237 237 50 79 238 238 20 79 239 239 10 80 240 240 92 80 241 241 53 80 242 242 93 81 243 243 18 81 244 244 54 81 245 245 57 82 246 246 54 82 247 247 21 82 248 248 15 83 249 249 93 83 250 250 57 83 251 251 94 84 252 252 9 84 253 253 58 84 254 254 61 85 255 255 58 85 256 256 22 85 257 257 19 86 258 258 94 86 259 259 61 86 260 260 95 87 261 261 17 87 262 262 62 87 263 263 65 88 264 264 62 88 265 265 23 88 266 266 16 89 267 267 95 89 268 268 65 89 269 269 96 90 270 270 14 90 271 271 66 90 272 272 69 91 273 273 66 91 274 274 24 91 275 275 13 92 276 276 96 92 277 277 69 92 278 278 97 93 279 279 11 93 280 280 70 93 281 281 73 94 282 282 70 94 283 283 25 94 284 284 10 95 285 285 97 95 286 286 73 95 287 287 28 96 288 288 3 96 289 289 41 96 290 290 31 97 291 291 7 97 292 292 44 97 293 293 34 98 294 294 5 98 295 295 47 98 296 296 37 99 297 297 1 99 298 298 27 99 299 299 45 100 300 300 4 100 301 301 36 100 302 302 40 101 303 303 1 101 304 304 37 101 305 305 50 102 306 306 19 102 307 307 49 102 308 308 51 103 309 309 20 103 310 310 52 103 311 311 28 104 312 312 10 104 313 313 53 104 314 314 54 105 315 315 18 105 316 316 48 105 317 317 55 106 318 318 21 106 319 319 56 106 320 320 33 107 321 321 15 107 322 322 57 107 323 323 58 108 324 324 9 108 325 325 39 108 326 326 59 109 327 327 22 109 328 328 60 109 329 329 49 110 330 330 19 110 331 331 61 110 332 332 62 111 333 333 17 111 334 334 35 111 335 335 63 112 336 336 23 112 337 337 64 112 338 338 46 113 339 339 16 113 340 340 65 113 341 341 66 114 342 342 14 114 343 343 32 114 344 344 67 115 345 345 24 115 346 346 68 115 347 347 43 116 348 348 13 116 349 349 69 116 350 350 70 117 351 351 11 117 352 352 29 117 353 353 71 118 354 354 25 118 355 355 72 118 356 356 40 119 357 357 10 119 358 358 73 119 359 359 74 120 360 360 73 120 361 361 25 120 362 362 27 121 363 363 74 121 364 364 71 121 365 365 1 122 366 366 40 122 367 367 74 122 368 368 75 123 369 369 72 123 370 370 8 123 371 371 39 124 372 372 75 124 373 373 26 124 374 374 9 125 375 375 71 125 376 376 75 125 377 377 76 126 378 378 29 126 379 379 2 126 380 380 72 127 381 381 76 127 382 382 38 127 383 383 25 128 384 384 70 128 385 385 76 128 386 386 77 129 387 387 69 129 388 388 24 129 389 389 41 130 390 390 77 130 391 391 67 130 392 392 3 131 393 393 43 131 394 394 77 131 395 395 78 132 396 396 68 132 397 397 12 132 398 398 29 133 399 399 78 133 400 400 30 133 401 401 11 134 402 402 67 134 403 403 78 134 404 404 79 135 405 405 32 135 406 406 6 135 407 407 68 136 408 408 79 136 409 409 42 136 410 410 24 137 411 411 66 137 412 412 79 137 413 413 80 138 414 414 65 138 415 415 23 138 416 416 44 139 417 417 80 139 418 418 63 139 419 419 7 140 420 420 46 140 421 421 80 140 422 422 81 141 423 423 64 141 424 424 15 141 425 425 32 142 426 426 81 142 427 427 33 142 428 428 14 143 429 429 63 143 430 430 81 143 431 431 82 144 432 432 35 144 433 433 4 144 434 434 64 145 435 435 82 145 436 436 45 145 437 437 23 146 438 438 62 146 439 439 82 146 440 440 83 147 441 441 61 147 442 442 22 147 443 443 47 148 444 444 83 148 445 445 59 148 446 446 5 149 447 447 49 149 448 448 83 149 449 449 84 150 450 450 60 150 451 451 18 150 452 452 35 151 453 453 84 151 454 454 36 151 455 455 17 152 456 456 59 152 457 457 84 152 458 458 85 153 459 459 39 153 460 460 0 153 461 461 60 154 462 462 85 154 463 463 48 154 464 464 22 155 465 465 58 155 466 466 85 155 467 467 86 156 468 468 57 156 469 469 21 156 470 470 42 157 471 471 86 157 472 472 55 157 473 473 6 158 474 474 33 158 475 475 86 158 476 476 87 159 477 477 56 159 478 478 8 159 479 479 30 160 480 480 87 160 481 481 38 160 482 482 12 161 483 483 55 161 484 484 87 161 485 485 88 162 486 486 48 162 487 487 0 162 488 488 56 163 489 489 88 163 490 490 26 163 491 491 21 164 492 492 54 164 493 493 88 164 494 494 89 165 495 495 53 165 496 496 20 165 497 497 43 166 498 498 89 166 499 499 51 166 500 500 3 167 501 501 28 167 502 502 89 167 503 503 90 168 504 504 52 168 505 505 16 168 506 506 31 169 507 507 90 169 508 508 46 169 509 509 13 170 510 510 51 170 511 511 90 170 512 512 91 171 513 513 49 171 514 514 5 171 515 515 52 172 516 516 91 172 517 517 34 172 518 518 20 173 519 519 50 173 520 520 91 173 521 521 92 174 522 522 37 174 523 523 19 174 524 524 53 175 525 525 92 175 526 526 50 175 527 527 10 176 528 528 40 176 529 529 92 176 530 530 93 177 531 531 36 177 532 532 18 177 533 533 57 178 534 534 93 178 535 535 54 178 536 536 15 179 537 537 45 179 538 538 93 179 539 539 94 180 540 540 27 180 541 541 9 180 542 542 61 181 543 543 94 181 544 544 58 181 545 545 19 182 546 546 37 182 547 547 94 182 548 548 95 183 549 549 47 183 550 550 17 183 551 551 65 184 552 552 95 184 553 553 62 184 554 554 16 185 555 555 34 185 556 556 95 185 557 557 96 186 558 558 44 186 559 559 14 186 560 560 69 187 561 561 96 187 562 562 66 187 563 563 13 188 564 564 31 188 565 565 96 188 566 566 97 189 567 567 41 189 568 568 11 189 569 569 73 190 570 570 97 190 571 571 70 190 572 572 10 191 573 573 28 191 574 574 97 191 575 575</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Cube" name="Cube" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube-mesh" name="Cube"/>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>