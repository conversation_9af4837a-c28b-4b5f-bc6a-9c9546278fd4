# OpenColorIO configuration file for Blender
#
# ACEScg and ACES2065-1 spaces using OpenColorIO's BuiltinTransform
#
# Filmic Dynamic Range LUT configuration crafted by <PERSON> with
# special thanks and feedback from <PERSON>, <PERSON>, <PERSON><PERSON>,
# <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.
#
# Based on original AgX by <PERSON>: https://github.com/sobotka/AgX https://github.com/sobotka/SB2383-Configuration-Generation
# Further Developed by <PERSON><PERSON><PERSON>法纤净, <PERSON>, and <PERSON><PERSON>
# Repository for this version: https://github.com/EaryChow/AgX
#
# See ocio-license.txt for details.

ocio_profile_version: 2

search_path: "luts:filmic"
strictparsing: true
luma: [0.2126, 0.7152, 0.0722]

description: RRT version ut33

roles:
  reference: Linear CIE-XYZ E

  # Internal scene linear space
  scene_linear: Linear Rec.709
  rendering: Linear Rec.709

  # Default color space for byte image
  default_byte: sRGB

  # Default color space for float images
  default_float: Linear Rec.709

  # Default color space sequencer is working in
  default_sequencer: sRGB

  # Distribution of colors in color picker
  color_picking: sRGB

  # Non-color data
  data: Non-Color

  # For interop between configs, and to determine XYZ for rendering
  aces_interchange: ACES2065-1
  cie_xyz_d65_interchange: Linear CIE-XYZ D65

  # Specified by OCIO, not used in Blender
  color_timing: AgX Log
  compositing_log: AgX Log
  default: Linear Rec.709
  matte_paint: Linear Rec.709
  texture_paint: Linear Rec.709

displays:
  sRGB:
    - !<View> {name: Standard, colorspace: sRGB}
    - !<View> {name: Khronos PBR Neutral, colorspace: Khronos PBR Neutral sRGB}
    - !<View> {name: AgX, colorspace: AgX Base sRGB}
    - !<View> {name: Filmic, colorspace: Filmic sRGB}
    - !<View> {name: Filmic Log, colorspace: Filmic Log}
    - !<View> {name: False Color, colorspace: AgX False Color Rec.709}
    - !<View> {name: Raw, colorspace: Non-Color}
  Display P3:
    - !<View> {name: Standard, colorspace: Display P3}
    - !<View> {name: AgX, colorspace: AgX Base Display P3}
    - !<View> {name: False Color, colorspace: AgX False Color P3}
    - !<View> {name: Raw, colorspace: Non-Color}
  Rec.1886:
    - !<View> {name: Standard, colorspace: Rec.1886}
    - !<View> {name: AgX, colorspace: AgX Base Rec.1886}
    - !<View> {name: False Color, colorspace: AgX False Color Rec.709}
    - !<View> {name: Raw, colorspace: Non-Color}
  Rec.2020:
    - !<View> {name: Standard, colorspace: Rec.2020}
    - !<View> {name: AgX, colorspace: AgX Base Rec.2020}
    - !<View> {name: False Color, colorspace: AgX False Color Rec.2020}
    - !<View> {name: Raw, colorspace: Non-Color}
active_displays: [sRGB, Display P3, Rec.1886, Rec.2020]
active_views: [Standard, Khronos PBR Neutral, AgX, Filmic, Filmic Log, False Color, Raw]
inactive_colorspaces: [Luminance Compensation Rec.2020, Luminance Compensation sRGB, Luminance Compensation P3, AgX False Color Rec.709, AgX False Color P3, AgX False Color Rec.2020]

colorspaces:
  - !<ColorSpace>
    name: Linear CIE-XYZ E
    aliases: ["FilmLight: Linear - XYZ", Linear CIE-XYZ I-E]
    family: Chromaticity
    equalitygroup:
    bitdepth: 32f
    description: |
      1931 CIE XYZ standard with assumed illuminant E white point
    isdata: false

  - !<ColorSpace>
    name: Linear CIE-XYZ D65
    aliases: [cie_xyz_d65, CIE-XYZ-D65, XYZ, Linear CIE-XYZ I-D65]
    family: Chromaticity
    equalitygroup:
    bitdepth: 32f
    description: |
      1931 CIE XYZ with adapted illuminant D65 white point
    isdata: false
    from_scene_reference: !<FileTransform> {src: xyz_E_to_D65.spimtx, interpolation: linear}

  - !<ColorSpace>
    name: Linear Rec.709
    aliases: [Linear, Linear BT.709, Linear BT.709 I-D65, Linear Tristimulus, linrec709, Utility - Linear - sRGB, Utility - Linear - Rec.709, lin_srgb, Linear Rec.709 (sRGB), lin_rec709_srgb, lin_rec709, lin_srgb, "CGI: Linear - Rec.709"]
    family: Linear
    equalitygroup:
    bitdepth: 32f
    description: |
      Linear BT.709 with illuminant D65 white point
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear CIE-XYZ D65}
        - !<MatrixTransform> {matrix: [3.2404373564920070, -1.5371305409000549, -0.4985288240756933, 0, -0.9692674982687597, 1.8760136862977035, 0.0415560804587997, 0, 0.0556434463874256, -0.2040259700872272, 1.0572254813610864, 0, 0, 0, 0, 1]}

  - !<ColorSpace>
    name: Linear DCI-P3 D65
    aliases: [Linear DCI-P3 I-D65, Linear P3-D65, lin_p3d65, Utility - Linear - P3-D65, Apple DCI-P3 D65]
    family: Linear
    equalitygroup:
    bitdepth: 32f
    description: |
      Linear DCI-P3 with illuminant D65 white point
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear CIE-XYZ D65}
        - !<MatrixTransform> {matrix: [2.4935091239346101, -0.9313881794047790, -0.4027127567416516, 0, -0.8294732139295544, 1.7626305796003032, 0.0236242371055886, 0, 0.0358512644339181, -0.0761839369220759, 0.9570295866943110, 0, 0, 0, 0, 1]}

  - !<ColorSpace>
    name: Linear Rec.2020
    aliases: [Linear BT.2020 I-D65, Linear BT.2020, lin_rec2020, Utility - Linear - Rec.2020]
    family: Linear
    equalitygroup:
    bitdepth: 32f
    description: |
      Linear BT.2020 with illuminant D65 white point
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear CIE-XYZ D65}
        - !<MatrixTransform> {matrix: [ 1.7166634277958805, -0.3556733197301399, -0.2533680878902478, 0, -0.6666738361988869, 1.6164557398246981, 0.0157682970961337, 0, 0.0176424817849772, -0.0427769763827532, 0.9422432810184308, 0, 0, 0, 0, 1]}

  - !<ColorSpace>
    name: ACES2065-1
    aliases: [Linear ACES, aces2065_1, ACES - ACES2065-1, lin_ap0, "ACES: Linear - AP0"]
    family: Linear
    equalitygroup:
    bitdepth: 32f
    description: |
      Linear AP0 with ACES white point
    isdata: false
    from_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear CIE-XYZ D65}
        - !<BuiltinTransform> {style: "UTILITY - ACES-AP0_to_CIE-XYZ-D65_BFD", direction: inverse}

  - !<ColorSpace>
    name: ACEScg
    aliases: [Linear ACEScg, lin_ap1, ACES - ACEScg, "ACEScg: Linear - AP1"]
    family: Linear
    equalitygroup:
    bitdepth: 32f
    description: |
      Linear AP1 with ACES white point
    isdata: false
    from_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear CIE-XYZ D65}
        - !<BuiltinTransform> {style: "UTILITY - ACES-AP1_to_CIE-XYZ-D65_BFD", direction: inverse}

  - !<ColorSpace>
    name: Linear FilmLight E-Gamut
    aliases: [Linear E-Gamut I-D65, "FilmLight: Linear - E-Gamut"]
    family: Linear
    equalitygroup:
    bitdepth: 32f
    description: |
      Linear E-Gamut with illuminant D65 white point
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ I-E, dst: Linear CIE-XYZ I-D65}
        - !<MatrixTransform> {matrix: [ 0.7053968501, 0.1640413283, 0.08101774865, 0, 0.2801307241, 0.8202066415, -0.1003373656, 0, -0.1037815116, -0.07290725703, 1.265746519, 0, 0, 0, 0, 1], direction: inverse}

  - !<ColorSpace>
    name: sRGB
    aliases: [sRGB 2.2, sRGB I-D65, srgb_display, sRGB - Display, g22_rec709, Utility - Gamma 2.2 - Rec.709 - Texture, Utility - sRGB - Texture, sRGB - Texture, srgb_tx, srgb_texture, Input - Generic - sRGB - Texture, "sRGB Display: 2.2 Gamma - Rec.709"]
    family: Display
    equalitygroup:
    bitdepth: 32f
    description: |
      sRGB IEC 61966-2-1 compound (piece-wise) encoding
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear Rec.709}
        - !<ExponentWithLinearTransform> {gamma: 2.4, offset: 0.055, direction: inverse}

  - !<ColorSpace>
    name: Display P3
    aliases: [Display P3 2.2, Display P3 I-D65, P3-D65 - Display, p3_d65_display, p3d65_display, AppleP3 sRGB OETF]
    family: Display
    equalitygroup:
    bitdepth: 32f
    description: |
      Apple's Display P3 with sRGB compound (piece-wise) encoding transfer function, common on Mac devices
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear DCI-P3 D65}
        - !<ExponentWithLinearTransform> {gamma: 2.4, offset: 0.055, direction: inverse}

  - !<ColorSpace>
    name: Rec.1886
    aliases: [BT.1886, BT.1886 2.4, BT.1886 EOTF, BT.1886 I-D65, Rec.1886 / Rec.709 Video - Display, rec1886_rec709_video_display, Rec.1886 Rec.709 - Display, rec1886_rec709_display, "Rec1886: 2.4 Gamma - Rec.709"]
    family: Display
    equalitygroup:
    bitdepth: 32f
    description: |
      BT.1886 2.4 Exponent EOTF Display, commonly used for TVs
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear Rec.709}
        - !<ExponentTransform> {value: 2.4, direction: inverse}

  - !<ColorSpace>
    name: Rec.2020
    aliases: [BT.2020, BT.2020 2.4, BT.2020 I-D65, Rec.1886 / Rec.2020 Video - Display, rec1886_rec2020_video_display, Rec.1886 Rec.2020 - Display, rec1886_rec2020_display, "Rec1886: 2.4 Gamma - Rec.2020"]
    family: Display
    equalitygroup:
    bitdepth: 32f
    description: |
      BT.2020 2.4 Exponent EOTF Display
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear Rec.2020}
        - !<ExponentTransform> {value: 2.4, direction: inverse}

  - !<ColorSpace>
    name: Non-Color
    aliases: [Generic Data, Non-Colour Data, Raw, Utility - Raw]
    family: Data
    description: |
        Generic data that is not color, will not apply any color transform (e.g. normal maps)
    equalitygroup:
    bitdepth: 32f
    isdata: true

  - !<ColorSpace>
    name: Filmic Log
    family: Log Encodings
    equalitygroup:
    bitdepth: 32f
    description: |
      Log based filmic shaper with 16.5 stops of latitude, and 25 stops of dynamic range
    isdata: false
    from_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear Rec.709}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.473931188, 12.526068812]}
        - !<FileTransform> {src: filmic_desat_33.cube, interpolation: tetrahedral}
        - !<AllocationTransform> {allocation: uniform, vars: [0, 0.66]}
    to_scene_reference: !<GroupTransform>
      children:
        - !<AllocationTransform> {allocation: lg2, vars: [-12.473931188, 4.026068812], direction: inverse}
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear Rec.709, direction: inverse}

  - !<ColorSpace>
    name: Filmic sRGB
    family: Filmic
    equalitygroup:
    bitdepth: 32f
    description: |
      sRGB display space with Filmic view transform
    isdata: false
    from_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Filmic Log}
        - !<FileTransform> {src: filmic_to_0-70_1-03.spi1d, interpolation: linear}

  - !<ColorSpace>
    name: Luminance Compensation Rec.2020
    aliases: [Luminance Compensation BT.2020]
    family: Utilities
    equalitygroup:
    bitdepth: 32f
    description: |
      Offset the negative values in BT.2020 and compensate for luminance, ensuring there is no negative values in Rec.2020
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear FilmLight E-Gamut}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117]}
        - !<FileTransform> {src: luminance_compensation_bt2020.cube, interpolation: tetrahedral}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117], direction: inverse}
        - !<ColorSpaceTransform> {src: Linear FilmLight E-Gamut, dst: Linear Rec.2020}

  - !<ColorSpace>
    name: Luminance Compensation sRGB
    family: Utilities
    equalitygroup:
    bitdepth: 32f
    description: |
      Offset the negative values in BT.709 and compensate for luminance, ensuring there is no negative values in Rec.709
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear FilmLight E-Gamut}
        - !<MatrixTransform> {matrix: [0.960599732262383, 0.0196075412762159, 0.019792726461401, 0, 0.0105997322623829, 0.969607541276216, 0.0197927264614012, 0, 0.0105997322623829, 0.0196075412762162, 0.969792726461401, 0, 0, 0, 0, 1]}
        - !<FileTransform> {src: Guard_Rail_Shaper_EOTF.spi1d, interpolation: linear, direction: inverse}
        - !<FileTransform> {src: luminance_compensation_srgb.cube, interpolation: tetrahedral}
        - !<FileTransform> {src: Guard_Rail_Shaper_EOTF.spi1d, interpolation: linear}
        - !<MatrixTransform> {matrix: [0.960599732262383, 0.0196075412762159, 0.019792726461401, 0, 0.0105997322623829, 0.969607541276216, 0.0197927264614012, 0, 0.0105997322623829, 0.0196075412762162, 0.969792726461401, 0, 0, 0, 0, 1], direction: inverse}
        - !<ColorSpaceTransform> {src: Linear FilmLight E-Gamut, dst: Linear Rec.709}
        - !<RangeTransform> {min_in_value: 0, min_out_value: 0}

  - !<ColorSpace>
    name: Luminance Compensation P3
    family: Utilities
    equalitygroup:
    bitdepth: 32f
    description: |
      Offset the negative values in P3 and compensate for luminance, ensuring there is no negative values in P3
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear FilmLight E-Gamut}
        - !<MatrixTransform> {matrix: [0.960599732262383, 0.0196075412762159, 0.019792726461401, 0, 0.0105997322623829, 0.969607541276216, 0.0197927264614012, 0, 0.0105997322623829, 0.0196075412762162, 0.969792726461401, 0, 0, 0, 0, 1]}
        - !<FileTransform> {src: Guard_Rail_Shaper_EOTF.spi1d, interpolation: linear, direction: inverse}
        - !<FileTransform> {src: luminance_compensation_p3.cube, interpolation: tetrahedral}
        - !<FileTransform> {src: Guard_Rail_Shaper_EOTF.spi1d, interpolation: linear}
        - !<MatrixTransform> {matrix: [0.960599732262383, 0.0196075412762159, 0.019792726461401, 0, 0.0105997322623829, 0.969607541276216, 0.0197927264614012, 0, 0.0105997322623829, 0.0196075412762162, 0.969792726461401, 0, 0, 0, 0, 1], direction: inverse}
        - !<ColorSpaceTransform> {src: Linear FilmLight E-Gamut, dst: Linear DCI-P3 D65}
        - !<RangeTransform> {min_in_value: 0, min_out_value: 0}

  - !<ColorSpace>
    name: AgX Log
    family: Log Encodings
    equalitygroup:
    bitdepth: 32f
    description: |
      Log Encoding with Chroma inset and rotation of primaries, and with 25 Stops of Dynamic Range
    isdata: false
    from_reference: !<GroupTransform>
      children:
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Luminance Compensation Rec.2020}
        # rotate = [3.0, -1, -2.0], inset = [0.4, 0.22, 0.13]
        - !<MatrixTransform> {matrix: [0.856627153315983, 0.0951212405381588, 0.0482516061458583, 0, 0.137318972929847, 0.761241990602591, 0.101439036467562, 0, 0.11189821299995, 0.0767994186031903, 0.811302368396859, 0, 0, 0, 0, 1]}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117]}
    to_scene_reference: !<GroupTransform>
      children:
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117], direction: inverse}
        - !<MatrixTransform> {matrix: [0.856627153315983, 0.0951212405381588, 0.0482516061458583, 0, 0.137318972929847, 0.761241990602591, 0.101439036467562, 0, 0.11189821299995, 0.0767994186031903, 0.811302368396859, 0, 0, 0, 0, 1], direction: inverse}
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear Rec.2020, direction: inverse}

  - !<ColorSpace>
    name: AgX Base Rec.2020
    aliases: [AgX Base BT.2020]
    family: AgX
    equalitygroup:
    bitdepth: 32f
    description: |
      AgX Base Image Encoding for BT.2020 Display
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear FilmLight E-Gamut}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117]}
        - !<FileTransform> {src: AgX_Base_Rec2020.cube, interpolation: tetrahedral}
    to_scene_reference: !<GroupTransform>
      children:
        - !<FileTransform> {src: Inverse_AgX_Base_Rec2020.cube, interpolation: tetrahedral}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 4.026069], direction: inverse}
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear Rec.2020, direction: inverse}

  - !<ColorSpace>
    name: AgX Base sRGB
    family: AgX
    equalitygroup:
    bitdepth: 32f
    description: |
      AgX Base Image Encoding for sRGB Display
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear FilmLight E-Gamut}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117]}
        - !<FileTransform> {src: AgX_Base_sRGB.cube, interpolation: tetrahedral}
        - !<ExponentTransform> {value: 2.4}
        - !<ColorSpaceTransform> {src: Linear Rec.709, dst: sRGB}
    to_scene_reference: !<GroupTransform>
      children:
       - !<ColorSpaceTransform> {src: sRGB, dst: Rec.2020}
       - !<ColorSpaceTransform> {src: AgX Base Rec.2020, dst: Linear CIE-XYZ E}

  - !<ColorSpace>
    name: AgX Base Display P3
    family: AgX
    equalitygroup:
    bitdepth: 32f
    description: |
      AgX Base Image Encoding for Display P3 Display
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear FilmLight E-Gamut}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117]}
        - !<FileTransform> {src: AgX_Base_P3.cube, interpolation: tetrahedral}
        - !<ExponentTransform> {value: 2.4}
        - !<ColorSpaceTransform> {src: Linear DCI-P3 D65, dst: Display P3}
    to_scene_reference: !<GroupTransform>
      children:
       - !<ColorSpaceTransform> {src: Display P3, dst: Rec.2020}
       - !<ColorSpaceTransform> {src: AgX Base Rec.2020, dst: Linear CIE-XYZ E}

  - !<ColorSpace>
    name: AgX Base Rec.1886
    aliases: [AgX Base BT.1886]
    family: AgX
    equalitygroup:
    bitdepth: 32f
    description: |
      AgX Base Image Encoding for Rec.1886 Display
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear FilmLight E-Gamut}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117]}
        - !<FileTransform> {src: AgX_Base_sRGB.cube, interpolation: tetrahedral}
    to_scene_reference: !<GroupTransform>
      children:
       - !<ColorSpaceTransform> {src: Rec.1886, dst: Rec.2020}
       - !<ColorSpaceTransform> {src: AgX Base Rec.2020, dst: Linear CIE-XYZ E}

  - !<ColorSpace>
    name: AgX False Color Rec.709
    aliases: [AgX False Color BT.709, False Colour, False Color]
    family: AgX
    equalitygroup:
    bitdepth: 32f
    description: |
      A heat-map-like image formed from AgX Base for sRGB and Rec.1886 displays
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: AgX Base Rec.2020}
        - !<ColorSpaceTransform> {src: Rec.2020, dst: Linear Rec.2020}
        - !<MatrixTransform> {matrix: [0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0, 0, 0, 1]}
        - !<ExponentTransform> {value: 2.5, direction: inverse}
        - !<FileTransform> {src: AgX_False_Color.spi1d, interpolation: linear}

  - !<ColorSpace>
    name: AgX False Color P3
    family: AgX
    equalitygroup:
    bitdepth: 32f
    description: |
      A heat-map-like image formed from AgX Base for Display P3 displays
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: AgX False Color Rec.709}
        - !<ColorSpaceTransform> {src: sRGB, dst: Display P3}

  - !<ColorSpace>
    name: AgX False Color Rec.2020
    aliases: [AgX False Color BT.2020]
    family: AgX
    equalitygroup:
    bitdepth: 32f
    description: |
      A heat-map-like image formed from AgX Base for Rec.2020 displays
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: AgX False Color Rec.709}
        - !<ColorSpaceTransform> {src: Rec.1886, dst: Rec.2020}

  - !<ColorSpace>
    name: Khronos PBR Neutral sRGB
    family: Khronos PBR Neutral
    equalitygroup:
    bitdepth: 32f
    description: |
      Khronos PBR Neutral Image Encoding for sRGB Display
    isdata: false
    from_scene_reference: !<GroupTransform>
      children:
        - !<ColorSpaceTransform> {src: Linear CIE-XYZ E, dst: Linear Rec.709}
        - !<AllocationTransform> {allocation: lg2, vars: [-9, 10]}
        - !<FileTransform> {src: pbrNeutral.cube, interpolation: tetrahedral}
        - !<ColorSpaceTransform> {src: Linear Rec.709, dst: sRGB}

looks:
  - !<Look>
    name: Very High Contrast
    process_space: Filmic Log
    transform: !<GroupTransform>
      children:
        - !<FileTransform> {src: filmic_to_1.20_1-00.spi1d, interpolation: linear}
        - !<FileTransform> {src: filmic_to_0-70_1-03.spi1d, interpolation: linear, direction: inverse}

  - !<Look>
    name: High Contrast
    process_space: Filmic Log
    transform: !<GroupTransform>
      children:
        - !<FileTransform> {src: filmic_to_0.99_1-0075.spi1d, interpolation: linear}
        - !<FileTransform> {src: filmic_to_0-70_1-03.spi1d, interpolation: linear, direction: inverse}

  - !<Look>
    name: Medium High Contrast
    process_space: Filmic Log
    transform: !<GroupTransform>
      children:
        - !<FileTransform> {src: filmic_to_0-85_1-011.spi1d, interpolation: linear}
        - !<FileTransform> {src: filmic_to_0-70_1-03.spi1d, interpolation: linear, direction: inverse}

  - !<Look>
    name: Medium Contrast
    process_space: Filmic Log
    transform: !<GroupTransform>
      children:

  - !<Look>
    name: Medium Low Contrast
    process_space: Filmic Log
    transform: !<GroupTransform>
      children:
        - !<FileTransform> {src: filmic_to_0-60_1-04.spi1d, interpolation: linear}
        - !<FileTransform> {src: filmic_to_0-70_1-03.spi1d, interpolation: linear, direction: inverse}

  - !<Look>
    name: Low Contrast
    process_space: Filmic Log
    transform: !<GroupTransform>
      children:
        - !<FileTransform> {src: filmic_to_0-48_1-09.spi1d, interpolation: linear}
        - !<FileTransform> {src: filmic_to_0-70_1-03.spi1d, interpolation: linear, direction: inverse}

  - !<Look>
    name: Very Low Contrast
    process_space: Filmic Log
    transform: !<GroupTransform>
      children:
        - !<FileTransform> {src: filmic_to_0-35_1-30.spi1d, interpolation: linear}
        - !<FileTransform> {src: filmic_to_0-70_1-03.spi1d, interpolation: linear, direction: inverse}

  - !<Look>
    name: AgX - Punchy
    process_space: AgX Log
    description: A darkening punchy look
    transform: !<GroupTransform>
      children:
        - !<GradingToneTransform>
         shadows: {rgb: [0.2, 0.2, 0.2], master: 0.35, start: 0.4, pivot: 0.1}
        - !<CDLTransform> {power: [1.0912, 1.0912, 1.0912]}

  - !<Look>
    name: AgX - Greyscale
    process_space: AgX Log
    description: A Black and White Look
    transform: !<GroupTransform>
      children:
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117], direction: inverse}
        - !<MatrixTransform> {matrix: [0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0, 0, 0, 1]}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117]}

  - !<Look>
    name: AgX - Very High Contrast
    process_space: AgX Log
    description: A Very High Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [1.57, 1.57, 1.57], master: 1}
          saturation: 0.9
          pivot: {contrast: -0.2}

  - !<Look>
    name: AgX - High Contrast
    process_space: AgX Log
    description: A High Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [1.4, 1.4, 1.4], master: 1}
          saturation: 0.95
          pivot: {contrast: -0.2}

  - !<Look>
    name: AgX - Medium High Contrast
    process_space: AgX Log
    description: A Medium High Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [1.2, 1.2, 1.2], master: 1}
          saturation: 1
          pivot: {contrast: -0.2}

  - !<Look>
    name: AgX - Base Contrast
    process_space: AgX Log
    description: A Base Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [1, 1, 1], master: 1}
          pivot: {contrast: -0.2}

  - !<Look>
    name: AgX - Medium Low Contrast
    process_space: AgX Log
    description: A Medium Low Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [0.9, 0.9, 0.9], master: 1}
          saturation: 1.05
          pivot: {contrast: -0.2}

  - !<Look>
    name: AgX - Low Contrast
    process_space: AgX Log
    description: A Low Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [0.8, 0.8, 0.8], master: 1}
          saturation: 1.1
          pivot: {contrast: -0.2}

  - !<Look>
    name: AgX - Very Low Contrast
    process_space: AgX Log
    description: A Very Low Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [0.7, 0.7, 0.7], master: 1}
          saturation: 1.15
          pivot: {contrast: -0.2}

  - !<Look>
    name: False Color - Punchy
    process_space: AgX Log
    description: A darkening punchy look
    transform: !<GroupTransform>
      children:
        - !<GradingToneTransform>
         shadows: {rgb: [0.2, 0.2, 0.2], master: 0.35, start: 0.4, pivot: 0.1}
        - !<CDLTransform> {power: [1.0912, 1.0912, 1.0912]}

  - !<Look>
    name: False Color - Greyscale
    process_space: AgX Log
    description: A Black and White Look
    transform: !<GroupTransform>
      children:
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117], direction: inverse}
        - !<MatrixTransform> {matrix: [0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0.2658180370250449, 0.59846986045365, 0.1357121025213052, 0, 0, 0, 0, 1]}
        - !<AllocationTransform> {allocation: lg2, vars: [-12.47393, 12.5260688117]}

  - !<Look>
    name: False Color - Very High Contrast
    process_space: AgX Log
    description: A Very High Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [1.57, 1.57, 1.57], master: 1}
          saturation: 0.9
          pivot: {contrast: -0.2}

  - !<Look>
    name: False Color - High Contrast
    process_space: AgX Log
    description: A High Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [1.4, 1.4, 1.4], master: 1}
          saturation: 0.95
          pivot: {contrast: -0.2}

  - !<Look>
    name: False Color - Medium High Contrast
    process_space: AgX Log
    description: A Medium High Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [1.2, 1.2, 1.2], master: 1}
          saturation: 1
          pivot: {contrast: -0.2}

  - !<Look>
    name: False Color - Base Contrast
    process_space: AgX Log
    description: A Base Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [1, 1, 1], master: 1}
          pivot: {contrast: -0.2}

  - !<Look>
    name: False Color - Medium Low Contrast
    process_space: AgX Log
    description: A Medium Low Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [0.9, 0.9, 0.9], master: 1}
          saturation: 1.05
          pivot: {contrast: -0.2}

  - !<Look>
    name: False Color - Low Contrast
    process_space: AgX Log
    description: A Low Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [0.8, 0.8, 0.8], master: 1}
          saturation: 1.1
          pivot: {contrast: -0.2}

  - !<Look>
    name: False Color - Very Low Contrast
    process_space: AgX Log
    description: A Very Low Contrast Look
    transform: !<GroupTransform>
      children:
        - !<GradingPrimaryTransform>
          style: log
          contrast: {rgb: [0.7, 0.7, 0.7], master: 1}
          saturation: 1.15
          pivot: {contrast: -0.2}