<?xml version="1.0"?>
<materialx version="1.39">
  <!--
    OpenPBR Surface node definition
  -->
  <nodedef name="ND_open_pbr_surface_surfaceshader" node="open_pbr_surface" nodegroup="pbr" version="1.1" isdefaultversion="true"
           doc="OpenPBR Surface Shading Model" uiname="OpenPBR Surface">
    <input name="base_weight" type="float" value="1.0" uimin="0.0" uimax="1.0" uiname="Base Weight" uifolder="Base"
           doc="Multiplier on the intensity of the reflection from the diffuse and metallic base." />
    <input name="base_color" type="color3" value="0.8, 0.8, 0.8" uimin="0,0,0" uimax="1,1,1" uiname="Base Color" uifolder="Base"
           doc="Color of the reflection from the diffuse and metallic base." />
    <input name="base_diffuse_roughness" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Base Diffuse Roughness" uifolder="Base" uiadvanced="true"
           doc="Roughness of the diffuse reflection. Higher values cause the surface to appear flatter." />
    <input name="base_metalness" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Base Metalness" uifolder="Base"
           doc="Specifies how metallic the base material appears (dials the base from pure dielectric to pure metal)." />
    <input name="specular_weight" type="float" value="1.0" uimin="0.0" uisoftmax="1.0" uiname="Specular Weight" uifolder="Specular"
           doc="Multiplies the specular reflectivity." />
    <input name="specular_color" type="color3" value="1, 1, 1" uimin="0,0,0" uimax="1,1,1" uiname="Specular Color" uifolder="Specular"
           doc="Color of the specular reflection (controls the physical edge-tint for metals, and a non-physical overall tint for dielectrics)." />
    <input name="specular_roughness" type="float" value="0.3" uimin="0.0" uimax="1.0" uiname="Specular Roughness" uifolder="Specular"
           doc="The roughness of the specular reflection. Lower numbers produce sharper reflections, higher numbers produce blurrier reflections." />
    <input name="specular_ior" type="float" value="1.5" uimin="0.0" uisoftmin="1.0" uisoftmax="3.0" uiname="Specular Index of Refraction" uifolder="Specular"
           doc="Index of refraction of the dielectric base." />
    <input name="specular_roughness_anisotropy" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Specular Anisotropy" uifolder="Specular" uiadvanced="true"
           doc="The directional bias of the roughness of the metal/dielectric base, resulting in increasingly stretched highlights along the tangent direction." />
    <input name="transmission_weight" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Transmission Weight" uifolder="Transmission" uiadvanced="true" hint="transparency"
           doc="Mixture weight between the transparent and opaque dielectric base. The greater the value the more transparent the material." />
    <input name="transmission_color" type="color3" value="1, 1, 1" uimin="0,0,0" uimax="1,1,1" uiname="Transmission Color" uifolder="Transmission" uiadvanced="true"
           doc="Controls color of the transparent base due to Beer's law volumetric absorption under the surface (reverts to a non-physical tint when transmission_depth is zero)." />
    <input name="transmission_depth" type="float" value="0.0" uimin="0.0" uisoftmax="1.0" uiname="Transmission Depth" uifolder="Transmission" uiadvanced="true"
           doc="Specifies the distance light travels inside the transparent base before it becomes exactly the transmission_color according to Beer's law." />
    <input name="transmission_scatter" type="color3" value="0, 0, 0" uimin="0,0,0" uimax="1,1,1" uiname="Transmission Scatter" uifolder="Transmission" uiadvanced="true"
           doc="Controls the color of light volumetrically scattered inside the transparent base. Suitable for materials with visually significant scattering such as honey, fruit juice, murky water, opalescent glass, or milky glass." />
    <input name="transmission_scatter_anisotropy" type="float" value="0.0" uimin="-1.0" uimax="1.0" uiname="Transmission Anisotropy" uifolder="Transmission" uiadvanced="true"
           doc="The amount of directional bias, or anisotropy, of the volumetric scattering in the transparent base." />
    <input name="transmission_dispersion_scale" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Transmission Dispersion Scale" uifolder="Transmission" uiadvanced="true"
           doc="Linearly scales the amount of dispersion." />
    <input name="transmission_dispersion_abbe_number" type="float" value="20.0" uimin="0.0" uisoftmin="9.0" uisoftmax="91.0" uiname="Transmission Dispersion Abbe Number" uifolder="Transmission" uiadvanced="true"
           doc="Physical Abbe number of the dielectric medium, describing how much the dielectric index of refraction varies across wavelengths." />
    <input name="subsurface_weight" type="float" value="0" uimin="0.0" uimax="1.0" uiname="Subsurface Weight" uifolder="Subsurface" uiadvanced="true"
           doc="Mixture weight which dials the opaque dielectric base between diffuse reflection and subsurface scattering. A value of 1.0 indicates full subsurface scattering and a value 0 for diffuse reflection only." />
    <input name="subsurface_color" type="color3" value="0.8, 0.8, 0.8" uimin="0,0,0" uimax="1,1,1" uiname="Subsurface Color" uifolder="Subsurface" uiadvanced="true"
           doc="The observed reflection color of the subsurface scattering medium." />
    <input name="subsurface_radius" type="float" value="1.0" uimin="0.0" uisoftmax="1.0" uiname="Subsurface Radius" uifolder="Subsurface" uiadvanced="true"
           doc="Length scale of the subsurface scattering mean free path." />
    <input name="subsurface_radius_scale" type="color3" value="1.0, 0.5, 0.25" uimin="0,0,0" uimax="1,1,1" uiname="Subsurface Radius Scale" uifolder="Subsurface" uiadvanced="true"
           doc="RGB multiplier to subsurface_radius, giving the per-channel scattering mean-free-paths." />
    <input name="subsurface_scatter_anisotropy" type="float" value="0.0" uimin="-1.0" uimax="1.0" uiname="Subsurface Anisotropy" uifolder="Subsurface" uiadvanced="true"
           doc="Controls the phase-function of subsurface scattering, where zero scatters light evenly, positive values scatter forwards, and negative values scatter backwards." />
    <input name="fuzz_weight" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Fuzz Weight" uifolder="Fuzz" uiadvanced="true"
           doc="The presence weight of a fuzz layer that can be used to approximate microfibers, for fabrics such as velvet and satin as well as dust grains." />
    <input name="fuzz_color" type="color3" value="1, 1, 1" uimin="0,0,0" uimax="1,1,1" uiname="Fuzz Color" uifolder="Fuzz" uiadvanced="true"
           doc="The color of the fuzz layer." />
    <input name="fuzz_roughness" type="float" value="0.5" uimin="0.0" uimax="1.0" uiname="Fuzz Roughness" uifolder="Fuzz" uiadvanced="true"
           doc="The roughness of the fuzz layer." />
    <input name="coat_weight" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Coat Weight" uifolder="Coat"
           doc="The presence weight of a reflective clear-coat layer on top of the material. Use for materials such as car paint or an oily layer." />
    <input name="coat_color" type="color3" value="1, 1, 1" uimin="0,0,0" uimax="1,1,1" uiname="Coat Color" uifolder="Coat"
           doc="The color of the clear-coat layer's transparency, due to absorption in the coat." />
    <input name="coat_roughness" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Coat Roughness" uifolder="Coat"
           doc="The roughness of the clear-coat reflections. The lower the value, the sharper the reflection." />
    <input name="coat_roughness_anisotropy" type="float" value="0.0" uimin="0.0" uimax="1.0" uiname="Coat Anisotropy" uifolder="Coat" uiadvanced="true"
           doc="The directional bias of the roughness of the clear-coat layer, resulting in increasingly stretched highlights along the coat tangent direction." />
    <input name="coat_ior" type="float" value="1.6" uimin="0.0" uisoftmin="1.0" uisoftmax="3.0" uiname="Coat Index of Refraction" uifolder="Coat"
           doc="The index of refraction of the clear-coat layer." />
    <input name="coat_darkening" type="float" value="1.0" uimin="0.0" uimax="1.0" uiname="Coat Darkening" uifolder="Coat"
           doc="Modulates the physical coat darkening effect." />
    <input name="thin_film_weight" type="float" value="0" uimin="0.0" uimax="1.0" uiname="Thin Film Weight" uifolder="Thin Film" uiadvanced="true"
           doc="Coverage weight of the thin-film. Use for materials such as multi-tone car paint or soap bubbles." />
    <input name="thin_film_thickness" type="float" value="0.5" uimin="0.0" uisoftmax="1.0" uiname="Thin Film Thickness" uifolder="Thin Film" uiadvanced="true"
           doc="The thickness of the thin-film layer on the base (in micrometers)." />
    <input name="thin_film_ior" type="float" value="1.4" uimin="0.0" uisoftmin="1.0" uisoftmax="3.0" uiname="Thin Film Index of Refraction" uifolder="Thin Film" uiadvanced="true"
           doc="The index of refraction of the thin-film." />
    <input name="emission_luminance" type="float" value="0.0" uimin="0.0" uisoftmax="1000.0" uiname="Emission Luminance" uifolder="Emission"
           doc="The amount of emitted light, as a luminance in nits." />
    <input name="emission_color" type="color3" value="1, 1, 1" uimin="0,0,0" uimax="1,1,1" uiname="Emission Color" uifolder="Emission"
           doc="The color of the emitted light." />
    <input name="geometry_opacity" type="float" value="1" uimin="0" uimax="1" uiname="Opacity" uifolder="Geometry" hint="opacity"
           doc="The opacity of the entire material." />
    <input name="geometry_thin_walled" type="boolean" value="false" uiname="Thin Walled" uifolder="Geometry" uiadvanced="true"
           doc="If true the surface is double-sided and represents an infinitesimally thin shell. Suitable for extremely geometrically thin objects such as leaves or paper." />
    <input name="geometry_normal" type="vector3" defaultgeomprop="Nworld" uiname="Normal" uifolder="Geometry"
           doc="Input geometric normal" />
    <input name="geometry_coat_normal" type="vector3" defaultgeomprop="Nworld" uiname="Coat Normal" uifolder="Geometry"
           doc="Input normal for coat layer" />
    <input name="geometry_tangent" type="vector3" defaultgeomprop="Tworld" uiname="Tangent" uifolder="Geometry"
           doc="Input geometric tangent" />
    <input name="geometry_coat_tangent" type="vector3" defaultgeomprop="Tworld" uiname="Coat Tangent" uifolder="Geometry"
           doc="Input geometric tangent for coat layer" />
    <output name="out" type="surfaceshader" />
  </nodedef>

  <!--
    OpenPBR Surface graph definition
  -->
  <nodegraph name="NG_open_pbr_surface_surfaceshader" nodedef="ND_open_pbr_surface_surfaceshader">

    <!-- Roughening due to coat-->
    <power name="coat_roughness_to_power_4" type="float">
      <input name="in1" type="float" interfacename="coat_roughness" />
      <input name="in2" type="float" value="4.0" />
    </power>
    <multiply name="two_times_coat_roughness_to_power_4" type="float">
      <input name="in1" type="float" nodename="coat_roughness_to_power_4" />
      <input name="in2" type="float" value="2.0" />
    </multiply>
    <power name="specular_roughness_to_power_4" type="float">
      <input name="in1" type="float" interfacename="specular_roughness" />
      <input name="in2" type="float" value="4.0" />
    </power>
    <add name="add_coat_and_spec_roughnesses_to_power_4" type="float">
      <input name="in1" type="float" nodename="two_times_coat_roughness_to_power_4" />
      <input name="in2" type="float" nodename="specular_roughness_to_power_4" />
    </add>
    <min name="min_1_add_coat_and_spec_roughnesses_to_power_4" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="add_coat_and_spec_roughnesses_to_power_4" />
    </min>
    <power name="coat_affected_specular_roughness" type="float">
      <input name="in1" type="float" nodename="min_1_add_coat_and_spec_roughnesses_to_power_4" />
      <input name="in2" type="float" value="0.25" />
    </power>
    <mix name="effective_specular_roughness" type="float">
      <input name="fg" type="float" nodename="coat_affected_specular_roughness" />
      <input name="bg" type="float" interfacename="specular_roughness" />
      <input name="mix" type="float" interfacename="coat_weight" />
    </mix>

    <!-- Calculate main specular roughness -->
    <open_pbr_anisotropy name="main_roughness" type="vector2">
      <input name="roughness" type="float" nodename="effective_specular_roughness" />
      <input name="anisotropy" type="float" interfacename="specular_roughness_anisotropy" />
    </open_pbr_anisotropy>

    <!-- Subsurface (thin-walled) -->
    <max name="subsurface_color_nonnegative" type="color3">
      <input name="in1" type="color3" interfacename="subsurface_color" />
      <input name="in2" type="float" value="0.0" />
    </max>
    <oren_nayar_diffuse_bsdf name="subsurface_thin_walled_reflection_bsdf" type="BSDF">
      <input name="color" type="color3" nodename="subsurface_color_nonnegative" />
      <input name="roughness" type="float" interfacename="base_diffuse_roughness" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
    </oren_nayar_diffuse_bsdf>
    <subtract name="one_minus_subsurface_scatter_anisotropy" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="subsurface_scatter_anisotropy" />
    </subtract>
    <multiply name="subsurface_thin_walled_brdf_factor" type="color3">
      <input name="in1" type="color3" interfacename="subsurface_color" />
      <input name="in2" type="float" nodename="one_minus_subsurface_scatter_anisotropy" />
    </multiply>
    <multiply name="subsurface_thin_walled_reflection" type="BSDF">
      <input name="in1" type="BSDF" nodename="subsurface_thin_walled_reflection_bsdf" />
      <input name="in2" type="color3" nodename="subsurface_thin_walled_brdf_factor" />
    </multiply>
    <translucent_bsdf name="subsurface_thin_walled_transmission_bsdf" type="BSDF">
      <input name="color" type="color3" nodename="subsurface_color_nonnegative" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
    </translucent_bsdf>
    <add name="one_plus_subsurface_scatter_anisotropy" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="subsurface_scatter_anisotropy" />
    </add>
    <multiply name="subsurface_thin_walled_btdf_factor" type="color3">
      <input name="in1" type="color3" interfacename="subsurface_color" />
      <input name="in2" type="float" nodename="one_plus_subsurface_scatter_anisotropy" />
    </multiply>
    <multiply name="subsurface_thin_walled_transmission" type="BSDF">
      <input name="in1" type="BSDF" nodename="subsurface_thin_walled_transmission_bsdf" />
      <input name="in2" type="color3" nodename="subsurface_thin_walled_btdf_factor" />
    </multiply>
    <mix name="subsurface_thin_walled" type="BSDF">
      <input name="fg" type="BSDF" nodename="subsurface_thin_walled_reflection" />
      <input name="bg" type="BSDF" nodename="subsurface_thin_walled_transmission" />
      <input name="mix" type="float" value="0.5" />
    </mix>

    <!-- Subsurface (non-thin-walled) -->
    <multiply name="subsurface_radius_scaled" type="color3">
      <input name="in1" type="color3" interfacename="subsurface_radius_scale" />
      <input name="in2" type="float" interfacename="subsurface_radius" />
    </multiply>
    <subsurface_bsdf name="subsurface_bsdf" type="BSDF">
      <input name="color" type="color3" nodename="subsurface_color_nonnegative" />
      <input name="radius" type="color3" nodename="subsurface_radius_scaled" />
      <input name="anisotropy" type="float" interfacename="subsurface_scatter_anisotropy" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
    </subsurface_bsdf>

    <!-- Opaque Dielectric Base -->
    <max name="base_color_nonnegative" type="color3">
      <input name="in1" type="color3" interfacename="base_color" />
      <input name="in2" type="float" value="0.0" />
    </max>
    <oren_nayar_diffuse_bsdf name="diffuse_bsdf" type="BSDF">
      <input name="weight" type="float" interfacename="base_weight" />
      <input name="color" type="color3" nodename="base_color_nonnegative" />
      <input name="roughness" type="float" interfacename="base_diffuse_roughness" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
      <input name="energy_compensation" type="boolean" value="true" />
    </oren_nayar_diffuse_bsdf>
    <convert name="subsurface_selector" type="float">
      <input name="in" type="boolean" interfacename="geometry_thin_walled" />
    </convert>
    <mix name="selected_subsurface" type="BSDF">
      <input name="fg" type="BSDF" nodename="subsurface_thin_walled" />
      <input name="bg" type="BSDF" nodename="subsurface_bsdf" />
      <input name="mix" type="float" nodename="subsurface_selector" />
    </mix>
    <mix name="opaque_base" type="BSDF">
      <input name="fg" type="BSDF" nodename="selected_subsurface" />
      <input name="bg" type="BSDF" nodename="diffuse_bsdf" />
      <input name="mix" type="float" interfacename="subsurface_weight" />
    </mix>

    <!-- Dielectric Base VDF -->
    <convert name="transmission_color_vector" type="vector3">
      <input name="in" type="color3" interfacename="transmission_color" />
    </convert>
    <ln name="transmission_color_ln" type="vector3">
      <input name="in" type="vector3" nodename="transmission_color_vector" />
    </ln>
    <multiply name="extinction_coeff_denom" type="vector3">
      <input name="in1" type="vector3" nodename="transmission_color_ln" />
      <input name="in2" type="float" value="-1.0" />
    </multiply>
    <convert name="transmission_depth_vector" type="vector3">
      <input name="in" type="float" interfacename="transmission_depth" />
    </convert>
    <divide name="extinction_coeff" type="vector3">
      <input name="in1" type="vector3" nodename="extinction_coeff_denom" />
      <input name="in2" type="vector3" nodename="transmission_depth_vector" />
    </divide>
    <convert name="transmission_scatter_vector" type="vector3">
      <input name="in" type="color3" interfacename="transmission_scatter" />
    </convert>
    <divide name="scattering_coeff" type="vector3">
      <input name="in1" type="vector3" nodename="transmission_scatter_vector" />
      <input name="in2" type="vector3" nodename="transmission_depth_vector" />
    </divide>
    <subtract name="absorption_coeff" type="vector3">
      <input name="in1" type="vector3" nodename="extinction_coeff" />
      <input name="in2" type="vector3" nodename="scattering_coeff" />
    </subtract>
    <extract name="absorption_coeff_x" type="float">
      <input name="in" type="vector3" nodename="absorption_coeff" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="absorption_coeff_y" type="float">
      <input name="in" type="vector3" nodename="absorption_coeff" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="absorption_coeff_z" type="float">
      <input name="in" type="vector3" nodename="absorption_coeff" />
      <input name="index" type="integer" value="2" />
    </extract>
    <min name="absorption_coeff_min_x_y" type="float">
      <input name="in1" type="float" nodename="absorption_coeff_x" />
      <input name="in2" type="float" nodename="absorption_coeff_y" />
    </min>
    <min name="absorption_coeff_min" type="float">
      <input name="in1" type="float" nodename="absorption_coeff_min_x_y" />
      <input name="in2" type="float" nodename="absorption_coeff_z" />
    </min>
    <convert name="absorption_coeff_min_vector" type="vector3">
      <input name="in" type="float" nodename="absorption_coeff_min" />
    </convert>
    <subtract name="absorption_coeff_shifted" type="vector3">
      <input name="in1" type="vector3" nodename="absorption_coeff" />
      <input name="in2" type="vector3" nodename="absorption_coeff_min_vector" />
    </subtract>
    <ifgreater name="if_absorption_coeff_shifted" type="vector3">
      <input name="value1" type="float" value="0.0" />
      <input name="value2" type="float" nodename="absorption_coeff_min" />
      <input name="in1" type="vector3" nodename="absorption_coeff_shifted" />
      <input name="in2" type="vector3" nodename="absorption_coeff" />
    </ifgreater>
    <ifgreater name="if_volume_absorption" type="vector3">
      <input name="value1" type="float" interfacename="transmission_depth" />
      <input name="value2" type="float" value="0.0" />
      <input name="in1" type="vector3" nodename="if_absorption_coeff_shifted" />
      <input name="in2" type="vector3" value="0.0,0.0,0.0" />
    </ifgreater>
    <ifgreater name="if_volume_scattering" type="vector3">
      <input name="value1" type="float" interfacename="transmission_depth" />
      <input name="value2" type="float" value="0.0" />
      <input name="in1" type="vector3" nodename="scattering_coeff" />
      <input name="in2" type="vector3" value="0.0,0.0,0.0" />
    </ifgreater>
    <anisotropic_vdf name="dielectric_volume" type="VDF">
      <input name="absorption" type="vector3" nodename="if_volume_absorption" />
      <input name="scattering" type="vector3" nodename="if_volume_scattering" />
      <input name="anisotropy" type="float" interfacename="transmission_scatter_anisotropy" />
    </anisotropic_vdf>

    <!-- Thin-film Thickness -->
    <multiply name="thin_film_thickness_nm" type="float">
      <input name="in1" type="float" interfacename="thin_film_thickness" />
      <input name="in2" type="float" value="1000.0" />
    </multiply>

    <!-- Dielectric Base -->
      <!-- apply IOR ratio inversion method to avoid TIR artifact (as in Coat TIR section of spec) -->
      <divide name="specular_to_coat_ior_ratio" type="float">
         <input name="in1" type="float" interfacename="specular_ior" />
         <input name="in2" type="float" interfacename="coat_ior" />
      </divide>
      <divide name="coat_to_specular_ior_ratio" type="float">
         <input name="in1" type="float" interfacename="coat_ior" />
         <input name="in2" type="float" interfacename="specular_ior" />
      </divide>
      <ifgreater name="specular_to_coat_ior_ratio_tir_fix" type="float">
         <input name="value1" type="float" nodename="specular_to_coat_ior_ratio" />
         <input name="value2" type="float" value="1.0" />
         <input name="in1" type="float" nodename="specular_to_coat_ior_ratio" />
         <input name="in2" type="float" nodename="coat_to_specular_ior_ratio" />
      </ifgreater>
    <mix name="eta_s" type="float">
      <input name="fg" type="float" nodename="specular_to_coat_ior_ratio_tir_fix" />
      <input name="bg" type="float" interfacename="specular_ior" />
      <input name="mix" type="float" interfacename="coat_weight" />
    </mix>
    <subtract name="eta_s_minus_one" type="float">
      <input name="in1" type="float" nodename="eta_s" />
      <input name="in2" type="float" value="1.0" />
    </subtract>
    <add name="eta_s_plus_one" type="float">
      <input name="in1" type="float" nodename="eta_s" />
      <input name="in2" type="float" value="1.0" />
    </add>
    <divide name="specular_F0_sqrt" type="float">
      <input name="in1" type="float" nodename="eta_s_minus_one" />
      <input name="in2" type="float" nodename="eta_s_plus_one" />
    </divide>
    <multiply name="specular_F0" type="float">
      <input name="in1" type="float" nodename="specular_F0_sqrt" />
      <input name="in2" type="float" nodename="specular_F0_sqrt" />
    </multiply>
    <multiply name="scaled_specular_F0" type="float">
      <input name="in1" type="float" interfacename="specular_weight" />
      <input name="in2" type="float" nodename="specular_F0" />
    </multiply>
    <clamp name="scaled_specular_F0_clamped" type="float">
      <input name="in" type="float" nodename="scaled_specular_F0" />
      <input name="low" type="float" value="0.0" />
      <input name="high" type="float" value="0.99999" />
    </clamp>
    <sqrt name="sqrt_scaled_specular_F0" type="float">
      <input name="in" type="float" nodename="scaled_specular_F0_clamped" />
    </sqrt>
    <sign name="sign_eta_s_minus_one" type="float">
      <input name="in" type="float" nodename="eta_s_minus_one" />
    </sign>
    <multiply name="modulated_eta_s_epsilon" type="float">
      <input name="in1" type="float" nodename="sign_eta_s_minus_one" />
      <input name="in2" type="float" nodename="sqrt_scaled_specular_F0" />
    </multiply>
    <subtract name="one_minus_modulated_eta_s_epsilon" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="modulated_eta_s_epsilon" />
    </subtract>
    <add name="one_plus_modulated_eta_s_epsilon" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="modulated_eta_s_epsilon" />
    </add>
    <divide name="modulated_eta_s" type="float">
      <input name="in1" type="float" nodename="one_plus_modulated_eta_s_epsilon" />
      <input name="in2" type="float" nodename="one_minus_modulated_eta_s_epsilon" />
    </divide>
    <ifgreater name="if_transmission_tint" type="color3">
      <input name="value1" type="float" interfacename="transmission_depth" />
      <input name="value2" type="float" value="0.0" />
      <input name="in1" type="color3" value="1.0, 1.0, 1.0" />
      <input name="in2" type="color3" interfacename="transmission_color" />
    </ifgreater>
    <dielectric_bsdf name="dielectric_transmission" type="BSDF">
      <input name="tint" type="color3" nodename="if_transmission_tint" />
      <input name="ior" type="float" nodename="modulated_eta_s" />
      <input name="roughness" type="vector2" nodename="main_roughness" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
      <input name="tangent" type="vector3" interfacename="geometry_tangent" />
      <input name="scatter_mode" type="string" value="T" />
    </dielectric_bsdf>
    <layer name="dielectric_volume_transmission" type="BSDF">
      <input name="top" type="BSDF" nodename="dielectric_transmission" />
      <input name="base" type="VDF" nodename="dielectric_volume" />
    </layer>
    <mix name="dielectric_substrate" type="BSDF">
      <input name="fg" type="BSDF" nodename="dielectric_volume_transmission" />
      <input name="bg" type="BSDF" nodename="opaque_base" />
      <input name="mix" type="float" interfacename="transmission_weight" />
    </mix>
    <dielectric_bsdf name="dielectric_reflection" type="BSDF">
      <input name="tint" type="color3" interfacename="specular_color" />
      <input name="ior" type="float" nodename="modulated_eta_s" />
      <input name="roughness" type="vector2" nodename="main_roughness" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
      <input name="tangent" type="vector3" interfacename="geometry_tangent" />
      <input name="scatter_mode" type="string" value="R" />
    </dielectric_bsdf>
    <dielectric_bsdf name="dielectric_reflection_tf" type="BSDF">
      <input name="tint" type="color3" interfacename="specular_color" />
      <input name="ior" type="float" nodename="modulated_eta_s" />
      <input name="roughness" type="vector2" nodename="main_roughness" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
      <input name="tangent" type="vector3" interfacename="geometry_tangent" />
      <input name="scatter_mode" type="string" value="R" />
      <input name="thinfilm_thickness" type="float" nodename="thin_film_thickness_nm" />
      <input name="thinfilm_ior" type="float" interfacename="thin_film_ior" />
    </dielectric_bsdf>
    <mix name="dielectric_reflection_tf_mix" type="BSDF">
      <input name="fg" type="BSDF" nodename="dielectric_reflection_tf" />
      <input name="bg" type="BSDF" nodename="dielectric_reflection" />
      <input name="mix" type="float" interfacename="thin_film_weight" />
    </mix>
    <layer name="dielectric_base" type="BSDF">
      <input name="top" type="BSDF" nodename="dielectric_reflection_tf_mix" />
      <input name="base" type="BSDF" nodename="dielectric_substrate" />
    </layer>

    <!-- Metal Layer -->
    <multiply name="metal_reflectivity" type="color3">
      <input name="in1" type="color3" interfacename="base_color" />
      <input name="in2" type="float" interfacename="base_weight" />
    </multiply>
    <multiply name="metal_edgecolor" type="color3">
      <input name="in1" type="color3" interfacename="specular_color" />
      <input name="in2" type="float" interfacename="specular_weight" />
    </multiply>
    <generalized_schlick_bsdf name="metal_bsdf" type="BSDF">
      <input name="weight" type="float" interfacename="specular_weight" />
      <input name="color0" type="color3" nodename="metal_reflectivity" />
      <input name="color82" type="color3" nodename="metal_edgecolor" />
      <input name="roughness" type="vector2" nodename="main_roughness" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
      <input name="tangent" type="vector3" interfacename="geometry_tangent" />
    </generalized_schlick_bsdf>
    <generalized_schlick_bsdf name="metal_bsdf_tf" type="BSDF">
      <input name="weight" type="float" interfacename="specular_weight" />
      <input name="color0" type="color3" nodename="metal_reflectivity" />
      <input name="color82" type="color3" nodename="metal_edgecolor" />
      <input name="roughness" type="vector2" nodename="main_roughness" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
      <input name="tangent" type="vector3" interfacename="geometry_tangent" />
      <input name="thinfilm_thickness" type="float" nodename="thin_film_thickness_nm" />
      <input name="thinfilm_ior" type="float" interfacename="thin_film_ior" />
    </generalized_schlick_bsdf>
    <mix name="metal_bsdf_tf_mix" type="BSDF">
      <input name="fg" type="BSDF" nodename="metal_bsdf_tf" />
      <input name="bg" type="BSDF" nodename="metal_bsdf" />
      <input name="mix" type="float" interfacename="thin_film_weight" />
    </mix>
    <mix name="base_substrate" type="BSDF">
      <input name="fg" type="BSDF" nodename="metal_bsdf_tf_mix" />
      <input name="bg" type="BSDF" nodename="dielectric_base" />
      <input name="mix" type="float" interfacename="base_metalness" />
    </mix>

    <!-- Coat darkening calculation  -->
    <!-- approximate Kcoat, "internal diffuse reflection coefficient" of coat  -->
    <subtract name="one_minus_coat_F0" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="coat_ior_to_F0" />
    </subtract>
    <multiply name="coat_ior_sqr" type="float">
      <input name="in1" type="float" interfacename="coat_ior" />
      <input name="in2" type="float" interfacename="coat_ior" />
    </multiply>
    <divide name="one_minus_coat_F0_over_eta2" type="float">
      <input name="in1" type="float" nodename="one_minus_coat_F0" />
      <input name="in2" type="float" nodename="coat_ior_sqr" />
    </divide>
    <subtract name="Kcoat" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="one_minus_coat_F0_over_eta2" />
    </subtract>
    <!-- approximate base metal albedo estimate, Emetal  -->
    <multiply name="Emetal" type="color3">
      <input name="in1" type="color3" interfacename="base_color" />
      <input name="in2" type="float" interfacename="specular_weight" />
    </multiply>
    <!-- approximate base dielectric albedo estimate, Edielectric  -->
    <mix name="Edielectric" type="color3">
      <input name="fg" type="color3" interfacename="subsurface_color" />
      <input name="bg" type="color3" interfacename="base_color" />
      <input name="mix" type="float" interfacename="subsurface_weight" />
    </mix>
    <!-- thus calculate overall base albedo estimate approximation, Ebase  -->
    <mix name="Ebase" type="color3">
      <input name="fg" type="color3" nodename="Emetal" />
      <input name="bg" type="color3" nodename="Edielectric" />
      <input name="mix" type="float" interfacename="base_metalness" />
    </mix>
    <!-- final base darkening factor due to coat:  base_darkening = (1 - Kcoat) / (1 - Ebase*Kcoat)  -->
    <multiply name="Ebase_Kcoat" type="color3">
      <input name="in1" type="color3" nodename="Ebase" />
      <input name="in2" type="float" nodename="Kcoat" />
    </multiply>
    <subtract name="one_minus_Kcoat" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="Kcoat" />
    </subtract>
    <subtract name="one_minus_Ebase_Kcoat" type="color3">
      <input name="in1" type="color3" value="1.0, 1.0, 1.0" />
      <input name="in2" type="color3" nodename="Ebase_Kcoat" />
    </subtract>
    <convert name="one_minus_Kcoat_color" type="color3">
      <input name="in" type="float" nodename="one_minus_Kcoat" />
    </convert>
    <divide name="base_darkening" type="color3">
      <input name="in1" type="color3" nodename="one_minus_Kcoat_color" />
      <input name="in2" type="color3" nodename="one_minus_Ebase_Kcoat" />
    </divide>
    <multiply name="coat_weight_times_coat_darkening" type="float">
      <input name="in1" type="float" interfacename="coat_weight" />
      <input name="in2" type="float" interfacename="coat_darkening" />
    </multiply>
    <mix name="modulated_base_darkening" type="color3">
      <input name="fg" type="color3" nodename="base_darkening" />
      <input name="bg" type="color3" value="1.0, 1.0, 1.0" />
      <input name="mix" type="float" nodename="coat_weight_times_coat_darkening" />
    </mix>
    <multiply name="darkened_base_substrate" type="BSDF">
      <input name="in1" type="BSDF" nodename="base_substrate" />
      <input name="in2" type="color3" nodename="modulated_base_darkening" />
    </multiply>

    <!-- Coat Layer -->
    <mix name="coat_attenuation" type="color3">
      <input name="fg" type="color3" interfacename="coat_color" />
      <input name="bg" type="color3" value="1.0, 1.0, 1.0" />
      <input name="mix" type="float" interfacename="coat_weight" />
    </mix>
    <multiply name="coat_substrate_attenuated" type="BSDF">
      <input name="in1" type="BSDF" nodename="darkened_base_substrate" />
      <input name="in2" type="color3" nodename="coat_attenuation" />
    </multiply>

    <open_pbr_anisotropy name="coat_roughness_vector" type="vector2">
      <input name="roughness" type="float" interfacename="coat_roughness" />
      <input name="anisotropy" type="float" interfacename="coat_roughness_anisotropy" />
    </open_pbr_anisotropy>
    <dielectric_bsdf name="coat_bsdf" type="BSDF">
      <input name="weight" type="float" interfacename="coat_weight" />
      <input name="ior" type="float" interfacename="coat_ior" />
      <input name="roughness" type="vector2" nodename="coat_roughness_vector" />
      <input name="normal" type="vector3" interfacename="geometry_coat_normal" />
      <input name="tangent" type="vector3" interfacename="geometry_coat_tangent" />
      <input name="scatter_mode" type="string" value="R" />
    </dielectric_bsdf>
    <layer name="coat_layer" type="BSDF">
      <input name="top" type="BSDF" nodename="coat_bsdf" />
      <input name="base" type="BSDF" nodename="coat_substrate_attenuated" />
    </layer>

    <!-- Fuzz Layer -->
    <sheen_bsdf name="fuzz_bsdf" type="BSDF">
      <input name="weight" type="float" interfacename="fuzz_weight" />
      <input name="color" type="color3" interfacename="fuzz_color" />
      <input name="roughness" type="float" interfacename="fuzz_roughness" />
      <input name="normal" type="vector3" interfacename="geometry_normal" />
      <input name="mode" type="string" value="zeltner" />
    </sheen_bsdf>
    <layer name="fuzz_layer" type="BSDF">
      <input name="top" type="BSDF" nodename="fuzz_bsdf" />
      <input name="base" type="BSDF" nodename="coat_layer" />
    </layer>

    <!-- Emission Layer -->
    <subtract name="coat_ior_minus_one" type="float">
      <input name="in1" type="float" interfacename="coat_ior" />
      <input name="in2" type="float" value="1.0" />
    </subtract>
    <add name="coat_ior_plus_one" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="coat_ior" />
    </add>
    <divide name="coat_ior_to_F0_sqrt" type="float">
      <input name="in1" type="float" nodename="coat_ior_minus_one" />
      <input name="in2" type="float" nodename="coat_ior_plus_one" />
    </divide>
    <multiply name="coat_ior_to_F0" type="float">
      <input name="in1" type="float" nodename="coat_ior_to_F0_sqrt" />
      <input name="in2" type="float" nodename="coat_ior_to_F0_sqrt" />
    </multiply>
    <multiply name="emission_weight" type="color3">
      <input name="in1" type="color3" interfacename="emission_color" />
      <input name="in2" type="float" interfacename="emission_luminance" />
    </multiply>
    <uniform_edf name="uncoated_emission_edf" type="EDF">
      <input name="color" type="color3" nodename="emission_weight" />
    </uniform_edf>
    <multiply name="coat_tinted_emission_edf" type="EDF">
      <input name="in1" type="EDF" nodename="uncoated_emission_edf" />
      <input name="in2" type="color3" interfacename="coat_color" />
    </multiply>
    <convert name="one_minus_coat_F0_color" type="color3">
      <input name="in" type="float" nodename="one_minus_coat_F0" />
    </convert>
    <generalized_schlick_edf name="coated_emission_edf" type="EDF">
      <input name="color0" type="color3" nodename="one_minus_coat_F0_color" />
      <input name="color90" type="color3" value="0.0, 0.0, 0.0" />
      <input name="exponent" type="float" value="5.0" />
      <input name="base" type="EDF" nodename="coat_tinted_emission_edf" />
    </generalized_schlick_edf>
    <mix name="emission_edf" type="EDF">
      <input name="fg" type="EDF" nodename="coated_emission_edf" />
      <input name="bg" type="EDF" nodename="uncoated_emission_edf" />
      <input name="mix" type="float" interfacename="coat_weight" />
    </mix>

    <!-- Surface Construction -->
    <surface name="shader_constructor" type="surfaceshader">
      <input name="bsdf" type="BSDF" nodename="fuzz_layer" />
      <input name="edf" type="EDF" nodename="emission_edf" />
      <input name="opacity" type="float" interfacename="geometry_opacity" />
    </surface>

    <!-- Output -->
    <output name="out" type="surfaceshader" nodename="shader_constructor" />

  </nodegraph>

  <!--
    OpenPBR Anisotropy node definition
  -->
  <nodedef name="ND_open_pbr_anisotropy" node="open_pbr_anisotropy" nodegroup="pbr"
           doc="Computes anisotropic surface roughness as defined in the OpenPBR specification.">
    <input name="roughness" type="float" value="0.0" />
    <input name="anisotropy" type="float" value="0.0" />
    <output name="out" type="vector2" />
  </nodedef>

  <!--
    OpenPBR Anisotropy graph definition
  -->
  <nodegraph name="NG_open_pbr_anisotropy" nodedef="ND_open_pbr_anisotropy">
    <invert name="aniso_invert" type="float">
      <input name="in" type="float" interfacename="anisotropy" />
    </invert>
    <multiply name="aniso_invert_sq" type="float">
      <input name="in1" type="float" nodename="aniso_invert" />
      <input name="in2" type="float" nodename="aniso_invert" />
    </multiply>
    <add name="denom" type="float">
      <input name="in1" type="float" nodename="aniso_invert_sq" />
      <input name="in2" type="float" value="1.0" />
    </add>
    <divide name="fraction" type="float">
      <input name="in1" type="float" value="2.0" />
      <input name="in2" type="float" nodename="denom" />
    </divide>
    <sqrt name="sqrt" type="float">
      <input name="in" type="float" nodename="fraction" />
    </sqrt>
    <multiply name="rough_sq" type="float">
      <input name="in1" type="float" interfacename="roughness" />
      <input name="in2" type="float" interfacename="roughness" />
    </multiply>
    <multiply name="alpha_x" type="float">
      <input name="in1" type="float" nodename="rough_sq" />
      <input name="in2" type="float" nodename="sqrt" />
    </multiply>
    <multiply name="alpha_y" type="float">
      <input name="in1" type="float" nodename="aniso_invert" />
      <input name="in2" type="float" nodename="alpha_x" />
    </multiply>
    <combine2 name="result" type="vector2">
      <input name="in1" type="float" nodename="alpha_x" />
      <input name="in2" type="float" nodename="alpha_y" />
    </combine2>
    <output name="out" type="vector2" nodename="result" />
  </nodegraph>

</materialx>
