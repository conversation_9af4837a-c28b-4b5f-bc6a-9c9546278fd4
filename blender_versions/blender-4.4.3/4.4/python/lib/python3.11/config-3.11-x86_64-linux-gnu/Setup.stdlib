# -*- makefile -*-
#
# This file is autogenerated from Modules/Setup.stdlib.in.
#

# The file is not used by default yet. For testing do:
#
#     ln -sfr Modules/Setup.stdlib Modules/Setup.local
#

# * @MODULE_{NAME}_TRUE@ is removed when configure detects all build
#   dependencies for a module. Otherwise the template variable is replaced
#   by a comment "#" and the module is skipped.
# * Module lines without any compiler and linker flags automatically use
#   $(MODULE_{NAME}_CFLAGS) and $(MODULE_{NAME}_LDFLAGS). These flags are
#   generated by PY_STDLIB_MOD macro.
# * All source files automatically depend on $(PYTHON_HEADERS) and
#   $(MODULE_{NAME}_DEPS).
#
# See Modules/Setup and Modules/makesetup
#


# Build modules statically or as shared extensions
# *shared* / *static*
*shared*


############################################################################
# Modules that should always be present (POSIX and Windows):
array arraymodule.c
_asyncio _asynciomodule.c
_bisect _bisectmodule.c
_contextvars _contextvarsmodule.c
_csv _csv.c
_heapq _heapqmodule.c
_json _json.c
_lsprof _lsprof.c rotatingtree.c
_opcode _opcode.c
_pickle _pickle.c
_queue _queuemodule.c
_random _randommodule.c
_struct _struct.c
_typing _typingmodule.c
_xxsubinterpreters _xxsubinterpretersmodule.c
_zoneinfo _zoneinfo.c

# needs libm
audioop audioop.c
math mathmodule.c
cmath cmathmodule.c
_statistics _statisticsmodule.c

# needs libm and on some platforms librt
_datetime _datetimemodule.c

# _decimal uses libmpdec
# either static libmpdec.a from Modules/_decimal/libmpdec or libmpdec.so
# with ./configure --with-system-libmpdec
_decimal _decimal/_decimal.c

# compression libs and binascii (optional CRC32 from zlib)
# bindings need -lbz2, -lz, or -llzma, respectively
binascii binascii.c
_bz2 _bz2module.c
_lzma _lzmamodule.c
zlib zlibmodule.c

# dbm/gdbm
# dbm needs either libndbm, libgdbm_compat, or libdb 5.x
#@MODULE__DBM_TRUE@_dbm _dbmmodule.c
# gdbm module needs -lgdbm
#_gdbm _gdbmmodule.c

# needs -lreadline or -leditline, sometimes termcap, termlib, or tinfo
#@MODULE_READLINE_TRUE@readline readline.c

# hashing builtins, can be disabled with --without-builtin-hashlib-hashes
_md5 md5module.c
_sha1 sha1module.c
_sha256 sha256module.c
_sha512 sha512module.c
_sha3 _sha3/sha3module.c
_blake2 _blake2/blake2module.c _blake2/blake2b_impl.c _blake2/blake2s_impl.c

############################################################################
# XML and text

# pyexpat module uses libexpat
# either static libexpat.a from Modules/expat or libexpat.so with
# ./configure --with-system-expat
pyexpat pyexpat.c

# _elementtree libexpat via CAPI hook in pyexpat.
_elementtree _elementtree.c

_codecs_cn cjkcodecs/_codecs_cn.c
_codecs_hk cjkcodecs/_codecs_hk.c
_codecs_iso2022 cjkcodecs/_codecs_iso2022.c
_codecs_jp cjkcodecs/_codecs_jp.c
_codecs_kr cjkcodecs/_codecs_kr.c
_codecs_tw cjkcodecs/_codecs_tw.c
_multibytecodec cjkcodecs/multibytecodec.c
unicodedata unicodedata.c

############################################################################
# Modules with some UNIX dependencies
#

# needs -lcrypt on some systems
_crypt _cryptmodule.c
fcntl fcntlmodule.c
grp grpmodule.c
mmap mmapmodule.c
# FreeBSD: nis/yp APIs are in libc
# Linux: glibc has deprecated SUN RPC, APIs are in libnsl and libtirpc (bpo-32521)
#nis nismodule.c
# needs sys/soundcard.h or linux/soundcard.h (Linux, FreeBSD)
ossaudiodev ossaudiodev.c
_posixsubprocess _posixsubprocess.c
resource resource.c
select selectmodule.c
_socket socketmodule.c
# AIX has shadow passwords, but does not provide getspent API
spwd spwdmodule.c
syslog syslogmodule.c
termios termios.c

# multiprocessing
_posixshmem _multiprocessing/posixshmem.c
_multiprocessing _multiprocessing/multiprocessing.c _multiprocessing/semaphore.c


############################################################################
# Modules with third party dependencies
#

# needs -lffi and -ldl
#@MODULE__CTYPES_TRUE@_ctypes _ctypes/_ctypes.c _ctypes/callbacks.c _ctypes/callproc.c _ctypes/stgdict.c _ctypes/cfield.c

# needs -lncurses, -lncursesw or -lcurses, sometimes -ltermcap
#@MODULE__CURSES_TRUE@_curses _cursesmodule.c
# needs -lncurses and -lpanel
#@MODULE__CURSES_PANEL_TRUE@_curses_panel _curses_panel.c

_sqlite3 _sqlite/blob.c _sqlite/connection.c _sqlite/cursor.c _sqlite/microprotocols.c _sqlite/module.c _sqlite/prepare_protocol.c _sqlite/row.c _sqlite/statement.c _sqlite/util.c

# needs -lssl and -lcrypt
_ssl _ssl.c
# needs -lcrypt
_hashlib _hashopenssl.c

# Linux: -luuid, BSD/AIX: libc's uuid_create()
_uuid _uuidmodule.c

#_tkinter _tkinter.c tkappinit.c

############################################################################
# macOS specific modules

# _scproxy needs SystemConfiguration and CoreFoundation framework
#_scproxy _scproxy.c


############################################################################
# Test modules

_xxtestfuzz _xxtestfuzz/_xxtestfuzz.c _xxtestfuzz/fuzzer.c
_testbuffer _testbuffer.c
_testinternalcapi _testinternalcapi.c
_testcapi _testcapimodule.c
_testclinic _testclinic.c

# Some testing modules MUST be built as shared libraries.
*shared*
_testimportmultiple _testimportmultiple.c
_testmultiphase _testmultiphase.c
_ctypes_test _ctypes/_ctypes_test.c

# Limited API template modules; must be built as shared modules.
xxlimited xxlimited.c
xxlimited_35 xxlimited_35.c
