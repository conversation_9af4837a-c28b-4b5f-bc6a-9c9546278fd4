"""
Core COLLADA importer classes and functionality.
"""

import sys
import os
import math
from numbers import Real

import bpy
import bmesh
from mathutils import Matrix, Vector

from ...collada.camera import PerspectiveCamera, OrthographicCamera
from ...collada.common import DaeBrokenRefError, DaeObject, tag
from ...collada.light import AmbientLight, DirectionalLight, PointLight, SpotLight
from ...collada.material import Map
from ...collada.polylist import <PERSON>yl<PERSON>, BoundPolylist
from ...collada.primitive import BoundPrimitive
from ...collada.scene import Scene, Node, NodeNode, CameraNode, GeometryNode, LightNode
from ...collada.triangleset import TriangleSet, BoundTriangleSet
from ...collada.lineset import LineSet, BoundLineSet
from ...collada.geometry import BoundGeometry

MAX_NAME_LENGTH = 63
DEG = math.pi / 180

class DATABLOCK:
    CAMERA = "CAMERA"
    EMPTY = "EMPTY"
    LAMP = "LAMP"
    MATERIAL = "MATERIAL"
    MESH = "MESH"
    SCENE = "SCENE"

def unurlid(uid):
    assert uid.startswith("#")
    return uid[1:]

def find_main_shader(in_datablock, type_name):
    node_graph = in_datablock.node_tree
    shader = list(n for n in node_graph.nodes if n.type == type_name)[0]
    return shader, node_graph

class ColladaImport:
    """Standard COLLADA importer with complete functionality."""

    def __init__(self, ctx, collada, filepath, **kwargs):
        # Handle XML namespace
        try:
            self.DAE_NS = {"dae": collada.xmlnode.getroot().nsmap[None]}
        except (AttributeError, KeyError, TypeError):
            self.DAE_NS = {"dae": "http://www.collada.org/2005/11/COLLADASchema"}

        basename = os.path.basename(filepath)
        self._ctx = ctx
        self._collada = collada
        self._filepath = filepath  # Store filepath for image loading
        self._recognize_blender_extensions = kwargs["recognize_blender_extensions"]
        self._transformation = kwargs["transformation"]
        self._name_map = {}
        self._name_revmap = {}
        self._untitledcount = 0
        self._units = collada.assetInfo.unitmeter
        if self._units == None:
            self._units = 1

        # Set up orientation matrix
        orient = collada.assetInfo.upaxis
        if orient == "Z_UP":
            self._orient = Matrix.Identity(4)
        elif orient == "X_UP":
            self._orient = Matrix.Rotation(120 * DEG, 4, Vector(1, -1, 1))
        else:  # "Y_UP" or unspecified
            self._orient = Matrix.Rotation(90 * DEG, 4, "X")

        self._id_prefixes = None
        root_technique = self.get_blender_technique(True, self._collada.xmlnode.getroot())
        if root_technique != None:
            id_prefixes = root_technique.find(tag("id_prefixes"))
            if id_prefixes != None:
                self._id_prefixes = {}
                for prefix in id_prefixes.findall(tag("prefix")):
                    name = prefix.get("name")
                    value = prefix.get("value")
                    if name != None and value != None:
                        self._id_prefixes[name] = value

        self._imported = {}
        self._collection = bpy.data.collections.new(basename)
        self._ctx.scene.collection.children.link(self._collection)

    def get_blender_technique(self, as_extra, obj):
        """Get Blender-specific technique settings."""
        blendstuff = None
        if self._recognize_blender_extensions:
            if isinstance(obj, DaeObject):
                obj = obj.xmlnode
            if as_extra:
                parent = obj.find(tag("extra"))
            else:
                parent = obj
            if parent != None:
                blendstuff = parent.find(tag("technique") + "[@profile=\"BLENDER050\"]")
                if blendstuff is None:
                    blendstuff = parent.find(tag("technique") + "[@profile=\"BLENDER028\"]")
        return blendstuff

    def name(self, prefix_name, obj):
        """Generate efficient and human readable name."""
        def truncate_bytes(s, maxlen):
            b = s.encode()[:maxlen]
            while True:
                try:
                    s = b.decode()
                except UnicodeDecodeError as err:
                    b = b[:err.start]
                else:
                    break
            return s

        if hasattr(obj, "id") and obj.id != None:
            origname = obj.id
            if self._id_prefixes != None:
                prefix = self._id_prefixes.get(prefix_name)
                if prefix != None and origname.startswith(prefix):
                    origname = origname[len(prefix):]
            if origname in self._name_map:
                usename = self._name_map[origname]
            else:
                usename = truncate_bytes(origname, MAX_NAME_LENGTH)
                seq = 0
                while usename in self._name_revmap:
                    seq += 1
                    suffix = "-%0.3d" % seq
                    suffix_len = len(suffix.encode())
                    assert suffix_len < MAX_NAME_LENGTH
                    usename = "%s%s" % (truncate_bytes(origname, MAX_NAME_LENGTH - suffix_len), suffix)
                self._name_map[origname] = usename
                self._name_revmap[usename] = origname
        else:
            origname = id(obj)
            if origname in self._name_map:
                usename = self._name_map[origname]
            else:
                self._untitledcount += 1
                usename = "untitled %0.3d" % self._untitledcount
                self._name_map[origname] = usename
                self._name_revmap[usename] = origname
        return usename

    def _transform(self, t):
        return self._transformation == t

    def _convert_units_matrix(self, mat):
        """Convert translation part of Matrix from COLLADA units to Blender units."""
        mat = mat.copy()
        for i in range(3):
            mat[i][3] *= self._units
        return mat

    def _convert_units_verts(self, verts):
        """Convert vectors from COLLADA units to Blender units."""
        return list(self._units * Vector(v) for v in verts)

    def get_already_imported(self, category, b_name):
        result = None
        names = self._imported.get(category)
        if names != None:
            result = names.get(b_name)
        return result

    def set_already_imported(self, category, b_name, b_name_assigned):
        if not self._transform("APPLY"):
            if category not in self._imported:
                self._imported[category] = {}
            names = self._imported[category]
            assert b_name not in names
            names[b_name] = b_name_assigned

    def apply_blender_technique(self, as_extra, obj, b_data, attribs):
        """Get and apply any custom technique settings for this object."""
        blendstuff = self.get_blender_technique(as_extra, obj)
        if blendstuff != None:
            for tagname, parse, attrname in attribs:
                if hasattr(b_data, attrname):
                    subtag = blendstuff.find(tag(tagname))
                    if subtag != None:
                        try:
                            setattr(b_data, attrname, parse(subtag.text))
                        except ValueError as err:
                            sys.stderr.write(
                                "import_collada: error setting %s attribute for %s: %s\n"
                                % (attrname, b_data.name, str(err))
                            )
        return blendstuff != None

    def camera(self, bcam):
        """Import camera object."""
        def fudge_div(num, den):
            try:
                result = num / den
            except ZeroDivisionError:
                result = num
            return result

        b_name = self.name(DATABLOCK.CAMERA, bcam.original)
        b_cam = bpy.data.cameras.new(b_name)
        b_obj = bpy.data.objects.new(b_cam.name, b_cam)

        if isinstance(bcam.original, PerspectiveCamera):
            b_cam.type = "PERSP"
            prop = b_cam.bl_rna.properties.get("lens_unit")
            if "DEGREES" in prop.enum_items:
                b_cam.lens_unit = "DEGREES"
            elif "FOV" in prop.enum_items:
                b_cam.lens_unit = "FOV"
            else:
                b_cam.lens_unit = prop.default

            b_cam.angle = max((
                None,
                None,
                None,
                lambda: (bcam.yfov * DEG, bcam.xfov * DEG),
                None,
                lambda: (2 * math.atan(fudge_div(math.tan(bcam.xfov * DEG / 2), bcam.aspect_ratio)), bcam.xfov * DEG),
                lambda: (bcam.yfov * DEG, 2 * math.atan(math.tan(bcam.yfov * DEG / 2) * bcam.aspect_ratio)),
                None,
            )[
                (bcam.aspect_ratio != None) << 2
                | (bcam.yfov != None) << 1
                | (bcam.xfov != None)
            ]())
        elif isinstance(bcam.original, OrthographicCamera):
            b_cam.type = "ORTHO"
            b_cam.ortho_scale = max((
                None,
                None,
                None,
                lambda: (bcam.ymag, bcam.xmag),
                None,
                lambda: (fudge_div(bcam.xmag, bcam.aspect_ratio), bcam.xmag),
                lambda: (bcam.ymag, bcam.ymag * bcam.aspect_ratio),
                None,
            )[
                (bcam.aspect_ratio != None) << 2
                | (bcam.ymag != None) << 1
                | (bcam.xmag != None)
            ]())

        if bcam.znear != None:
            b_cam.clip_start = self._units * bcam.znear
        if bcam.zfar != None:
            b_cam.clip_end = self._units * bcam.zfar

        self._collection.objects.link(b_obj)
        return b_obj

    def light(self, blight):
        """Import light object."""
        result = None
        b_name = self.name(DATABLOCK.LAMP, blight.original)

        light_type = tuple(
            elt for elt in (
                (AmbientLight, "POINT"),
                (DirectionalLight, "SUN"),
                (PointLight, "POINT"),
                (SpotLight, "SPOT"),
            )
            if isinstance(blight.original, elt[0])
        )

        if len(light_type) != 0:
            light_type = light_type[0]
            b_light = bpy.data.lights.new(b_name, type=light_type[1])
            b_light.color = blight.original.color[:3]

            if isinstance(blight.original, AmbientLight):
                b_light.shadow_soft_size = 10000
                b_light.use_shadow = False
                b_light.use_nodes = True
                b_light.cycles.cast_shadow = False
                b_shader, node_graph = find_main_shader(b_light, "EMISSION")
                node_x, node_y = b_shader.location
                falloff = node_graph.nodes.new("ShaderNodeLightFalloff")
                falloff.location = (node_x - 200, node_y)
                falloff.inputs["Strength"].default_value = b_shader.inputs["Strength"].default_value
                node_graph.links.new(falloff.outputs["Constant"], b_shader.inputs["Strength"])
            else:
                for attr, battr, conv in (
                    ("falloff_ang", "spot_size", lambda ang: ang * DEG),
                    ("falloff_exp", "spot_blend", lambda exp: 1 / (1 + exp)),
                ):
                    if hasattr(b_light, battr):
                        val = getattr(blight, attr, None)
                        if val != None:
                            setattr(b_light, battr, conv(val))

                atten = filter(
                    lambda val: val[1] != None and val[1] != 0,
                    ((a[0], getattr(blight, a[1], None)) for a in ((0, "constant_att"), (1, "linear_att"), (2, "quad_att")))
                )
                atten = sorted(atten, key=lambda a: a[1], reverse=True)

                if len(atten) != 0:
                    pow, factor = atten[0]
                    b_light.use_nodes = True
                    b_shader, node_graph = find_main_shader(b_light, "EMISSION")
                    node_x, node_y = b_shader.location
                    falloff = node_graph.nodes.new("ShaderNodeLightFalloff")
                    falloff.location = (node_x - 200, node_y)
                    falloff.inputs["Strength"].default_value = b_shader.inputs["Strength"].default_value / factor
                    node_graph.links.new(falloff.outputs[("Constant", "Linear", "Quadratic")[pow]], b_shader.inputs["Strength"])

                self.apply_blender_technique(True, blight.original, b_light, [
                    ("angle", float, "angle"),
                    ("power", float, "energy"),
                    ("shadow_soft_size", float, "shadow_soft_size"),
                    ("spot_blend", float, "spot_blend"),
                    ("spot_size", float, "spot_size"),
                ])

            b_obj = bpy.data.objects.new(b_name, b_light)
            self._collection.objects.link(b_obj)
            result = b_obj

        return result

    def geometry(self, bgeom, containing_node=None, material_bindings=None):
        """Import geometry object with full functionality."""
        def collect_from_elts(p, attrname):
            return list(tuple(getattr(elt, attrname)) for elt in p)

        def is_flat_face(normal):
            a = Vector(normal[0])
            for n in normal[1:]:
                dp = a.dot(Vector(n))
                if dp < 0.99999 or dp > 1.00001:
                    return False
            return True

        # Get Blender technique settings
        blendstuff = self.get_blender_technique(True, bgeom.original.xmlnode)
        b_materials = {}

        # Native-like material handling: collect ALL material symbols from primitives first
        # Use OrderedDict to preserve order like native Blender COLLADA importer
        material_symbols = {}

        # Get primitives to analyze material references
        if self._transform("APPLY"):
            primitives = bgeom.primitives()
        else:
            primitives = bgeom.original.primitives

        # Collect material symbols from all primitives (like native importer)
        # Preserve order by using dict keys (which maintain insertion order in Python 3.7+)
        for p in primitives:
            if isinstance(p, (TriangleSet, BoundTriangleSet, Polylist, BoundPolylist)):
                if isinstance(p, BoundPrimitive):
                    symbol = p.original.material
                else:
                    symbol = p.material
                if symbol:
                    material_symbols[symbol] = True  # Use dict to preserve order

        # If no material symbols found in primitives, try material bindings
        if not material_symbols and material_bindings:
            material_symbols = {k: True for k in material_bindings.keys()}

        # If still no materials, try parsing XML directly as last resort
        if not material_symbols:
            if hasattr(bgeom, 'original') and hasattr(bgeom.original, 'xmlnode') and bgeom.original.xmlnode is not None:
                geom_xml = bgeom.original.xmlnode
                primitive_types = ['triangles', 'polylist', 'trifans', 'tristrips']
                for prim_type in primitive_types:
                    prims = geom_xml.findall(f".//{prim_type}")
                    for prim in prims:
                        symbol = prim.get('material')
                        if symbol:
                            material_symbols[symbol] = True

        # Create/find Blender materials for each symbol (native-like behavior)
        material_objs = []
        material_symbol_to_index = {}

        for symbol in material_symbols.keys():
            # Try to find the material in the library (case-insensitive)
            material = None

            # First, use material_bindings to map symbol to material ID (native behavior)
            material_id = None
            if material_bindings and symbol in material_bindings:
                material_id = material_bindings[symbol]
            else:
                # Fallback: try to find material by symbol directly
                if hasattr(self._collada, 'materials'):
                    # First try exact match
                    material = self._collada.materials.get(symbol)
                    if material is None:
                        # Try case-insensitive lookup
                        for mat in self._collada.materials:
                            if hasattr(mat, 'id') and mat.id and mat.id.lower() == symbol.lower():
                                material = mat
                                break

            # If we have a material_id from bindings, look up the material by ID
            if material_id and hasattr(self._collada, 'materials'):
                material = self._collada.materials.get(material_id)
                if material is None:
                    # Try case-insensitive lookup by ID
                    for mat in self._collada.materials:
                        if hasattr(mat, 'id') and mat.id and mat.id.lower() == material_id.lower():
                            material = mat
                            break

            # Determine material name
            b_matname = symbol
            if material and hasattr(material, 'name') and material.name:
                b_matname = material.name
            elif material and hasattr(material, 'id') and material.id:
                b_matname = material.id

            # Create or find Blender material
            if b_matname in bpy.data.materials:
                b_mat = bpy.data.materials[b_matname]
            else:
                if material:
                    # Use pycollada material if available
                    b_matname = self.material(material, b_matname)
                    b_mat = bpy.data.materials[b_matname]
                else:
                    # Create placeholder material (native-like behavior)
                    b_mat = bpy.data.materials.new(name=b_matname)
                    b_mat.use_nodes = True
                    # Set a default color to make it visible
                    if hasattr(b_mat, 'diffuse_color'):
                        b_mat.diffuse_color = (0.8, 0.8, 0.8, 1.0)

            b_materials[symbol] = b_mat
            material_symbol_to_index[symbol] = len(material_objs)
            material_objs.append(b_mat)

        # Get primitives and mesh name
        if self._transform("APPLY"):
            primitives = bgeom.primitives()
            if hasattr(bgeom.original, 'name') and bgeom.original.name:
                b_meshname = bgeom.original.name
            else:
                b_meshname = self.name(DATABLOCK.MESH, bgeom)
        else:
            primitives = bgeom.original.primitives
            if hasattr(bgeom.original, 'name') and bgeom.original.name:
                b_meshname = bgeom.original.name
            else:
                b_meshname = self.name(DATABLOCK.MESH, bgeom.original)

        materials = []
        new_mesh = self._transform("APPLY") or not self.get_already_imported("MESH", b_meshname) != None

        if new_mesh:
            verts = []
            vert_starts = {}
            faces = []
            smooth_shade = []
            got_normals = False
            material_assignments = []
            uvcoords = None
            uvcoord_ids = None
            vertex_normals = []
            loop_normals = []
            face_normals = []

            # Process primitives
            for p in primitives:
                # Add materials for face primitives
                if isinstance(p, (TriangleSet, BoundTriangleSet, Polylist, BoundPolylist)):
                    if isinstance(p, BoundPrimitive):
                        b_mat_key = p.original.material
                    else:
                        b_mat_key = p.material
                    materials.append(b_materials.get(b_mat_key, None))

                if isinstance(p, (TriangleSet, BoundTriangleSet, Polylist, BoundPolylist)):
                    if isinstance(p, BoundPrimitive):
                        op = p.original
                    else:
                        op = p

                    these_faces = p.vertex_index
                    if these_faces is not None and len(these_faces) > 0:
                        collect = lambda a: collect_from_elts(p, a)
                        verts_source_id = op.sources["VERTEX"][0][2]

                        if verts_source_id in vert_starts:
                            vert_start = vert_starts[verts_source_id]
                        else:
                            vert_start = len(verts)
                            vert_starts[verts_source_id] = vert_start
                            verts.extend(tuple(v) for v in p.vertex)

                        these_faces = collect("indices")
                        these_smooth_shade = [False] * len(these_faces)
                        current_material_index = len(materials) - 1
                        these_material_assignments = [current_material_index] * len(these_faces)

                        has_normals = p.normal is not None
                        if has_normals:
                            these_normcoords = list(p.normal)
                            these_normindices = collect("normal_indices")

                            for i in range(len(these_faces)):
                                face_normal_vectors = [these_normcoords[j] for j in these_normindices[i]]
                                face_normals.append(face_normal_vectors)
                                these_smooth_shade[i] = not is_flat_face(face_normal_vectors)

                            for face_idx, face in enumerate(these_faces):
                                face_loop_normals = []
                                for vert_idx in range(len(face)):
                                    normal_idx = these_normindices[face_idx][vert_idx]
                                    normal_vec = Vector(these_normcoords[normal_idx])
                                    face_loop_normals.append(normal_vec)
                                loop_normals.append(face_loop_normals)

                            got_normals = True
                        else:
                            for i in range(len(these_faces)):
                                face_normals.append(None)
                                loop_normals.append(None)

                        # Handle UV coordinates
                        if "TEXCOORD" in op.sources and len(op.sources["TEXCOORD"]) != 0:
                            if uvcoords == None:
                                uvcoords = [
                                    [[(0, 0)] * len(f) for f in faces]
                                    for i in range(len(op.sources["TEXCOORD"]))
                                ]
                                uvcoord_ids = tuple(s[2] for s in op.sources["TEXCOORD"])

                            for face in p:
                                for layer, coords in zip(uvcoords, face.texcoords):
                                    layer.append(list(tuple(v) for v in coords))
                        elif uvcoords != None:
                            for face in p:
                                for layer in uvcoords:
                                    layer.append([(0, 0)] * len(face.vertices))

                        faces.extend(tuple(i + vert_start for i in f) for f in these_faces)
                        smooth_shade.extend(these_smooth_shade)
                        material_assignments.extend(these_material_assignments)

            # Create mesh
            b_mesh = bpy.data.meshes.new(b_meshname)
            self.set_already_imported("MESH", b_meshname, b_mesh.name)
            b_mesh.from_pydata(self._convert_units_verts(verts), [], faces)

            # Assign material slots to mesh (native-like behavior)
            for b_mat in material_objs:
                b_mesh.materials.append(b_mat)

            # Apply normals
            if got_normals:
                for i, f in enumerate(b_mesh.polygons):
                    f.use_smooth = smooth_shade[i]

                b_mesh.calc_loop_triangles()
                custom_normals = []
                vertex_normals = {}

                for face_idx, face in enumerate(b_mesh.polygons):
                    if face_idx < len(loop_normals) and loop_normals[face_idx] is not None:
                        face_loop_normals = loop_normals[face_idx]
                        for vert_local_idx in range(face.loop_total):
                            vert_global_idx = face.vertices[vert_local_idx]
                            if vert_local_idx < len(face_loop_normals):
                                normal = Vector(face_loop_normals[vert_local_idx])
                                custom_normals.append(normal)
                                if vert_global_idx not in vertex_normals:
                                    vertex_normals[vert_global_idx] = []
                                vertex_normals[vert_global_idx].append(normal)
                            else:
                                normal = Vector((0, 0, 1))
                                custom_normals.append(normal)

                if len(custom_normals) == len(b_mesh.loops):
                    b_mesh.normals_split_custom_set(custom_normals)
            else:
                for face in b_mesh.polygons:
                    face.use_smooth = True

            # Apply UV coordinates
            if uvcoords != None:
                uv_layers_names = {}
                if blendstuff != None:
                    layer_names = blendstuff.find(tag("layer_names"))
                    if layer_names != None:
                        for name_entry in layer_names.findall(tag("name")):
                            if name_entry.get("type") == "UV":
                                layer_name = name_entry.get("name")
                                layer_refid = name_entry.get("refid")
                                if layer_name != None and layer_refid != None:
                                    uv_layers_names[layer_refid] = layer_name

                for layer, refid in zip(uvcoords, uvcoord_ids):
                    layer_name = uv_layers_names.get(unurlid(refid))
                    uv = b_mesh.uv_layers.new()
                    if uv is None:
                        print(f"Warning: Could not create UV layer for '{unurlid(refid)}' - Blender UV layer limit reached")
                        continue
                    if layer_name != None:
                        uv.name = layer_name
                    else:
                        clean_id = unurlid(refid)
                        if clean_id and len(clean_id) > 2:
                            uv.name = clean_id

                    uv_data = uv.data
                    for i, face in enumerate(b_mesh.polygons):
                        loop_start = face.loop_start
                        coords = layer[i]
                        for j in range(face.loop_total):
                            uv_data[loop_start + j].uv = coords[j]

            # --- Vertex Color (Color Attribute) Import ---
            # Extract color layer names from Blender technique profile (like native implementation)
            color_layers_names = {}
            if blendstuff is not None:
                layer_names = blendstuff.find(tag("layer_names"))
                if layer_names is not None:
                    for name_entry in layer_names.findall(tag("name")):
                        if name_entry.get("type") == "COLOR":
                            layer_name = name_entry.get("name")
                            layer_refid = name_entry.get("refid")
                            if layer_name is not None and layer_refid is not None:
                                color_layers_names[layer_refid] = layer_name
            # Helper to get <source> name attribute from DAE XML
            def get_source_name_from_xml(refid):
                src_id = refid[1:] if refid.startswith('#') else refid
                xmlroot = getattr(self._collada, 'xmlnode', None)
                if xmlroot is not None:
                    root_elem = xmlroot.getroot() if hasattr(xmlroot, "getroot") else xmlroot
                    ns = {'dae': root_elem.tag.split('}')[0].strip('{')}
                    for src_elem in root_elem.findall('.//dae:source', ns):
                        if src_elem.get('id') == src_id:
                            name_attr = src_elem.get('name')
                            if name_attr:
                                return name_attr
                return None
            # --- Collect color data and indices per layer (like native) ---
            color_layers = {}  # refid -> { 'data': [...], 'indices': [[...], ...], 'name': str }
            for p in primitives:
                if hasattr(p, 'sources') and 'COLOR' in p.sources and len(p.sources['COLOR']) > 0:
                    color_index_lists = getattr(p, 'color_indices', None)
                    if color_index_lists is None and hasattr(p, 'color_index'):
                        color_index_lists = [p.color_index]
                    for idx, color_source in enumerate(p.sources['COLOR']):
                        refid = color_source[2]
                        color_data = color_source[4].data
                        # Try to get the name from Blender technique, then from <source> XML, then from the source itself
                        layer_name = color_layers_names.get(unurlid(refid))
                        if not layer_name:
                            # Try to get from <source> XML 'name' attribute (native behavior)
                            layer_name = get_source_name_from_xml(refid)
                        if not layer_name:
                            # Try to get from color_source (pycollada stores name in [1] or [3] sometimes)
                            possible_names = [getattr(color_source[1], 'name', None), getattr(color_source[3], 'name', None)]
                            layer_name = next((n for n in possible_names if n), None)
                        if not layer_name:
                            clean_id = unurlid(refid)
                            if clean_id and len(clean_id) > 2:
                                layer_name = clean_id
                            else:
                                layer_name = f"Col_{len(color_layers)}"
                        if refid not in color_layers:
                            color_layers[refid] = {'data': color_data, 'indices': [], 'name': layer_name}
                        if color_index_lists is not None and idx < len(color_index_lists):
                            indices = color_index_lists[idx]
                            if isinstance(indices, list) and indices and isinstance(indices[0], list):
                                flat_indices = [i for face in indices for i in face]
                            else:
                                flat_indices = indices if indices else []
                            color_layers[refid]['indices'].extend(flat_indices)
            # Helper: float [0,1] -> byte [0,255] with clamping and rounding
            def float_to_byte(x):
                return int(round(max(0.0, min(1.0, x)) * 255.0))

            # Now assign color data to mesh loops using the correct indices
            for refid, layer_info in color_layers.items():
                color_data = layer_info['data']
                color_indices = layer_info['indices']
                layer_name = layer_info['name']
                print(f"[COLLADA DEBUG] refid: {refid}")
                print(f"[COLLADA DEBUG] resolved layer_name: {layer_name}")
                print(f"[COLLADA DEBUG] first 10 color_indices: {color_indices[:10] if color_indices else 'None'}")
                print(f"[COLLADA DEBUG] first 10 color_data (float): {color_data[:10]}")
                # Print first 10 raw color values as parsed by pycollada (before any conversion)
                if hasattr(color_data, '__getitem__'):
                    print(f"[COLLADA DEBUG] first 10 raw pycollada color values: {[tuple(color_data[i]) for i in range(min(10, len(color_data)))]}")
                print(f"[COLLADA DEBUG] mesh loop count: {len(b_mesh.loops) if hasattr(b_mesh, 'loops') else 'unknown'}")
                assigned_colors = []
                assigned_indices = []
                if hasattr(b_mesh, "color_attributes"):
                    existing_attr = b_mesh.color_attributes.get(layer_name)
                    if existing_attr is not None:
                        print(f"Warning: Color attribute '{layer_name}' already exists on mesh {b_mesh.name}, skipping")
                        continue
                    color_attr = b_mesh.color_attributes.new(name=layer_name, type='BYTE_COLOR', domain='CORNER')
                    if color_attr is not None:
                        for loop_idx, col in enumerate(color_attr.data):
                            color_index = loop_idx
                            if color_indices and loop_idx < len(color_indices):
                                color_index = color_indices[loop_idx]
                            if color_index < len(color_data):
                                c = color_data[color_index]
                                # Color channel assignment (R, G, B, A)
                                r = float_to_byte(c[0])  # R
                                g = float_to_byte(c[1])  # G
                                b = float_to_byte(c[2])  # B
                                a = float_to_byte(c[3]) if len(c) > 3 else 255

                                # Debug: Check for orange colors and log the assignment
                                if layer_name == 'Attribute' and len(assigned_colors) < 5 and c[0] > 0.9 and c[1] > 0.3 and c[1] < 0.6 and c[2] < 0.1:
                                    print(f"[COLLADA DEBUG] Orange color detected: float={c} -> bytes=({r}, {g}, {b}, {a})")

                                col.color = (r, g, b, a)
                                if layer_name == 'Col' and len(assigned_colors) < 10:
                                    assigned_colors.append((r, g, b, a))
                                    assigned_indices.append(color_index)
                        if layer_name == 'Col':
                            print(f"[COLLADA DEBUG] first 10 assigned color indices: {assigned_indices}")
                            print(f"[COLLADA DEBUG] first 10 assigned byte colors: {assigned_colors}")
                    else:
                        print(f"Warning: Could not create color attribute '{layer_name}' on mesh {b_mesh.name}")
                elif hasattr(b_mesh, "vertex_colors"):
                    existing_attr = b_mesh.vertex_colors.get(layer_name)
                    if existing_attr is not None:
                        print(f"Warning: Vertex color layer '{layer_name}' already exists on mesh {b_mesh.name}, skipping")
                        continue
                    color_layer = b_mesh.vertex_colors.new(name=layer_name)
                    if color_layer is not None:
                        for loop_idx, col in enumerate(color_layer.data):
                            color_index = loop_idx
                            if color_indices and loop_idx < len(color_indices):
                                color_index = color_indices[loop_idx]
                            if color_index < len(color_data):
                                c = color_data[color_index]
                                # Color channel assignment (R, G, B, A)
                                r = float_to_byte(c[0])  # R
                                g = float_to_byte(c[1])  # G
                                b = float_to_byte(c[2])  # B
                                a = float_to_byte(c[3]) if len(c) > 3 else 255

                                # Debug: Check for orange colors and log the assignment
                                if layer_name == 'Attribute' and len(assigned_colors) < 5 and c[0] > 0.9 and c[1] > 0.3 and c[1] < 0.6 and c[2] < 0.1:
                                    print(f"[COLLADA DEBUG] Orange color detected (vertex_colors): float={c} -> bytes=({r}, {g}, {b}, {a})")

                                col.color = (r, g, b, a)
                                if layer_name == 'Col' and len(assigned_colors) < 10:
                                    assigned_colors.append((r, g, b, a))
                                    assigned_indices.append(color_index)
                        if layer_name == 'Col':
                            print(f"[COLLADA DEBUG] first 10 assigned color indices: {assigned_indices}")
                            print(f"[COLLADA DEBUG] first 10 assigned byte colors: {assigned_colors}")
                    else:
                        print(f"Warning: Could not create vertex color layer '{layer_name}' on mesh {b_mesh.name}")
            # --- End Vertex Color Import ---

            # Apply materials
            for i, face in enumerate(b_mesh.polygons):
                face.material_index = material_assignments[i]

            b_mesh.update()
            self._add_loose_edges(primitives, b_mesh)

        else:
            b_mesh = bpy.data.meshes[self.get_already_imported("MESH", b_meshname)]
            for p in primitives:
                if isinstance(p, BoundPrimitive):
                    b_mat_key = p.original.material
                else:
                    b_mat_key = p.material
                b_mat = b_materials.get(b_mat_key, None)
                materials.append(b_mat)

        # Create object with proper naming
        if containing_node:
            if hasattr(containing_node, 'name') and containing_node.name:
                object_name = containing_node.name
            elif hasattr(containing_node, 'id') and containing_node.id:
                object_name = containing_node.id
            else:
                object_name = b_meshname
        else:
            object_name = b_meshname

        b_obj = bpy.data.objects.new(object_name, b_mesh)
        b_obj.data = b_mesh
        self._collection.objects.link(b_obj)
        self._ctx.view_layer.objects.active = b_obj

        # Handle material slots - ensure consistency with mesh materials
        b_obj.data.materials.clear()

        # Use material_objs (our native-like material collection) as the primary source
        materials_to_assign = material_objs

        # Assign materials to object (should match mesh materials)
        for m in materials_to_assign:
            if m is not None:
                b_obj.data.materials.append(m)

        # Create mapping for face material assignments
        material_index_mapping = {}
        for i, mat in enumerate(materials_to_assign):
            if mat is not None:
                slot_index = i  # Direct mapping since we're using material_objs order
                material_index_mapping[i] = slot_index

        # Apply material assignments to faces if we have a new mesh
        if new_mesh:
            for i, face in enumerate(b_mesh.polygons):
                if i < len(material_assignments):
                    original_material_index = material_assignments[i]
                    if original_material_index in material_index_mapping:
                        face.material_index = material_index_mapping[original_material_index]

        if self._transform("APPLY"):
            bpy.ops.object.mode_set(mode="EDIT")
            bpy.ops.object.mode_set(mode="OBJECT")

        # Apply up-axis orientation if this is a root object (no parent)
        if containing_node is not None and (not hasattr(containing_node, 'parent') or containing_node.parent is None):
            b_obj.matrix_world = self._orient @ b_obj.matrix_world

        return b_obj

    def _add_loose_edges(self, primitives, b_mesh):
        """Add loose edges from LINES primitives like native COLLADA importer."""
        loose_edges = []

        for p in primitives:
            if isinstance(p, (LineSet, BoundLineSet)):
                try:
                    if hasattr(p, 'vertex_index') and p.vertex_index is not None:
                        line_segments = p.vertex_index
                        for line in line_segments:
                            if len(line) >= 2:
                                loose_edges.append((int(line[0]), int(line[1])))
                except (AttributeError, TypeError, IndexError):
                    continue

        if loose_edges:
            import bmesh
            bm = bmesh.new()
            bm.from_mesh(b_mesh)
            bm.verts.ensure_lookup_table()

            for edge in loose_edges:
                try:
                    if edge[0] < len(bm.verts) and edge[1] < len(bm.verts):
                        vert1 = bm.verts[edge[0]]
                        vert2 = bm.verts[edge[1]]
                        existing_edge = None
                        for existing in vert1.link_edges:
                            if existing.other_vert(vert1) == vert2:
                                existing_edge = existing
                                break
                        if not existing_edge:
                            bm.edges.new([vert1, vert2])
                except (IndexError, ValueError):
                    continue

            bm.to_mesh(b_mesh)
            bm.free()

    def material(self, mat, b_name):
        """Create and return material."""
        material_handler = self.Material(self, mat, b_name)
        return material_handler.name

    def parent_node(self, node, parent, node_matrix=None):
        """Handle node parenting and transformation."""
        from ...collada.scene import Node, NodeNode

        if isinstance(node, (Node, NodeNode)):
            b_obj = bpy.data.objects.new(self.name(DATABLOCK.EMPTY, node), None)
            b_obj.matrix_world = self._convert_units_matrix(Matrix(node.matrix))
            if node_matrix != None:
                b_obj.matrix_world = node_matrix @ b_obj.matrix_world
            self._collection.objects.link(b_obj)
            if parent != None:
                b_obj.parent = parent
            else:
                # Apply up-axis orientation for root objects (no parent)
                b_obj.matrix_world = self._orient @ b_obj.matrix_world
            parent = b_obj
        else:
            handle_type = tuple(h for h in self.obj_type_handlers if isinstance(node, h[2]))
            if len(handle_type) != 0:
                handle_type = handle_type[0]
                bobj = list(node.objects(handle_type[0]))
                assert len(bobj) == 1
                bobj = bobj[0]
                b_obj = handle_type[1](self, bobj)

                # Native behavior: rename object to use node name
                if handle_type[0] == 'geometry' and b_obj:
                    node_name = None
                    if hasattr(node, 'xmlnode') and node.xmlnode is not None:
                        node_name = node.xmlnode.get('name') or node.xmlnode.get('id')

                    if not node_name:
                        if hasattr(node, 'name') and node.name:
                            node_name = node.name
                        elif hasattr(node, 'id') and node.id:
                            node_name = node.id

                    if node_name:
                        b_obj.name = node_name

                if b_obj != None:
                    if node_matrix != None:
                        b_obj.matrix_world = node_matrix @ b_obj.matrix_world
                    b_obj.parent = parent

                    # Apply up-axis orientation for root objects (no parent)
                    if parent is None:
                        b_obj.matrix_world = self._orient @ b_obj.matrix_world

                    parent = b_obj

        return parent

    obj_type_handlers = [
        ("camera", camera, CameraNode),
        ("light", light, LightNode),
        ("geometry", geometry, GeometryNode),
    ]

    class Material:
        """Interpretation of Collada material settings."""

        def __init__(self, parent, mat, b_name):
            self.parent = parent
            effect = mat.effect
            rendering = {
                "blinn": self.rendering_blinn,
                "constant": self.rendering_constant,
                "lambert": self.rendering_lambert,
                "phong": self.rendering_phong,
            }

            self.images = {}
            self.effect = effect
            b_mat = bpy.data.materials.new(b_name)
            self.b_mat = b_mat
            self.name = b_mat.name
            b_mat.use_nodes = True
            b_shader = find_main_shader(b_mat, "BSDF_PRINCIPLED")[0]
            self.b_shader = b_shader
            self.node_x, self.node_y = b_shader.location
            self.node_x -= 350
            self.node_y += 200
            self.tex_coords_src = None

            # Native behavior: create Principled BSDF shader
            if b_shader.type != 'BSDF_PRINCIPLED':
                # Remove existing shader and create Principled BSDF
                b_mat.node_tree.nodes.clear()
                b_shader = b_mat.node_tree.nodes.new('ShaderNodeBsdfPrincipled')
                b_shader.location = (0, 0)
                b_mat.node_tree.links.new(b_shader.outputs['BSDF'], b_mat.node_tree.nodes['Material Output'].inputs['Surface'])
                self.b_shader = b_shader

            if effect.shadingtype in rendering:
                rendering[effect.shadingtype]()
            else:
                # Default to lambert if shading type not supported
                self.rendering_lambert()

        def rendering_constant(self):
            # Native behavior: constant shader uses emission
            self.color_or_texture(self.effect.diffuse, "diffuse", "Emission Color")

        def rendering_lambert(self):
            # Native behavior: lambert shader uses Base Color
            self.color_or_texture(self.effect.diffuse, "diffuse", "Base Color", True)
            # Set default values like native importer
            inputs = self.b_shader.inputs
            if "Specular IOR Level" in inputs:
                inputs["Specular IOR Level"].default_value = 0.0
            if "Metallic" in inputs:
                inputs["Metallic"].default_value = 0.0
            if "Roughness" in inputs:
                inputs["Roughness"].default_value = 1.0

        def rendering_phong(self):
            # Native behavior: phong shader uses Base Color + specular
            self.color_or_texture(self.effect.diffuse, "diffuse", "Base Color", True)
            self.rendering_specular(False)

        def rendering_blinn(self):
            # Native behavior: blinn shader uses Base Color + specular
            self.color_or_texture(self.effect.diffuse, "diffuse", "Base Color", True)
            self.rendering_specular(True)

        def rendering_diffuse(self):
            # Native behavior: diffuse maps to Base Color
            self.color_or_texture(self.effect.diffuse, "diffuse", "Base Color", True)

        def rendering_specular(self, blinn=False):
            # Native behavior: specular handling
            effect = self.effect
            b_shader = self.b_shader

            if isinstance(effect.specular, tuple):
                # Specular color - native importer sets Specular IOR Level
                if "Specular IOR Level" in b_shader.inputs:
                    b_shader.inputs["Specular IOR Level"].default_value = 1.0
                elif "Specular" in b_shader.inputs:
                    b_shader.inputs["Specular"].default_value = 1.0

            if isinstance(effect.shininess, Real):
                # Shininess maps to Roughness (inverted)
                if "Roughness" in b_shader.inputs:
                    # Native behavior: convert shininess to roughness
                    roughness = 1.0 / (1.0 + effect.shininess)
                    b_shader.inputs["Roughness"].default_value = roughness

        def rendering_reflectivity(self):
            # Native behavior: reflectivity maps to Metallic
            effect = self.effect
            b_shader = self.b_shader
            if isinstance(effect.reflectivity, Real) and effect.reflectivity > 0:
                if "Metallic" in b_shader.inputs:
                    b_shader.inputs["Metallic"].default_value = effect.reflectivity

        def rendering_ior(self):
            # Native behavior: IOR handling
            effect = self.effect
            b_shader = self.b_shader
            if isinstance(effect.index_of_refraction, Real):
                if "IOR" in b_shader.inputs:
                    b_shader.inputs["IOR"].default_value = effect.index_of_refraction

        def rendering_transparency(self):
            # Native behavior: transparency handling
            effect = self.effect
            if effect.transparency == None:
                return

            opaque_mode = effect.opaque_mode
            flip = opaque_mode in ("A_ZERO", "RGB_ZERO")
            b_mat = self.b_mat
            b_shader = self.b_shader

            if isinstance(effect.transparency, Real):
                alpha = effect.transparency
                if flip:
                    alpha = 1 - alpha

                # Native behavior: set Alpha input
                if "Alpha" in b_shader.inputs:
                    b_shader.inputs["Alpha"].default_value = alpha

                # Set material blend method if transparent
                if alpha < 1.0:
                    b_mat.blend_method = "BLEND"
                    b_mat.diffuse_color[3] = alpha

        def rendering_emission(self):
            # Native behavior: emission maps to Emission Color
            self.color_or_texture(self.effect.emission, "emission", "Emission Color")

            # Native behavior: set emission strength if emission is present
            if isinstance(self.effect.emission, tuple) and any(c > 0 for c in self.effect.emission[:3]):
                b_shader = self.b_shader
                if "Emission Strength" in b_shader.inputs:
                    b_shader.inputs["Emission Strength"].default_value = 1.0

        def color_or_texture(self, color_or_texture, tex_name, shader_input_name, set_mat_color=False):
            if isinstance(color_or_texture, tuple):
                # Color value - set directly to shader input
                if shader_input_name in self.b_shader.inputs:
                    self.b_shader.inputs[shader_input_name].default_value = color_or_texture
                if set_mat_color:
                    self.b_mat.diffuse_color = color_or_texture
            elif isinstance(color_or_texture, Map):
                # Texture - only try to load image if it exists (matches native behavior)
                surface = color_or_texture.sampler.surface
                if surface is not None and getattr(surface, 'image', None) is not None:
                    b_image = self.get_image(surface.image)
                    if b_image != None:
                        # Native behavior: create texture node and connect to shader
                        b_shader = self.b_shader
                        node_graph = b_shader.id_data
                        b_texture = node_graph.nodes.new("ShaderNodeTexImage")
                        b_texture.image = b_image
                        b_texture.location = (self.node_x, self.node_y)
                        self.node_y -= 300

                        # Connect texture to shader input
                        if shader_input_name in b_shader.inputs:
                            node_graph.links.new(b_texture.outputs["Color"], b_shader.inputs[shader_input_name])

                        # Create texture coordinates node if needed
                        if self.tex_coords_src == None:
                            self.tex_coords_src = node_graph.nodes.new("ShaderNodeTexCoord")
                            self.tex_coords_src.location = (self.node_x - 200, self.node_y)

                        # Connect texture coordinates
                        node_graph.links.new(self.tex_coords_src.outputs["UV"], b_texture.inputs["Vector"])
                else:
                    # No image to load, skip (native behavior)
                    pass

        def get_image(self, c_image):
            """
            Load image from COLLADA file matching native Blender COLLADA importer behavior.

            Native behavior:
            - Always join DAE directory with image path (even if it starts with ./)
            - Only try absolute path if joined path does not exist
            - Do not normalize away ./ before joining
            - Print warning if neither exists
            """
            b_image = None
            if c_image.path in self.images:
                b_image = self.images[c_image.path]
            else:
                if c_image.path is not None:
                    # Get the DAE file directory - try multiple sources
                    collada_dir = ""
                    if hasattr(self.parent._collada, 'filename') and self.parent._collada.filename:
                        collada_dir = os.path.dirname(self.parent._collada.filename)
                    elif hasattr(self.parent, '_filepath') and self.parent._filepath:
                        collada_dir = os.path.dirname(self.parent._filepath)
                    elif hasattr(self.parent, 'filepath') and self.parent.filepath:
                        collada_dir = os.path.dirname(self.parent.filepath)

                    # Try joined path first (native behavior)
                    joined_path = os.path.join(collada_dir, c_image.path)
                    if os.path.exists(joined_path):
                        try:
                            b_image = bpy.data.images.load(joined_path)
                        except Exception as e:
                            print(f"|! Cannot create image: {joined_path}")
                            return None
                    elif os.path.exists(c_image.path):
                        try:
                            b_image = bpy.data.images.load(c_image.path)
                        except Exception as e:
                            print(f"|! Cannot create image: {c_image.path}")
                            return None
                    else:
                        print(f"|! Image not found: {c_image.path}")
                        print(f"|! Cannot create image: {c_image.path}")
                        return None
                if b_image is not None:
                    self.images[c_image.path] = b_image
                    print(f"| import Image: {c_image.path}")
            return b_image



    @classmethod
    def match(cls, collada):
        return True

# Import specialized importers
from .sketchup_importer import SketchUpImport

VENDOR_SPECIFIC = [
    SketchUpImport,
]

def get_import(collada):
    """Return suitable importer for the given Collada object."""
    for i in VENDOR_SPECIFIC:
        if i.match(collada):
            return i
    return ColladaImport