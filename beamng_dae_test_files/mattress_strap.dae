<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.91.0 commit date:2020-11-25, commit time:08:34, hash:0f45cab862b8</authoring_tool>
    </contributor>
    <created>2021-02-02T11:52:17</created>
    <modified>2021-02-02T11:52:17</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="woodplanks-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images/>
  <library_materials>
    <material id="woodplanks-material" name="woodplanks">
      <instance_effect url="#woodplanks-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="mattress_strap-mesh" name="mattress_strap">
      <mesh>
        <source id="mattress_strap-mesh-positions">
          <float_array id="mattress_strap-mesh-positions-array" count="192">-0.6859963 -0.02424407 0.09388583 0.6951805 -0.02424407 0.09388583 -0.6592696 -0.02424407 0.1293184 -0.6879033 -0.02424407 -0.1127372 -0.6852734 -0.02424407 -0.1159079 -0.6852734 0.02424383 -0.1159079 -0.6879033 0.02424383 -0.1127372 -0.6611807 -0.02424407 0.1291795 -0.6611807 0.02424383 0.1291795 -0.6578404 0.02424383 0.1320484 0.6970874 -0.02424407 0.09371858 0.6684538 -0.02424407 0.1293184 0.6970874 -0.02424407 -0.1127372 0.6944575 -0.02424407 -0.1159079 0.6970874 0.02424383 -0.1127372 0.6670246 -0.02424407 0.1320484 0.670365 0.02424383 0.1291795 -0.6860194 -0.02424407 -0.1136954 -0.6592696 0.02424383 0.1293184 -0.6859963 0.02424383 0.09388583 -0.6860194 0.02424383 -0.1136954 -0.6879033 -0.02424407 0.09371858 -0.6879033 0.02424383 0.09371858 -0.6578404 -0.02424407 0.1320484 0.6952037 -0.02424407 -0.1136954 0.3531825 -0.02424407 -0.1738567 0.6684538 0.02424383 0.1293184 0.6951805 0.02424383 0.09388583 -0.3439983 -0.02424407 -0.1738567 0.6952037 0.02424383 -0.1136954 0.3535597 0.02424383 -0.1716442 0.6944575 0.02424383 -0.1159079 -0.3443753 0.02424383 -0.1716442 0.3535597 -0.02424407 -0.1716442 0.6970874 0.02424383 0.09371858 0.670365 -0.02424407 0.1291795 -0.3443753 -0.02424407 -0.1716442 0.6670246 0.02424383 0.1320484 0.3531825 0.02424383 -0.1738567 -0.3439983 0.02424383 -0.1738567 0.7075569 0.02467787 -0.009908318 0.7094861 0.02467787 -0.009505748 0.7094861 -0.02467811 -0.009505748 0.7075569 -0.02467811 -0.009908318 -0.700302 0.02467787 -0.009505748 -0.700302 -0.02467811 -0.009505748 -0.6983727 -0.02467811 -0.009908318 -0.6983727 0.02467787 -0.009908318 0.08756548 0.02424383 0.1534155 0.363726 0.02424383 0.146723 -0.354542 0.02424383 0.146723 -0.07838112 0.02424383 0.1534155 0.08804184 -0.02424407 0.1506854 0.3641999 -0.02424407 0.1440075 -0.355016 -0.02424407 0.1440075 -0.07885748 -0.02424407 0.1506854 -0.07885748 0.02424383 0.1506854 -0.3550159 0.02424383 0.1440075 0.3641999 0.02424383 0.1440075 0.08804184 0.02424383 0.1506854 0.363726 -0.02424407 0.146723 0.08756548 -0.02424407 0.1534155 -0.07838112 -0.02424407 0.1534155 -0.354542 -0.02424407 0.146723</float_array>
          <technique_common>
            <accessor source="#mattress_strap-mesh-positions-array" count="64" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="mattress_strap-mesh-normals">
          <float_array id="mattress_strap-mesh-normals-array" count="210">0 -1 0 -0.1674047 0 -0.9858884 -0.4997217 0 -0.8661861 -0.3699703 0 0.9290437 -0.03627955 0 0.9993417 0 1 -5.72666e-7 0 1 1.84333e-6 0 1 0 2.5534e-4 0.9999913 0.004162847 0 -1 2.10707e-6 2.5534e-4 -0.9999913 0.004162788 0 -1 -1.44962e-6 0 1 -1.44965e-6 0.9188678 0 -0.3945655 0.4997216 0 -0.8661861 0.467443 0 -0.8840232 0.9278815 0 -0.3728753 0.9278815 0 -0.3728754 0.7293053 0 0.6841884 0.9278072 0 0.3730602 -2.55362e-4 -0.9999913 0.004163026 0 -1 -1.24933e-6 0 -1 3.45924e-7 -0.9188678 0 -0.3945655 0 1 -2.27323e-7 0 1 -5.76099e-7 -1 0 4.04756e-6 -1 0 4.05235e-6 -0.9278067 0 0.3730612 0 1 5.88757e-7 0 -1 1.05354e-6 -0.7293076 0 0.684186 0 -1 1.44957e-6 -0.4674426 0 -0.8840235 -0.9278812 0 -0.3728762 1 0 -1.05865e-4 1 0 -1.0586e-4 -1 0 -1.06147e-4 -1 0 -1.06142e-4 0 -1 -4.62954e-7 0 -1 4.39056e-7 -0.03620147 0 -0.9993446 -0.167229 0 0.9859182 1 0 4.04756e-6 1 0 4.05235e-6 0 1 5.59827e-7 -2.55362e-4 0.9999913 0.004163026 0.1674047 0 -0.9858883 0.03620147 0 -0.9993445 0.03620147 0 -0.9993445 0.3699662 0 0.9290453 0 1 -5.72667e-7 0.03627955 0 0.9993418 0.167229 0 0.9859182 0.992991 0 0.1181905 -0.0121144 0 0.9999266 0.0121144 0 0.9999266 0.0121144 0 0.9999266 0.01208823 0 -0.999927 -0.01208823 0 -0.999927 -0.01208823 0 -0.999927 0 -1 5.89126e-7 0 -1 0 0 1 -3.1825e-7 0 1 -1.22684e-7 -0.001563072 -0.9999893 -0.004380285 -0.001563072 0.9999893 -0.004380404 0.001563191 0.9999892 -0.004380524 -0.992991 0 0.1181905 0.001563251 -0.9999892 -0.004380345</float_array>
          <technique_common>
            <accessor source="#mattress_strap-mesh-normals-array" count="70" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="mattress_strap-mesh-map-0">
          <float_array id="mattress_strap-mesh-map-0-array" count="512">0.7346978 0.112247 0.7323073 0.112247 0.7323073 0.04344791 0.7323073 0.04344791 0.7346979 0.04344737 0.7346978 0.112247 0.7725846 0.1437845 0.7821442 0.1437848 0.7821441 0.04333305 0.7725846 0.04333257 0.8324032 0.2756714 0.8324032 0.2255975 0.8347122 0.2253539 0.8347121 0.2748677 0.8499413 0.4477621 0.8499413 0.3572664 0.8405171 0.357267 0.8405171 0.4477621 0.8536048 0.1429575 0.8536049 0.05246227 0.8559141 0.05146026 0.855914 0.1427437 0.7563127 0.112247 0.7539224 0.112247 0.7539224 0.04344737 0.7539224 0.04344737 0.7563127 0.04344791 0.7563127 0.112247 0.7563127 0.2591187 0.7539224 0.2588909 0.7539224 0.206155 0.7563127 0.206155 0.7677079 0.06573462 0.7677079 0.0686016 0.7653172 0.06716841 0.7869322 0.06860125 0.7869322 0.06573462 0.7893228 0.06716841 0.7346979 0.2588909 0.7323073 0.2591187 0.7323073 0.206155 0.7346978 0.206155 0.7346978 0.03834468 0.7346979 0.04344737 0.7323073 0.04344791 0.7539224 0.04344737 0.7539222 0.03834468 0.7563127 0.04344791 0.8162527 0.05453193 0.8162528 0.05254662 0.8068286 0.05254662 0.8068286 0.05453193 0.7398466 0.04344737 0.7494063 0.04344737 0.7494063 0.05942744 0.7398466 0.05942744 0.8499411 0.05045706 0.8499411 0.03347861 0.8405171 0.03347861 0.8405169 0.05046117 0.8015955 0.2500907 0.8015954 0.3123787 0.7992864 0.3123787 0.7992864 0.2499385 0.8347122 0.03347861 0.8347122 0.05045706 0.8324032 0.05146002 0.8324032 0.05146002 0.8324032 0.03347861 0.8347122 0.03347861 0.7821441 0.04058814 0.7725846 0.04058814 0.7725846 0.04333257 0.7821441 0.04333305 0.8559139 0.03347861 0.8559141 0.05146026 0.8536049 0.05046117 0.8536049 0.05046117 0.8536047 0.03347861 0.8559139 0.03347861 0.7398462 0.2513089 0.7494059 0.2513089 0.7494059 0.1909909 0.7398462 0.1909909 0.7893228 0.1623603 0.7869322 0.162736 0.7869322 0.06860125 0.7893228 0.06716841 0.8015955 0.05453193 0.7992864 0.05357486 0.8015954 0.05254662 0.8204882 0.05254662 0.8227974 0.05357486 0.8204882 0.05453193 0.7494058 0.04141277 0.7398462 0.04141277 0.7398462 0.06455844 0.7494059 0.06455844 0.8347122 0.05245959 0.8324032 0.05146002 0.8347122 0.05045706 0.8536049 0.05046117 0.8559141 0.05146026 0.8536049 0.05246227 0.840517 0.05045706 0.840517 0.0334407 0.8499413 0.0334407 0.8499413 0.05045706 0.7494054 0.2504931 0.7398457 0.2504931 0.7398466 0.1893593 0.7494063 0.1893593 0.8162527 0.2506076 0.8162526 0.3123787 0.8068286 0.3123787 0.8068286 0.2506076 0.7677078 0.1627362 0.7653172 0.1623603 0.7653172 0.06716841 0.7677079 0.0686016 0.7677077 0.4451404 0.7653172 0.4479362 0.7653172 0.3527443 0.7677077 0.3510057 0.840517 0.140893 0.840517 0.05045706 0.8499413 0.05045706 0.8499413 0.140893 0.7821443 0.4451404 0.7725846 0.4451404 0.7725846 0.3510056 0.7821443 0.3510056 0.8068286 0.2497749 0.8068286 0.3117471 0.8162528 0.3117471 0.8162528 0.2497749 0.7893228 0.4479362 0.7869322 0.4451404 0.7869322 0.3510056 0.7893227 0.3527443 0.8227973 0.2499385 0.8227972 0.3123787 0.8204882 0.3123787 0.8204882 0.2500907 0.7725846 0.4451404 0.7821444 0.4451404 0.7821443 0.3446885 0.7725846 0.3446884 0.8405172 0.4451074 0.8405172 0.3546715 0.8499411 0.3546715 0.8499411 0.4451074 0.8405169 0.05046117 0.8405169 0.05246227 0.8499411 0.05245959 0.8499411 0.05045706 0.8536047 0.4477621 0.8536048 0.357267 0.8559138 0.3585256 0.8559138 0.449809 0.8499411 0.1429554 0.8499411 0.05245959 0.8405169 0.05246227 0.8405169 0.1429575 0.7398462 0.04141277 0.7494058 0.04141277 0.7494059 0.03834468 0.7398463 0.03834468 0.8324032 0.4498089 0.8324032 0.3585255 0.834712 0.3572664 0.834712 0.4477621 0.7821443 0.162736 0.7725846 0.162736 0.7725846 0.06860125 0.7821443 0.06860125 0.7494045 0.3116267 0.7398448 0.3116267 0.7398457 0.2504931 0.7494054 0.2504931 0.8324032 0.1427435 0.8324032 0.05146002 0.8347122 0.05245959 0.8347122 0.1429554 0.8499413 0.2748677 0.8499413 0.2253539 0.840517 0.2253554 0.8405171 0.2748689 0.8405172 0.2725867 0.8405171 0.2229778 0.8499413 0.2229777 0.8499411 0.2725867 0.8324032 0.1427435 0.8347122 0.1429554 0.8347122 0.2253539 0.8324032 0.2255975 0.8324032 0.2756714 0.8347121 0.2748677 0.834712 0.3572664 0.8324032 0.3585255 0.8536048 0.1429575 0.855914 0.1427437 0.8559139 0.2255977 0.8536048 0.2253554 0.8536048 0.2748689 0.8559139 0.2756715 0.8559138 0.3585256 0.8536048 0.357267 0.8499411 0.1429554 0.8405169 0.1429575 0.840517 0.2253554 0.8499413 0.2253539 0.8499413 0.2748677 0.8405171 0.2748689 0.8405171 0.357267 0.8499413 0.3572664 0.840517 0.140893 0.8499413 0.140893 0.8499413 0.2229777 0.8405171 0.2229778 0.8405172 0.2725867 0.8499411 0.2725867 0.8499411 0.3546715 0.8405172 0.3546715 0.8536048 0.2748689 0.8536048 0.2253554 0.8559139 0.2255977 0.8559139 0.2756715 0.7398462 0.3116267 0.7494058 0.3116267 0.7494059 0.2513089 0.7398462 0.2513089 0.7346979 0.3116267 0.7323073 0.3120824 0.7323073 0.2591187 0.7346979 0.2588909 0.7563127 0.3120825 0.7539224 0.3116267 0.7539224 0.2588909 0.7563127 0.2591187 0.8227974 0.1874983 0.8227973 0.2499385 0.8204882 0.2500907 0.8204882 0.1878026 0.8068286 0.1878026 0.8068286 0.2497749 0.8162528 0.2497749 0.8162527 0.1878026 0.8162528 0.1888364 0.8162527 0.2506076 0.8068286 0.2506076 0.8068286 0.1888364 0.8015955 0.1878026 0.8015955 0.2500907 0.7992864 0.2499385 0.7992864 0.1874983</float_array>
          <technique_common>
            <accessor source="#mattress_strap-mesh-map-0-array" count="256" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="mattress_strap-mesh-colors-geom-woodplanks_straps-map0" name="geom-woodplanks_straps-map0">
          <float_array id="mattress_strap-mesh-colors-geom-woodplanks_straps-map0-array" count="1024">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#mattress_strap-mesh-colors-geom-woodplanks_straps-map0-array" count="256" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="mattress_strap-mesh-vertices">
          <input semantic="POSITION" source="#mattress_strap-mesh-positions"/>
        </vertices>
        <polylist material="woodplanks-material" count="68">
          <input semantic="VERTEX" source="#mattress_strap-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#mattress_strap-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#mattress_strap-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#mattress_strap-mesh-colors-geom-woodplanks_straps-map0" offset="3" set="0"/>
          <vcount>3 3 4 4 4 4 3 3 4 3 3 4 3 3 4 4 4 4 3 3 4 3 3 4 4 3 3 4 3 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>21 0 0 0 0 0 1 1 2 0 2 2 2 0 3 3 7 0 4 4 21 0 5 5 39 1 6 6 28 1 7 7 4 2 8 8 5 2 9 9 55 0 10 10 52 0 11 11 61 0 12 12 62 0 13 13 23 3 14 14 63 4 15 15 50 4 16 16 9 3 17 17 49 5 18 18 37 5 19 19 26 5 20 20 58 5 21 21 19 6 22 22 22 6 23 23 8 6 24 24 8 7 25 25 18 7 26 26 19 7 27 27 47 8 28 28 44 8 29 29 22 8 30 30 19 8 31 31 3 9 32 32 4 9 33 33 17 9 34 34 5 7 35 35 6 7 36 36 20 7 37 37 45 10 38 38 46 10 39 39 0 10 40 40 21 10 41 41 23 11 42 42 7 11 43 43 2 11 44 44 8 12 45 45 9 12 46 46 18 12 47 47 12 13 48 48 13 14 49 49 31 14 50 50 14 13 51 51 18 15 52 52 2 15 53 53 0 16 54 54 19 17 55 55 35 18 56 56 10 19 57 57 34 19 58 58 16 18 59 59 42 20 60 60 10 20 61 61 1 20 62 62 43 20 63 63 10 21 64 64 35 21 65 65 11 21 66 66 11 22 67 67 1 22 68 68 10 22 69 69 3 23 70 70 6 23 71 71 5 2 72 72 4 2 73 73 27 24 74 74 26 24 75 75 16 24 76 76 16 25 77 77 34 25 78 78 27 25 79 79 44 26 80 80 45 27 81 81 21 28 82 82 22 28 83 83 32 29 84 84 39 29 85 85 5 29 86 86 20 29 87 87 12 30 88 88 24 30 89 89 13 30 90 90 31 7 91 91 29 7 92 92 14 7 93 93 7 31 94 94 8 31 95 95 22 28 96 96 21 28 97 97 15 32 98 98 11 32 99 99 35 32 100 100 16 7 101 101 26 7 102 102 37 7 103 103 26 33 104 104 27 34 105 105 1 34 106 106 11 33 107 107 46 35 108 108 47 36 109 109 19 17 110 110 0 16 111 111 43 37 112 112 1 34 113 113 27 34 114 114 40 38 115 115 28 39 116 116 36 39 117 117 17 39 118 118 4 39 119 119 13 40 120 120 24 40 121 121 33 40 122 122 25 40 123 123 58 41 124 124 26 33 125 125 11 33 126 126 53 41 127 127 24 42 128 128 29 42 129 129 30 42 130 130 33 42 131 131 41 43 132 132 34 19 133 133 10 19 134 134 42 44 135 135 29 45 136 136 31 45 137 137 38 45 138 138 30 45 139 139 40 46 140 140 27 46 141 141 34 46 142 142 41 46 143 143 31 14 144 144 13 14 145 145 25 47 146 146 38 47 147 147 18 15 148 148 57 48 149 149 54 49 150 150 2 15 151 151 16 18 152 152 37 50 153 153 15 50 154 154 35 18 155 155 9 51 156 156 50 51 157 157 57 51 158 158 18 51 159 159 60 52 160 160 15 50 161 161 37 50 162 162 49 52 163 163 8 31 164 164 7 31 165 165 23 3 166 166 9 3 167 167 2 0 168 168 54 0 169 169 63 0 170 170 23 0 171 171 36 53 172 172 32 53 173 173 20 53 174 174 17 53 175 175 17 54 176 176 20 54 177 177 47 36 178 178 46 35 179 179 53 0 180 180 11 0 181 181 15 0 182 182 60 0 183 183 62 55 184 184 61 56 185 185 48 57 186 186 51 55 187 187 56 58 188 188 59 59 189 189 52 60 190 190 55 58 191 191 53 61 192 192 60 61 193 193 61 61 194 194 52 61 195 195 55 62 196 196 62 62 197 197 63 62 198 198 54 62 199 199 49 63 200 200 58 63 201 201 59 63 202 202 48 63 203 203 51 64 204 204 56 64 205 205 57 64 206 206 50 64 207 207 60 52 208 208 49 52 209 209 48 57 210 210 61 56 211 211 62 55 212 212 51 55 213 213 50 4 214 214 63 4 215 215 58 41 216 216 53 41 217 217 52 60 218 218 59 59 219 219 56 58 220 220 55 58 221 221 54 49 222 222 57 48 223 223 51 7 224 224 48 7 225 225 59 7 226 226 56 7 227 227 6 23 228 228 3 23 229 229 45 27 230 230 44 26 231 231 3 65 232 232 17 65 233 233 46 65 234 234 45 65 235 235 20 66 236 236 6 66 237 237 44 66 238 238 47 66 239 239 29 67 240 240 40 67 241 241 41 67 242 242 14 67 243 243 14 13 244 244 41 43 245 245 42 44 246 246 12 13 247 247 24 68 248 248 43 37 249 249 40 38 250 250 29 68 251 251 12 69 252 252 42 69 253 253 43 69 254 254 24 69 255 255</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="mattress_strap" name="mattress_strap" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#mattress_strap-mesh" name="mattress_strap">
          <bind_material>
            <technique_common>
              <instance_material symbol="woodplanks-material" target="#woodplanks-material">
                <bind_vertex_input semantic="woodplanks_straps-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>