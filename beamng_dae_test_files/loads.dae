<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 4.3.2 commit date:2024-12-16, commit time:21:10, hash:32f5fdce0a0a</authoring_tool>
    </contributor>
    <created>2025-01-23T10:22:14</created>
    <modified>2025-01-23T10:22:14</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="money_stack-effect">
      <profile_COMMON>
        <newparam sid="money_stack_b_color_png-surface">
          <surface type="2D">
            <init_from>money_stack_b_color_png</init_from>
          </surface>
        </newparam>
        <newparam sid="money_stack_b_color_png-sampler">
          <sampler2D>
            <source>money_stack_b_color_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <texture texture="money_stack_b_color_png-sampler" texcoord="Attribute"/>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.5</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="crate_cola-effect">
      <profile_COMMON>
        <newparam sid="crate_cola_d_dds_001-surface">
          <surface type="2D">
            <init_from>crate_cola_d_dds_001</init_from>
          </surface>
        </newparam>
        <newparam sid="crate_cola_d_dds_001-sampler">
          <sampler2D>
            <source>crate_cola_d_dds_001-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <texture texture="crate_cola_d_dds_001-sampler" texcoord="geom-crate_cola-map1"/>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.5</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="crate_cola_d_dds_001" name="crate_cola_d_dds_001">
      <init_from>/C:/Work/vehicle_art/vehicle_artwork/common/loads/crate_cola_d.dds</init_from>
    </image>
    <image id="money_stack_b_color_png" name="money_stack_b_color_png">
      <init_from>/C:/BeamNG_SVN/vehicles/common/loads/money_stack_b.color.png</init_from>
    </image>
  </library_images>
  <library_materials>
    <material id="money_stack-material" name="money_stack">
      <instance_effect url="#money_stack-effect"/>
    </material>
    <material id="crate_cola-material" name="crate_cola">
      <instance_effect url="#crate_cola-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Mesh_003-mesh" name="Mesh.003">
      <mesh>
        <source id="Mesh_003-mesh-positions">
          <float_array id="Mesh_003-mesh-positions-array" count="24">0.2344779 0.2654937 0.454234 -0.2344779 0.2654937 0.454234 -0.2344779 0.2654936 0.1160418 0.2344779 0.2654936 0.1160418 0.2344779 -0.2654936 0.454234 0.2344779 -0.2654936 0.1160418 -0.2344779 -0.2654937 0.1160418 -0.2344778 -0.2654936 0.454234</float_array>
          <technique_common>
            <accessor source="#Mesh_003-mesh-positions-array" count="8" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Mesh_003-mesh-normals">
          <float_array id="Mesh_003-mesh-normals-array" count="63">1 0 0 1 0 0 1 0 0 0 1 0 0 1 0 0 1 -1.87912e-7 0 0 1 0 -1 0 0 -1 0 0 -1 0 -1 0 1.76245e-7 -1 0 1.76245e-7 -1 0 1.76245e-7 0 0 -1 0 0 -1 0 0 -1 1 0 0 0 1 0 0 -1 1.87912e-7 -1 0 1.76245e-7 0 0 -1</float_array>
          <technique_common>
            <accessor source="#Mesh_003-mesh-normals-array" count="21" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Mesh_003-mesh-map-0">
          <float_array id="Mesh_003-mesh-map-0-array" count="72">0.7577843 0.4932378 0.995735 0.3430937 0.995735 0.4932378 0.464553 0.4932378 0.2351891 0.3430937 0.464553 0.3430937 0.08296316 0.7179837 0.3123265 0.9559348 0.08296316 0.9559348 0.08295959 0.4932378 0.3123235 0.3430937 0.3123235 0.4932378 0.551774 0.3430937 0.7897247 0.4932378 0.551774 0.4932378 0.9269669 0.8713341 0.7393509 0.676694 0.9269669 0.676694 0.7577843 0.4932378 0.7577843 0.3430937 0.995735 0.3430937 0.464553 0.4932378 0.2351891 0.4932378 0.2351891 0.3430937 0.08296316 0.7179837 0.3123265 0.7179837 0.3123265 0.9559348 0.08295959 0.4932378 0.08295959 0.3430937 0.3123235 0.3430937 0.551774 0.3430937 0.7897247 0.3430937 0.7897247 0.4932378 0.9269669 0.8713341 0.7393509 0.8713341 0.7393509 0.676694</float_array>
          <technique_common>
            <accessor source="#Mesh_003-mesh-map-0-array" count="36" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Mesh_003-mesh-vertices">
          <input semantic="POSITION" source="#Mesh_003-mesh-positions"/>
        </vertices>
        <triangles material="money_stack-material" count="12">
          <input semantic="VERTEX" source="#Mesh_003-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Mesh_003-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Mesh_003-mesh-map-0" offset="2" set="0"/>
          <p>4 0 0 3 1 1 0 2 2 1 3 3 3 4 4 2 5 5 7 6 6 0 6 7 1 6 8 7 7 9 5 8 10 4 9 11 2 10 12 7 11 13 1 12 14 2 13 15 5 14 16 6 15 17 4 0 18 5 16 19 3 1 20 1 3 21 0 17 22 3 4 23 7 6 24 4 6 25 0 6 26 7 7 27 6 18 28 5 8 29 2 10 30 6 19 31 7 11 32 2 13 33 3 20 34 5 14 35</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Mesh_002-mesh" name="Mesh.002">
      <mesh>
        <source id="Mesh_002-mesh-positions">
          <float_array id="Mesh_002-mesh-positions-array" count="24">0.4673674 0.4953499 0.1160126 0.4673674 -0.4953498 0.665378 0.4673674 0.49535 0.665378 -0.4673672 -0.4953498 0.665378 -0.4673673 0.49535 0.665378 0.4673674 -0.4953498 0.1160126 -0.4673674 0.4953499 0.1160126 -0.4673672 -0.4953498 0.1160126</float_array>
          <technique_common>
            <accessor source="#Mesh_002-mesh-positions-array" count="8" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Mesh_002-mesh-normals">
          <float_array id="Mesh_002-mesh-normals-array" count="30">0 0 -1 0 0 1 0 -1 0 0 -1 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 1 0 -1 0 0</float_array>
          <technique_common>
            <accessor source="#Mesh_002-mesh-normals-array" count="10" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Mesh_002-mesh-map-0">
          <float_array id="Mesh_002-mesh-map-0-array" count="72">0.9269669 0.5081807 0.5530058 0.8713341 0.5530058 0.5081807 0.08296316 0.511973 0.540136 0.9559347 0.08296316 0.9559348 0.08295959 0.4932378 0.5401335 0.249341 0.5401335 0.4932378 0.551774 0.4932378 0.995735 0.249341 0.995735 0.4932378 0.551774 0.4932378 0.995735 0.249341 0.995735 0.4932378 0.464553 0.4932378 0.007379114 0.249341 0.464553 0.249341 0.9269669 0.5081807 0.9269669 0.8713341 0.5530058 0.8713341 0.08296316 0.511973 0.540136 0.511973 0.540136 0.9559347 0.08295959 0.4932378 0.08295959 0.249341 0.5401335 0.249341 0.551774 0.4932378 0.551774 0.249341 0.995735 0.249341 0.551774 0.4932378 0.551774 0.249341 0.995735 0.249341 0.464553 0.4932378 0.007379114 0.4932378 0.007379114 0.249341</float_array>
          <technique_common>
            <accessor source="#Mesh_002-mesh-map-0-array" count="36" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Mesh_002-mesh-vertices">
          <input semantic="POSITION" source="#Mesh_002-mesh-positions"/>
        </vertices>
        <triangles material="money_stack-material" count="12">
          <input semantic="VERTEX" source="#Mesh_002-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Mesh_002-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Mesh_002-mesh-map-0" offset="2" set="0"/>
          <p>7 0 0 0 0 1 5 0 2 3 1 3 2 1 4 4 1 5 3 2 6 5 3 7 1 2 8 1 4 9 0 4 10 2 4 11 4 5 12 7 6 13 3 7 14 4 8 15 0 8 16 6 8 17 7 0 18 6 0 19 0 0 20 3 1 21 1 1 22 2 1 23 3 2 24 7 2 25 5 3 26 1 4 27 5 4 28 0 4 29 4 5 30 6 9 31 7 6 32 4 8 33 2 8 34 0 8 35</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="crate_cola_001-mesh" name="crate_cola.001">
      <mesh>
        <source id="crate_cola_001-mesh-positions">
          <float_array id="crate_cola_001-mesh-positions-array" count="24">60 -50 11.5 60 50 11.5 60 -50 111.5 60 50 111.5 -60 -50 11.5 -60.00001 50 11.5 -59.99999 -50 111.5 -60 50 111.5</float_array>
          <technique_common>
            <accessor source="#crate_cola_001-mesh-positions-array" count="8" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="crate_cola_001-mesh-normals">
          <float_array id="crate_cola_001-mesh-normals-array" count="27">1 0 0 -1 -1.95313e-7 0 -1 0 0 0 0 -1 0 1 0 0 1 0 0 0 1 0 -1 0 0 -1 0</float_array>
          <technique_common>
            <accessor source="#crate_cola_001-mesh-normals-array" count="9" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="crate_cola_001-mesh-map-0">
          <float_array id="crate_cola_001-mesh-map-0-array" count="72">0.551774 0.04927676 0.995735 0.04927676 0.995735 0.4932378 0.995735 0.4932378 0.551774 0.4932378 0.551774 0.04927676 0.995735 0.04927676 0.995735 0.4932378 0.551774 0.4932378 0.551774 0.4932378 0.551774 0.04927676 0.995735 0.04927676 0.5530058 0.5081807 0.9887906 0.5081807 0.9887906 0.8713341 0.9887906 0.8713341 0.5530058 0.8713341 0.5530058 0.5081807 0.007379114 0.04927676 0.5401335 0.04927676 0.5401335 0.4932378 0.5401335 0.4932378 0.007379114 0.4932378 0.007379114 0.04927676 0.540136 0.9559347 0.007382869 0.9559347 0.007382869 0.511973 0.007382869 0.511973 0.540136 0.511973 0.540136 0.9559347 0.5401335 0.4932378 0.007379114 0.4932378 0.007379114 0.04927676 0.007379114 0.04927676 0.5401335 0.04927676 0.5401335 0.4932378</float_array>
          <technique_common>
            <accessor source="#crate_cola_001-mesh-map-0-array" count="36" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="crate_cola_001-mesh-vertices">
          <input semantic="POSITION" source="#crate_cola_001-mesh-positions"/>
        </vertices>
        <triangles material="crate_cola-material" count="12">
          <input semantic="VERTEX" source="#crate_cola_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#crate_cola_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#crate_cola_001-mesh-map-0" offset="2" set="0"/>
          <p>0 0 0 1 0 1 3 0 2 3 0 3 2 0 4 0 0 5 4 1 6 6 1 7 7 1 8 7 2 9 5 2 10 4 2 11 0 3 12 4 3 13 5 3 14 5 3 15 1 3 16 0 3 17 1 4 18 5 4 19 7 4 20 7 5 21 3 5 22 1 5 23 3 6 24 7 6 25 6 6 26 6 6 27 2 6 28 3 6 29 2 7 30 6 7 31 4 7 32 4 8 33 0 8 34 2 8 35</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="money_stack_b" name="money_stack_b" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Mesh_003-mesh" name="money_stack_b">
          <bind_material>
            <technique_common>
              <instance_material symbol="money_stack-material" target="#money_stack-material">
                <bind_vertex_input semantic="Attribute" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="money_stack_a" name="money_stack_a" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Mesh_002-mesh" name="money_stack_a">
          <bind_material>
            <technique_common>
              <instance_material symbol="money_stack-material" target="#money_stack-material">
                <bind_vertex_input semantic="Attribute" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="crate_cola" name="crate_cola" type="NODE">
        <matrix sid="transform">0.01 0 0 0 0 0.01 0 0 0 0 0.01 0 0 0 0 1</matrix>
        <instance_geometry url="#crate_cola_001-mesh" name="crate_cola">
          <bind_material>
            <technique_common>
              <instance_material symbol="crate_cola-material" target="#crate_cola-material">
                <bind_vertex_input semantic="geom-crate_cola-map1" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>