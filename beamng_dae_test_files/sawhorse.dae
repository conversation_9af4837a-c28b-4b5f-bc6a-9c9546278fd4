<?xml version='1.0' encoding='utf-8'?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.74.0 commit date:2015-03-31, commit time:13:39, hash:000dfc0</authoring_tool>
    </contributor>
    <created>2015-05-21T22:53:51</created>
    <modified>2015-05-21T22:53:51</modified>
    <unit meter="1" name="meter" />
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="sawhorse-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="warninglight-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="sawhorse-material" name="sawhorse">
      <instance_effect url="#sawhorse-effect" />
    </material>
    <material id="warninglight-material" name="warninglight">
      <instance_effect url="#warninglight-effect" />
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Plane_001-mesh" name="Plane.001">
      <mesh>
        <source id="Plane_001-mesh-positions">
          <float_array count="888" id="Plane_001-mesh-positions-array">-0.7443177 -0.03893828 0.9852196 -0.7038242 0.00270164 0.9852196 -0.7038241 -0.3976921 -6.03233e-4 -0.7443177 -0.3115543 0.002618372 -0.7038242 0.001871407 0.8617259 -0.7443177 0.001871407 0.8617259 -0.7443177 -0.3976921 -6.03233e-4 -0.7443177 0.00270164 0.9852196 -0.7038242 -0.3115543 0.002618372 -0.7038242 -0.03893828 0.9852196 -0.7443177 0.2682338 0.3790823 -0.7848114 0.2400746 0.4555324 -0.7443177 -0.2605464 0.3790823 -0.7848114 -0.2312405 0.4555324 -0.7848114 -0.2605464 0.3790823 -0.7443177 -0.2312405 0.4555324 -0.7848114 0.2682338 0.3790823 -0.7443177 0.2400746 0.4555324 0.6636913 -0.3163325 0.002618372 0.7041847 -0.03884285 0.9852196 0.7041847 0.0036273 0.8617259 0.7041847 -0.3966755 0.00192821 0.7041847 0.002797067 0.9852196 0.6636913 0.002797067 0.9852196 0.6636914 -0.3966755 0.00192821 0.6636913 0.0036273 0.8617259 0.6636913 -0.03884285 0.9852196 0.7041847 -0.3163325 0.002618372 -0.8058171 -0.2629313 0.3704774 0.7623922 -0.2629312 0.3704774 -0.8058171 -0.321964 0.2104881 0.7623922 -0.3219639 0.2104881 -0.8058171 -0.2781273 0.3760844 0.7623922 -0.2781271 0.3760844 -0.8058171 -0.3371601 0.2160951 0.7623922 -0.3371599 0.2160951 -0.795296 -0.2084465 0.4261815 -0.795296 -0.1995002 0.4210164 -0.795296 -0.1995002 0.410686 -0.795296 -0.2084465 0.4055209 -0.795296 -0.2173928 0.410686 -0.795296 -0.2173928 0.4210164 -0.6902478 -0.2084465 0.4261815 -0.6902478 -0.1995001 0.4210164 -0.6902478 -0.1995001 0.410686 -0.6902478 -0.2084465 0.4055209 -0.6902478 -0.2173928 0.410686 -0.6902478 -0.2173928 0.4210164 0.6495352 -0.1989438 0.4210164 0.6495352 -0.1989438 0.410686 0.6495352 -0.2078901 0.4055209 0.6495352 -0.2168365 0.410686 0.6495352 -0.2168365 0.4210164 0.6495352 -0.2078901 0.4261815 0.7545835 -0.1989438 0.4210164 0.7545835 -0.1989438 0.410686 0.7545835 -0.2078901 0.4055209 0.7545835 -0.2168364 0.410686 0.7545835 -0.2168364 0.4210164 0.7545835 -0.2078901 0.4261815 0.7044156 0.04663676 0.9852196 0.6639222 0.004996776 0.9852196 0.6639221 0.4053906 -6.03233e-4 0.7044158 0.3192527 0.002618372 0.6639222 0.005827009 0.8617259 0.7044156 0.005827009 0.8617259 0.7044157 0.4053906 -6.03233e-4 0.7044156 0.004996776 0.9852196 0.6639222 0.3192527 0.002618372 0.6639222 0.04663676 0.9852196 0.7044156 -0.2605352 0.3790823 0.7449094 -0.2323762 0.4555324 0.7044158 0.2682449 0.3790823 0.7449094 0.2389389 0.4555324 0.7449094 0.2682449 0.3790823 0.7044158 0.2389389 0.4555324 0.7449094 -0.2605352 0.3790823 0.7044156 -0.2323762 0.4555324 -0.7035933 0.324031 0.002618372 -0.7440867 0.04654145 0.9852196 -0.7440867 0.004071235 0.8617259 -0.7440868 0.404374 0.00192815 -0.7440867 0.004901468 0.9852196 -0.7035933 0.004901468 0.9852196 -0.7035934 0.404374 0.00192815 -0.7035933 0.004071235 0.8617259 -0.7035933 0.04654145 0.9852196 -0.7440867 0.324031 0.002618372 0.7659151 0.2706298 0.3704774 -0.8022943 0.2706298 0.3704774 0.7659151 0.3296625 0.2104881 -0.8022943 0.3296625 0.2104881 0.7659151 0.2858257 0.3760844 -0.8022943 0.2858258 0.3760844 0.7659151 0.3448585 0.2160951 -0.8022943 0.3448585 0.2160951 0.7553941 0.2161449 0.4261815 0.7553941 0.2071986 0.4210164 0.7553941 0.2071986 0.410686 0.7553941 0.2161449 0.4055209 0.7553941 0.2250913 0.410686 0.7553941 0.2250913 0.4210164 0.6503458 0.2161449 0.4261815 0.6503458 0.2071986 0.4210164 0.6503458 0.2071986 0.410686 0.6503458 0.2161449 0.4055209 0.6503458 0.2250912 0.410686 0.6503458 0.2250912 0.4210164 -0.6894372 0.2066425 0.4210164 -0.6894372 0.2066425 0.410686 -0.6894372 0.2155887 0.4055209 -0.6894372 0.2245351 0.410686 -0.6894372 0.2245351 0.4210164 -0.6894372 0.2155887 0.4261815 -0.7944855 0.2066424 0.4210164 -0.7944855 0.2066424 0.410686 -0.7944855 0.2155887 0.4055209 -0.7944855 0.2245351 0.410686 -0.7944855 0.2245351 0.4210164 -0.7944855 0.2155887 0.4261815 -0.7038241 -0.3976921 -6.03233e-4 -0.7038241 -0.3976921 -6.03233e-4 -0.7038241 -0.3976921 -6.03233e-4 -0.7443177 -0.3976921 -6.03233e-4 -0.7443177 -0.3976921 -6.03233e-4 -0.7443177 -0.3976921 -6.03233e-4 -0.7443177 0.001871407 0.8617259 -0.7443177 0.001871407 0.8617259 -0.7443177 0.001871407 0.8617259 -0.7443177 0.001871407 0.8617259 -0.7443177 0.00270164 0.9852196 -0.7443177 0.00270164 0.9852196 -0.7038242 0.00270164 0.9852196 -0.7038242 0.00270164 0.9852196 -0.7038242 -0.03893828 0.9852196 -0.7038242 -0.03893828 0.9852196 -0.7038242 -0.03893828 0.9852196 -0.7443177 -0.03893828 0.9852196 -0.7443177 -0.03893828 0.9852196 -0.7443177 -0.03893828 0.9852196 -0.7443177 -0.3115543 0.002618372 -0.7443177 -0.3115543 0.002618372 -0.7038242 -0.3115543 0.002618372 -0.7038242 -0.3115543 0.002618372 -0.7038242 0.001871407 0.8617259 -0.7038242 0.001871407 0.8617259 -0.7038242 0.001871407 0.8617259 -0.7038242 0.001871407 0.8617259 -0.7443177 0.2400746 0.4555324 -0.7443177 0.2400746 0.4555324 -0.7848114 0.2400746 0.4555324 -0.7848114 0.2400746 0.4555324 -0.7443177 -0.2605464 0.3790823 -0.7443177 -0.2605464 0.3790823 -0.7443177 0.2682338 0.3790823 -0.7443177 0.2682338 0.3790823 -0.7848114 0.2682338 0.3790823 -0.7848114 0.2682338 0.3790823 -0.7848114 -0.2605464 0.3790823 -0.7848114 -0.2605464 0.3790823 -0.7848114 -0.2312405 0.4555324 -0.7848114 -0.2312405 0.4555324 -0.7443177 -0.2312405 0.4555324 -0.7443177 -0.2312405 0.4555324 0.7041847 -0.03884285 0.9852196 0.7041847 -0.03884285 0.9852196 0.7041847 -0.03884285 0.9852196 0.7041847 0.002797067 0.9852196 0.7041847 0.002797067 0.9852196 0.7041847 0.0036273 0.8617259 0.7041847 0.0036273 0.8617259 0.7041847 0.0036273 0.8617259 0.7041847 0.0036273 0.8617259 0.7041847 -0.3966755 0.00192821 0.7041847 -0.3966755 0.00192821 0.7041847 -0.3966755 0.00192821 0.6636913 -0.03884285 0.9852196 0.6636913 -0.03884285 0.9852196 0.6636913 -0.03884285 0.9852196 0.6636913 0.002797067 0.9852196 0.6636913 0.002797067 0.9852196 0.6636914 -0.3966755 0.00192821 0.6636914 -0.3966755 0.00192821 0.6636914 -0.3966755 0.00192821 0.6636913 0.0036273 0.8617259 0.6636913 0.0036273 0.8617259 0.6636913 0.0036273 0.8617259 0.6636913 0.0036273 0.8617259 0.6636913 -0.3163325 0.002618372 0.6636913 -0.3163325 0.002618372 0.7041847 -0.3163325 0.002618372 0.7041847 -0.3163325 0.002618372 -0.8058171 -0.2629313 0.3704774 -0.8058171 -0.2629313 0.3704774 0.7623922 -0.2629312 0.3704774 0.7623922 -0.2629312 0.3704774 -0.8058171 -0.321964 0.2104881 -0.8058171 -0.321964 0.2104881 0.7623922 -0.3219639 0.2104881 0.7623922 -0.3219639 0.2104881 -0.8058171 -0.2781273 0.3760844 -0.8058171 -0.2781273 0.3760844 0.7623922 -0.2781271 0.3760844 0.7623922 -0.2781271 0.3760844 -0.8058171 -0.3371601 0.2160951 -0.8058171 -0.3371601 0.2160951 0.7623922 -0.3371599 0.2160951 0.7623922 -0.3371599 0.2160951 0.6639221 0.4053906 -6.03233e-4 0.6639221 0.4053906 -6.03233e-4 0.6639221 0.4053906 -6.03233e-4 0.7044157 0.4053906 -6.03233e-4 0.7044157 0.4053906 -6.03233e-4 0.7044157 0.4053906 -6.03233e-4 0.7044156 0.005827009 0.8617259 0.7044156 0.005827009 0.8617259 0.7044156 0.005827009 0.8617259 0.7044156 0.005827009 0.8617259 0.7044156 0.004996776 0.9852196 0.7044156 0.004996776 0.9852196 0.6639222 0.004996776 0.9852196 0.6639222 0.004996776 0.9852196 0.6639222 0.04663676 0.9852196 0.6639222 0.04663676 0.9852196 0.6639222 0.04663676 0.9852196 0.7044156 0.04663676 0.9852196 0.7044156 0.04663676 0.9852196 0.7044156 0.04663676 0.9852196 0.7044158 0.3192527 0.002618372 0.7044158 0.3192527 0.002618372 0.6639222 0.3192527 0.002618372 0.6639222 0.3192527 0.002618372 0.6639222 0.005827009 0.8617259 0.6639222 0.005827009 0.8617259 0.6639222 0.005827009 0.8617259 0.6639222 0.005827009 0.8617259 0.7044156 -0.2323762 0.4555324 0.7044156 -0.2323762 0.4555324 0.7449094 -0.2323762 0.4555324 0.7449094 -0.2323762 0.4555324 0.7044158 0.2682449 0.3790823 0.7044158 0.2682449 0.3790823 0.7044156 -0.2605352 0.3790823 0.7044156 -0.2605352 0.3790823 0.7449094 -0.2605352 0.3790823 0.7449094 -0.2605352 0.3790823 0.7449094 0.2682449 0.3790823 0.7449094 0.2682449 0.3790823 0.7449094 0.2389389 0.4555324 0.7449094 0.2389389 0.4555324 0.7044158 0.2389389 0.4555324 0.7044158 0.2389389 0.4555324 -0.7440867 0.04654145 0.9852196 -0.7440867 0.04654145 0.9852196 -0.7440867 0.04654145 0.9852196 -0.7440867 0.004901468 0.9852196 -0.7440867 0.004901468 0.9852196 -0.7440867 0.004071235 0.8617259 -0.7440867 0.004071235 0.8617259 -0.7440867 0.004071235 0.8617259 -0.7440867 0.004071235 0.8617259 -0.7440868 0.404374 0.00192815 -0.7440868 0.404374 0.00192815 -0.7440868 0.404374 0.00192815 -0.7035933 0.04654145 0.9852196 -0.7035933 0.04654145 0.9852196 -0.7035933 0.04654145 0.9852196 -0.7035933 0.004901468 0.9852196 -0.7035933 0.004901468 0.9852196 -0.7035934 0.404374 0.00192815 -0.7035934 0.404374 0.00192815 -0.7035934 0.404374 0.00192815 -0.7035933 0.004071235 0.8617259 -0.7035933 0.004071235 0.8617259 -0.7035933 0.004071235 0.8617259 -0.7035933 0.004071235 0.8617259 -0.7035933 0.324031 0.002618372 -0.7035933 0.324031 0.002618372 -0.7440867 0.324031 0.002618372 -0.7440867 0.324031 0.002618372 0.7659151 0.2706298 0.3704774 0.7659151 0.2706298 0.3704774 -0.8022943 0.2706298 0.3704774 -0.8022943 0.2706298 0.3704774 0.7659151 0.3296625 0.2104881 0.7659151 0.3296625 0.2104881 -0.8022943 0.3296625 0.2104881 -0.8022943 0.3296625 0.2104881 0.7659151 0.2858257 0.3760844 0.7659151 0.2858257 0.3760844 -0.8022943 0.2858258 0.3760844 -0.8022943 0.2858258 0.3760844 0.7659151 0.3448585 0.2160951 0.7659151 0.3448585 0.2160951 -0.8022943 0.3448585 0.2160951 -0.8022943 0.3448585 0.2160951</float_array>
          <technique_common>
            <accessor count="296" source="#Plane_001-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_001-mesh-normals">
          <float_array count="888" id="Plane_001-mesh-normals-array">0 0.9999695 -0.006714046 0 0.9999695 -0.006714046 0 0.9999695 -0.006714046 0 0.03735464 -0.9992981 0 0.03735464 -0.9992981 0 0.03735464 -0.9992981 0 -0.9396954 0.3419599 0 -0.9396954 0.3419599 0 -0.9396954 0.3419599 0 0 1 0 0 1 0 0 1 0 0.9394208 -0.3427228 0 0.9394208 -0.3427228 0 0.9394208 -0.3427228 1 0 0 1 0 0 0.9999695 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0.9999695 0 0 0 0 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 0.9383526 0.3456221 0 0.9383526 0.3456221 0 0.9383526 0.3456221 0 -0.9337443 0.3578906 0 -0.9337443 0.3578906 0 -0.9337443 0.3578906 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999695 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999695 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 0.9999695 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0 0 1 0 0 1 0 0 1 0 -0.9396954 0.3419599 0 -0.9396954 0.3419599 0 -0.9396954 0.3419599 0 0.9999695 0.006714046 0 0.9999695 0.006714046 0 0.9999695 0.006714046 0 0.008575677 -0.999939 0 0.008575677 -0.999939 0 0.008575677 -0.999939 0 0.9371014 -0.3490096 0 0.9371014 -0.3490096 0 0.9371014 -0.3490096 -1 0 0 -0.9999695 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999695 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 0.9999695 0 0 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 -0.6099734 -0.6862392 -0.3961913 -0.6099734 0 0.7923826 -0.6099734 0 -0.7923826 0.6099734 -0.6862392 -0.3961913 0.6099734 0 -0.7923826 0.6099734 0 0.7923826 -0.6099734 -0.6862392 0.3961913 -0.6099734 0.6862392 0.3961913 -0.6099734 0.6862392 -0.3961913 0.6099734 -0.6862392 0.3961913 0.6099734 0.6862392 -0.3961913 0.6099734 0.6862392 0.3961913 -0.6099734 0 0.7923826 -0.6099734 -0.6862392 0.3961913 0.6099734 -0.6862392 0.3961913 -0.6099734 -0.6862392 -0.3961913 0.6099734 -0.6862392 -0.3961913 -0.6099734 0 -0.7923826 0.6099734 0 -0.7923826 -0.6099734 0.6862392 -0.3961913 0.6099734 0.6862392 -0.3961913 -0.6099734 0.6862392 0.3961913 0.6099734 0.6862392 0.3961913 0.6099734 0 0.7923826 0 -0.9999695 -0.006714046 0 -0.9999695 -0.006714046 0 -0.9999695 -0.006714046 0 -0.03735464 -0.9992981 0 -0.03735464 -0.9992981 0 -0.03735464 -0.9992981 0 0.9396954 0.3419599 0 0.9396954 0.3419599 0 0.9396954 0.3419599 0 0 1 0 0 1 0 0 1 0 -0.9394208 -0.3427228 0 -0.9394208 -0.3427228 0 -0.9394208 -0.3427228 -1 0 0 -1 0 0 -0.9999695 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999695 0 0 0 0 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 -0.9383526 0.3456221 0 -0.9383526 0.3456221 0 -0.9383526 0.3456221 0 0.9337443 0.3578906 0 0.9337443 0.3578906 0 0.9337443 0.3578906 1 0 0 1 0 0 1 0 0 1 0 0 0.9999695 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0.9999695 0 0 1 0 0 -1 0 0 -0.9999695 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999695 0 0 -0.9999695 0 0 0 0 1 0 0 1 0 0 1 0 0.9396954 0.3419599 0 0.9396954 0.3419599 0 0.9396954 0.3419599 0 -0.9999695 0.006714046 0 -0.9999695 0.006714046 0 -0.9999695 0.006714046 0 -0.008575677 -0.999939 0 -0.008575677 -0.999939 0 -0.008575677 -0.999939 0 -0.9371014 -0.3490096 0 -0.9371014 -0.3490096 0 -0.9371014 -0.3490096 1 0 0 1 0 0 1 0 0 1 0 0 0.9999695 0 0 0.9999695 0 0 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0.6099734 0.6862392 -0.3961913 0.6099734 0 0.7923826 0.6099734 0 -0.7923826 -0.6099734 0.6862392 -0.3961913 -0.6099734 0 -0.7923826 -0.6099734 0 0.7923826 0.6099734 0.6862392 0.3961913 0.6099734 -0.6862392 0.3961913 0.6099734 -0.6862392 -0.3961913 -0.6099734 0.6862392 0.3961913 -0.6099734 -0.6862392 -0.3961913 -0.6099734 -0.6862392 0.3961913 0.6099734 0 0.7923826 0.6099734 0.6862392 0.3961913 -0.6099734 0.6862087 0.3961913 0.6099734 0.6862392 -0.3961913 -0.6099734 0.6862392 -0.3961913 0.6099734 0 -0.7923826 -0.6099734 0 -0.7923826 0.6099734 -0.6862392 -0.3961913 -0.6099734 -0.6862392 -0.3961913 0.6099734 -0.6862392 0.3961913 -0.6099734 -0.6862392 0.3961913 -0.6099734 0 0.7923826 0 0.9999695 -0.006714046 0 0.03735464 -0.9992981 0 -0.9396954 0.3419599 0 0 1 0 0.9394208 -0.3427228 0 0 -1 0 0 1 0 0.9383526 0.3456221 0 -0.9337443 0.3578906 -1 0 0 1 0 0 0 0 1 0 -0.9396954 0.3419599 0 0.9999695 0.006714046 0 0.008575677 -0.999939 0 0.9371014 -0.3490096 0 0.9381695 -0.3461409 0 -0.9381695 0.3461409 1 0 0 -1 0 0 0 -0.3461409 -0.9381695 0 0.3461409 0.9381695 0 -0.9999695 -0.006714046 0 -0.03735464 -0.9992981 0 0.9396954 0.3419599 0 0 1 0 -0.9394208 -0.3427228 0 0 -0.9999695 0 0 1 0 -0.9383526 0.3456221 0 0.9337443 0.3578906 0.9999695 0 0 -1 0 0 0 0 1 0 0.9396954 0.3419599 0 -0.9999695 0.006714046 0 -0.008575677 -0.999939 0 -0.9371014 -0.3490096 0 -0.9381695 -0.3461409 0 0.9381695 0.3461409 -1 0 0 1 0 0 0 0.3461409 -0.9381695 0 -0.3461409 0.9381695</float_array>
          <technique_common>
            <accessor count="296" source="#Plane_001-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_001-mesh-map-0">
          <float_array count="1152" id="Plane_001-mesh-map-0-array">0.9620724 0 0.9620724 0.07658088 0.9382339 0.07658088 0.8147518 0.6690027 0.8147518 0.7211002 0.7909132 0.7211001 0.8905566 0 0.8905566 0.6570449 0.8667181 0.6570449 0.04749411 0.8884348 0.02374708 0.8884348 0.02374708 0.8629575 0.9620724 0.07658088 0.9620725 0.6433487 0.938234 0.6433487 0.8192802 0.07319265 0.8667181 0.6690027 0.8197147 0.6485647 0.8192802 0.07319265 0.866718 0.008959233 0.8667181 0.6690027 0.8436825 0 0.866718 0.008959233 0.8192802 0.07319265 0.7718424 0.6673079 0.7718423 0.9901304 0.7480039 0.9901304 0.7241653 0.9545748 0.7241653 0.6673079 0.7480038 0.6673079 0.7909132 0.8654707 0.7909132 0.8212329 0.8147518 0.8212329 0.7957178 0.7722882 0.7957178 0.7211002 0.8099471 0.7211002 0.7201356 0.6673077 0.7201356 1 0.6751295 0.9815616 0.7962447 0.6690027 0.7718424 0.59581 0.8192802 0.6600435 0.7718424 0.59581 0.8192802 0 0.8192802 0.6600435 0.7718424 0.59581 0.7722768 0.02043801 0.8192802 0 0.9117242 0.9897371 0.8667181 0.9720203 0.8667181 0.6754833 0.7004504 0 0.7234859 0.008959174 0.6751295 0.07283538 0.723486 0.6673078 0.6751295 0.07283538 0.7234859 0.008959174 0.02374708 0.8884348 0 0.8884348 0 0.8629575 0.8905566 0.6553577 0.8905566 0 0.9143952 0 0.9143952 0.07639729 0.9143952 0 0.9382337 0 0.8147518 0.7722881 0.8147518 0.8212329 0.7909132 0.8212329 0.9143953 0.6438876 0.9143952 0.07639729 0.9382337 0.07639729 0.723486 0.5944724 0.727535 0.01769453 0.7718424 0 0.7718424 0 0.7718424 0.6583486 0.723486 0.5944724 0.7488069 0.6673079 0.723486 0.5944724 0.7718424 0.6583486 0.6751295 0.07283538 0.723486 0.6673078 0.6791787 0.6496133 0.6705136 0.299829 0.5809183 0.299829 0.5809183 0.001027286 0.5763021 0 0.5763024 0.8629574 0.4764097 0.8629575 0.8242872 0.7211002 0.8242872 0.8283942 0.8147518 0.8283942 0.8242872 0.7211002 0.8338226 0.7211002 0.8338226 0.8283942 0.9905215 0.8629573 0.9905214 0 0.9999999 0 0.9620725 0.8629574 0.9620724 0 0.9715575 0 0.1406888 0.8698984 0.1354221 0.8796476 0.1354221 0.8666486 0.05372112 0.8735895 0.05898779 0.8703398 0.05898779 0.8833388 0.1406888 0.8763979 0.1354221 0.8796476 0.1406888 0.8698984 0.1354221 0.8796476 0.1301555 0.8763979 0.1301555 0.8698984 0.1354221 0.8796476 0.1301555 0.8698984 0.1354221 0.8666486 0.05372112 0.8800891 0.05372112 0.8735895 0.05898779 0.8833388 0.05898779 0.8833388 0.06425446 0.8735895 0.06425446 0.8800891 0.05898779 0.8833388 0.05898779 0.8703398 0.06425446 0.8735895 0.8351935 0.8283942 0.8395412 0.8283942 0.8395413 0.8944873 0.8500136 0.7211002 0.8559004 0.7211002 0.8559004 0.7871932 0.8191271 0.8283942 0.8249726 0.8283942 0.8249727 0.8944873 0.8147518 0.8944873 0.8147518 0.8283942 0.8191271 0.8283942 0.8453976 0.8283942 0.8513844 0.8283942 0.8513844 0.8944873 0.8395412 0.8283942 0.8453976 0.8283942 0.8453976 0.8944873 0.8395412 0.8283942 0.8453976 0.8283942 0.8453976 0.8944874 0.8453976 0.8283942 0.8513844 0.8283942 0.8513844 0.8944874 0.8147518 0.8283942 0.8191271 0.8283942 0.8191271 0.8944874 0.8191271 0.8283942 0.8249726 0.8283942 0.8249727 0.8944874 0.8500136 0.7211001 0.8559004 0.7211001 0.8559004 0.7871933 0.8351935 0.8283942 0.8395412 0.8283942 0.8395413 0.8944874 0.318535 0.8936613 0.318535 0.8806622 0.3238017 0.883912 0.318535 0.8936613 0.3238017 0.883912 0.3238017 0.8904115 0.3132684 0.8904115 0.3132684 0.883912 0.318535 0.8936613 0.3949694 0.8899701 0.3897027 0.8802208 0.3949694 0.8769711 0.3949694 0.8899701 0.3897027 0.8867203 0.3897027 0.8802208 0.400236 0.8867203 0.3949694 0.8899701 0.400236 0.8802208 0.3132684 0.883912 0.318535 0.8806622 0.318535 0.8936613 0.400236 0.8802208 0.3949694 0.8899701 0.3949694 0.8769711 0.9620724 0 0.9620724 0.07658088 0.9382339 0.07658088 0.8147518 0.6690027 0.8147518 0.7211002 0.7909132 0.7211001 0.8905566 0 0.8905566 0.6570449 0.8667181 0.6570449 0.04749411 0.8884348 0.02374708 0.8884348 0.02374708 0.8629575 0.9620724 0.07658088 0.9620725 0.6433487 0.938234 0.6433487 0.8192802 0.07319265 0.8667181 0.6690027 0.8197147 0.6485647 0.8192802 0.07319265 0.866718 0.008959233 0.8667181 0.6690027 0.8436825 0 0.866718 0.008959233 0.8192802 0.07319265 0.7480039 0.6673079 0.7718424 0.6673079 0.7718423 0.9901304 0.7241653 0.9545748 0.7241653 0.6673079 0.7480038 0.6673079 0.7909132 0.8654707 0.7909132 0.8212329 0.8147518 0.8212329 0.7957178 0.7722882 0.7957178 0.7211002 0.8099471 0.7211002 0.7201356 0.6673077 0.7201356 1 0.6751295 0.9815616 0.7962447 0.6690027 0.7718424 0.59581 0.8192802 0.6600435 0.7718424 0.59581 0.8192802 0 0.8192802 0.6600435 0.7718424 0.59581 0.7722768 0.02043801 0.8192802 0 0.9117242 0.9897371 0.8667181 0.9720203 0.8667181 0.6754833 0.7004504 0 0.7234859 0.008959174 0.6751295 0.07283538 0.723486 0.6673078 0.6751295 0.07283538 0.7234859 0.008959174 0.02374708 0.8884348 0 0.8884348 0 0.8629575 0.8905566 0.6553577 0.8905566 0 0.9143952 0 0.9143952 0.07639729 0.9143952 0 0.9382337 0 0.8147518 0.7722881 0.8147518 0.8212329 0.7909132 0.8212329 0.9143953 0.6438876 0.9143952 0.07639729 0.9382337 0.07639729 0.723486 0.5944724 0.727535 0.01769453 0.7718424 0 0.7718424 0 0.7718424 0.6583486 0.723486 0.5944724 0.7488069 0.6673079 0.723486 0.5944724 0.7718424 0.6583486 0.6751295 0.07283538 0.723486 0.6673078 0.6791787 0.6496133 0.6705136 0.299829 0.5809183 0.299829 0.5809183 0.001027286 0.5763021 0 0.5763024 0.8629574 0.4764097 0.8629575 0.8242872 0.7211002 0.8242872 0.8283942 0.8147518 0.8283942 0.8242872 0.7211002 0.8338226 0.7211002 0.8338226 0.8283942 0.9905215 0.8629573 0.9905214 0 0.9999999 0 0.9620725 0.8629574 0.9620724 0 0.9715575 0 0.1406888 0.8698984 0.1354221 0.8796476 0.1354221 0.8666486 0.05372112 0.8735895 0.05898779 0.8703398 0.05898779 0.8833388 0.1406888 0.8763979 0.1354221 0.8796476 0.1406888 0.8698984 0.1354221 0.8796476 0.1301555 0.8763979 0.1301555 0.8698984 0.1354221 0.8796476 0.1301555 0.8698984 0.1354221 0.8666486 0.05372112 0.8800891 0.05372112 0.8735895 0.05898779 0.8833388 0.05898779 0.8833388 0.06425446 0.8735895 0.06425446 0.8800891 0.05898779 0.8833388 0.05898779 0.8703398 0.06425446 0.8735895 0.8351935 0.8283942 0.8395412 0.8283942 0.8395413 0.8944873 0.8500136 0.7211002 0.8559004 0.7211002 0.8559004 0.7871932 0.8191271 0.8283942 0.8249726 0.8283942 0.8249727 0.8944873 0.8147518 0.8944873 0.8147518 0.8283942 0.8191271 0.8283942 0.8453976 0.8283942 0.8513844 0.8283942 0.8513844 0.8944873 0.8395412 0.8283942 0.8453976 0.8283942 0.8453976 0.8944873 0.8395412 0.8283942 0.8453976 0.8283942 0.8453976 0.8944874 0.8453976 0.8283942 0.8513844 0.8283942 0.8513844 0.8944874 0.8147518 0.8283942 0.8191271 0.8283942 0.8191271 0.8944874 0.8191271 0.8283942 0.8249726 0.8283942 0.8249727 0.8944874 0.8500136 0.7211001 0.8559004 0.7211001 0.8559004 0.7871933 0.8351935 0.8283942 0.8395412 0.8283942 0.8395413 0.8944874 0.318535 0.8936613 0.318535 0.8806622 0.3238017 0.883912 0.318535 0.8936613 0.3238017 0.883912 0.3238017 0.8904115 0.3132684 0.8904115 0.3132684 0.883912 0.318535 0.8936613 0.3949694 0.8899701 0.3897027 0.8802208 0.3949694 0.8769711 0.3949694 0.8899701 0.3897027 0.8867203 0.3897027 0.8802208 0.400236 0.8867203 0.3949694 0.8899701 0.400236 0.8802208 0.3132684 0.883912 0.318535 0.8806622 0.318535 0.8936613 0.400236 0.8802208 0.3949694 0.8899701 0.3949694 0.8769711 0.9382339 0 0.9620724 0 0.9382339 0.07658088 0.7909132 0.6690027 0.8147518 0.6690027 0.7909132 0.7211001 0.8667181 0 0.8905566 0 0.8667181 0.6570449 0.04749411 0.8629574 0.04749411 0.8884348 0.02374708 0.8629575 0.9382339 0.07658088 0.9620724 0.07658088 0.938234 0.6433487 0.7480039 0.6673079 0.7718424 0.6673079 0.7480039 0.9901304 0.7480039 0.9545748 0.7241653 0.9545748 0.7480038 0.6673079 0.8147518 0.8654707 0.7909132 0.8654707 0.8147518 0.8212329 0.8099471 0.7722882 0.7957178 0.7722882 0.8099471 0.7211002 0.6751295 0.6850246 0.7201356 0.6673077 0.6751295 0.9815616 0.9117241 0.6570449 0.9117242 0.9897371 0.8667181 0.6754833 0.02374708 0.8629574 0.02374708 0.8884348 0 0.8629575 0.9143952 0.6553577 0.8905566 0.6553577 0.9143952 0 0.9382337 0.07639729 0.9143952 0.07639729 0.9382337 0 0.7909132 0.7722882 0.8147518 0.7722881 0.7909132 0.8212329 0.9382339 0.6438876 0.9143953 0.6438876 0.9382337 0.07639729 0.6705136 0.001027286 0.6705136 0.299829 0.5809183 0.001027286 0.4764095 0 0.5763021 0 0.4764097 0.8629575 0.8147518 0.7211002 0.8242872 0.7211002 0.8147518 0.8283942 0.8242872 0.8283942 0.8242872 0.7211002 0.8338226 0.8283942 1 0.8629573 0.9905215 0.8629573 0.9999999 0 0.9715576 0.8629574 0.9620725 0.8629574 0.9715575 0 0.8351935 0.8944873 0.8351935 0.8283942 0.8395413 0.8944873 0.8500136 0.7871932 0.8500136 0.7211002 0.8559004 0.7871932 0.8191271 0.8944873 0.8191271 0.8283942 0.8249727 0.8944873 0.8191271 0.8944873 0.8147518 0.8944873 0.8191271 0.8283942 0.8453976 0.8944873 0.8453976 0.8283942 0.8513844 0.8944873 0.8395413 0.8944873 0.8395412 0.8283942 0.8453976 0.8944873 0.8395413 0.8944874 0.8395412 0.8283942 0.8453976 0.8944874 0.8453976 0.8944874 0.8453976 0.8283942 0.8513844 0.8944874 0.8147518 0.8944874 0.8147518 0.8283942 0.8191271 0.8944874 0.8191271 0.8944874 0.8191271 0.8283942 0.8249727 0.8944874 0.8500136 0.7871933 0.8500136 0.7211001 0.8559004 0.7871933 0.8351935 0.8944874 0.8351935 0.8283942 0.8395413 0.8944874 0.9382339 0 0.9620724 0 0.9382339 0.07658088 0.7909132 0.6690027 0.8147518 0.6690027 0.7909132 0.7211001 0.8667181 0 0.8905566 0 0.8667181 0.6570449 0.04749411 0.8629574 0.04749411 0.8884348 0.02374708 0.8629575 0.9382339 0.07658088 0.9620724 0.07658088 0.938234 0.6433487 0.7480039 0.9901304 0.7480039 0.6673079 0.7718423 0.9901304 0.7480039 0.9545748 0.7241653 0.9545748 0.7480038 0.6673079 0.8147518 0.8654707 0.7909132 0.8654707 0.8147518 0.8212329 0.8099471 0.7722882 0.7957178 0.7722882 0.8099471 0.7211002 0.6751295 0.6850246 0.7201356 0.6673077 0.6751295 0.9815616 0.9117241 0.6570449 0.9117242 0.9897371 0.8667181 0.6754833 0.02374708 0.8629574 0.02374708 0.8884348 0 0.8629575 0.9143952 0.6553577 0.8905566 0.6553577 0.9143952 0 0.9382337 0.07639729 0.9143952 0.07639729 0.9382337 0 0.7909132 0.7722882 0.8147518 0.7722881 0.7909132 0.8212329 0.9382339 0.6438876 0.9143953 0.6438876 0.9382337 0.07639729 0.6705136 0.001027286 0.6705136 0.299829 0.5809183 0.001027286 0.4764095 0 0.5763021 0 0.4764097 0.8629575 0.8147518 0.7211002 0.8242872 0.7211002 0.8147518 0.8283942 0.8242872 0.8283942 0.8242872 0.7211002 0.8338226 0.8283942 1 0.8629573 0.9905215 0.8629573 0.9999999 0 0.9715576 0.8629574 0.9620725 0.8629574 0.9715575 0 0.8351935 0.8944873 0.8351935 0.8283942 0.8395413 0.8944873 0.8500136 0.7871932 0.8500136 0.7211002 0.8559004 0.7871932 0.8191271 0.8944873 0.8191271 0.8283942 0.8249727 0.8944873 0.8191271 0.8944873 0.8147518 0.8944873 0.8191271 0.8283942 0.8453976 0.8944873 0.8453976 0.8283942 0.8513844 0.8944873 0.8395413 0.8944873 0.8395412 0.8283942 0.8453976 0.8944873 0.8395413 0.8944874 0.8395412 0.8283942 0.8453976 0.8944874 0.8453976 0.8944874 0.8453976 0.8283942 0.8513844 0.8944874 0.8147518 0.8944874 0.8147518 0.8283942 0.8191271 0.8944874 0.8191271 0.8944874 0.8191271 0.8283942 0.8249727 0.8944874 0.8500136 0.7871933 0.8500136 0.7211001 0.8559004 0.7871933 0.8351935 0.8944874 0.8351935 0.8283942 0.8395413 0.8944874</float_array>
          <technique_common>
            <accessor count="576" source="#Plane_001-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane_001-mesh-vertices">
          <input semantic="POSITION" source="#Plane_001-mesh-positions" />
        </vertices>
        <polylist count="192" material="sawhorse-material">
          <input offset="0" semantic="VERTEX" source="#Plane_001-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Plane_001-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Plane_001-mesh-map-0" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>1 0 0 4 1 1 126 2 2 8 3 3 2 4 4 6 5 5 120 6 6 9 7 7 0 8 8 135 9 9 132 10 10 130 11 11 144 12 12 142 13 13 140 14 14 146 15 15 121 16 16 143 17 17 145 18 18 134 19 19 122 20 20 133 21 21 136 22 22 147 23 23 14 24 24 16 25 25 10 26 26 11 27 27 13 28 28 15 29 29 156 30 30 150 31 31 148 32 32 160 33 33 158 34 34 152 35 35 157 36 36 159 37 37 161 38 38 131 39 39 127 40 40 138 41 41 5 42 42 123 43 43 139 44 44 129 45 45 141 46 46 125 47 47 155 48 48 149 49 49 163 50 50 22 51 51 19 52 52 169 53 53 21 54 54 20 55 55 165 56 56 23 57 57 26 58 58 164 59 59 176 60 60 24 61 61 173 62 62 25 63 63 179 64 64 168 65 65 181 66 66 18 67 67 27 68 68 188 69 69 184 70 70 171 71 71 186 72 72 189 73 73 182 74 74 183 75 75 177 76 76 185 77 77 180 78 78 187 79 79 178 80 80 172 81 81 175 82 82 191 83 83 29 84 84 31 85 85 30 86 86 34 87 87 35 88 88 33 89 89 202 90 90 206 91 91 198 92 92 196 93 93 204 94 94 200 95 95 207 96 96 205 97 97 197 98 98 201 99 99 203 100 100 195 101 101 40 102 102 36 103 103 39 104 104 46 105 105 45 106 106 42 107 107 41 108 108 36 103 109 40 102 110 36 103 111 37 109 112 38 110 113 36 103 114 38 110 115 39 104 116 47 111 117 46 105 118 42 107 119 42 107 120 44 112 121 43 113 122 42 107 123 45 106 124 44 112 125 47 111 126 42 107 127 36 103 128 46 105 129 47 111 130 41 108 131 45 106 132 46 105 133 40 102 134 38 110 135 44 112 136 45 106 137 43 113 138 44 112 139 38 110 140 42 107 141 43 113 142 37 109 143 53 114 144 52 115 145 58 116 146 52 115 147 51 117 148 57 118 149 51 117 150 50 119 151 56 120 152 50 119 153 49 121 154 55 122 155 49 121 156 48 123 157 54 124 158 48 123 159 53 114 160 59 125 161 53 114 162 50 119 163 51 117 164 53 114 165 51 117 166 52 115 167 48 123 168 49 121 169 53 114 170 59 125 171 57 118 172 56 120 173 59 125 174 58 116 175 57 118 176 54 124 177 59 125 178 55 122 179 49 121 180 50 119 181 53 114 182 55 122 183 59 125 184 56 120 185 61 126 186 64 127 187 214 128 188 68 129 189 62 130 190 66 131 191 208 132 192 69 133 193 60 134 194 223 135 195 220 136 196 218 137 197 232 138 198 230 139 199 228 140 200 234 141 201 209 142 202 231 143 203 233 144 204 222 145 205 210 146 206 221 147 207 224 148 208 235 149 209 72 150 210 74 151 211 76 152 212 71 153 213 73 154 214 75 155 215 244 156 216 238 157 217 236 158 218 248 159 219 246 160 220 240 161 221 245 162 222 247 163 223 249 164 224 219 165 225 215 166 226 226 167 227 65 168 228 211 169 229 227 170 230 217 171 231 229 172 232 213 173 233 243 174 234 237 175 235 251 176 236 82 177 237 79 178 238 257 179 239 81 180 240 80 181 241 253 182 242 83 183 243 86 184 244 252 185 245 264 186 246 84 187 247 261 188 248 85 189 249 267 190 250 256 191 251 269 192 252 78 193 253 87 194 254 276 195 255 272 196 256 259 197 257 274 198 258 277 199 259 270 200 260 271 201 261 265 202 262 273 203 263 268 204 264 275 205 265 266 206 266 260 207 267 263 208 268 279 209 269 89 210 270 91 211 271 90 212 272 94 213 273 95 214 274 93 215 275 290 216 276 294 217 277 286 218 278 284 219 279 292 220 280 288 221 281 295 222 282 293 223 283 285 224 284 289 225 285 291 226 286 283 227 287 100 228 288 96 229 289 99 230 290 106 231 291 105 232 292 102 233 293 101 234 294 96 229 295 100 228 296 96 229 297 97 235 298 98 236 299 96 229 300 98 236 301 99 230 302 107 237 303 106 231 304 102 233 305 102 233 306 104 238 307 103 239 308 102 233 309 105 232 310 104 238 311 107 237 312 102 233 313 96 229 314 106 231 315 107 237 316 101 234 317 105 232 318 106 231 319 100 228 320 98 236 321 104 238 322 105 232 323 103 239 324 104 238 325 98 236 326 102 233 327 103 239 328 97 235 329 113 240 330 112 241 331 118 242 332 112 241 333 111 243 334 117 244 335 111 243 336 110 245 337 116 246 338 110 245 339 109 247 340 115 248 341 109 247 342 108 249 343 114 250 344 108 249 345 113 240 346 119 251 347 113 240 348 110 245 349 111 243 350 113 240 351 111 243 352 112 241 353 108 249 354 109 247 355 113 240 356 119 251 357 117 244 358 116 246 359 119 251 360 118 242 361 117 244 362 114 250 363 119 251 364 115 248 365 109 247 366 110 245 367 113 240 368 115 248 369 119 251 370 116 246 371 7 252 372 1 0 373 126 2 374 3 253 375 8 3 376 6 5 377 124 254 378 120 6 379 0 8 380 137 255 381 135 9 382 130 11 383 128 256 384 144 12 385 140 14 386 12 257 387 14 24 388 10 26 389 17 258 390 11 27 391 15 29 392 154 259 393 156 30 394 148 32 395 162 260 396 160 33 397 152 35 398 151 261 399 157 36 400 161 38 401 153 262 402 155 48 403 163 50 404 167 263 405 23 57 406 164 59 407 166 264 408 176 60 409 173 62 410 170 265 411 25 63 412 168 65 413 174 266 414 181 66 415 27 68 416 190 267 417 188 69 418 171 71 419 28 268 420 29 84 421 30 86 422 32 269 423 34 87 424 33 89 425 194 270 426 202 90 427 198 92 428 192 271 429 196 93 430 200 95 431 199 272 432 207 96 433 197 98 434 193 273 435 201 99 436 195 101 437 41 108 438 47 111 439 36 103 440 40 102 441 46 105 442 41 108 443 39 104 444 45 106 445 40 102 446 39 104 447 38 110 448 45 106 449 37 109 450 43 113 451 38 110 452 36 103 453 42 107 454 37 109 455 59 125 456 53 114 457 58 116 458 58 116 459 52 115 460 57 118 461 57 118 462 51 117 463 56 120 464 56 120 465 50 119 466 55 122 467 55 122 468 49 121 469 54 124 470 54 124 471 48 123 472 59 125 473 67 274 474 61 126 475 214 128 476 63 275 477 68 129 478 66 131 479 212 276 480 208 132 481 60 134 482 225 277 483 223 135 484 218 137 485 216 278 486 232 138 487 228 140 488 70 279 489 72 150 490 76 152 491 77 280 492 71 153 493 75 155 494 242 281 495 244 156 496 236 158 497 250 282 498 248 159 499 240 161 500 239 283 501 245 162 502 249 164 503 241 284 504 243 174 505 251 176 506 255 285 507 83 183 508 252 185 509 254 286 510 264 186 511 261 188 512 258 287 513 85 189 514 256 191 515 262 288 516 269 192 517 87 194 518 278 289 519 276 195 520 259 197 521 88 290 522 89 210 523 90 212 524 92 291 525 94 213 526 93 215 527 282 292 528 290 216 529 286 218 530 280 293 531 284 219 532 288 221 533 287 294 534 295 222 535 285 224 536 281 295 537 289 225 538 283 227 539 101 234 540 107 237 541 96 229 542 100 228 543 106 231 544 101 234 545 99 230 546 105 232 547 100 228 548 99 230 549 98 236 550 105 232 551 97 235 552 103 239 553 98 236 554 96 229 555 102 233 556 97 235 557 119 251 558 113 240 559 118 242 560 118 242 561 112 241 562 117 244 563 117 244 564 111 243 565 116 246 566 116 246 567 110 245 568 115 248 569 115 248 570 109 247 571 114 250 572 114 250 573 108 249 574 119 251 575</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Plane_009-mesh" name="Plane.009">
      <mesh>
        <source id="Plane_009-mesh-positions">
          <float_array count="192" id="Plane_009-mesh-positions-array">-0.8058173 -0.03893828 0.9852196 0.7699329 -0.03893816 0.9852196 -0.8058171 -0.180463 0.6016624 0.7699329 -0.1804629 0.6016624 -0.8058173 -0.0541343 0.9908267 0.7699329 -0.05413419 0.9908267 -0.8058171 -0.195659 0.6072695 0.7699329 -0.1956589 0.6072695 -0.005699336 -0.03893822 0.9852196 -0.005699336 -0.180463 0.6016624 -0.005699336 -0.05413424 0.9908267 -0.005699336 -0.195659 0.6072695 -0.8058173 0.04719918 0.9852196 0.7699329 0.0471993 0.9852196 -0.8058171 0.1887239 0.6016624 0.7699329 0.1887241 0.6016624 -0.8058173 0.06239515 0.9908267 0.7699329 0.06239527 0.9908267 -0.8058171 0.2039199 0.6072695 0.7699329 0.20392 0.6072695 -0.005699336 0.04719924 0.9852196 -0.005699336 0.188724 0.6016624 -0.005699336 0.06239521 0.9908267 -0.005699336 0.20392 0.6072695 -0.005699336 -0.03893822 0.9852196 0.7699329 -0.03893816 0.9852196 0.7699329 -0.03893816 0.9852196 -0.005699336 -0.180463 0.6016624 0.7699329 -0.1804629 0.6016624 0.7699329 -0.1804629 0.6016624 -0.8058171 -0.180463 0.6016624 -0.8058171 -0.180463 0.6016624 -0.8058173 -0.03893828 0.9852196 -0.8058173 -0.03893828 0.9852196 -0.005699336 -0.05413424 0.9908267 0.7699329 -0.05413419 0.9908267 0.7699329 -0.05413419 0.9908267 -0.005699336 -0.195659 0.6072695 0.7699329 -0.1956589 0.6072695 0.7699329 -0.1956589 0.6072695 -0.8058171 -0.195659 0.6072695 -0.8058171 -0.195659 0.6072695 -0.8058173 -0.0541343 0.9908267 -0.8058173 -0.0541343 0.9908267 -0.005699336 0.04719924 0.9852196 0.7699329 0.0471993 0.9852196 0.7699329 0.0471993 0.9852196 -0.005699336 0.188724 0.6016624 0.7699329 0.1887241 0.6016624 0.7699329 0.1887241 0.6016624 -0.8058171 0.1887239 0.6016624 -0.8058171 0.1887239 0.6016624 -0.8058173 0.04719918 0.9852196 -0.8058173 0.04719918 0.9852196 -0.005699336 0.06239521 0.9908267 0.7699329 0.06239527 0.9908267 0.7699329 0.06239527 0.9908267 -0.005699336 0.20392 0.6072695 0.7699329 0.20392 0.6072695 0.7699329 0.20392 0.6072695 -0.8058171 0.2039199 0.6072695 -0.8058171 0.2039199 0.6072695 -0.8058173 0.06239515 0.9908267 -0.8058173 0.06239515 0.9908267</float_array>
          <technique_common>
            <accessor count="64" source="#Plane_009-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_009-mesh-normals">
          <float_array count="192" id="Plane_009-mesh-normals-array">0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 -0.9381695 -0.3461409 1 0 0 -1 0 0 0 0.3461409 0.9381695 0 -0.3461409 -0.9381695 0 -0.9381695 0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 -0.3461409 1 0 0 -1 0 0 0 -0.3461409 0.9381695 0 0.3461409 -0.9381695 0 0.9381695 0.3461409 0 -0.9381695 -0.3461409</float_array>
          <technique_common>
            <accessor count="64" source="#Plane_009-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_009-mesh-map-0">
          <float_array count="240" id="Plane_009-mesh-map-0-array">0.6703416 0.8602694 0.5829191 0.8602694 0.5829191 0.583398 0.2394819 0.00157988 0.2394821 0.4330586 2.53889e-7 0.4330586 0.7718424 0.6690027 0.7813778 0.6690028 0.7813778 0.9262285 0.7813778 0.9262285 0.7813778 0.6690027 0.7909132 0.6690027 0.9810428 0.4314786 0.9810427 0 0.9905212 0 0.9715576 0.4314787 0.9715576 0 0.9810426 0 0.9715576 0.8629574 0.9715576 0.4314787 0.9810426 0.4314787 0.9810429 0.8629573 0.9810428 0.4314786 0.9905213 0.4314786 0.2394818 0 0.2394819 0.4314787 1.26945e-7 0.4314787 0.6703416 0.583398 0.5829191 0.583398 0.5829191 0.3065266 0.6703416 0.583398 0.5829191 0.583398 0.5829191 0.8602694 2.5332e-7 0 0.2394821 0 0.2394819 0.4314787 0.7718424 0.6690027 0.7718424 0.9262286 0.7813778 0.9262285 0.7909132 0.6690027 0.7813778 0.6690027 0.7813778 0.9262285 0.9905212 0 0.9810427 0 0.9810428 0.4314786 0.9810426 0.4314787 0.9810426 0 0.9715576 0 0.9810427 0.8629574 0.9810426 0.4314787 0.9715576 0.4314787 0.9905213 0.4314786 0.9810428 0.4314786 0.9810429 0.8629573 1.2666e-7 0.00157988 0.2394819 0.00157994 0.2394818 0.4330586 0.5829191 0.3065266 0.5829191 0.583398 0.6703416 0.583398 0.6703416 0.583398 0.6703416 0.8602694 0.5829191 0.583398 1.26945e-7 0.00157994 0.2394819 0.00157988 2.53889e-7 0.4330586 0.7718424 0.9262286 0.7718424 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7813778 0.9262285 0.7909132 0.6690027 0.9905213 0.4314786 0.9810428 0.4314786 0.9905212 0 0.9810426 0.4314787 0.9715576 0.4314787 0.9810426 0 0.9810427 0.8629574 0.9715576 0.8629574 0.9810426 0.4314787 0.9905214 0.8629573 0.9810429 0.8629573 0.9905213 0.4314786 0 0 0.2394818 0 1.26945e-7 0.4314787 0.6703416 0.3065265 0.6703416 0.583398 0.5829191 0.3065266 0.6703416 0.8602694 0.6703416 0.583398 0.5829191 0.8602694 1.2666e-7 0.4314787 2.5332e-7 0 0.2394819 0.4314787 0.7813778 0.6690028 0.7718424 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7909132 0.6690027 0.7813778 0.9262285 0.9905213 0.4314786 0.9905212 0 0.9810428 0.4314786 0.9715576 0.4314787 0.9810426 0.4314787 0.9715576 0 0.9715576 0.8629574 0.9810427 0.8629574 0.9715576 0.4314787 0.9905214 0.8629573 0.9905213 0.4314786 0.9810429 0.8629573 0 0.4330586 1.2666e-7 0.00157988 0.2394818 0.4330586 0.6703416 0.3065265 0.5829191 0.3065266 0.6703416 0.583398</float_array>
          <technique_common>
            <accessor count="120" source="#Plane_009-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane_009-mesh-vertices">
          <input semantic="POSITION" source="#Plane_009-mesh-positions" />
        </vertices>
        <polylist count="40" material="sawhorse-material">
          <input offset="0" semantic="VERTEX" source="#Plane_009-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Plane_009-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Plane_009-mesh-map-0" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>1 0 0 3 1 1 9 2 2 11 3 3 7 4 4 5 5 5 25 6 6 35 7 7 38 8 8 6 9 9 4 10 10 0 11 11 37 12 12 40 13 13 30 14 14 34 15 15 36 16 16 26 17 17 42 18 18 34 15 19 24 19 20 39 20 21 37 12 22 27 21 23 41 22 24 11 3 25 10 23 26 8 24 27 9 2 28 31 25 29 20 26 30 21 27 31 15 28 32 17 29 33 19 30 34 23 31 35 45 32 36 48 33 37 58 34 38 12 35 39 16 36 40 18 37 41 50 38 42 60 39 43 57 40 44 44 41 45 46 42 46 56 43 47 52 44 48 44 41 49 54 45 50 47 46 51 57 40 52 59 47 53 22 48 54 23 31 55 61 49 56 51 50 57 21 27 58 20 26 59 8 24 60 1 0 61 9 2 62 10 23 63 11 3 64 5 5 65 28 51 66 25 6 67 38 8 68 2 52 69 6 9 70 0 11 71 27 21 72 37 12 73 30 14 74 24 19 75 34 15 76 26 17 77 32 53 78 42 18 79 24 19 80 29 54 81 39 20 82 27 21 83 43 55 84 41 22 85 10 23 86 33 56 87 8 24 88 31 25 89 13 57 90 20 26 91 15 28 92 22 48 93 17 29 94 23 31 95 55 58 96 45 32 97 58 34 98 14 59 99 12 35 100 18 37 101 47 46 102 50 38 103 57 40 104 54 45 105 44 41 106 56 43 107 62 60 108 52 44 109 54 45 110 49 61 111 47 46 112 59 47 113 63 62 114 22 48 115 61 49 116 53 63 117 51 50 118 20 26 119</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Plane_010-mesh" name="Plane.010">
      <mesh>
        <source id="Plane_010-mesh-positions">
          <float_array count="192" id="Plane_010-mesh-positions-array">-0.8058173 -0.03893828 0.9852196 0.7715585 -0.03893816 0.9852196 -0.8058171 -0.180463 0.6016624 0.7715585 -0.1804629 0.6016624 -0.8058173 -0.0541343 0.9908267 0.7715585 -0.05413419 0.9908267 -0.8058171 -0.195659 0.6072695 0.7715585 -0.1956589 0.6072695 -0.001126289 -0.03893822 0.9852196 -0.001126229 -0.180463 0.6016624 -0.001126229 -0.05413424 0.9908267 -0.001126229 -0.195659 0.6072695 -0.8058173 0.04719918 0.9852196 0.7715585 0.0471993 0.9852196 -0.8058171 0.1887239 0.6016624 0.7715585 0.1887241 0.6016624 -0.8058173 0.06239515 0.9908267 0.7715585 0.06239527 0.9908267 -0.8058171 0.2039199 0.6072695 0.7715585 0.20392 0.6072695 -0.001126289 0.04719924 0.9852196 -0.001126289 0.188724 0.6016624 -0.001126289 0.06239521 0.9908267 -0.001126289 0.20392 0.6072695 -0.001126289 -0.03893822 0.9852196 0.7715585 -0.03893816 0.9852196 0.7715585 -0.03893816 0.9852196 -0.001126229 -0.180463 0.6016624 0.7715585 -0.1804629 0.6016624 0.7715585 -0.1804629 0.6016624 -0.8058171 -0.180463 0.6016624 -0.8058171 -0.180463 0.6016624 -0.8058173 -0.03893828 0.9852196 -0.8058173 -0.03893828 0.9852196 -0.001126229 -0.05413424 0.9908267 0.7715585 -0.05413419 0.9908267 0.7715585 -0.05413419 0.9908267 -0.001126229 -0.195659 0.6072695 0.7715585 -0.1956589 0.6072695 0.7715585 -0.1956589 0.6072695 -0.8058171 -0.195659 0.6072695 -0.8058171 -0.195659 0.6072695 -0.8058173 -0.0541343 0.9908267 -0.8058173 -0.0541343 0.9908267 -0.001126289 0.04719924 0.9852196 0.7715585 0.0471993 0.9852196 0.7715585 0.0471993 0.9852196 -0.001126289 0.188724 0.6016624 0.7715585 0.1887241 0.6016624 0.7715585 0.1887241 0.6016624 -0.8058171 0.1887239 0.6016624 -0.8058171 0.1887239 0.6016624 -0.8058173 0.04719918 0.9852196 -0.8058173 0.04719918 0.9852196 -0.001126289 0.06239521 0.9908267 0.7715585 0.06239527 0.9908267 0.7715585 0.06239527 0.9908267 -0.001126289 0.20392 0.6072695 0.7715585 0.20392 0.6072695 0.7715585 0.20392 0.6072695 -0.8058171 0.2039199 0.6072695 -0.8058171 0.2039199 0.6072695 -0.8058173 0.06239515 0.9908267 -0.8058173 0.06239515 0.9908267</float_array>
          <technique_common>
            <accessor count="64" source="#Plane_010-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_010-mesh-normals">
          <float_array count="192" id="Plane_010-mesh-normals-array">0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 1 0 0 -1 0 0 0 0.3461409 0.9381695 0 -0.3461409 -0.9381695 0 -0.9381695 0.3461409 0 0.9381695 -0.3461409 1 0 0 -1 0 0 0 -0.3461409 0.9381695 0 0.3461409 -0.9381695 0 0.9381695 0.3461409 0 -0.9381695 -0.3461409</float_array>
          <technique_common>
            <accessor count="64" source="#Plane_010-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_010-mesh-map-0">
          <float_array count="240" id="Plane_010-mesh-map-0-array">0.6703416 0.8602694 0.5829191 0.8602694 0.5829191 0.583398 0.2394819 0.4314787 0.2394821 0 2.5332e-7 0 0.7718424 0.6690027 0.7813778 0.6690028 0.7813778 0.9262285 0.7813778 0.9262285 0.7813778 0.6690027 0.7909132 0.6690027 0.9810428 0.4314786 0.9810427 0 0.9905212 0 0.9715576 0.4314787 0.9715576 0 0.9810426 0 0.9715576 0.8629574 0.9715576 0.4314787 0.9810426 0.4314787 0.9810429 0.8629573 0.9810428 0.4314786 0.9905213 0.4314786 0.2394818 0.4330586 0.2394819 0.00157994 1.2666e-7 0.00157988 0.6703416 0.583398 0.5829191 0.583398 0.5829191 0.3065266 0.5829191 0.583398 0.5829191 0.8602694 0.6703416 0.8602694 2.5332e-7 0.4330586 0.2394821 0.4330585 0.2394819 0.001579821 0.7718424 0.6690027 0.7718424 0.9262286 0.7813778 0.9262285 0.7909132 0.6690027 0.7813778 0.6690027 0.7813778 0.9262285 0.9905212 0 0.9810427 0 0.9810428 0.4314786 0.9810426 0 0.9715576 0 0.9715576 0.4314787 0.9810426 0.4314787 0.9715576 0.4314787 0.9715576 0.8629574 0.9905213 0.4314786 0.9810428 0.4314786 0.9810429 0.8629573 1.2666e-7 0.4314787 0.2394819 0.4314786 0.2394818 0 0.5829191 0.3065266 0.5829191 0.583398 0.6703416 0.583398 0.6703416 0.583398 0.6703416 0.8602694 0.5829191 0.583398 1.2666e-7 0.4314787 0.2394819 0.4314787 2.5332e-7 0 0.7718424 0.9262286 0.7718424 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7813778 0.9262285 0.7909132 0.6690027 0.9905213 0.4314786 0.9810428 0.4314786 0.9905212 0 0.9810426 0.4314787 0.9715576 0.4314787 0.9810426 0 0.9810427 0.8629574 0.9715576 0.8629574 0.9810426 0.4314787 0.9905214 0.8629573 0.9810429 0.8629573 0.9905213 0.4314786 0 0.4330586 0.2394818 0.4330586 1.2666e-7 0.00157988 0.6703416 0.3065265 0.6703416 0.583398 0.5829191 0.3065266 0.6703416 0.583398 0.5829191 0.583398 0.6703416 0.8602694 1.2666e-7 0.00157988 2.5332e-7 0.4330586 0.2394819 0.001579821 0.7813778 0.6690028 0.7718424 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7909132 0.6690027 0.7813778 0.9262285 0.9905213 0.4314786 0.9905212 0 0.9810428 0.4314786 0.9810426 0.4314787 0.9810426 0 0.9715576 0.4314787 0.9810427 0.8629574 0.9810426 0.4314787 0.9715576 0.8629574 0.9905214 0.8629573 0.9905213 0.4314786 0.9810429 0.8629573 0 0 1.2666e-7 0.4314787 0.2394818 0 0.6703416 0.3065265 0.5829191 0.3065266 0.6703416 0.583398</float_array>
          <technique_common>
            <accessor count="120" source="#Plane_010-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane_010-mesh-vertices">
          <input semantic="POSITION" source="#Plane_010-mesh-positions" />
        </vertices>
        <polylist count="40" material="sawhorse-material">
          <input offset="0" semantic="VERTEX" source="#Plane_010-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Plane_010-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Plane_010-mesh-map-0" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>1 0 0 3 1 1 9 2 2 11 3 3 7 4 4 5 5 5 25 6 6 35 7 7 38 8 8 6 9 9 4 10 10 0 11 11 37 12 12 40 13 13 30 14 14 34 15 15 36 16 16 26 17 17 42 18 18 34 15 19 24 19 20 39 20 21 37 12 22 27 21 23 41 22 24 11 3 25 10 23 26 8 24 27 9 2 28 31 25 29 21 26 30 15 27 31 13 28 32 17 29 33 19 30 34 23 31 35 45 32 36 48 33 37 58 34 38 12 35 39 16 36 40 18 37 41 50 38 42 60 39 43 57 40 44 46 41 45 56 42 46 54 43 47 44 44 48 54 43 49 62 45 50 47 46 51 57 40 52 59 47 53 22 48 54 23 31 55 61 49 56 51 50 57 21 26 58 20 51 59 8 24 60 1 0 61 9 2 62 10 23 63 11 3 64 5 5 65 28 52 66 25 6 67 38 8 68 2 53 69 6 9 70 0 11 71 27 21 72 37 12 73 30 14 74 24 19 75 34 15 76 26 17 77 32 54 78 42 18 79 24 19 80 29 55 81 39 20 82 27 21 83 43 56 84 41 22 85 10 23 86 33 57 87 8 24 88 31 25 89 20 51 90 21 26 91 13 28 92 22 48 93 17 29 94 23 31 95 55 58 96 45 32 97 58 34 98 14 59 99 12 35 100 18 37 101 47 46 102 50 38 103 57 40 104 44 44 105 46 41 106 54 43 107 52 60 108 44 44 109 62 45 110 49 61 111 47 46 112 59 47 113 63 62 114 22 48 115 61 49 116 53 63 117 51 50 118 20 51 119</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Plane_011-mesh" name="Plane.011">
      <mesh>
        <source id="Plane_011-mesh-positions">
          <float_array count="192" id="Plane_011-mesh-positions-array">-0.8058173 -0.03893828 0.9852196 0.7673321 -0.03893816 0.9852196 -0.8058171 -0.180463 0.6016624 0.7673321 -0.1804629 0.6016624 -0.8058173 -0.0541343 0.9908267 0.7673321 -0.05413419 0.9908267 -0.8058171 -0.195659 0.6072695 0.7673321 -0.1956589 0.6072695 -0.005699336 -0.03893822 0.9852196 -0.005699336 -0.180463 0.6016624 -0.005699336 -0.05413424 0.9908267 -0.005699336 -0.195659 0.6072695 -0.8058173 0.04719918 0.9852196 0.7673321 0.0471993 0.9852196 -0.8058171 0.1887239 0.6016624 0.7673321 0.1887241 0.6016624 -0.8058173 0.06239515 0.9908267 0.7673321 0.06239527 0.9908267 -0.8058171 0.2039199 0.6072695 0.7673321 0.20392 0.6072695 -0.005699336 0.04719924 0.9852196 -0.005699336 0.188724 0.6016624 -0.005699336 0.06239521 0.9908267 -0.005699336 0.20392 0.6072695 -0.005699336 -0.03893822 0.9852196 0.7673321 -0.03893816 0.9852196 0.7673321 -0.03893816 0.9852196 -0.005699336 -0.180463 0.6016624 0.7673321 -0.1804629 0.6016624 0.7673321 -0.1804629 0.6016624 -0.8058171 -0.180463 0.6016624 -0.8058171 -0.180463 0.6016624 -0.8058173 -0.03893828 0.9852196 -0.8058173 -0.03893828 0.9852196 -0.005699336 -0.05413424 0.9908267 0.7673321 -0.05413419 0.9908267 0.7673321 -0.05413419 0.9908267 -0.005699336 -0.195659 0.6072695 0.7673321 -0.1956589 0.6072695 0.7673321 -0.1956589 0.6072695 -0.8058171 -0.195659 0.6072695 -0.8058171 -0.195659 0.6072695 -0.8058173 -0.0541343 0.9908267 -0.8058173 -0.0541343 0.9908267 -0.005699336 0.04719924 0.9852196 0.7673321 0.0471993 0.9852196 0.7673321 0.0471993 0.9852196 -0.005699336 0.188724 0.6016624 0.7673321 0.1887241 0.6016624 0.7673321 0.1887241 0.6016624 -0.8058171 0.1887239 0.6016624 -0.8058171 0.1887239 0.6016624 -0.8058173 0.04719918 0.9852196 -0.8058173 0.04719918 0.9852196 -0.005699336 0.06239521 0.9908267 0.7673321 0.06239527 0.9908267 0.7673321 0.06239527 0.9908267 -0.005699336 0.20392 0.6072695 0.7673321 0.20392 0.6072695 0.7673321 0.20392 0.6072695 -0.8058171 0.2039199 0.6072695 -0.8058171 0.2039199 0.6072695 -0.8058173 0.06239515 0.9908267 -0.8058173 0.06239515 0.9908267</float_array>
          <technique_common>
            <accessor count="64" source="#Plane_011-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_011-mesh-normals">
          <float_array count="192" id="Plane_011-mesh-normals-array">0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 -0.9381695 -0.3461409 1 0 0 -1 0 0 0 0.3461409 0.9381695 0 -0.3461409 -0.9381695 0 -0.9381695 0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 -0.3461409 1 0 0 -1 0 0 0 -0.3461409 0.9381695 0 0.3461409 -0.9381695 0 0.9381695 0.3461409 0 -0.9381695 -0.3461409</float_array>
          <technique_common>
            <accessor count="64" source="#Plane_011-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_011-mesh-map-0">
          <float_array count="240" id="Plane_011-mesh-map-0-array">0.6703416 0.8602694 0.5829191 0.8602694 0.5829191 0.583398 0.2352964 0.8492967 0.2352966 0.4422664 -0.004185259 0.4422664 0.7718424 0.6690027 0.7813778 0.6690028 0.7813778 0.9262285 0.7813778 0.9262285 0.7813778 0.6690027 0.7909132 0.6690027 0.9810428 0.4314786 0.9810427 0 0.9905212 0 0.9715576 0.4314787 0.9715576 0 0.9810426 0 0.9715576 0.8629574 0.9715576 0.4314787 0.9810426 0.4314787 0.9810429 0.8629573 0.9810428 0.4314786 0.9905213 0.4314786 0.2394818 0.8538876 0.2394819 0.4468572 1.2666e-7 0.4468572 0.6703416 0.583398 0.5829191 0.583398 0.5829191 0.3065266 0.6703416 0.583398 0.5829191 0.583398 0.5829191 0.8602694 2.5332e-7 0.8538876 0.2394821 0.8538874 0.2394819 0.4468572 0.7718424 0.6690027 0.7718424 0.9262286 0.7813778 0.9262285 0.7909132 0.6690027 0.7813778 0.6690027 0.7813778 0.9262285 0.9905212 0 0.9810427 0 0.9810428 0.4314786 0.9810426 0.4314787 0.9810426 0 0.9715576 0 0.9810427 0.8629574 0.9810426 0.4314787 0.9715576 0.4314787 0.9905213 0.4314786 0.9810428 0.4314786 0.9810429 0.8629573 -0.003691017 0.8499364 0.2357907 0.8499363 0.2357906 0.442906 0.5829191 0.3065266 0.5829191 0.583398 0.6703416 0.583398 0.6703416 0.583398 0.6703416 0.8602694 0.5829191 0.583398 -0.004185378 0.8492967 0.2352964 0.8492967 -0.004185259 0.4422664 0.7718424 0.9262286 0.7718424 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7813778 0.9262285 0.7909132 0.6690027 0.9905213 0.4314786 0.9810428 0.4314786 0.9905212 0 0.9810426 0.4314787 0.9715576 0.4314787 0.9810426 0 0.9810427 0.8629574 0.9715576 0.8629574 0.9810426 0.4314787 0.9905214 0.8629573 0.9810429 0.8629573 0.9905213 0.4314786 0 0.8538874 0.2394818 0.8538876 1.2666e-7 0.4468572 0.6703416 0.3065265 0.6703416 0.583398 0.5829191 0.3065266 0.6703416 0.8602694 0.6703416 0.583398 0.5829191 0.8602694 1.2666e-7 0.4468572 2.5332e-7 0.8538876 0.2394819 0.4468572 0.7813778 0.6690028 0.7718424 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7909132 0.6690027 0.7813778 0.9262285 0.9905213 0.4314786 0.9905212 0 0.9810428 0.4314786 0.9715576 0.4314787 0.9810426 0.4314787 0.9715576 0 0.9715576 0.8629574 0.9810427 0.8629574 0.9715576 0.4314787 0.9905214 0.8629573 0.9905213 0.4314786 0.9810429 0.8629573 -0.003691136 0.4429061 -0.003691017 0.8499364 0.2357906 0.442906 0.6703416 0.3065265 0.5829191 0.3065266 0.6703416 0.583398</float_array>
          <technique_common>
            <accessor count="120" source="#Plane_011-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane_011-mesh-vertices">
          <input semantic="POSITION" source="#Plane_011-mesh-positions" />
        </vertices>
        <polylist count="40" material="sawhorse-material">
          <input offset="0" semantic="VERTEX" source="#Plane_011-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Plane_011-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Plane_011-mesh-map-0" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>1 0 0 3 1 1 9 2 2 11 3 3 7 4 4 5 5 5 25 6 6 35 7 7 38 8 8 6 9 9 4 10 10 0 11 11 37 12 12 40 13 13 30 14 14 34 15 15 36 16 16 26 17 17 42 18 18 34 15 19 24 19 20 39 20 21 37 12 22 27 21 23 41 22 24 11 3 25 10 23 26 8 24 27 9 2 28 31 25 29 20 26 30 21 27 31 15 28 32 17 29 33 19 30 34 23 31 35 45 32 36 48 33 37 58 34 38 12 35 39 16 36 40 18 37 41 50 38 42 60 39 43 57 40 44 44 41 45 46 42 46 56 43 47 52 44 48 44 41 49 54 45 50 47 46 51 57 40 52 59 47 53 22 48 54 23 31 55 61 49 56 51 50 57 21 27 58 20 26 59 8 24 60 1 0 61 9 2 62 10 23 63 11 3 64 5 5 65 28 51 66 25 6 67 38 8 68 2 52 69 6 9 70 0 11 71 27 21 72 37 12 73 30 14 74 24 19 75 34 15 76 26 17 77 32 53 78 42 18 79 24 19 80 29 54 81 39 20 82 27 21 83 43 55 84 41 22 85 10 23 86 33 56 87 8 24 88 31 25 89 13 57 90 20 26 91 15 28 92 22 48 93 17 29 94 23 31 95 55 58 96 45 32 97 58 34 98 14 59 99 12 35 100 18 37 101 47 46 102 50 38 103 57 40 104 54 45 105 44 41 106 56 43 107 62 60 108 52 44 109 54 45 110 49 61 111 47 46 112 59 47 113 63 62 114 22 48 115 61 49 116 53 63 117 51 50 118 20 26 119</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Plane_013-mesh" name="Plane.013">
      <mesh>
        <source id="Plane_013-mesh-positions">
          <float_array count="576" id="Plane_013-mesh-positions-array">-0.6929025 0.035609 0.8873292 -0.7034405 0.035609 0.8978675 -0.6929025 0.04505497 0.8978675 -0.6929025 0.04505497 1.00797 -0.7034405 0.035609 1.00797 -0.6929025 0.035609 1.018508 -0.6247855 0.04505497 0.8978675 -0.6142475 0.035609 0.8978675 -0.6247855 0.035609 0.8873292 -0.6247855 0.04505497 1.00797 -0.6247855 0.035609 1.018508 -0.6142475 0.035609 1.00797 -0.6929025 -0.03489494 0.8978675 -0.7034405 -0.02544897 0.8978675 -0.6929025 -0.02544897 0.8873292 -0.6929025 -0.03489494 1.00797 -0.6929025 -0.02544897 1.018508 -0.7034405 -0.02544897 1.00797 -0.6142475 -0.02544897 0.8978675 -0.6247855 -0.03489494 0.8978675 -0.6247855 -0.02544897 0.8873292 -0.6142475 -0.02544897 1.00797 -0.6247855 -0.02544897 1.018508 -0.6247855 -0.03489494 1.00797 -0.6588439 -0.005299627 1.157071 -0.6952493 -0.005299627 1.147316 -0.7218999 -0.005299627 1.120666 -0.7316548 -0.005299627 1.08426 -0.7218999 -0.005299627 1.047855 -0.6952493 -0.005299627 1.021204 -0.6588439 -0.005299627 1.011449 -0.6224384 -0.005299627 1.021204 -0.5957877 -0.005299627 1.047855 -0.5860329 -0.005299627 1.08426 -0.5957877 -0.005299627 1.120665 -0.6224384 -0.005299627 1.147316 -0.6588439 -0.02125227 1.108764 -0.6710959 -0.02125227 1.105481 -0.680065 -0.02125227 1.096512 -0.683348 -0.02125227 1.08426 -0.680065 -0.02125227 1.072008 -0.6710959 -0.02125233 1.063039 -0.6588439 -0.02125233 1.059756 -0.6465919 -0.02125233 1.063039 -0.6376227 -0.02125233 1.072008 -0.6343397 -0.02125233 1.08426 -0.6376227 -0.02125233 1.096512 -0.6465917 -0.02125233 1.105481 -0.6588439 0.01250249 1.157071 -0.6952493 0.01250249 1.147316 -0.7218999 0.01250249 1.120666 -0.7316548 0.01250249 1.08426 -0.7218999 0.01250249 1.047855 -0.6952493 0.01250249 1.021204 -0.6588439 0.01250249 1.011449 -0.6224384 0.01250249 1.021204 -0.5957877 0.01250249 1.047855 -0.5860329 0.01250249 1.08426 -0.5957877 0.01250249 1.120665 -0.6224384 0.01250249 1.147316 -0.6588439 0.02709633 1.108709 -0.6710684 0.02709633 1.105434 -0.6800174 0.02709633 1.096485 -0.6832929 0.02709633 1.08426 -0.6800174 0.02709633 1.072036 -0.6710684 0.02709633 1.063087 -0.6588439 0.02709633 1.059811 -0.6466193 0.02709633 1.063087 -0.6376703 0.02709633 1.072036 -0.6343946 0.02709633 1.08426 -0.6376702 0.02709633 1.096485 -0.6466193 0.02709633 1.105434 0.5800974 0.03560912 0.8873292 0.5695594 0.03560912 0.8978675 0.5800974 0.04505509 0.8978675 0.5800974 0.04505509 1.00797 0.5695594 0.03560912 1.00797 0.5800974 0.03560912 1.018508 0.6482145 0.04505509 0.8978675 0.6587525 0.03560912 0.8978675 0.6482145 0.03560912 0.8873292 0.6482145 0.04505509 1.00797 0.6482145 0.03560912 1.018508 0.6587525 0.03560912 1.00797 0.5800974 -0.03489482 0.8978675 0.5695594 -0.02544891 0.8978675 0.5800974 -0.02544891 0.8873292 0.5800974 -0.03489482 1.00797 0.5800974 -0.02544891 1.018508 0.5695594 -0.02544891 1.00797 0.6587525 -0.02544885 0.8978675 0.6482145 -0.03489482 0.8978675 0.6482145 -0.02544885 0.8873292 0.6587525 -0.02544885 1.00797 0.6482145 -0.02544885 1.018508 0.6482145 -0.03489482 1.00797 0.6141563 -0.005299568 1.157071 0.5777506 -0.005299568 1.147316 0.5511001 -0.005299568 1.120666 0.5413453 -0.005299568 1.08426 0.5511001 -0.005299568 1.047855 0.5777506 -0.005299568 1.021204 0.6141563 -0.005299568 1.011449 0.6505616 -0.005299568 1.021204 0.6772125 -0.005299568 1.047855 0.6869673 -0.005299568 1.08426 0.6772125 -0.005299568 1.120665 0.6505616 -0.005299568 1.147316 0.6141563 -0.02125221 1.108764 0.6019041 -0.02125221 1.105481 0.5929351 -0.02125221 1.096512 0.5896522 -0.02125221 1.08426 0.5929351 -0.02125221 1.072008 0.6019041 -0.02125221 1.063039 0.6141563 -0.02125221 1.059756 0.6264082 -0.02125221 1.063039 0.6353775 -0.02125221 1.072008 0.6386604 -0.02125221 1.08426 0.6353775 -0.02125221 1.096512 0.6264082 -0.02125221 1.105481 0.6141563 0.01250261 1.157071 0.5777506 0.01250261 1.147316 0.5511001 0.01250261 1.120666 0.5413453 0.01250261 1.08426 0.5511001 0.01250261 1.047855 0.5777506 0.01250261 1.021204 0.6141563 0.01250261 1.011449 0.6505616 0.01250261 1.021204 0.6772125 0.01250261 1.047855 0.6869673 0.01250261 1.08426 0.6772125 0.01250261 1.120665 0.6505616 0.01250261 1.147316 0.6141563 0.02709645 1.108709 0.6019315 0.02709645 1.105434 0.5929826 0.02709645 1.096485 0.5897071 0.02709645 1.08426 0.5929826 0.02709645 1.072036 0.6019315 0.02709639 1.063087 0.6141563 0.02709639 1.059811 0.6263808 0.02709639 1.063087 0.6353297 0.02709639 1.072036 0.6386056 0.02709639 1.08426 0.63533 0.02709639 1.096485 0.6263808 0.02709645 1.105434 -0.6952493 -0.005299627 1.147316 -0.6588439 -0.005299627 1.157071 -0.7218999 -0.005299627 1.120666 -0.7316548 -0.005299627 1.08426 -0.7218999 -0.005299627 1.047855 -0.6952493 -0.005299627 1.021204 -0.6588439 -0.005299627 1.011449 -0.6224384 -0.005299627 1.021204 -0.5957877 -0.005299627 1.047855 -0.5860329 -0.005299627 1.08426 -0.5957877 -0.005299627 1.120665 -0.6224384 -0.005299627 1.147316 -0.6952493 0.01250249 1.147316 -0.6588439 0.01250249 1.157071 -0.7218999 0.01250249 1.120666 -0.7316548 0.01250249 1.08426 -0.7218999 0.01250249 1.047855 -0.6952493 0.01250249 1.021204 -0.6588439 0.01250249 1.011449 -0.6224384 0.01250249 1.021204 -0.5957877 0.01250249 1.047855 -0.5860329 0.01250249 1.08426 -0.5957877 0.01250249 1.120665 -0.6224384 0.01250249 1.147316 0.5777506 -0.005299568 1.147316 0.6141563 -0.005299568 1.157071 0.5511001 -0.005299568 1.120666 0.5413453 -0.005299568 1.08426 0.5511001 -0.005299568 1.047855 0.5777506 -0.005299568 1.021204 0.6141563 -0.005299568 1.011449 0.6505616 -0.005299568 1.021204 0.6772125 -0.005299568 1.047855 0.6869673 -0.005299568 1.08426 0.6772125 -0.005299568 1.120665 0.6505616 -0.005299568 1.147316 0.5777506 0.01250261 1.147316 0.6141563 0.01250261 1.157071 0.5511001 0.01250261 1.120666 0.5413453 0.01250261 1.08426 0.5511001 0.01250261 1.047855 0.5777506 0.01250261 1.021204 0.6141563 0.01250261 1.011449 0.6505616 0.01250261 1.021204 0.6772125 0.01250261 1.047855 0.6869673 0.01250261 1.08426 0.6772125 0.01250261 1.120665 0.6505616 0.01250261 1.147316</float_array>
          <technique_common>
            <accessor count="192" source="#Plane_013-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_013-mesh-normals">
          <float_array count="576" id="Plane_013-mesh-normals-array">-0.3385113 0.3635975 -0.8678548 0.3385113 0.3635975 -0.8678548 0.3385113 -0.3635975 -0.8678548 -0.3222144 -0.8901029 0.3222144 -0.3222144 -0.8901029 -0.3222144 0.3222144 -0.8901029 -0.3222144 0.3384808 -0.3635975 0.8678548 0.3384808 0.3635975 0.8678548 -0.3385113 0.3635975 0.8678548 -0.8678548 0.3635975 0.3384808 -0.8678548 0.3635975 -0.3384808 -0.8678548 -0.3635975 -0.3384808 0.8678548 -0.3635975 0.3384808 0.8678548 -0.3635975 -0.3384808 0.8678548 0.3635975 -0.3384808 -0.3222144 0.8901029 -0.3222144 -0.3222144 0.8901029 0.3222144 0.3222144 0.8901029 -0.3222144 0.3222144 0.8901029 0.3222144 0.8678548 0.3635975 0.3384808 -0.3385113 -0.3635975 -0.8678548 -0.3384808 -0.3635975 0.8678548 -0.8678548 -0.3635975 0.3384808 0.3222144 -0.8901029 0.3222144 0 -0.9495529 0.3135777 0 -0.9828486 0.1843623 0.09216588 -0.9828486 0.1596423 0.1596423 -0.9828486 0.09216588 0.2715537 -0.9495529 0.1567736 0.1843623 -0.9828486 0 0.1596423 -0.9828486 -0.09216588 0.2715537 -0.9495529 -0.1567736 0.09216588 -0.9828486 -0.1596423 0 -0.9828486 -0.1843623 0 -0.9495529 -0.3135777 -0.09216588 -0.9828486 -0.1596423 -0.1596423 -0.9828486 -0.09216588 -0.2715537 -0.9495529 -0.1567736 -0.1843623 -0.9828486 0 -0.1596423 -0.9828486 0.09216588 -0.2715537 -0.9495529 0.1567736 -0.09216588 -0.9828486 0.1596423 0.4999848 0 0.8660237 0.4999848 0 0.8660237 0 0 1 0.8660237 0 0.4999848 1 0 0 0.8660237 0 0.4999848 0.8660237 0 -0.4999848 1 0 0 0.4999848 0 -0.8660237 0.8660237 0 -0.4999848 0 0 -1 0.4999848 0 -0.8660237 -0.4999848 0 -0.8660237 0 0 -1 -0.8660237 0 -0.4999848 -0.4999848 0 -0.8660237 -1 0 0 -0.8660237 0 -0.4999848 -0.8660237 0 0.4999848 -1 0 0 -0.4999848 0 0.8660237 -0.4999848 0 0.8660237 0 0 1 0.08481091 0.9855037 0.1469161 0 0.9855037 0.1696218 0 0.9573351 0.2888882 0.2501907 0.9573351 0.1444441 0.1469161 0.9855037 0.08481091 0.1696218 0.9855037 0 0.2501907 0.9573351 -0.1444441 0.1468856 0.9855037 -0.08481091 0.08481091 0.9855037 -0.1468856 0 0.9573351 -0.2888882 0 0.9855037 -0.1696218 -0.08481091 0.9855037 -0.1469161 -0.2501907 0.9573351 -0.1444441 -0.1469161 0.9855037 -0.08481091 -0.1696218 0.9855037 0 -0.1469161 0.9855037 0.08481091 -0.2888882 0.9573351 0 -0.08481091 0.9855037 0.1469161 -0.2501907 0.9573351 0.1444441 -0.3385113 0.3635975 -0.8678548 0.3385113 0.3635975 -0.8678548 0.3385113 -0.3635975 -0.8678548 -0.3222144 -0.8901029 0.3222144 -0.3222144 -0.8901029 -0.3222144 0.3222144 -0.8901029 -0.3222144 0.3384808 -0.3635975 0.8678548 0.3384808 0.3635975 0.8678548 -0.3384808 0.3635975 0.8678548 -0.8678548 0.3635975 0.3384808 -0.8678548 0.3635975 -0.3384808 -0.8678548 -0.3635975 -0.3384808 0.8678548 -0.3635975 0.3384808 0.8678548 -0.3635975 -0.3384808 0.8678548 0.3635975 -0.3384808 -0.3222144 0.8901029 -0.3222144 -0.3222144 0.8901029 0.3222144 0.3222144 0.8901029 -0.3222144 0.3222144 0.8901029 0.3222144 0.8678548 0.3635975 0.3384808 -0.3385113 -0.3635975 -0.8678548 -0.3384808 -0.3635975 0.8678548 -0.8678548 -0.3635975 0.3384808 0.3222144 -0.8901029 0.3222144 0 -0.9495529 0.3135777 0 -0.9828486 0.1843623 0.09216588 -0.9828486 0.1596423 0.1567736 -0.9495529 0.2715537 0.1596423 -0.9828486 0.09216588 0.2715537 -0.9495529 0.1567736 0.1843623 -0.9828486 0 0.3135777 -0.9495529 0 0.1596423 -0.9828486 -0.09216588 0.09216588 -0.9828486 -0.1596423 0.1567736 -0.9495529 -0.2715537 0 -0.9828486 -0.1843623 0 -0.9495529 -0.3135777 -0.09216588 -0.9828486 -0.1596423 -0.1596423 -0.9828486 -0.09216588 -0.2715537 -0.9495529 -0.1567736 -0.1843623 -0.9828486 0 -0.3135777 -0.9495529 0 -0.1596423 -0.9828486 0.09216588 -0.2715537 -0.9495529 0.1567736 -0.09216588 -0.9828486 0.1596423 0.4999848 0 0.8660237 0 0 1 0 0 1 0.8660237 0 0.4999848 0.4999848 0 0.8660237 1 0 0 1 0 0 0.8660237 0 -0.4999848 0.4999848 0 -0.8660237 0.8660237 0 -0.4999848 0 0 -1 0.4999848 0 -0.8660237 -0.4999848 0 -0.8660237 0 0 -1 -0.8660237 0 -0.4999848 -0.4999848 0 -0.8660237 -1 0 0 -0.8660237 0 -0.4999848 -0.8660237 0 0.4999848 -1 0 0 -0.4999848 0 0.8660237 -0.4999848 0 0.8660237 0.08481091 0.9855037 0.1469161 0 0.9855037 0.1696218 0 0.9573351 0.2888882 0.1469161 0.9855037 0.08481091 0.1444441 0.9573351 0.2501907 0.1696218 0.9855037 0 0.2501907 0.9573351 0.1444441 0.2501602 0.9573351 -0.1444441 0.1468856 0.9855037 -0.08481091 0.1444441 0.9573351 -0.2501907 0.08481091 0.9855037 -0.1468856 0 0.9573351 -0.2888882 0 0.9855037 -0.1696218 -0.08481091 0.9855037 -0.1469161 -0.2501907 0.9573351 -0.1444441 -0.1469161 0.9855037 -0.08481091 -0.1696218 0.9855037 0 -0.1469161 0.9855037 0.08481091 -0.2888882 0.9573351 0 -0.08481091 0.9855037 0.1469161 -0.2501907 0.9573351 0.1444441 0.1567736 -0.9495529 0.2715537 0.3135777 -0.9495529 0 0.1567736 -0.9495529 -0.2715537 -0.1567736 -0.9495529 -0.2715537 -0.3135777 -0.9495529 0 -0.1567736 -0.9495529 0.2715537 -0.8660237 0 0.4999848 0.1444441 0.9573351 0.2501907 0.2888882 0.9573351 0 0.1444441 0.9573351 -0.2501907 -0.1444441 0.9573351 -0.2501907 -0.1444441 0.9573351 0.2501907 0.2715537 -0.9495529 -0.1567736 -0.1567736 -0.9495529 -0.2715537 -0.1567736 -0.9495529 0.2715537 0.8660237 0 0.4999848 -0.8660237 0 0.4999848 0.2888882 0.9573351 0 -0.1444441 0.9573351 -0.2501907 -0.1444441 0.9573351 0.2501907</float_array>
          <technique_common>
            <accessor count="192" source="#Plane_013-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_013-mesh-map-0">
          <float_array count="1632" id="Plane_013-mesh-map-0-array">0.06662327 0.9323309 0.06662327 0.9833319 0.02699524 0.9833319 0.200166 0.9840073 0.08596175 0.9840073 0.08596175 0.922761 0.470786 0.9426021 0.470786 0.9822303 0.4347228 0.9822303 0.2265552 0.9300898 0.3073098 0.9300898 0.3073098 0.9849892 0.4819443 0.9426021 0.5402356 0.9426021 0.5402356 0.9822304 0.321722 0.925854 0.3296122 0.9190146 0.3296122 0.925854 0.218826 0.9215965 0.2265552 0.9300898 0.2110968 0.9300898 0.3296122 0.9700638 0.3296122 0.9769032 0.321722 0.9700638 0.4763652 0.9883609 0.470786 0.9822303 0.4819443 0.9822303 0.08596175 0.9840073 0.08596175 0.9934824 0.07503098 0.9840073 0.218826 0.9934824 0.2110968 0.9849892 0.2265552 0.9849892 0.08596175 0.9132859 0.08596175 0.922761 0.07503098 0.922761 0.4819443 0.9426021 0.470786 0.9426021 0.4763652 0.9364714 0.4120486 0.9190146 0.4120486 0.925854 0.3296122 0.925854 0.4199387 0.9700638 0.4120486 0.9700638 0.4120486 0.925854 0.3296122 0.9769032 0.3296122 0.9700638 0.4120486 0.9700638 0.321722 0.925854 0.3296122 0.925854 0.3296122 0.9700638 0.470786 0.9822303 0.470786 0.9426021 0.4819443 0.9426021 0.08596175 0.922761 0.08596175 0.9132859 0.2001662 0.9132859 0.06662327 0.9833319 0.06662327 0.991222 0.02699524 0.991222 0.2110968 0.9840073 0.200166 0.9840073 0.200166 0.922761 0.08596175 0.9934824 0.08596175 0.9840073 0.200166 0.9840073 0.07503098 0.922761 0.08596175 0.922761 0.08596175 0.9840073 0.2110968 0.9849892 0.2110968 0.9300898 0.2265552 0.9300898 0.02699524 0.9323309 0.02699524 0.9244408 0.06662327 0.9244408 0.4120486 0.9700638 0.3296122 0.9700638 0.3296122 0.925854 0.112042 0.230708 0.2010077 0.4128014 0.1841126 0.4713609 0.1841126 0.4713609 0.1841126 0.5389791 0.06184017 0.6056292 0.06184017 0.6056292 0.1841126 0.5389791 0.2010077 0.5975393 0.2010077 0.5975393 0.2302702 0.631348 0.198992 0.8800904 0.198992 0.8800904 0.2302702 0.631348 0.2640603 0.631348 0.2640603 0.631348 0.2933228 0.5975393 0.3863456 0.7796314 0.3863456 0.7796314 0.2933228 0.5975393 0.3102189 0.5389797 0.3102189 0.5389797 0.3102181 0.4713609 0.4365481 0.4047121 0.4365481 0.4047121 0.3102181 0.4713609 0.2933232 0.4128014 0.2933232 0.4128014 0.2640606 0.3789927 0.2993964 0.1302501 0.2993964 0.1302501 0.2640606 0.3789927 0.2302706 0.3789927 0.2302706 0.3789927 0.2010077 0.4128014 0.112042 0.230708 0.2010077 0.4128014 0.2933228 0.5975393 0.2302702 0.631348 0.2933228 0.5975393 0.3102181 0.4713609 0.3102189 0.5389797 0.3102181 0.4713609 0.2933228 0.5975393 0.2010077 0.4128014 0.377833 0.9513651 0.3778327 0.975439 0.3268652 0.9754388 0.2010077 0.4128014 0.2302702 0.631348 0.1841126 0.5389791 0.1841126 0.5389791 0.2302702 0.631348 0.2010077 0.5975393 0.2640606 0.3789927 0.2933232 0.4128014 0.2010077 0.4128014 0.3102181 0.4713609 0.2010077 0.4128014 0.2933232 0.4128014 0.2302706 0.3789927 0.2640606 0.3789927 0.2010077 0.4128014 0.2010077 0.4128014 0.1841126 0.5389791 0.1841126 0.4713609 0.2302702 0.631348 0.2933228 0.5975393 0.2640603 0.631348 0.4288009 0.975439 0.3778327 0.975439 0.377833 0.9513651 0.4797686 0.975439 0.4288009 0.975439 0.4288009 0.9513651 0.5307368 0.9754388 0.4797686 0.975439 0.4797686 0.9513651 0.5817047 0.9754386 0.5307368 0.9754388 0.5307368 0.9513648 0.6326729 0.9754385 0.5817047 0.9754386 0.5817046 0.9513643 0.07202637 0.9754386 0.02105879 0.9754385 0.02105903 0.9513646 0.1229941 0.9754388 0.07202637 0.9754386 0.07202655 0.9513648 0.1739616 0.975439 0.1229941 0.9754388 0.1229943 0.9513651 0.2249295 0.975439 0.1739616 0.975439 0.1739616 0.9513652 0.2758973 0.9513651 0.2758975 0.975439 0.2249295 0.975439 0.3268652 0.9513651 0.3268652 0.9754388 0.2758975 0.975439 0.2952222 0.4130078 0.2659919 0.3792751 0.2993737 0.1302501 0.4366823 0.4047102 0.3120985 0.4714372 0.2952222 0.4130078 0.3120985 0.5389034 0.3120985 0.4714372 0.4366823 0.4047102 0.3864238 0.7796314 0.2952222 0.5973314 0.3120985 0.5389034 0.2659917 0.6310654 0.2952222 0.5973314 0.3864238 0.7796314 0.1988572 0.8800911 0.2322391 0.6310644 0.2659917 0.6310654 0.2030085 0.5973314 0.2322391 0.6310644 0.1988572 0.8800911 0.06154835 0.6056303 0.1861324 0.5389034 0.2030085 0.5973314 0.1861324 0.4714372 0.1861324 0.5389034 0.06154835 0.6056303 0.2030085 0.4130103 0.1861324 0.4714372 0.06154811 0.4047113 0.2322391 0.3792751 0.2030085 0.4130103 0.1118066 0.2307102 0.2993737 0.1302501 0.2659919 0.3792751 0.2322391 0.3792751 0.2952222 0.5973314 0.2659919 0.3792751 0.3120985 0.4714372 0.2322391 0.6310644 0.2952222 0.5973314 0.2659917 0.6310654 0.1861324 0.5389034 0.2952222 0.5973314 0.2322391 0.6310644 0.2952222 0.5973314 0.1861324 0.5389034 0.2030085 0.4130103 0.2030085 0.4130103 0.1861324 0.5389034 0.1861324 0.4714372 0.2951241 0.5818942 0.3080685 0.4899516 0.3129407 0.5339983 0.2030085 0.4130103 0.2659919 0.3792751 0.2952222 0.5973314 0.2952222 0.4130078 0.3120985 0.4714372 0.2659919 0.3792751 0.2659919 0.3792751 0.2030085 0.4130103 0.2322391 0.3792751 0.1861324 0.5389034 0.2322391 0.6310644 0.2030085 0.5973314 0.06662327 0.9323309 0.06662327 0.9833319 0.02699524 0.9833319 0.200166 0.9840073 0.08596175 0.9840073 0.08596175 0.922761 0.470786 0.9426021 0.470786 0.9822303 0.4347228 0.9822303 0.2265552 0.9300898 0.3073098 0.9300898 0.3073098 0.9849892 0.4819443 0.9426021 0.5402356 0.9426021 0.5402356 0.9822304 0.321722 0.925854 0.3296122 0.9190146 0.3296122 0.925854 0.218826 0.9215965 0.2265552 0.9300898 0.2110968 0.9300898 0.3296122 0.9700638 0.3296122 0.9769032 0.321722 0.9700638 0.4763652 0.9883609 0.470786 0.9822303 0.4819443 0.9822303 0.08596175 0.9840073 0.08596175 0.9934824 0.07503098 0.9840073 0.218826 0.9934824 0.2110968 0.9849892 0.2265552 0.9849892 0.08596175 0.9132859 0.08596175 0.922761 0.07503098 0.922761 0.4819443 0.9426021 0.470786 0.9426021 0.4763652 0.9364714 0.4120486 0.9190146 0.4120486 0.925854 0.3296122 0.925854 0.4199387 0.9700638 0.4120486 0.9700638 0.4120486 0.925854 0.3296122 0.9769032 0.3296122 0.9700638 0.4120486 0.9700638 0.321722 0.925854 0.3296122 0.925854 0.3296122 0.9700638 0.470786 0.9426021 0.4819443 0.9426021 0.4819443 0.9822303 0.08596175 0.922761 0.08596175 0.9132859 0.2001662 0.9132859 0.06662327 0.9833319 0.06662327 0.991222 0.02699524 0.991222 0.2110968 0.9840073 0.200166 0.9840073 0.200166 0.922761 0.08596175 0.9934824 0.08596175 0.9840073 0.200166 0.9840073 0.07503098 0.922761 0.08596175 0.922761 0.08596175 0.9840073 0.2110968 0.9849892 0.2110968 0.9300898 0.2265552 0.9300898 0.02699524 0.9323309 0.02699524 0.9244408 0.06662327 0.9244408 0.4120486 0.9700638 0.3296122 0.9700638 0.3296122 0.925854 0.612042 0.230708 0.7010077 0.4128014 0.6841126 0.4713609 0.5618402 0.4047085 0.6841126 0.4713609 0.6841126 0.5389791 0.5618402 0.6056292 0.6841126 0.5389791 0.7010077 0.5975393 0.6120412 0.7796307 0.7010077 0.5975393 0.7302702 0.631348 0.7302702 0.631348 0.7640603 0.631348 0.7993947 0.8800911 0.7640603 0.631348 0.7933228 0.5975393 0.8863456 0.7796314 0.8863456 0.7796314 0.7933228 0.5975393 0.8102189 0.5389797 0.8102189 0.5389797 0.8102181 0.4713609 0.9365481 0.4047121 0.9365481 0.4047121 0.8102181 0.4713609 0.7933232 0.4128014 0.8863468 0.2307102 0.7933232 0.4128014 0.7640606 0.3789927 0.7993964 0.1302501 0.7640606 0.3789927 0.7302706 0.3789927 0.7302706 0.3789927 0.7010077 0.4128014 0.612042 0.230708 0.7010077 0.4128014 0.7933228 0.5975393 0.7302702 0.631348 0.7933228 0.5975393 0.8102181 0.4713609 0.8102189 0.5389797 0.8102181 0.4713609 0.7933228 0.5975393 0.7010077 0.4128014 0.3778327 0.975439 0.3268652 0.9754388 0.3268652 0.9513651 0.7010077 0.4128014 0.7302702 0.631348 0.6841126 0.5389791 0.6841126 0.5389791 0.7302702 0.631348 0.7010077 0.5975393 0.7640606 0.3789927 0.7933232 0.4128014 0.7010077 0.4128014 0.8102181 0.4713609 0.7010077 0.4128014 0.7933232 0.4128014 0.7302706 0.3789927 0.7640606 0.3789927 0.7010077 0.4128014 0.7010077 0.4128014 0.6841126 0.5389791 0.6841126 0.4713609 0.7302702 0.631348 0.7933228 0.5975393 0.7640603 0.631348 0.4288009 0.975439 0.3778327 0.975439 0.377833 0.9513651 0.4797686 0.9513651 0.4797686 0.975439 0.4288009 0.975439 0.5307368 0.9754388 0.4797686 0.975439 0.4797686 0.9513651 0.5817047 0.9754386 0.5307368 0.9754388 0.5307368 0.9513648 0.6326729 0.9754385 0.5817047 0.9754386 0.5817046 0.9513643 0.07202637 0.9754386 0.02105879 0.9754385 0.02105903 0.9513646 0.1229941 0.9754388 0.07202637 0.9754386 0.07202655 0.9513648 0.1739616 0.975439 0.1229941 0.9754388 0.1229943 0.9513651 0.2249295 0.975439 0.1739616 0.975439 0.1739616 0.9513652 0.2758973 0.9513651 0.2758975 0.975439 0.2249295 0.975439 0.3268652 0.9754388 0.2758975 0.975439 0.2758973 0.9513651 0.7952223 0.4130078 0.7659919 0.3792751 0.7993737 0.1302501 0.8120985 0.4714372 0.7952223 0.4130078 0.8864238 0.230708 0.8120985 0.5389034 0.8120985 0.4714372 0.9366823 0.4047102 0.8864238 0.7796314 0.7952223 0.5973314 0.8120985 0.5389034 0.7993738 0.8800911 0.7659917 0.6310654 0.7952223 0.5973314 0.6988572 0.8800911 0.7322392 0.6310644 0.7659917 0.6310654 0.7030085 0.5973314 0.7322392 0.6310644 0.6988572 0.8800911 0.5615484 0.6056303 0.6861324 0.5389034 0.7030085 0.5973314 0.6861324 0.4714372 0.6861324 0.5389034 0.5615484 0.6056303 0.7030085 0.4130103 0.6861324 0.4714372 0.5615481 0.4047113 0.7322392 0.3792751 0.7030085 0.4130103 0.6118066 0.2307102 0.7993737 0.1302501 0.7659919 0.3792751 0.7322392 0.3792751 0.7952223 0.5973314 0.7659919 0.3792751 0.8120985 0.4714372 0.7322392 0.6310644 0.7952223 0.5973314 0.7659917 0.6310654 0.6861324 0.5389034 0.7952223 0.5973314 0.7322392 0.6310644 0.7952223 0.5973314 0.6861324 0.5389034 0.7030085 0.4130103 0.7030085 0.4130103 0.6861324 0.5389034 0.6861324 0.4714372 0.7942872 0.5818942 0.8072316 0.4899516 0.8121038 0.5339983 0.7030085 0.4130103 0.7659919 0.3792751 0.7952223 0.5973314 0.7952223 0.4130078 0.8120985 0.4714372 0.7659919 0.3792751 0.7659919 0.3792751 0.7030085 0.4130103 0.7322392 0.3792751 0.6861324 0.5389034 0.7322392 0.6310644 0.7030085 0.5973314 0.02699524 0.9323309 0.06662327 0.9323309 0.02699524 0.9833319 0.200166 0.922761 0.200166 0.9840073 0.08596175 0.922761 0.4347228 0.9426021 0.470786 0.9426021 0.4347228 0.9822303 0.2265552 0.9849892 0.2265552 0.9300898 0.3073098 0.9849892 0.4819443 0.9822303 0.4819443 0.9426021 0.5402356 0.9822304 0.3296122 0.9190146 0.4120486 0.9190146 0.3296122 0.925854 0.4199387 0.925854 0.4199387 0.9700638 0.4120486 0.925854 0.4120486 0.9769032 0.3296122 0.9769032 0.4120486 0.9700638 0.321722 0.9700638 0.321722 0.925854 0.3296122 0.9700638 0.4819443 0.9822303 0.470786 0.9822303 0.4819443 0.9426021 0.200166 0.922761 0.08596175 0.922761 0.2001662 0.9132859 0.02699524 0.9833319 0.06662327 0.9833319 0.02699524 0.991222 0.2110968 0.922761 0.2110968 0.9840073 0.200166 0.922761 0.2001662 0.9934824 0.08596175 0.9934824 0.200166 0.9840073 0.07503098 0.9840073 0.07503098 0.922761 0.08596175 0.9840073 0.2265552 0.9849892 0.2110968 0.9849892 0.2265552 0.9300898 0.06662327 0.9323309 0.02699524 0.9323309 0.06662327 0.9244408 0.4120486 0.925854 0.4120486 0.9700638 0.3296122 0.925854 0.06184017 0.4047085 0.112042 0.230708 0.1841126 0.4713609 0.06184017 0.4047085 0.1841126 0.4713609 0.06184017 0.6056292 0.1120412 0.7796307 0.06184017 0.6056292 0.2010077 0.5975393 0.1120412 0.7796307 0.2010077 0.5975393 0.198992 0.8800904 0.2993947 0.8800911 0.198992 0.8800904 0.2640603 0.631348 0.2993947 0.8800911 0.2640603 0.631348 0.3863456 0.7796314 0.4365481 0.6056322 0.3863456 0.7796314 0.3102189 0.5389797 0.4365481 0.6056322 0.3102189 0.5389797 0.4365481 0.4047121 0.3863468 0.2307102 0.4365481 0.4047121 0.2933232 0.4128014 0.3863468 0.2307102 0.2933232 0.4128014 0.2993964 0.1302501 0.1989933 0.130249 0.2993964 0.1302501 0.2302706 0.3789927 0.1989933 0.130249 0.2302706 0.3789927 0.112042 0.230708 0.3268652 0.9513651 0.377833 0.9513651 0.3268652 0.9754388 0.4288009 0.9513651 0.4288009 0.975439 0.377833 0.9513651 0.4797686 0.9513651 0.4797686 0.975439 0.4288009 0.9513651 0.5307368 0.9513648 0.5307368 0.9754388 0.4797686 0.9513651 0.5817046 0.9513643 0.5817047 0.9754386 0.5307368 0.9513648 0.6326727 0.9513642 0.6326729 0.9754385 0.5817046 0.9513643 0.07202655 0.9513648 0.07202637 0.9754386 0.02105903 0.9513646 0.1229943 0.9513651 0.1229941 0.9754388 0.07202655 0.9513648 0.1739616 0.9513652 0.1739616 0.975439 0.1229943 0.9513651 0.2249295 0.9513651 0.2249295 0.975439 0.1739616 0.9513652 0.2249295 0.9513651 0.2758973 0.9513651 0.2249295 0.975439 0.2758973 0.9513651 0.3268652 0.9513651 0.2758975 0.975439 0.3864238 0.230708 0.2952222 0.4130078 0.2993737 0.1302501 0.3864238 0.230708 0.4366823 0.4047102 0.2952222 0.4130078 0.4366827 0.6056303 0.3120985 0.5389034 0.4366823 0.4047102 0.4366827 0.6056303 0.3864238 0.7796314 0.3120985 0.5389034 0.2993738 0.8800911 0.2659917 0.6310654 0.3864238 0.7796314 0.2993738 0.8800911 0.1988572 0.8800911 0.2659917 0.6310654 0.1118071 0.7796326 0.2030085 0.5973314 0.1988572 0.8800911 0.1118071 0.7796326 0.06154835 0.6056303 0.2030085 0.5973314 0.06154811 0.4047113 0.1861324 0.4714372 0.06154835 0.6056303 0.1118066 0.2307102 0.2030085 0.4130103 0.06154811 0.4047113 0.1988569 0.130249 0.2322391 0.3792751 0.1118066 0.2307102 0.1988569 0.130249 0.2993737 0.1302501 0.2322391 0.3792751 0.02699524 0.9323309 0.06662327 0.9323309 0.02699524 0.9833319 0.200166 0.922761 0.200166 0.9840073 0.08596175 0.922761 0.4347228 0.9426021 0.470786 0.9426021 0.4347228 0.9822303 0.2265552 0.9849892 0.2265552 0.9300898 0.3073098 0.9849892 0.4819443 0.9822303 0.4819443 0.9426021 0.5402356 0.9822304 0.3296122 0.9190146 0.4120486 0.9190146 0.3296122 0.925854 0.4199387 0.925854 0.4199387 0.9700638 0.4120486 0.925854 0.4120486 0.9769032 0.3296122 0.9769032 0.4120486 0.9700638 0.321722 0.9700638 0.321722 0.925854 0.3296122 0.9700638 0.470786 0.9822303 0.470786 0.9426021 0.4819443 0.9822303 0.200166 0.922761 0.08596175 0.922761 0.2001662 0.9132859 0.02699524 0.9833319 0.06662327 0.9833319 0.02699524 0.991222 0.2110968 0.922761 0.2110968 0.9840073 0.200166 0.922761 0.2001662 0.9934824 0.08596175 0.9934824 0.200166 0.9840073 0.07503098 0.9840073 0.07503098 0.922761 0.08596175 0.9840073 0.2265552 0.9849892 0.2110968 0.9849892 0.2265552 0.9300898 0.06662327 0.9323309 0.02699524 0.9323309 0.06662327 0.9244408 0.4120486 0.925854 0.4120486 0.9700638 0.3296122 0.925854 0.5618402 0.4047085 0.612042 0.230708 0.6841126 0.4713609 0.5618402 0.6056292 0.5618402 0.4047085 0.6841126 0.5389791 0.6120412 0.7796307 0.5618402 0.6056292 0.7010077 0.5975393 0.698992 0.8800904 0.6120412 0.7796307 0.7302702 0.631348 0.698992 0.8800904 0.7302702 0.631348 0.7993947 0.8800911 0.7993947 0.8800911 0.7640603 0.631348 0.8863456 0.7796314 0.9365481 0.6056322 0.8863456 0.7796314 0.8102189 0.5389797 0.9365481 0.6056322 0.8102189 0.5389797 0.9365481 0.4047121 0.8863468 0.2307102 0.9365481 0.4047121 0.7933232 0.4128014 0.7993964 0.1302501 0.8863468 0.2307102 0.7640606 0.3789927 0.6989933 0.130249 0.7993964 0.1302501 0.7302706 0.3789927 0.6989933 0.130249 0.7302706 0.3789927 0.612042 0.230708 0.377833 0.9513651 0.3778327 0.975439 0.3268652 0.9513651 0.4288009 0.9513651 0.4288009 0.975439 0.377833 0.9513651 0.4288009 0.9513651 0.4797686 0.9513651 0.4288009 0.975439 0.5307368 0.9513648 0.5307368 0.9754388 0.4797686 0.9513651 0.5817046 0.9513643 0.5817047 0.9754386 0.5307368 0.9513648 0.6326727 0.9513642 0.6326729 0.9754385 0.5817046 0.9513643 0.07202655 0.9513648 0.07202637 0.9754386 0.02105903 0.9513646 0.1229943 0.9513651 0.1229941 0.9754388 0.07202655 0.9513648 0.1739616 0.9513652 0.1739616 0.975439 0.1229943 0.9513651 0.2249295 0.9513651 0.2249295 0.975439 0.1739616 0.9513652 0.2249295 0.9513651 0.2758973 0.9513651 0.2249295 0.975439 0.3268652 0.9513651 0.3268652 0.9754388 0.2758973 0.9513651 0.8864238 0.230708 0.7952223 0.4130078 0.7993737 0.1302501 0.9366823 0.4047102 0.8120985 0.4714372 0.8864238 0.230708 0.9366827 0.6056303 0.8120985 0.5389034 0.9366823 0.4047102 0.9366827 0.6056303 0.8864238 0.7796314 0.8120985 0.5389034 0.8864238 0.7796314 0.7993738 0.8800911 0.7952223 0.5973314 0.7993738 0.8800911 0.6988572 0.8800911 0.7659917 0.6310654 0.6118071 0.7796326 0.7030085 0.5973314 0.6988572 0.8800911 0.6118071 0.7796326 0.5615484 0.6056303 0.7030085 0.5973314 0.5615481 0.4047113 0.6861324 0.4714372 0.5615484 0.6056303 0.6118066 0.2307102 0.7030085 0.4130103 0.5615481 0.4047113 0.698857 0.130249 0.7322392 0.3792751 0.6118066 0.2307102 0.698857 0.130249 0.7993737 0.1302501 0.7322392 0.3792751</float_array>
          <technique_common>
            <accessor count="816" source="#Plane_013-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane_013-mesh-vertices">
          <input semantic="POSITION" source="#Plane_013-mesh-positions" />
        </vertices>
        <polylist count="136" material="sawhorse-material">
          <input offset="0" semantic="VERTEX" source="#Plane_013-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Plane_013-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Plane_013-mesh-map-0" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>0 0 0 8 1 1 20 2 2 15 3 3 12 4 4 19 5 5 22 6 6 10 7 7 5 8 8 4 9 9 1 10 10 13 11 11 21 12 12 18 13 13 7 14 14 0 0 15 1 10 16 2 15 17 3 16 18 4 9 19 5 8 20 6 17 21 7 14 22 8 1 23 9 18 24 10 7 25 11 19 26 12 4 27 13 11 28 14 20 29 15 3 30 16 21 31 17 22 32 18 13 33 19 5 34 20 2 35 21 12 36 22 6 37 23 23 38 4 9 39 3 16 40 2 15 41 10 7 42 9 18 43 3 16 44 7 14 45 6 17 46 9 18 47 0 0 48 2 15 49 6 17 50 10 7 51 22 6 52 21 12 53 19 5 54 18 13 55 21 12 56 8 1 57 7 14 58 18 13 59 16 21 60 15 3 61 23 23 62 13 11 63 12 4 64 15 3 65 20 2 66 19 5 67 12 4 68 16 21 69 5 8 70 4 9 71 14 20 72 13 11 73 1 10 74 9 18 75 6 17 76 2 15 77 155 42 123 59 43 124 48 44 125 58 45 147 59 43 148 155 42 149 57 46 150 58 45 151 154 47 152 56 48 153 57 46 154 153 49 155 55 50 156 56 48 157 152 51 158 54 52 159 55 50 160 151 53 161 53 54 162 54 52 163 150 55 164 52 56 165 53 54 166 149 57 167 51 58 168 52 56 169 148 59 170 50 60 171 51 58 172 147 61 173 144 62 174 49 63 175 50 60 176 145 64 177 48 44 178 49 63 179 72 84 246 80 85 247 92 86 248 87 87 249 84 88 250 91 89 251 94 90 252 82 91 253 77 92 254 76 93 255 73 94 256 85 95 257 93 96 258 90 97 259 79 98 260 72 84 261 73 94 262 74 99 263 75 100 264 76 93 265 77 92 266 78 101 267 79 98 268 80 85 269 81 102 270 82 91 271 83 103 272 84 88 273 85 95 274 86 104 275 87 87 276 88 105 277 89 106 278 90 97 279 91 89 280 92 86 281 93 96 282 94 90 283 95 107 284 76 93 285 75 100 286 74 99 287 82 91 288 81 102 289 75 100 290 79 98 291 78 101 292 81 102 293 72 84 294 74 99 295 78 101 296 94 90 297 93 96 298 83 103 299 91 89 300 90 97 301 93 96 302 80 85 303 79 98 304 90 97 305 88 105 306 87 87 307 95 107 308 85 95 309 84 88 310 87 87 311 92 86 312 91 89 313 84 88 314 88 105 315 77 92 316 76 93 317 86 104 318 85 95 319 73 94 320 81 102 321 78 101 322 74 99 323 131 129 369 120 130 370 169 131 371 130 132 393 131 129 394 179 133 395 177 134 396 129 135 397 130 132 398 128 136 399 129 135 400 177 134 401 127 137 402 128 136 403 176 138 404 126 139 405 127 137 406 175 140 407 125 141 408 126 139 409 174 142 410 124 143 411 125 141 412 173 144 413 123 145 414 124 143 415 172 146 416 122 147 417 123 145 418 171 148 419 168 149 420 121 150 421 122 147 422 120 130 423 121 150 424 168 149 425 14 20 492 0 0 493 20 2 494 23 23 495 15 3 496 19 5 497 16 21 498 22 6 499 5 8 500 17 22 501 4 9 502 13 11 503 11 19 504 21 12 505 7 14 506 1 10 507 4 9 508 2 15 509 5 8 510 10 7 511 3 16 512 11 19 513 7 14 514 9 18 515 8 1 516 0 0 517 6 17 518 11 19 519 10 7 520 21 12 521 23 23 522 19 5 523 21 12 524 20 2 525 8 1 526 18 13 527 22 6 528 16 21 529 23 23 530 17 22 531 13 11 532 15 3 533 14 20 534 20 2 535 12 4 536 17 22 537 16 21 538 4 9 539 0 0 540 14 20 541 1 10 542 3 16 543 9 18 544 2 15 545 145 64 582 155 42 583 48 44 584 154 47 585 58 45 586 155 42 587 153 49 588 57 46 589 154 47 590 152 51 591 56 48 592 153 49 593 151 53 594 55 50 595 152 51 596 150 55 597 54 52 598 151 53 599 149 57 600 53 54 601 150 55 602 148 59 603 52 56 604 149 57 605 147 61 606 51 58 607 148 59 608 146 178 609 50 60 610 147 61 611 146 178 612 144 62 613 50 60 614 144 62 615 145 64 616 49 63 617 86 104 654 72 84 655 92 86 656 95 107 657 87 87 658 91 89 659 88 105 660 94 90 661 77 92 662 89 106 663 76 93 664 85 95 665 83 103 666 93 96 667 79 98 668 73 94 669 76 93 670 74 99 671 77 92 672 82 91 673 75 100 674 83 103 675 79 98 676 81 102 677 80 85 678 72 84 679 78 101 680 82 91 681 94 90 682 83 103 683 95 107 684 91 89 685 93 96 686 92 86 687 80 85 688 90 97 689 94 90 690 88 105 691 95 107 692 89 106 693 85 95 694 87 87 695 86 104 696 92 86 697 84 88 698 89 106 699 88 105 700 76 93 701 72 84 702 86 104 703 73 94 704 75 100 705 81 102 706 74 99 707 179 133 744 131 129 745 169 131 746 178 187 747 130 132 748 179 133 749 178 187 750 177 134 751 130 132 752 176 138 753 128 136 754 177 134 755 175 140 756 127 137 757 176 138 758 174 142 759 126 139 760 175 140 761 173 144 762 125 141 763 174 142 764 172 146 765 124 143 766 173 144 767 171 148 768 123 145 769 172 146 770 170 188 771 122 147 772 171 148 773 170 188 774 168 149 775 122 147 776 169 131 777 120 130 778 168 149 779</p>
        </polylist>
        <polylist count="136" material="warninglight-material">
          <input offset="0" semantic="VERTEX" source="#Plane_013-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Plane_013-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Plane_013-mesh-map-0" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>24 24 78 36 25 79 47 26 80 47 26 81 46 27 82 34 28 83 34 28 84 46 27 85 45 29 86 45 29 87 44 30 88 32 31 89 32 31 90 44 30 91 43 32 92 43 32 93 42 33 94 30 34 95 30 34 96 42 33 97 41 35 98 41 35 99 40 36 100 28 37 101 28 37 102 40 36 103 39 38 104 39 38 105 38 39 106 26 40 107 26 40 108 38 39 109 37 41 110 37 41 111 36 25 112 24 24 113 36 25 114 42 33 115 44 30 116 42 33 117 40 36 118 41 35 119 40 36 120 42 33 121 36 25 122 36 25 126 44 30 127 46 27 128 46 27 129 44 30 130 45 29 131 38 39 132 39 38 133 36 25 134 40 36 135 36 25 136 39 38 137 37 41 138 38 39 139 36 25 140 36 25 141 46 27 142 47 26 143 44 30 144 42 33 145 43 32 146 71 65 180 60 66 181 157 67 182 166 68 183 70 69 184 71 65 185 69 70 186 70 69 187 166 68 188 164 71 189 68 72 190 69 70 191 67 73 192 68 72 193 164 71 194 162 74 195 66 75 196 67 73 197 65 76 198 66 75 199 162 74 200 160 77 201 64 78 202 65 76 203 63 79 204 64 78 205 160 77 206 62 80 207 63 79 208 159 81 209 61 82 210 62 80 211 158 83 212 157 67 213 60 66 214 61 82 215 68 72 216 60 66 217 70 69 218 66 75 219 68 72 220 67 73 221 64 78 222 68 72 223 66 75 224 68 72 225 64 78 226 62 80 227 62 80 228 64 78 229 63 79 230 68 72 231 70 69 232 69 70 233 62 80 234 60 66 235 68 72 236 71 65 237 70 69 238 60 66 239 60 66 240 62 80 241 61 82 242 64 78 243 66 75 244 65 76 245 96 108 324 108 109 325 119 110 326 107 111 327 119 110 328 118 112 329 106 113 330 118 112 331 117 114 332 105 115 333 117 114 334 116 116 335 116 116 336 115 117 337 103 118 338 115 117 339 114 119 340 102 120 341 102 120 342 114 119 343 113 121 344 113 121 345 112 122 346 100 123 347 100 123 348 112 122 349 111 124 350 99 125 351 111 124 352 110 126 353 98 127 354 110 126 355 109 128 356 109 128 357 108 109 358 96 108 359 108 109 360 114 119 361 116 116 362 114 119 363 112 122 364 113 121 365 112 122 366 114 119 367 108 109 368 108 109 372 116 116 373 118 112 374 118 112 375 116 116 376 117 114 377 110 126 378 111 124 379 108 109 380 112 122 381 108 109 382 111 124 383 109 128 384 110 126 385 108 109 386 108 109 387 118 112 388 119 110 389 116 116 390 114 119 391 115 117 392 143 151 426 132 152 427 181 153 428 142 154 429 143 151 430 191 155 431 141 156 432 142 154 433 190 157 434 188 158 435 140 159 436 141 156 437 187 160 438 139 161 439 140 159 440 186 162 441 138 163 442 139 161 443 137 164 444 138 163 445 186 162 446 184 165 447 136 166 448 137 164 449 135 167 450 136 166 451 184 165 452 134 168 453 135 167 454 183 169 455 133 170 456 134 168 457 182 171 458 181 153 459 132 152 460 133 170 461 140 159 462 132 152 463 142 154 464 138 163 465 140 159 466 139 161 467 136 166 468 140 159 469 138 163 470 140 159 471 136 166 472 134 168 473 134 168 474 136 166 475 135 167 476 140 159 477 142 154 478 141 156 479 134 168 480 132 152 481 140 159 482 143 151 483 142 154 484 132 152 485 132 152 486 134 168 487 133 170 488 136 166 489 138 163 490 137 164 491 35 172 546 24 24 547 47 26 548 35 172 549 47 26 550 34 28 551 33 173 552 34 28 553 45 29 554 33 173 555 45 29 556 32 31 557 31 174 558 32 31 559 43 32 560 31 174 561 43 32 562 30 34 563 29 175 564 30 34 565 41 35 566 29 175 567 41 35 568 28 37 569 27 176 570 28 37 571 39 38 572 27 176 573 39 38 574 26 40 575 25 177 576 26 40 577 37 41 578 25 177 579 37 41 580 24 24 581 167 179 618 71 65 619 157 67 620 167 179 621 166 68 622 71 65 623 165 180 624 69 70 625 166 68 626 165 180 627 164 71 628 69 70 629 163 181 630 67 73 631 164 71 632 163 181 633 162 74 634 67 73 635 161 182 636 65 76 637 162 74 638 161 182 639 160 77 640 65 76 641 159 81 642 63 79 643 160 77 644 158 83 645 62 80 646 159 81 647 156 183 648 61 82 649 158 83 650 156 183 651 157 67 652 61 82 653 107 111 708 96 108 709 119 110 710 106 113 711 107 111 712 118 112 713 105 115 714 106 113 715 117 114 716 104 184 717 105 115 718 116 116 719 104 184 720 116 116 721 103 118 722 103 118 723 115 117 724 102 120 725 101 185 726 102 120 727 113 121 728 101 185 729 113 121 730 100 123 731 99 125 732 100 123 733 111 124 734 98 127 735 99 125 736 110 126 737 97 186 738 98 127 739 109 128 740 97 186 741 109 128 742 96 108 743 191 155 780 143 151 781 181 153 782 190 157 783 142 154 784 191 155 785 189 189 786 141 156 787 190 157 788 189 189 789 188 158 790 141 156 791 188 158 792 187 160 793 140 159 794 187 160 795 186 162 796 139 161 797 185 190 798 137 164 799 186 162 800 185 190 801 184 165 802 137 164 803 183 169 804 135 167 805 184 165 806 182 171 807 134 168 808 183 169 809 180 191 810 133 170 811 182 171 812 180 191 813 181 153 814 133 170 815</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Plane_016-mesh" name="Plane.016">
      <mesh>
        <source id="Plane_016-mesh-positions">
          <float_array count="252" id="Plane_016-mesh-positions-array">-0.8058173 -0.03893828 0.9852196 0.7673321 -0.03893816 0.9852196 -0.8058171 -0.180463 0.6016624 0.7673321 -0.1804629 0.6016624 -0.8058173 -0.0541343 0.9908267 0.7673321 -0.05413419 0.9908267 -0.8058171 -0.195659 0.6072695 0.7673321 -0.1956589 0.6072695 -0.005699336 -0.03893822 0.9852196 -0.005699336 -0.180463 0.6016624 -0.005699336 -0.05413424 0.9908267 -0.005699336 -0.195659 0.6072695 -0.8058173 0.04719918 0.9852196 0.7673321 0.0471993 0.9852196 -0.8058171 0.1887239 0.6016624 0.7673321 0.1887241 0.6016624 -0.8058173 0.06239515 0.9908267 0.7673321 0.06239527 0.9908267 -0.8058171 0.2039199 0.6072695 0.7673321 0.20392 0.6072695 -0.005699336 0.04719924 0.9852196 -0.005699336 0.188724 0.6016624 -0.005699336 0.06239521 0.9908267 -0.005699336 0.20392 0.6072695 0.3098239 0.2068282 0.6331426 0.3098239 0.07986861 0.9772256 0.3098239 0.1931961 0.6281126 0.3098239 0.06623655 0.9721956 -0.3053883 0.2068282 0.6331426 -0.3053884 0.07986867 0.9772256 -0.3053883 0.1931962 0.6281126 -0.3053884 0.06623661 0.9721956 -0.005699336 -0.03893822 0.9852196 0.7673321 -0.03893816 0.9852196 0.7673321 -0.03893816 0.9852196 -0.005699336 -0.180463 0.6016624 0.7673321 -0.1804629 0.6016624 0.7673321 -0.1804629 0.6016624 -0.8058171 -0.180463 0.6016624 -0.8058171 -0.180463 0.6016624 -0.8058173 -0.03893828 0.9852196 -0.8058173 -0.03893828 0.9852196 -0.005699336 -0.05413424 0.9908267 0.7673321 -0.05413419 0.9908267 0.7673321 -0.05413419 0.9908267 -0.005699336 -0.195659 0.6072695 0.7673321 -0.1956589 0.6072695 0.7673321 -0.1956589 0.6072695 -0.8058171 -0.195659 0.6072695 -0.8058171 -0.195659 0.6072695 -0.8058173 -0.0541343 0.9908267 -0.8058173 -0.0541343 0.9908267 -0.005699336 0.04719924 0.9852196 0.7673321 0.0471993 0.9852196 0.7673321 0.0471993 0.9852196 -0.005699336 0.188724 0.6016624 0.7673321 0.1887241 0.6016624 0.7673321 0.1887241 0.6016624 -0.8058171 0.1887239 0.6016624 -0.8058171 0.1887239 0.6016624 -0.8058173 0.04719918 0.9852196 -0.8058173 0.04719918 0.9852196 -0.005699336 0.06239521 0.9908267 0.7673321 0.06239527 0.9908267 0.7673321 0.06239527 0.9908267 -0.005699336 0.20392 0.6072695 0.7673321 0.20392 0.6072695 0.7673321 0.20392 0.6072695 -0.8058171 0.2039199 0.6072695 -0.8058171 0.2039199 0.6072695 -0.8058173 0.06239515 0.9908267 -0.8058173 0.06239515 0.9908267 0.3098239 0.2068282 0.6331426 0.3098239 0.2068282 0.6331426 0.3098239 0.07986861 0.9772256 0.3098239 0.07986861 0.9772256 0.3098239 0.1931961 0.6281126 0.3098239 0.06623655 0.9721956 -0.3053883 0.2068282 0.6331426 -0.3053883 0.2068282 0.6331426 -0.3053884 0.07986867 0.9772256 -0.3053884 0.07986867 0.9772256 -0.3053884 0.06623661 0.9721956 -0.3053883 0.1931962 0.6281126</float_array>
          <technique_common>
            <accessor count="84" source="#Plane_016-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_016-mesh-normals">
          <float_array count="252" id="Plane_016-mesh-normals-array">0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 0.3461409 0.9381695 0 -0.3461409 -0.9381695 0 -0.3461409 -0.9381695 0 -0.9381695 0.3461409 0 -0.9381695 0.3461409 0 0.9381695 -0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 -0.9381695 -0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 0.3461409 -0.9381695 0 0.3461409 -0.9381695 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 -0.9381695 -0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 0.9381695 0.3461409 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 -0.3461409 0.9381695 0 0.3461714 -0.9381695 0 0.3461714 -0.9381695 0 0.3461714 -0.9381695 -1 0 0 -1 0 0 -1 0 0 1 0 0 0.9999695 0 0 1 0 0 1 0 0 -1 0 0 0 0.3461409 0.9381695 0 -0.3461409 -0.9381695 0 -0.9381695 0.3461409 0 0.9381695 -0.3461409 0 -0.9381695 -0.3461409 1 0 0 -1 0 0 0 -0.3461409 0.9381695 0 0.3461409 -0.9381695 0 0.9381695 0.3461409 0 -0.9381695 -0.3461409 0 0.9381695 0.3461409 0 -0.3461409 0.9381695 0 0.3461714 -0.9381695 -1 0 0 1 0 0</float_array>
          <technique_common>
            <accessor count="84" source="#Plane_016-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_016-mesh-map-0">
          <float_array count="300" id="Plane_016-mesh-map-0-array">0.6703416 0.8602694 0.5829191 0.8602694 0.5829191 0.583398 0.2356064 0.8492967 0.2356066 0.4422664 -0.003875195 0.4422664 0.7718424 0.6690027 0.7813778 0.6690028 0.7813778 0.9262285 0.7813778 0.9262285 0.7813778 0.6690027 0.7909132 0.6690027 0.9810428 0.4314786 0.9810427 0 0.9905212 0 0.9715576 0.4314787 0.9715576 0 0.9810426 0 0.9715576 0.8629574 0.9715576 0.4314787 0.9810426 0.4314787 0.9810429 0.8629573 0.9810428 0.4314786 0.9905213 0.4314786 0.2394818 0.8538876 0.2394819 0.4468572 1.2666e-7 0.4468572 0.6703416 0.583398 0.5829191 0.583398 0.5829191 0.3065266 0.6703416 0.583398 0.5829191 0.583398 0.5829191 0.8602694 2.5332e-7 0.8538876 0.2394821 0.8538874 0.2394819 0.4468572 0.7718424 0.6690027 0.7718424 0.9262286 0.7813778 0.9262285 0.7909132 0.6690027 0.7813778 0.6690027 0.7813778 0.9262285 0.9905212 0 0.9810427 0 0.9810428 0.4314786 0.9810426 0.4314787 0.9810426 0 0.9715576 0 0.9810427 0.8629574 0.9810426 0.4314787 0.9715576 0.4314787 0.9905213 0.4314786 0.9810428 0.4314786 0.9810429 0.8629573 1.2666e-7 0.8523972 0.2394819 0.8523971 0.2394818 0.4453667 0.5829191 0.3065266 0.5829191 0.583398 0.6703416 0.583398 0.4661129 0.6131961 0.2439636 0.6131961 0.2439637 0.2356247 0.9810427 0.8629574 0.9810426 0.4314787 0.9715576 0.4314787 0.9905212 0 0.9810427 0 0.9810428 0.4314786 0.7909132 0.6690027 0.7813778 0.6690027 0.7813778 0.9262285 0.7813778 0.9262285 0.7813778 0.6690027 0.7909132 0.6690027 0.6703416 0.583398 0.6703416 0.8602694 0.5829191 0.583398 -0.003875315 0.8492967 0.2356064 0.8492967 -0.003875195 0.4422664 0.7718424 0.9262286 0.7718424 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7813778 0.9262285 0.7909132 0.6690027 0.9905213 0.4314786 0.9810428 0.4314786 0.9905212 0 0.9810426 0.4314787 0.9715576 0.4314787 0.9810426 0 0.9810427 0.8629574 0.9715576 0.8629574 0.9810426 0.4314787 0.9905214 0.8629573 0.9810429 0.8629573 0.9905213 0.4314786 0 0.8538874 0.2394818 0.8538876 1.2666e-7 0.4468572 0.6703416 0.3065265 0.6703416 0.583398 0.5829191 0.3065266 0.6703416 0.8602694 0.6703416 0.583398 0.5829191 0.8602694 1.2666e-7 0.4468572 2.5332e-7 0.8538876 0.2394819 0.4468572 0.7813778 0.6690028 0.7718424 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7909132 0.6690027 0.7813778 0.9262285 0.9905213 0.4314786 0.9905212 0 0.9810428 0.4314786 0.9715576 0.4314787 0.9810426 0.4314787 0.9715576 0 0.9715576 0.8629574 0.9810427 0.8629574 0.9715576 0.4314787 0.9905214 0.8629573 0.9905213 0.4314786 0.9810429 0.8629573 0 0.4453669 1.2666e-7 0.8523972 0.2394818 0.4453667 0.6703416 0.3065265 0.5829191 0.3065266 0.6703416 0.583398 0.466113 0.2356248 0.4661129 0.6131961 0.2439637 0.2356247 0.9715576 0.8629574 0.9810427 0.8629574 0.9715576 0.4314787 0.9905213 0.4314786 0.9905212 0 0.9810428 0.4314786 0.7909132 0.9262285 0.7909132 0.6690027 0.7813778 0.9262285 0.7909132 0.9262285 0.7813778 0.9262285 0.7909132 0.6690027</float_array>
          <technique_common>
            <accessor count="150" source="#Plane_016-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane_016-mesh-vertices">
          <input semantic="POSITION" source="#Plane_016-mesh-positions" />
        </vertices>
        <polylist count="50" material="sawhorse-material">
          <input offset="0" semantic="VERTEX" source="#Plane_016-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Plane_016-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Plane_016-mesh-map-0" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>1 0 0 3 1 1 9 2 2 11 3 3 7 4 4 5 5 5 33 6 6 43 7 7 46 8 8 6 9 9 4 10 10 0 11 11 45 12 12 48 13 13 38 14 14 42 15 15 44 16 16 34 17 17 50 18 18 42 15 19 32 19 20 47 20 21 45 12 22 35 21 23 49 22 24 11 3 25 10 23 26 8 24 27 9 2 28 39 25 29 20 26 30 21 27 31 15 28 32 17 29 33 19 30 34 23 31 35 53 32 36 56 33 37 66 34 38 12 35 39 16 36 40 18 37 41 58 38 42 68 39 43 65 40 44 52 41 45 54 42 46 64 43 47 60 44 48 52 41 49 62 45 50 55 46 51 65 40 52 67 47 53 22 48 54 23 31 55 69 49 56 59 50 57 21 27 58 20 26 59 25 51 60 24 52 61 28 53 62 31 54 63 27 55 64 74 56 65 30 57 66 78 58 67 72 59 68 82 60 69 81 61 70 79 62 71 73 63 72 75 64 73 77 65 74 8 24 75 1 0 76 9 2 77 10 23 78 11 3 79 5 5 80 36 66 81 33 6 82 46 8 83 2 67 84 6 9 85 0 11 86 35 21 87 45 12 88 38 14 89 32 19 90 42 15 91 34 17 92 40 68 93 50 18 94 32 19 95 37 69 96 47 20 97 35 21 98 51 70 99 49 22 100 10 23 101 41 71 102 8 24 103 39 25 104 13 72 105 20 26 106 15 28 107 22 48 108 17 29 109 23 31 110 63 73 111 53 32 112 66 34 113 14 74 114 12 35 115 18 37 116 55 46 117 58 38 118 65 40 119 62 45 120 52 41 121 64 43 122 70 75 123 60 44 124 62 45 125 57 76 126 55 46 127 67 47 128 71 77 129 22 48 130 69 49 131 61 78 132 59 50 133 20 26 134 29 79 135 25 51 136 28 53 137 80 80 138 31 54 139 74 56 140 26 81 141 30 57 142 72 59 143 83 82 144 82 60 145 79 62 146 76 83 147 73 63 148 77 65 149</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers />
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="sawhorse" name="sawhorse" type="NODE">
        <matrix sid="transform">1 0 0 0.002818844 0 1 0 -0.003918601 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane_001-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="sawhorse-material" target="#sawhorse-material" />
              <instance_material symbol="warninglight-material" target="#warninglight-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="arrows_left" name="arrows_left" type="NODE">
        <matrix sid="transform">1 0 0 0.002818844 0 1 0 -0.003918601 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane_009-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="sawhorse-material" target="#sawhorse-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="arrows_right" name="arrows_right" type="NODE">
        <matrix sid="transform">1 0 0 0.002818844 0 1 0 -0.003918601 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane_010-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="sawhorse-material" target="#sawhorse-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="stripes" name="stripes" type="NODE">
        <matrix sid="transform">1 0 0 0.002818844 0 1 0 -0.003918601 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane_011-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="sawhorse-material" target="#sawhorse-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="warninglights" name="warninglights" type="NODE">
        <matrix sid="transform">1 0 0 0.002818844 0 1 0 -0.003918601 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane_013-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="sawhorse-material" target="#sawhorse-material" />
              <instance_material symbol="warninglight-material" target="#warninglight-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="road_closed" name="road_closed" type="NODE">
        <matrix sid="transform">1 0 0 0.002818844 0 1 0 -0.003918601 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane_016-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="sawhorse-material" target="#sawhorse-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene" />
  </scene>
</COLLADA>