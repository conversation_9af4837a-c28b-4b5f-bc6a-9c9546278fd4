<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 3.6.9 commit date:2024-02-19, commit time:16:48, hash:e958717a0c25</authoring_tool>
    </contributor>
    <created>2024-11-19T16:21:03</created>
    <modified>2024-11-19T16:21:03</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="licenseplate-effect">
      <profile_COMMON>
        <newparam sid="Untitled_png-surface">
          <surface type="2D">
            <init_from>Untitled_png</init_from>
          </surface>
        </newparam>
        <newparam sid="Untitled_png-sampler">
          <sampler2D>
            <source>Untitled_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <texture texture="Untitled_png-sampler" texcoord="licenseplate-mesh-map-0"/>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="Untitled_png" name="Untitled_png">
      <init_from>Untitled.png</init_from>
    </image>
  </library_images>
  <library_materials>
    <material id="licenseplate-material" name="licenseplate">
      <instance_effect url="#licenseplate-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="licenseplate-mesh" name="licenseplate">
      <mesh>
        <source id="licenseplate-mesh-positions">
          <float_array id="licenseplate-mesh-positions-array" count="60">14.45254 0.006441295 7.600002 -4.77809e-7 0.006439149 7.600002 -4.75865e-7 -0.006439149 -7.599998 14.45254 -0.006436944 -7.599995 15.25253 -0.005765438 -6.799995 15.25253 0.005770087 6.800007 -14.45254 0.006436944 7.600002 -14.45254 -0.006441295 -7.599995 -15.25253 0.005765438 6.800007 -15.25253 -0.005770087 -6.799995 14.45254 -0.01236528 7.600002 -4.77809e-7 -0.01236742 7.600002 -4.75865e-7 -0.02524584 -7.599998 14.45254 -0.02524363 -7.599995 15.25253 -0.02457213 -6.799995 15.25253 -0.01303654 6.800007 -14.45254 -0.01236963 7.600002 -14.45254 -0.02524799 -7.599995 -15.25253 -0.01304113 6.800007 -15.25253 -0.02457672 -6.799995</float_array>
          <technique_common>
            <accessor source="#licenseplate-mesh-positions-array" count="20" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-mesh-normals">
          <float_array id="licenseplate-mesh-normals-array" count="48">1.48502e-7 -0.9999997 8.4726e-4 1.52394e-7 -0.9999997 8.47264e-4 8.17135e-6 -0.9999997 8.47262e-4 -8.82244e-6 -0.9999997 8.482e-4 1.4867e-7 -0.9999997 8.47264e-4 1.52562e-7 -0.9999997 8.4726e-4 9.12104e-6 -0.9999997 8.48201e-4 -7.87278e-6 -0.9999997 8.47262e-4 -1.4847e-7 0.9999997 -8.4726e-4 -1.52426e-7 0.9999997 -8.47264e-4 -8.17089e-6 0.9999997 -8.47262e-4 8.82252e-6 0.9999997 -8.482e-4 -1.48638e-7 0.9999997 -8.47264e-4 -1.52594e-7 0.9999997 -8.4726e-4 -9.12062e-6 0.9999997 -8.48201e-4 7.87294e-6 0.9999997 -8.47262e-4</float_array>
          <technique_common>
            <accessor source="#licenseplate-mesh-normals-array" count="16" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-mesh-map-0">
          <float_array id="licenseplate-mesh-map-0-array" count="96">0.9737753 4.76837e-7 0.9737753 1 0.5 1 0.5 1 0.5 2.38419e-7 0.9737753 4.76837e-7 1 0.9473689 0.9737753 1 0.9737753 4.76837e-7 0.9737753 4.76837e-7 1 0.05263209 1 0.9473689 0.02622473 4.76837e-7 0.5 2.38419e-7 0.5 1 0.5 1 0.02622473 1 0.02622473 4.76837e-7 0 0.9473689 0 0.05263209 0.02622473 4.76837e-7 0.02622473 4.76837e-7 0.02622473 1 0 0.9473689 0.001872837 0.002759337 0.001399099 0.002759337 0.001872837 0.002759337 0.001399099 0.002759337 0.001872837 0.002759337 0.001399099 0.002759337 0.001899063 0.002759337 0.001872837 0.002759337 0.001872837 0.002759337 0.001872837 0.002759337 0.001899063 0.002759337 0.001899063 0.002759337 9.25338e-4 0.002759337 0.001399099 0.002759337 0.001399099 0.002759337 0.001399099 0.002759337 9.25338e-4 0.002759337 9.25338e-4 0.002759337 8.99114e-4 0.002759337 9.25338e-4 0.002759337 8.99114e-4 0.002759337 9.25338e-4 0.002759337 8.99114e-4 0.002759337 9.25338e-4 0.002759337</float_array>
          <technique_common>
            <accessor source="#licenseplate-mesh-map-0-array" count="48" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-mesh-map-1">
          <float_array id="licenseplate-mesh-map-1-array" count="96">0.9737753 4.76837e-7 0.9737753 1 0.5 1 0.5 1 0.5 2.38419e-7 0.9737753 4.76837e-7 1 0.9473689 0.9737753 1 0.9737753 4.76837e-7 0.9737753 4.76837e-7 1 0.05263209 1 0.9473689 0.02622473 4.76837e-7 0.5 2.38419e-7 0.5 1 0.5 1 0.02622473 1 0.02622473 4.76837e-7 0 0.9473689 0 0.05263209 0.02622473 4.76837e-7 0.02622473 4.76837e-7 0.02622473 1 0 0.9473689 0.9737753 4.76837e-7 0.5 1 0.9737753 1 0.5 1 0.9737753 4.76837e-7 0.5 2.38419e-7 1 0.9473689 0.9737753 4.76837e-7 0.9737753 1 0.9737753 4.76837e-7 1 0.9473689 1 0.05263209 0.02622473 4.76837e-7 0.5 1 0.5 2.38419e-7 0.5 1 0.02622473 4.76837e-7 0.02622473 1 0 0.9473689 0.02622473 4.76837e-7 0 0.05263209 0.02622473 4.76837e-7 0 0.9473689 0.02622473 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-mesh-map-1-array" count="48" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-mesh-colors-geom-licenseplate-map-2" name="geom-licenseplate-map-2">
          <float_array id="licenseplate-mesh-colors-geom-licenseplate-map-2-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-mesh-colors-geom-licenseplate-map-2-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-mesh-colors-geom-licenseplate-map-1" name="geom-licenseplate-map-1">
          <float_array id="licenseplate-mesh-colors-geom-licenseplate-map-1-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-mesh-colors-geom-licenseplate-map-1-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-mesh-colors-geom-licenseplate-map0" name="geom-licenseplate-map0">
          <float_array id="licenseplate-mesh-colors-geom-licenseplate-map0-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-mesh-colors-geom-licenseplate-map0-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-material" count="16">
          <input semantic="VERTEX" source="#licenseplate-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-mesh-colors-geom-licenseplate-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-mesh-colors-geom-licenseplate-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-mesh-colors-geom-licenseplate-map0" offset="3" set="2"/>
          <p>3 0 0 0 0 0 1 1 1 0 2 2 1 1 3 3 2 1 4 4 3 1 5 5 5 2 6 6 0 2 7 7 3 2 8 8 3 3 9 9 4 3 10 10 5 3 11 11 7 4 12 12 2 4 13 13 1 4 14 14 1 5 15 15 6 5 16 16 7 5 17 17 8 6 18 18 9 6 19 19 7 6 20 20 7 7 21 21 6 7 22 22 8 7 23 23 13 8 24 24 11 8 25 25 10 8 26 26 11 9 27 27 13 9 28 28 12 9 29 29 15 10 30 30 13 10 31 31 10 10 32 32 13 11 33 33 15 11 34 34 14 11 35 35 17 12 36 36 11 12 37 37 12 12 38 38 11 13 39 39 17 13 40 40 16 13 41 41 18 14 42 42 17 14 43 43 19 14 44 44 17 15 45 45 18 15 46 46 16 15 47 47</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="licenseplate" name="licenseplate" type="NODE">
        <matrix sid="transform">0.01 0 0 0 0 0.01 0 0 0 0 0.01 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-mesh" name="licenseplate">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-material" target="#licenseplate-material">
                <bind_vertex_input semantic="licenseplate-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>