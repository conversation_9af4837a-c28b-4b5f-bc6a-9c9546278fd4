<?xml version='1.0' encoding='utf-8'?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2018-03-22, commit time:14:10, hash:f4dc9f9d68b</authoring_tool>
    </contributor>
    <created>2018-11-27T14:59:25</created>
    <modified>2018-11-27T14:59:25</modified>
    <unit meter="1" name="meter" />
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="shiftlight_single_l0-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight-effect">
      <profile_COMMON>
        <newparam sid="Diffuse_png_001-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="Diffuse_png_001-sampler">
          <sampler2D>
            <source>Diffuse_png_001-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="Normal_png-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="Normal_png-sampler">
          <sampler2D>
            <source>Normal_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="Diffuse_png_001-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
          <extra>
            <technique profile="FCOLLADA">
              <bump>
                <texture texture="Normal_png-sampler" />
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi-effect">
      <profile_COMMON>
        <newparam sid="Diffuse_png_001-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="Diffuse_png_001-sampler">
          <sampler2D>
            <source>Diffuse_png_001-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="Specular_png-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="Specular_png-sampler">
          <sampler2D>
            <source>Specular_png-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="Normal_png-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="Normal_png-sampler">
          <sampler2D>
            <source>Normal_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="Diffuse_png_001-sampler" />
            </diffuse>
            <specular>
              <texture texture="Specular_png-sampler" />
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
          <extra>
            <technique profile="FCOLLADA">
              <bump>
                <texture texture="Normal_png-sampler" />
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi_l0-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi_l1-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi_l2-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi_l3-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi_l4-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi_l5-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi_l6-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="shiftlight_multi_l7-effect">
      <profile_COMMON>
        <newparam sid="shiftlight_l_psd-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="shiftlight_l_psd-sampler">
          <sampler2D>
            <source>shiftlight_l_psd-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <texture texture="shiftlight_l_psd-sampler" />
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="shiftlight_l_psd-sampler" />
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="shiftlight_single_l0-material" name="shiftlight_single_l0">
      <instance_effect url="#shiftlight_single_l0-effect" />
    </material>
    <material id="shiftlight-material" name="shiftlight">
      <instance_effect url="#shiftlight-effect" />
    </material>
    <material id="shiftlight_multi-material" name="shiftlight_multi">
      <instance_effect url="#shiftlight_multi-effect" />
    </material>
    <material id="shiftlight_multi_l0-material" name="shiftlight_multi_l0">
      <instance_effect url="#shiftlight_multi_l0-effect" />
    </material>
    <material id="shiftlight_multi_l1-material" name="shiftlight_multi_l1">
      <instance_effect url="#shiftlight_multi_l1-effect" />
    </material>
    <material id="shiftlight_multi_l2-material" name="shiftlight_multi_l2">
      <instance_effect url="#shiftlight_multi_l2-effect" />
    </material>
    <material id="shiftlight_multi_l3-material" name="shiftlight_multi_l3">
      <instance_effect url="#shiftlight_multi_l3-effect" />
    </material>
    <material id="shiftlight_multi_l4-material" name="shiftlight_multi_l4">
      <instance_effect url="#shiftlight_multi_l4-effect" />
    </material>
    <material id="shiftlight_multi_l5-material" name="shiftlight_multi_l5">
      <instance_effect url="#shiftlight_multi_l5-effect" />
    </material>
    <material id="shiftlight_multi_l6-material" name="shiftlight_multi_l6">
      <instance_effect url="#shiftlight_multi_l6-effect" />
    </material>
    <material id="shiftlight_multi_l7-material" name="shiftlight_multi_l7">
      <instance_effect url="#shiftlight_multi_l7-effect" />
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="shiftlight_light-mesh" name="shiftlight_light">
      <mesh>
        <source id="shiftlight_light-mesh-positions">
          <float_array count="51" id="shiftlight_light-mesh-positions-array">0 0.02600967 0.008323371 0.003185212 0.02600967 0.007689774 0.005885481 0.02600967 0.005885481 0.007689774 0.02600967 0.003185212 0.008323371 0.02600967 0 0.007689774 0.02600967 -0.003185212 0.005885481 0.02600967 -0.005885481 0.003185212 0.02600967 -0.007689774 0 0.02600967 -0.008323371 -0.003185212 0.02600967 -0.007689774 -0.005885481 0.02600967 -0.005885481 -0.007689774 0.02600967 -0.003185212 -0.008323371 0.02600967 0 -0.007689774 0.02600967 0.003185212 -0.005885481 0.02600967 0.005885481 -0.003185212 0.02600967 0.007689774 0 0.02930176 0</float_array>
          <technique_common>
            <accessor count="17" source="#shiftlight_light-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="shiftlight_light-mesh-normals">
          <float_array count="51" id="shiftlight_light-mesh-normals-array">-0.1407226 0.9299112 0.3397976 0 1 0 -0.2600531 0.9299166 0.2600531 0 0.9299115 -0.3677835 0.1407226 0.9299112 -0.3397976 0.1407226 0.9299112 0.3397976 0 0.9299115 0.3677835 -0.1407226 0.9299112 -0.3397976 0.2600531 0.9299166 0.2600531 -0.2600531 0.9299166 -0.2600531 0.3397976 0.9299112 0.1407226 -0.3397976 0.9299112 -0.1407226 0.3677835 0.9299115 0 -0.3677835 0.9299115 0 0.3397976 0.9299112 -0.1407226 -0.3397976 0.9299112 0.1407226 0.2600531 0.9299166 -0.2600531</float_array>
          <technique_common>
            <accessor count="17" source="#shiftlight_light-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="shiftlight_light-mesh-map-0">
          <float_array count="96" id="shiftlight_light-mesh-map-0-array">0.8907356 0.9377032 0.845157 0.4975572 0.9293754 0.8344298 0.845157 0.02114665 0.845157 0.4975572 0.7995784 0.05741161 0.7995784 0.9377032 0.845157 0.4975572 0.845157 0.9739682 0.845157 0.9739682 0.845157 0.4975572 0.8907356 0.9377032 0.8907356 0.05741161 0.845157 0.4975572 0.845157 0.02114665 0.7609387 0.8344298 0.845157 0.4975572 0.7995784 0.9377032 0.9293754 0.1606844 0.845157 0.4975572 0.8907356 0.05741161 0.7351205 0.6798723 0.845157 0.4975572 0.7609387 0.8344298 0.9551936 0.3152431 0.845157 0.4975572 0.9293754 0.1606844 0.7260544 0.4975572 0.845157 0.4975572 0.7351205 0.6798723 0.9642599 0.4975572 0.845157 0.4975572 0.9551936 0.3152431 0.7351205 0.3152431 0.845157 0.4975572 0.7260544 0.4975572 0.9551936 0.6798723 0.845157 0.4975572 0.9642599 0.4975572 0.7609387 0.1606844 0.845157 0.4975572 0.7351205 0.3152431 0.9293754 0.8344298 0.845157 0.4975572 0.9551936 0.6798723 0.7995784 0.05741161 0.845157 0.4975572 0.7609387 0.1606844</float_array>
          <technique_common>
            <accessor count="48" source="#shiftlight_light-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="shiftlight_light-mesh-vertices">
          <input semantic="POSITION" source="#shiftlight_light-mesh-positions" />
        </vertices>
        <triangles count="16" material="shiftlight_single_l0-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_light-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_light-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_light-mesh-map-0" />
          <p>15 0 0 16 1 1 14 2 2 8 3 3 16 1 4 7 4 5 1 5 6 16 1 7 0 6 8 0 6 9 16 1 10 15 0 11 9 7 12 16 1 13 8 3 14 2 8 15 16 1 16 1 5 17 10 9 18 16 1 19 9 7 20 3 10 21 16 1 22 2 8 23 11 11 24 16 1 25 10 9 26 4 12 27 16 1 28 3 10 29 12 13 30 16 1 31 11 11 32 5 14 33 16 1 34 4 12 35 13 15 36 16 1 37 12 13 38 6 16 39 16 1 40 5 14 41 14 2 42 16 1 43 13 15 44 7 4 45 16 1 46 6 16 47</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="shiftlight-mesh" name="shiftlight">
      <mesh>
        <source id="shiftlight-mesh-positions">
          <float_array count="243" id="shiftlight-mesh-positions-array">0 0.03499954 0.00999999 0.003826797 0.03499954 0.009238779 0.007071018 0.03499954 0.007071018 0.009238779 0.03499954 0.003826797 0.00999999 0.03499954 0 0.009238779 0.03499954 -0.003826797 0.007071018 0.03499954 -0.007071018 0.003826797 0.03499954 -0.009238779 0 0.03499954 -0.00999999 -0.003826797 0.03499954 -0.009238779 -0.007071018 0.03499954 -0.007071018 -0.009238779 0.03499954 -0.003826797 -0.00999999 0.03499954 0 -0.009238779 0.03499954 0.003826797 -0.007071018 0.03499954 0.007071018 -0.003826797 0.03499954 0.009238779 0 -0.03500038 0.00999999 0.003826856 -0.03500038 0.009238779 0.007071077 -0.03500038 0.007071018 0.009238779 -0.03500038 0.003826797 0.00999999 -0.03500038 0 0.009238779 -0.03500038 -0.003826797 0.007071077 -0.03500038 -0.007071018 0.003826856 -0.03500038 -0.009238779 0 -0.03500038 -0.00999999 -0.003826797 -0.03500038 -0.009238779 -0.007071018 -0.03500038 -0.007071018 -0.009238719 -0.03500038 -0.003826797 -0.00999993 -0.03500038 0 -0.009238719 -0.03500038 0.003826797 -0.007071018 -0.03500038 0.007071018 -0.003826797 -0.03500038 0.009238779 0 0.03499954 0.008323371 0.003185212 0.03499954 0.007689774 0.005885481 0.03499954 0.005885481 0.007689774 0.03499954 0.003185212 0.008323311 0.03499954 0 0.007689774 0.03499954 -0.003185212 0.005885481 0.03499954 -0.005885481 0.003185212 0.03499954 -0.007689774 0 0.03499954 -0.008323371 -0.003185212 0.03499954 -0.007689774 -0.005885481 0.03499954 -0.005885481 -0.007689774 0.03499954 -0.003185212 -0.008323371 0.03499954 0 -0.007689774 0.03499954 0.003185212 -0.005885481 0.03499954 0.005885481 -0.003185212 0.03499954 0.007689774 0 0.02572298 0.008323371 0.003185212 0.02572298 0.007689774 0.005885481 0.02572298 0.005885481 0.007689774 0.02572298 0.003185212 0.008323371 0.02572298 0 0.007689774 0.02572298 -0.003185212 0.005885481 0.02572298 -0.005885481 0.003185212 0.02572298 -0.007689774 0 0.02572298 -0.008323371 -0.003185212 0.02572298 -0.007689774 -0.005885481 0.02572298 -0.005885481 -0.007689774 0.02572298 -0.003185212 -0.008323371 0.02572298 0 -0.007689774 0.02572298 0.003185212 -0.005885481 0.02572298 0.005885481 -0.003185212 0.02572298 0.007689774 0 -0.03717756 0.006772339 0.002591669 -0.03717756 0.006256818 0.004788815 -0.03717756 0.004788756 0.006256878 -0.03717756 0.002591669 0.006772398 -0.03717756 0 0.006256878 -0.03717756 -0.002591669 0.004788815 -0.03717756 -0.004788756 0.002591669 -0.03717756 -0.006256818 0 -0.03717756 -0.006772339 -0.002591609 -0.03717756 -0.006256818 -0.004788756 -0.03717756 -0.004788756 -0.006256818 -0.03717756 -0.002591669 -0.006772339 -0.03717756 0 -0.006256818 -0.03717756 0.002591669 -0.004788756 -0.03717756 0.004788756 -0.002591609 -0.03717756 0.006256818 0 -0.03717756 0</float_array>
          <technique_common>
            <accessor count="81" source="#shiftlight-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="shiftlight-mesh-normals">
          <float_array count="243" id="shiftlight-mesh-normals-array">-0.5276469 0.6657159 0.5276469 -0.3423676 -0.4468052 0.8265287 -0.2855407 0.6657225 0.6894056 0.2855407 0.6657225 -0.6894056 0 -0.4467985 -0.8946346 0 0.6657209 -0.7462008 0.2855407 0.6657225 0.6894056 0 -0.4467985 0.8946346 0.3423676 -0.4468052 0.8265287 0 0.6657209 0.7462008 -0.3423676 -0.4468052 -0.8265287 -0.2855407 0.6657225 -0.6894056 0.6326015 -0.4468007 0.6326015 0.5276469 0.6657159 0.5276469 -0.5276469 0.6657159 -0.5276469 -0.6326015 -0.4468007 -0.6326015 0.8265287 -0.4468052 0.3423676 0.6894056 0.6657225 0.2855407 -0.8265287 -0.4468052 -0.3423676 -0.6894056 0.6657225 -0.2855407 0.8946346 -0.4467985 0 0.7462008 0.6657209 0 -0.8946346 -0.4467985 0 -0.7462008 0.6657209 0 0.8265287 -0.4468052 -0.3423676 0.6894056 0.6657225 -0.2855407 -0.8265287 -0.4468052 0.3423676 -0.6894056 0.6657225 0.2855407 0.6326015 -0.4468007 -0.6326015 0.5276469 0.6657159 -0.5276469 -0.6326015 -0.4468007 0.6326015 0.3423676 -0.4468052 -0.8265287 0 0.7537665 0.6571423 -0.2514791 0.7537658 0.6071206 0 0.7537665 -0.6571423 0.2514791 0.7537658 -0.6071206 -0.4646625 0.7537756 -0.4646625 -0.2514791 0.7537658 -0.6071206 0.4646625 0.7537756 0.4646625 0.2514791 0.7537658 0.6071206 -0.6071206 0.7537658 -0.2514791 0.6071206 0.7537658 0.2514791 0.6571423 0.7537665 0 -0.6071206 0.7537658 0.2514791 -0.6571423 0.7537665 0 0.6071206 0.7537658 -0.2514791 -0.4646625 0.7537756 0.4646625 0.4646625 0.7537756 -0.4646625 1 0 0 0.9238803 0 0.3826816 -0.9238803 0 0.3826816 -1 0 0 -0.7071068 0 0.7071068 0.9238803 0 -0.3826816 0.3826816 0 -0.9238803 0.7071068 0 -0.7071068 -0.3826816 0 0.9238803 0 0 -1 0 0 1 -0.7071068 0 -0.7071068 -0.3826816 0 -0.9238803 0.3826816 0 0.9238803 0.7071068 0 0.7071068 -0.9238803 0 -0.3826816 0.2298403 -0.9456993 -0.2298403 0.1243959 -0.9456959 -0.3003081 -0.1243959 -0.9456959 0.3003081 0 -0.945694 -0.3250585 0 -0.945694 0.3250585 0.1243959 -0.9456959 0.3003081 -0.1243959 -0.9456959 -0.3003081 0.2298403 -0.9456993 0.2298403 -0.2298403 -0.9456993 -0.2298403 0.3003081 -0.9456959 0.1243959 -0.3003081 -0.9456959 -0.1243959 0.3250585 -0.945694 0 -0.3250585 -0.945694 0 0.3003081 -0.9456959 -0.1243959 -0.3003081 -0.9456959 0.1243959 -0.2298403 -0.9456993 0.2298403 0 -1 0</float_array>
          <technique_common>
            <accessor count="81" source="#shiftlight-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="shiftlight-mesh-map-0">
          <float_array count="864" id="shiftlight-mesh-map-0-array">0.3761117 0.3857167 0.6786273 0.4187932 0.3761117 0.4187932 0.3761117 0.4176397 0.6786273 0.4507162 0.3761117 0.4507162 0.3761117 0.219181 0.6786273 0.1861041 0.6786273 0.219181 0.3761117 0.4518695 0.6786273 0.4187932 0.6786273 0.4518695 0.3761117 0.187258 0.6786273 0.2203344 0.3761117 0.2203344 0.3761117 0.219181 0.6786273 0.2522574 0.3761117 0.2522574 0.3761117 0.2534108 0.6786273 0.2203344 0.6786273 0.2534108 0.3761117 0.2522574 0.6786273 0.2853337 0.3761117 0.2853337 0.3761117 0.2534108 0.6786273 0.2864874 0.3761117 0.2864874 0.3761117 0.2853337 0.6786273 0.3184102 0.3761117 0.3184102 0.3761117 0.2864874 0.6786273 0.319564 0.3761117 0.3195641 0.3761117 0.3184102 0.6786273 0.3514868 0.3761117 0.3514868 0.3761117 0.3195641 0.6786273 0.3526403 0.3761117 0.3526403 0.3761117 0.3514868 0.6786273 0.3845633 0.3761117 0.3845633 0.3761117 0.3526403 0.6786273 0.3857167 0.3761117 0.3857167 0.3761117 0.4176397 0.6786273 0.3845633 0.6786273 0.4176397 0.3761117 0.4164183 0.3688659 0.4494947 0.3688659 0.4164183 0.3761117 0.2179595 0.3688659 0.1848828 0.3761117 0.1848828 0.3761117 0.3854817 0.3688659 0.418558 0.3688659 0.3854817 0.3761117 0.450615 0.3688659 0.4175384 0.3761117 0.4175384 0.3761117 0.2179595 0.3688659 0.2510359 0.3688659 0.2179595 0.3761117 0.1870226 0.3688659 0.2200991 0.3688659 0.1870226 0.3761117 0.2510359 0.3688659 0.2841122 0.3688659 0.2510359 0.3761117 0.2200991 0.3688659 0.2531758 0.3688659 0.2200991 0.3761117 0.317189 0.3688659 0.2841122 0.3761117 0.2841122 0.3761117 0.2531758 0.3688659 0.2862523 0.3688659 0.2531758 0.3761117 0.317189 0.3688659 0.3502653 0.3688659 0.317189 0.3761117 0.3193287 0.3688659 0.2862523 0.3761117 0.2862523 0.3761117 0.3833419 0.3688659 0.3502653 0.3761117 0.3502653 0.3761117 0.3524051 0.3688659 0.3193287 0.3761117 0.3193287 0.3761117 0.4164183 0.3688659 0.3833419 0.3761117 0.3833419 0.3761117 0.3854817 0.3688659 0.3524051 0.3761117 0.3524051 0.3688659 0.2531758 0.3287758 0.2862523 0.3287758 0.2531758 0.3688659 0.317189 0.3287758 0.3502653 0.3287758 0.317189 0.3688659 0.3193287 0.3287758 0.2862523 0.3688659 0.2862523 0.3688659 0.3502653 0.3287758 0.3833419 0.3287758 0.3502653 0.3688659 0.3524051 0.3287758 0.3193287 0.3688659 0.3193287 0.3688659 0.4164183 0.3287758 0.3833419 0.3688659 0.3833419 0.3688659 0.3524051 0.3287758 0.3854817 0.3287758 0.3524051 0.3688659 0.4494947 0.3287758 0.4164183 0.3688659 0.4164183 0.3688659 0.2179595 0.3287758 0.1848829 0.3688659 0.1848828 0.3688659 0.418558 0.3287758 0.3854817 0.3688659 0.3854817 0.3688659 0.450615 0.3287758 0.4175385 0.3688659 0.4175384 0.3688659 0.2179595 0.3287758 0.2510359 0.3287758 0.2179595 0.3688659 0.2200991 0.3287758 0.1870226 0.3688659 0.1870226 0.3688659 0.2841122 0.3287758 0.2510359 0.3688659 0.2510359 0.3688659 0.2200991 0.3287758 0.2531758 0.3287758 0.2200991 0.3688659 0.317189 0.3287758 0.2841122 0.3688659 0.2841122 0.6786273 0.4176397 0.6954527 0.3845633 0.6954527 0.4176397 0.6786273 0.3857167 0.6954527 0.4187932 0.6786273 0.4187932 0.6786273 0.4176397 0.6954527 0.4507162 0.6786273 0.4507162 0.6786273 0.219181 0.6954527 0.1861041 0.6954527 0.219181 0.6786273 0.4187932 0.6954527 0.4518695 0.6786273 0.4518695 0.6786273 0.2203344 0.6954527 0.187258 0.6954527 0.2203344 0.6786273 0.219181 0.6954527 0.2522574 0.6786273 0.2522574 0.6786273 0.2534108 0.6954527 0.2203344 0.6954527 0.2534108 0.6786273 0.2522574 0.6954527 0.2853337 0.6786273 0.2853337 0.6786273 0.2864874 0.6954527 0.2534108 0.6954527 0.2864874 0.6786273 0.3184102 0.6954527 0.2853337 0.6954527 0.3184102 0.6786273 0.2864874 0.6954527 0.319564 0.6786273 0.319564 0.6786273 0.3184102 0.6954527 0.3514868 0.6786273 0.3514868 0.6786273 0.3526403 0.6954527 0.319564 0.6954527 0.3526403 0.6786273 0.3845633 0.6954527 0.3514868 0.6954527 0.3845633 0.6786273 0.3857167 0.6954527 0.3526403 0.6954527 0.3857167 1.098913 0.8635805 1.118508 0.889766 1.075799 0.9751836 1.052685 1.086787 1.033091 1.060601 1.075799 0.9751836 1.075799 0.8543853 1.098913 0.8635805 1.075799 0.9751836 1.098913 1.086787 1.075799 1.095982 1.075799 0.9751836 1.075799 1.095982 1.052685 1.086787 1.075799 0.9751836 1.052685 0.8635805 1.075799 0.8543853 1.075799 0.9751836 1.118508 1.060601 1.098913 1.086787 1.075799 0.9751836 1.033091 0.889766 1.052685 0.8635805 1.075799 0.9751836 1.1316 1.021411 1.118508 1.060601 1.075799 0.9751836 1.019998 0.9289559 1.033091 0.889766 1.075799 0.9751836 1.136198 0.9751836 1.1316 1.021411 1.075799 0.9751836 1.0154 0.9751836 1.019998 0.9289559 1.075799 0.9751836 1.1316 0.9289559 1.136198 0.9751836 1.075799 0.9751836 1.019998 1.021411 1.0154 0.9751836 1.075799 0.9751836 1.118508 0.889766 1.1316 0.9289559 1.075799 0.9751836 1.033091 1.060601 1.019998 1.021411 1.075799 0.9751836 0.3761117 0.3857167 0.6786273 0.3857167 0.6786273 0.4187932 0.3761117 0.4176397 0.6786273 0.4176397 0.6786273 0.4507162 0.3761117 0.219181 0.3761117 0.1861041 0.6786273 0.1861041 0.3761117 0.4518695 0.3761117 0.4187932 0.6786273 0.4187932 0.3761117 0.187258 0.6786273 0.187258 0.6786273 0.2203344 0.3761117 0.219181 0.6786273 0.219181 0.6786273 0.2522574 0.3761117 0.2534108 0.3761117 0.2203344 0.6786273 0.2203344 0.3761117 0.2522574 0.6786273 0.2522574 0.6786273 0.2853337 0.3761117 0.2534108 0.6786273 0.2534108 0.6786273 0.2864874 0.3761117 0.2853337 0.6786273 0.2853337 0.6786273 0.3184102 0.3761117 0.2864874 0.6786273 0.2864874 0.6786273 0.319564 0.3761117 0.3184102 0.6786273 0.3184102 0.6786273 0.3514868 0.3761117 0.3195641 0.6786273 0.319564 0.6786273 0.3526403 0.3761117 0.3514868 0.6786273 0.3514868 0.6786273 0.3845633 0.3761117 0.3526403 0.6786273 0.3526403 0.6786273 0.3857167 0.3761117 0.4176397 0.3761117 0.3845633 0.6786273 0.3845633 0.3761117 0.4164183 0.3761117 0.4494947 0.3688659 0.4494947 0.3761117 0.2179595 0.3688659 0.2179595 0.3688659 0.1848828 0.3761117 0.3854817 0.3761117 0.418558 0.3688659 0.418558 0.3761117 0.450615 0.3688659 0.450615 0.3688659 0.4175384 0.3761117 0.2179595 0.3761117 0.2510359 0.3688659 0.2510359 0.3761117 0.1870226 0.3761117 0.2200991 0.3688659 0.2200991 0.3761117 0.2510359 0.3761117 0.2841122 0.3688659 0.2841122 0.3761117 0.2200991 0.3761117 0.2531758 0.3688659 0.2531758 0.3761117 0.317189 0.3688659 0.317189 0.3688659 0.2841122 0.3761117 0.2531758 0.3761117 0.2862523 0.3688659 0.2862523 0.3761117 0.317189 0.3761117 0.3502653 0.3688659 0.3502653 0.3761117 0.3193287 0.3688659 0.3193287 0.3688659 0.2862523 0.3761117 0.3833419 0.3688659 0.3833419 0.3688659 0.3502653 0.3761117 0.3524051 0.3688659 0.3524051 0.3688659 0.3193287 0.3761117 0.4164183 0.3688659 0.4164183 0.3688659 0.3833419 0.3761117 0.3854817 0.3688659 0.3854817 0.3688659 0.3524051 0.3688659 0.2531758 0.3688659 0.2862523 0.3287758 0.2862523 0.3688659 0.317189 0.3688659 0.3502653 0.3287758 0.3502653 0.3688659 0.3193287 0.3287758 0.3193287 0.3287758 0.2862523 0.3688659 0.3502653 0.3688659 0.3833419 0.3287758 0.3833419 0.3688659 0.3524051 0.3287758 0.3524051 0.3287758 0.3193287 0.3688659 0.4164183 0.3287758 0.4164183 0.3287758 0.3833419 0.3688659 0.3524051 0.3688659 0.3854817 0.3287758 0.3854817 0.3688659 0.4494947 0.3287758 0.4494948 0.3287758 0.4164183 0.3688659 0.2179595 0.3287758 0.2179595 0.3287758 0.1848829 0.3688659 0.418558 0.3287758 0.418558 0.3287758 0.3854817 0.3688659 0.450615 0.3287758 0.450615 0.3287758 0.4175385 0.3688659 0.2179595 0.3688659 0.2510359 0.3287758 0.2510359 0.3688659 0.2200991 0.3287758 0.2200991 0.3287758 0.1870226 0.3688659 0.2841122 0.3287758 0.2841122 0.3287758 0.2510359 0.3688659 0.2200991 0.3688659 0.2531758 0.3287758 0.2531758 0.3688659 0.317189 0.3287758 0.317189 0.3287758 0.2841122 0.6786273 0.4176397 0.6786273 0.3845633 0.6954527 0.3845633 0.6786273 0.3857167 0.6954527 0.3857167 0.6954527 0.4187932 0.6786273 0.4176397 0.6954527 0.4176397 0.6954527 0.4507162 0.6786273 0.219181 0.6786273 0.1861041 0.6954527 0.1861041 0.6786273 0.4187932 0.6954527 0.4187932 0.6954527 0.4518695 0.6786273 0.2203344 0.6786273 0.187258 0.6954527 0.187258 0.6786273 0.219181 0.6954527 0.219181 0.6954527 0.2522574 0.6786273 0.2534108 0.6786273 0.2203344 0.6954527 0.2203344 0.6786273 0.2522574 0.6954527 0.2522574 0.6954527 0.2853337 0.6786273 0.2864874 0.6786273 0.2534108 0.6954527 0.2534108 0.6786273 0.3184102 0.6786273 0.2853337 0.6954527 0.2853337 0.6786273 0.2864874 0.6954527 0.2864874 0.6954527 0.319564 0.6786273 0.3184102 0.6954527 0.3184102 0.6954527 0.3514868 0.6786273 0.3526403 0.6786273 0.319564 0.6954527 0.319564 0.6786273 0.3845633 0.6786273 0.3514868 0.6954527 0.3514868 0.6786273 0.3857167 0.6786273 0.3526403 0.6954527 0.3526403</float_array>
          <technique_common>
            <accessor count="432" source="#shiftlight-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="shiftlight-mesh-vertices">
          <input semantic="POSITION" source="#shiftlight-mesh-positions" />
        </vertices>
        <triangles count="144" material="shiftlight-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight-mesh-map-0" />
          <p>14 0 0 31 1 1 15 2 2 7 3 3 24 4 4 8 5 5 1 6 6 16 7 7 17 8 8 0 9 9 31 1 10 16 7 11 8 5 12 25 10 13 9 11 14 1 6 15 18 12 16 2 13 17 10 14 18 25 10 19 26 15 20 2 13 21 19 16 22 3 17 23 10 14 24 27 18 25 11 19 26 3 17 27 20 20 28 4 21 29 11 19 30 28 22 31 12 23 32 4 21 33 21 24 34 5 25 35 12 23 36 29 26 37 13 27 38 5 25 39 22 28 40 6 29 41 13 27 42 30 30 43 14 0 44 7 3 45 22 28 46 23 31 47 7 3 48 40 32 49 39 33 50 1 6 51 32 34 52 0 9 53 15 2 54 32 34 55 47 35 56 9 11 57 40 32 58 8 5 59 1 6 60 34 36 61 33 37 62 9 11 63 42 38 64 41 39 65 2 13 66 35 40 67 34 36 68 10 14 69 43 41 70 42 38 71 4 21 72 35 40 73 3 17 74 11 19 75 44 42 76 43 41 77 4 21 78 37 43 79 36 44 80 13 27 81 44 42 82 12 23 83 6 29 84 37 43 85 5 25 86 14 0 87 45 45 88 13 27 89 7 3 90 38 46 91 6 29 92 15 2 93 46 47 94 14 0 95 43 41 96 60 48 97 59 49 98 36 44 99 53 50 100 52 51 101 45 45 102 60 48 103 44 42 104 37 43 105 54 52 106 53 50 107 46 47 108 61 53 109 45 45 110 39 33 111 54 52 112 38 46 113 46 47 114 63 54 115 62 55 116 40 32 117 55 56 118 39 33 119 33 37 120 48 57 121 32 34 122 32 34 123 63 54 124 47 35 125 41 39 126 56 58 127 40 32 128 33 37 129 50 59 130 49 60 131 42 38 132 57 61 133 41 39 134 35 40 135 50 59 136 34 36 137 42 38 138 59 49 139 58 62 140 36 44 141 51 63 142 35 40 143 23 31 144 70 64 145 71 65 146 30 30 147 79 66 148 31 1 149 23 31 150 72 67 151 24 4 152 17 8 153 64 68 154 65 69 155 31 1 156 64 68 157 16 7 158 25 10 159 72 67 160 73 70 161 17 8 162 66 71 163 18 12 164 26 15 165 73 70 166 74 72 167 18 12 168 67 73 169 19 16 170 27 18 171 74 72 172 75 74 173 20 20 174 67 73 175 68 75 176 27 18 177 76 76 178 28 22 179 20 20 180 69 77 181 21 24 182 29 26 183 76 76 184 77 78 185 22 28 186 69 77 187 70 64 188 30 30 189 77 78 190 78 79 191 71 65 192 70 64 193 80 80 194 79 66 195 78 79 196 80 80 197 72 67 198 71 65 199 80 80 200 65 69 201 64 68 202 80 80 203 64 68 204 79 66 205 80 80 206 73 70 207 72 67 208 80 80 209 66 71 210 65 69 211 80 80 212 74 72 213 73 70 214 80 80 215 67 73 216 66 71 217 80 80 218 75 74 219 74 72 220 80 80 221 68 75 222 67 73 223 80 80 224 76 76 225 75 74 226 80 80 227 69 77 228 68 75 229 80 80 230 77 78 231 76 76 232 80 80 233 70 64 234 69 77 235 80 80 236 78 79 237 77 78 238 80 80 239 14 0 240 30 30 241 31 1 242 7 3 243 23 31 244 24 4 245 1 6 246 0 9 247 16 7 248 0 9 249 15 2 250 31 1 251 8 5 252 24 4 253 25 10 254 1 6 255 17 8 256 18 12 257 10 14 258 9 11 259 25 10 260 2 13 261 18 12 262 19 16 263 10 14 264 26 15 265 27 18 266 3 17 267 19 16 268 20 20 269 11 19 270 27 18 271 28 22 272 4 21 273 20 20 274 21 24 275 12 23 276 28 22 277 29 26 278 5 25 279 21 24 280 22 28 281 13 27 282 29 26 283 30 30 284 7 3 285 6 29 286 22 28 287 7 3 288 8 5 289 40 32 290 1 6 291 33 37 292 32 34 293 15 2 294 0 9 295 32 34 296 9 11 297 41 39 298 40 32 299 1 6 300 2 13 301 34 36 302 9 11 303 10 14 304 42 38 305 2 13 306 3 17 307 35 40 308 10 14 309 11 19 310 43 41 311 4 21 312 36 44 313 35 40 314 11 19 315 12 23 316 44 42 317 4 21 318 5 25 319 37 43 320 13 27 321 45 45 322 44 42 323 6 29 324 38 46 325 37 43 326 14 0 327 46 47 328 45 45 329 7 3 330 39 33 331 38 46 332 15 2 333 47 35 334 46 47 335 43 41 336 44 42 337 60 48 338 36 44 339 37 43 340 53 50 341 45 45 342 61 53 343 60 48 344 37 43 345 38 46 346 54 52 347 46 47 348 62 55 349 61 53 350 39 33 351 55 56 352 54 52 353 46 47 354 47 35 355 63 54 356 40 32 357 56 58 358 55 56 359 33 37 360 49 60 361 48 57 362 32 34 363 48 57 364 63 54 365 41 39 366 57 61 367 56 58 368 33 37 369 34 36 370 50 59 371 42 38 372 58 62 373 57 61 374 35 40 375 51 63 376 50 59 377 42 38 378 43 41 379 59 49 380 36 44 381 52 51 382 51 63 383 23 31 384 22 28 385 70 64 386 30 30 387 78 79 388 79 66 389 23 31 390 71 65 391 72 67 392 17 8 393 16 7 394 64 68 395 31 1 396 79 66 397 64 68 398 25 10 399 24 4 400 72 67 401 17 8 402 65 69 403 66 71 404 26 15 405 25 10 406 73 70 407 18 12 408 66 71 409 67 73 410 27 18 411 26 15 412 74 72 413 20 20 414 19 16 415 67 73 416 27 18 417 75 74 418 76 76 419 20 20 420 68 75 421 69 77 422 29 26 423 28 22 424 76 76 425 22 28 426 21 24 427 69 77 428 30 30 429 29 26 430 77 78 431</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="shiftlight_multi-mesh" name="shiftlight_multi">
      <mesh>
        <source id="shiftlight_multi-mesh-positions">
          <float_array count="264" id="shiftlight_multi-mesh-positions-array">-0.04559934 -0.006749987 0.01465523 -0.04479163 -0.006749987 0.01280087 -0.04318428 -0.006749987 0.0114817 0.04120802 -0.006749927 0.01105117 0.0455994 -0.006749927 0.01465523 0.04318434 -0.006749927 0.0114817 0.04479163 -0.006749927 0.01280087 -0.04430001 -0.006749987 0.02957099 -0.04867428 -0.006749987 0.0238775 -0.0467894 -0.006749987 0.0286135 -0.0483905 -0.006749987 0.02652949 0.04867434 -0.006749927 0.0238775 0.04430001 -0.006749927 0.02957099 0.0483905 -0.006749927 0.02652949 0.0467894 -0.006749927 0.0286135 -0.04318434 0.006749927 0.0114817 -0.04120802 0.006749927 0.01105117 -0.04479163 0.006749927 0.01280087 -0.0455994 0.006749927 0.01465523 0.04120802 0.006749987 0.01105117 0.04318428 0.006749987 0.0114817 0.04479163 0.006749987 0.01280087 0.04559934 0.006749987 0.01465523 -0.0483905 0.006749927 0.02652949 -0.04867434 0.006749927 0.0238775 -0.0467894 0.006749927 0.0286135 -0.04430001 0.006749927 0.02957099 0.04867428 0.006749987 0.0238775 0.0483905 0.006749987 0.02652949 0.0467894 0.006749987 0.0286135 0.04430001 0.006749987 0.02957099 -0.04253089 0.006749927 0.01283138 -0.04107826 0.006749927 0.01251488 -0.04359537 0.006749927 0.01370501 -0.04418903 0.006749927 0.01506799 0.04107826 0.006749987 0.01251488 0.04253089 0.006749987 0.01283138 0.04359531 0.006749987 0.01370501 0.04418903 0.006749987 0.01506799 -0.04698461 0.006749927 0.02596658 -0.04720407 0.006749927 0.02391576 -0.04588323 0.006749927 0.02740007 -0.04395818 0.006749927 0.02814054 0.04720407 0.006749987 0.02391576 0.04698455 0.006749987 0.02596658 0.04588323 0.006749987 0.02740007 0.04395818 0.006749987 0.02814054 -0.04062259 -0.006749987 0.01105117 -0.02060401 0.006749927 0.01164722 0 0.006749987 0.01183843 0.02060401 0.006749987 0.01164722 -0.02214998 -0.006749987 0.03137046 0 -0.006749987 0.03208464 0.02215003 -0.006749927 0.03137046 0.02214998 0.006749987 0.03137046 0 0.006749987 0.03208464 -0.02215003 0.006749927 0.03137046 -0.0205391 0.006749927 0.01311099 0 0.006749987 0.01330214 0.0205391 0.006749987 0.01311099 0.02197909 0.006749987 0.02994 0 0.006749987 0.03065419 -0.02197909 0.006749927 0.02994 0.0207504 -0.006749927 0.01164722 2.92732e-4 -0.006749987 0.01183843 -0.0201649 -0.006749987 0.01164722 -0.04559934 -0.006749987 0.01465523 -0.04479163 -0.006749987 0.01280087 -0.04318428 -0.006749987 0.0114817 0.04120802 -0.006749927 0.01105117 0.0455994 -0.006749927 0.01465523 0.04318434 -0.006749927 0.0114817 0.04479163 -0.006749927 0.01280087 -0.04430001 -0.006749987 0.02957099 -0.04867428 -0.006749987 0.0238775 -0.0467894 -0.006749987 0.0286135 -0.0483905 -0.006749987 0.02652949 0.04867434 -0.006749927 0.0238775 0.04430001 -0.006749927 0.02957099 0.0483905 -0.006749927 0.02652949 0.0467894 -0.006749927 0.0286135 -0.04062259 -0.006749987 0.01105117 -0.02214998 -0.006749987 0.03137046 0 -0.006749987 0.03208464 0.02215003 -0.006749927 0.03137046 0.0207504 -0.006749927 0.01164722 2.92732e-4 -0.006749987 0.01183843 -0.0201649 -0.006749987 0.01164722</float_array>
          <technique_common>
            <accessor count="88" source="#shiftlight_multi-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="shiftlight_multi-mesh-normals">
          <float_array count="138" id="shiftlight_multi-mesh-normals-array">0 1 0 0.6692813 0.6972367 -0.2567562 -0.6692813 0.6972367 -0.2567562 0.9336575 0 -0.3581671 0.7449548 0.6622465 -0.08044987 0.9942163 0 -0.1073962 -0.9942163 0 -0.1073962 -0.9336575 0 -0.3581671 0.05661261 0 0.9983963 0.163708 0.6763792 0.7181302 0.04031562 0.7016627 0.7113678 -0.0192272 -1.22078e-4 -0.9998152 0.06781274 0.680752 -0.7293683 0.09253329 0 -0.9957096 -0.0681501 0.6808298 -0.7292643 -0.4160964 -0.003357052 -0.9093144 -0.3281142 0.6579985 -0.6777751 -0.7977936 0 -0.6029307 -0.6007509 0.6580059 -0.4540119 0.3281142 0.6579985 -0.6777751 0.4357508 0 -0.9000675 0.6007509 0.6580059 -0.4540119 0.7977936 0 -0.6029307 -0.7047544 0.6509181 0.2821825 -0.7449684 0.6622314 -0.08044803 -0.9283462 0 0.3717169 -0.4542248 0.6509243 0.6082577 -0.5983629 0 0.8012253 -0.1637044 0.6763643 0.718145 0.9283462 0 0.3717169 0.7047544 0.6509181 0.2821825 0.5983629 0 0.8012253 0.4542248 0.6509243 0.6082577 0.2222397 0 0.9749922 -0.01342844 0.709237 -0.7048423 -0.04031562 0.7016627 0.7113678 0 0.6999292 0.7142123 0.01364201 0.7095695 -0.7045034 9.15582e-5 0.7092095 -0.7049978 -0.06601232 -0.002899289 -0.9978147 0.01898252 5.79853e-4 -0.9998196 -9.15555e-5 0 -1 -0.2222397 0 0.9749922 -0.05661261 0 0.9983963 0 0 1 0 -1 0</float_array>
          <technique_common>
            <accessor count="46" source="#shiftlight_multi-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="shiftlight_multi-mesh-map-0">
          <float_array count="768" id="shiftlight_multi-mesh-map-0-array">0.01924842 0.8040249 0.03558319 0.615495 0.04993933 0.6238979 0.9639196 0.615495 0.9802544 0.8040249 0.9495636 0.6238979 0.6761615 0.00716257 0.6323755 0.06578671 0.6323755 0.007047593 0.1764842 0.005850791 0.1326982 0.06743919 0.1326983 0.005735814 0.5017024 0.006704509 0.5991331 0.06589663 0.5017024 0.06621885 0.7988371 0.007484555 0.7014072 0.06555837 0.7014073 0.007228791 0.1065778 0.06752562 0.1158658 0.005691647 0.1158657 0.06749492 0.1158657 0.06749492 0.124285 0.005713701 0.1242849 0.06746703 0.1242849 0.06746703 0.1326983 0.005735814 0.1326982 0.06743919 0.7014073 0.007228791 0.6929939 0.0655862 0.692994 0.007206737 0.692994 0.007206737 0.6845747 0.06561404 0.6845748 0.007184624 0.6845748 0.007184624 0.6761614 0.06564193 0.6761615 0.00716257 0.1764842 0.005850791 0.1877338 0.06725722 0.1764842 0.06729441 0.1877339 0.005880296 0.1984769 0.0672217 0.1877338 0.06725722 0.1984769 0.005908489 0.2097265 0.0671845 0.1984769 0.0672217 0.6323755 0.06578671 0.6211259 0.007018029 0.6323755 0.007047593 0.6211258 0.06582391 0.6103829 0.006989836 0.6211259 0.007018029 0.6103827 0.06585943 0.5991332 0.006960332 0.6103829 0.006989836 0.08160465 0.5719206 0.2900177 0.5542562 0.290678 0.5840559 0.9472136 0.8900364 0.7252225 0.9557926 0.7234825 0.9266706 0.9326847 0.5783633 0.9192191 0.5421208 0.9393362 0.5508863 0.9326847 0.5783633 0.9556977 0.5777431 0.9435202 0.5961493 0.9435202 0.5961493 0.9639196 0.615495 0.9495636 0.6238979 0.06681829 0.5783633 0.08028388 0.5421208 0.08160465 0.5719206 0.06681829 0.5783633 0.04380518 0.5777431 0.06016677 0.5508863 0.05598264 0.5961493 0.03558319 0.615495 0.04380518 0.5777431 0.9780201 0.8457769 0.9952204 0.8032465 0.9923312 0.8572376 0.9668092 0.8749613 0.9923312 0.8572376 0.9760331 0.8996641 0.9668092 0.8749613 0.9506934 0.9191581 0.9472136 0.8900364 0.02148276 0.8457769 0.004282653 0.8032465 0.01924842 0.8040249 0.02148276 0.8457769 0.02346974 0.8996641 0.007171928 0.8572376 0.0326938 0.8749613 0.04880946 0.9191581 0.02346974 0.8996641 0.9668092 0.8749613 0.9472136 0.8900364 0.9802544 0.8040249 0.05228942 0.8900364 0.2742805 0.9557926 0.04880946 0.9191581 0.2760204 0.9266706 0.4997515 0.9703319 0.2742805 0.9557926 0.7234825 0.9266706 0.4997515 0.9703319 0.4997515 0.94121 0.9178983 0.5719206 0.7094853 0.5542562 0.9192191 0.5421208 0.7088248 0.5840559 0.4997515 0.5581485 0.7094853 0.5542562 0.290678 0.5840559 0.4997515 0.5581485 0.4997515 0.5879482 0.1065779 0.005667209 0.009146928 0.06784796 0.009146988 0.005411446 0.9933804 0.007995188 0.8961087 0.06491422 0.8961087 0.007739901 0.8961087 0.007739901 0.7988371 0.06523603 0.7988371 0.007484555 0.2097266 0.005938053 0.3071573 0.06686228 0.2097265 0.0671845 0.3071574 0.006193816 0.4044299 0.06654053 0.3071573 0.06686228 0.4044299 0.006449162 0.5017024 0.06621885 0.4044299 0.06654053 0.2742807 0.5086674 0.08624356 0.09499597 0.2944875 0.1071309 0.04993933 0.6238979 0.05598264 0.5961493 0.06681829 0.5783633 0.03558349 0.1683697 0.04380548 0.1306178 0.06016701 0.103761 0.9923315 0.4101125 0.9760334 0.4525389 0.9506937 0.4720329 0.290678 0.5840559 0.4997515 0.94121 0.2760204 0.9266706 0.4997518 0.5232067 0.7109753 0.1071309 0.7252227 0.5086674 0.08160465 0.5719206 0.2760204 0.9266706 0.05228942 0.8900364 0.9178983 0.5719206 0.7234825 0.9266706 0.7088248 0.5840559 0.7252227 0.5086674 0.9192193 0.09499597 0.9506937 0.4720329 0.4997515 0.94121 0.7088248 0.5840559 0.7234825 0.9266706 0.4997518 0.5232067 0.2944875 0.1071309 0.5027313 0.1110234 0.01924842 0.8040249 0.004282653 0.8032465 0.03558319 0.615495 0.9639196 0.615495 0.9952204 0.8032465 0.9802544 0.8040249 0.6761615 0.00716257 0.6761614 0.06564193 0.6323755 0.06578671 0.1764842 0.005850791 0.1764842 0.06729441 0.1326982 0.06743919 0.5017024 0.006704509 0.5991332 0.006960332 0.5991331 0.06589663 0.7988371 0.007484555 0.7988371 0.06523603 0.7014072 0.06555837 0.1065778 0.06752562 0.1065779 0.005667209 0.1158658 0.005691647 0.1158657 0.06749492 0.1158658 0.005691647 0.124285 0.005713701 0.1242849 0.06746703 0.124285 0.005713701 0.1326983 0.005735814 0.7014073 0.007228791 0.7014072 0.06555837 0.6929939 0.0655862 0.692994 0.007206737 0.6929939 0.0655862 0.6845747 0.06561404 0.6845748 0.007184624 0.6845747 0.06561404 0.6761614 0.06564193 0.1764842 0.005850791 0.1877339 0.005880296 0.1877338 0.06725722 0.1877339 0.005880296 0.1984769 0.005908489 0.1984769 0.0672217 0.1984769 0.005908489 0.2097266 0.005938053 0.2097265 0.0671845 0.6323755 0.06578671 0.6211258 0.06582391 0.6211259 0.007018029 0.6211258 0.06582391 0.6103827 0.06585943 0.6103829 0.006989836 0.6103827 0.06585943 0.5991331 0.06589663 0.5991332 0.006960332 0.08160465 0.5719206 0.08028388 0.5421208 0.2900177 0.5542562 0.9472136 0.8900364 0.9506934 0.9191581 0.7252225 0.9557926 0.9326847 0.5783633 0.9178983 0.5719206 0.9192191 0.5421208 0.9326847 0.5783633 0.9393362 0.5508863 0.9556977 0.5777431 0.9435202 0.5961493 0.9556977 0.5777431 0.9639196 0.615495 0.06681829 0.5783633 0.06016677 0.5508863 0.08028388 0.5421208 0.06681829 0.5783633 0.05598264 0.5961493 0.04380518 0.5777431 0.05598264 0.5961493 0.04993933 0.6238979 0.03558319 0.615495 0.9780201 0.8457769 0.9802544 0.8040249 0.9952204 0.8032465 0.9668092 0.8749613 0.9780201 0.8457769 0.9923312 0.8572376 0.9668092 0.8749613 0.9760331 0.8996641 0.9506934 0.9191581 0.02148276 0.8457769 0.007171928 0.8572376 0.004282653 0.8032465 0.02148276 0.8457769 0.0326938 0.8749613 0.02346974 0.8996641 0.0326938 0.8749613 0.05228942 0.8900364 0.04880946 0.9191581 0.9178983 0.5719206 0.9326847 0.5783633 0.9435202 0.5961493 0.9435202 0.5961493 0.9495636 0.6238979 0.9178983 0.5719206 0.9495636 0.6238979 0.9802544 0.8040249 0.9472136 0.8900364 0.9178983 0.5719206 0.9495636 0.6238979 0.9472136 0.8900364 0.9802544 0.8040249 0.9780201 0.8457769 0.9668092 0.8749613 0.05228942 0.8900364 0.2760204 0.9266706 0.2742805 0.9557926 0.2760204 0.9266706 0.4997515 0.94121 0.4997515 0.9703319 0.7234825 0.9266706 0.7252225 0.9557926 0.4997515 0.9703319 0.9178983 0.5719206 0.7088248 0.5840559 0.7094853 0.5542562 0.7088248 0.5840559 0.4997515 0.5879482 0.4997515 0.5581485 0.290678 0.5840559 0.2900177 0.5542562 0.4997515 0.5581485 0.1065779 0.005667209 0.1065778 0.06752562 0.009146928 0.06784796 0.9933804 0.007995188 0.9933803 0.06459242 0.8961087 0.06491422 0.8961087 0.007739901 0.8961087 0.06491422 0.7988371 0.06523603 0.2097266 0.005938053 0.3071574 0.006193816 0.3071573 0.06686228 0.3071574 0.006193816 0.4044299 0.006449162 0.4044299 0.06654053 0.4044299 0.006449162 0.5017024 0.006704509 0.5017024 0.06621885 0.2742807 0.5086674 0.04880982 0.4720329 0.08624356 0.09499597 0.06681829 0.5783633 0.08160465 0.5719206 0.04993933 0.6238979 0.08160465 0.5719206 0.05228942 0.8900364 0.04993933 0.6238979 0.05228942 0.8900364 0.0326938 0.8749613 0.02148276 0.8457769 0.02148276 0.8457769 0.01924842 0.8040249 0.05228942 0.8900364 0.01924842 0.8040249 0.04993933 0.6238979 0.05228942 0.8900364 0.06016701 0.103761 0.08624356 0.09499597 0.03558349 0.1683697 0.08624356 0.09499597 0.04880982 0.4720329 0.03558349 0.1683697 0.04880982 0.4720329 0.02346998 0.4525389 0.007172048 0.4101125 0.007172048 0.4101125 0.004282891 0.3561213 0.04880982 0.4720329 0.004282891 0.3561213 0.03558349 0.1683697 0.04880982 0.4720329 0.9506937 0.4720329 0.9192193 0.09499597 0.9639198 0.1683697 0.9192193 0.09499597 0.9393362 0.103761 0.9639198 0.1683697 0.9393362 0.103761 0.9556978 0.1306178 0.9639198 0.1683697 0.9639198 0.1683697 0.9952206 0.3561213 0.9506937 0.4720329 0.9952206 0.3561213 0.9923315 0.4101125 0.9506937 0.4720329 0.290678 0.5840559 0.4997515 0.5879482 0.4997515 0.94121 0.4997518 0.5232067 0.5027313 0.1110234 0.7109753 0.1071309 0.08160465 0.5719206 0.290678 0.5840559 0.2760204 0.9266706 0.9178983 0.5719206 0.9472136 0.8900364 0.7234825 0.9266706 0.7252227 0.5086674 0.7109753 0.1071309 0.9192193 0.09499597 0.4997515 0.94121 0.4997515 0.5879482 0.7088248 0.5840559 0.4997518 0.5232067 0.2742807 0.5086674 0.2944875 0.1071309</float_array>
          <technique_common>
            <accessor count="384" source="#shiftlight_multi-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="shiftlight_multi-mesh-vertices">
          <input semantic="POSITION" source="#shiftlight_multi-mesh-positions" />
        </vertices>
        <triangles count="128" material="shiftlight_multi-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi-mesh-map-0" />
          <p>43 0 0 22 1 1 38 0 2 18 2 3 40 0 4 34 0 5 4 3 6 27 4 7 11 5 8 8 6 9 18 2 10 0 7 11 53 8 12 30 9 13 54 10 14 63 11 15 19 12 16 3 13 17 16 14 18 2 15 19 15 16 20 15 16 21 1 17 22 17 18 23 17 18 24 0 7 25 18 2 26 3 13 27 20 19 28 5 20 29 5 20 30 21 21 31 6 22 32 6 22 33 22 1 34 4 3 35 8 6 36 23 23 37 24 24 38 10 25 39 25 26 40 23 23 41 9 27 42 26 28 43 25 26 44 27 4 45 13 29 46 11 5 47 28 30 48 14 31 49 13 29 50 29 32 51 12 33 52 14 31 53 35 0 54 50 34 55 59 0 56 42 0 57 56 35 58 62 0 59 31 0 60 16 14 61 15 16 62 31 0 63 17 18 64 33 0 65 33 0 66 18 2 67 34 0 68 36 0 69 19 12 70 35 0 71 36 0 72 21 21 73 20 19 74 37 0 75 22 1 76 21 21 77 39 0 78 24 24 79 23 23 80 41 0 81 23 23 82 25 26 83 41 0 84 26 28 85 42 0 86 44 0 87 27 4 88 43 0 89 44 0 90 29 32 91 28 30 92 45 0 93 30 9 94 29 32 95 41 0 96 42 0 97 40 0 98 46 0 99 54 10 100 30 9 101 60 0 102 55 36 103 54 10 104 62 0 105 55 36 106 61 0 107 32 0 108 48 37 109 16 14 110 57 0 111 49 38 112 48 37 113 59 0 114 49 38 115 58 0 116 47 39 117 48 37 118 65 40 119 65 40 120 49 38 121 64 41 122 64 41 123 50 34 124 63 11 125 7 42 126 56 35 127 26 28 128 51 43 129 55 36 130 56 35 131 52 44 132 54 10 133 55 36 134 82 45 135 81 45 136 87 45 137 38 0 138 37 0 139 36 0 140 66 45 141 67 45 142 68 45 143 79 45 144 80 45 145 78 45 146 59 0 147 61 0 148 60 0 149 83 45 150 85 45 151 84 45 152 35 0 153 60 0 154 46 0 155 32 0 156 62 0 157 57 0 158 84 45 159 69 45 160 78 45 161 61 0 162 57 0 163 62 0 164 83 45 165 87 45 166 86 45 167 43 0 168 27 4 169 22 1 170 18 2 171 24 24 172 40 0 173 4 3 174 22 1 175 27 4 176 8 6 177 24 24 178 18 2 179 53 8 180 12 33 181 30 9 182 63 11 183 50 34 184 19 12 185 16 14 186 47 39 187 2 15 188 15 16 189 2 15 190 1 17 191 17 18 192 1 17 193 0 7 194 3 13 195 19 12 196 20 19 197 5 20 198 20 19 199 21 21 200 6 22 201 21 21 202 22 1 203 8 6 204 10 25 205 23 23 206 10 25 207 9 27 208 25 26 209 9 27 210 7 42 211 26 28 212 27 4 213 28 30 214 13 29 215 28 30 216 29 32 217 14 31 218 29 32 219 30 9 220 12 33 221 35 0 222 19 12 223 50 34 224 42 0 225 26 28 226 56 35 227 31 0 228 32 0 229 16 14 230 31 0 231 15 16 232 17 18 233 33 0 234 17 18 235 18 2 236 36 0 237 20 19 238 19 12 239 36 0 240 37 0 241 21 21 242 37 0 243 38 0 244 22 1 245 39 0 246 40 0 247 24 24 248 41 0 249 39 0 250 23 23 251 41 0 252 25 26 253 26 28 254 44 0 255 28 30 256 27 4 257 44 0 258 45 0 259 29 32 260 45 0 261 46 0 262 30 9 263 32 0 264 31 0 265 33 0 266 33 0 267 34 0 268 32 0 269 34 0 270 40 0 271 42 0 272 32 0 273 34 0 274 42 0 275 40 0 276 39 0 277 41 0 278 46 0 279 60 0 280 54 10 281 60 0 282 61 0 283 55 36 284 62 0 285 56 35 286 55 36 287 32 0 288 57 0 289 48 37 290 57 0 291 58 0 292 49 38 293 59 0 294 50 34 295 49 38 296 47 39 297 16 14 298 48 37 299 65 40 300 48 37 301 49 38 302 64 41 303 49 38 304 50 34 305 7 42 306 51 43 307 56 35 308 51 43 309 52 44 310 55 36 311 52 44 312 53 8 313 54 10 314 82 45 315 73 45 316 81 45 317 36 0 318 35 0 319 38 0 320 35 0 321 46 0 322 38 0 323 46 0 324 45 0 325 44 0 326 44 0 327 43 0 328 46 0 329 43 0 330 38 0 331 46 0 332 68 45 333 81 45 334 66 45 335 81 45 336 73 45 337 66 45 338 73 45 339 75 45 340 76 45 341 76 45 342 74 45 343 73 45 344 74 45 345 66 45 346 73 45 347 78 45 348 69 45 349 70 45 350 69 45 351 71 45 352 70 45 353 71 45 354 72 45 355 70 45 356 70 45 357 77 45 358 78 45 359 77 45 360 79 45 361 78 45 362 59 0 363 58 0 364 61 0 365 83 45 366 86 45 367 85 45 368 35 0 369 59 0 370 60 0 371 32 0 372 42 0 373 62 0 374 84 45 375 85 45 376 69 45 377 61 0 378 58 0 379 57 0 380 83 45 381 82 45 382 87 45 383</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="shiftlight_multi_led_mirror-mesh" name="shiftlight_multi_led_mirror">
      <mesh>
        <source id="shiftlight_multi_led_mirror-mesh-positions">
          <float_array count="336" id="shiftlight_multi_led_mirror-mesh-positions-array">-0.04184126 0.006648957 0.02196073 -0.04366827 0.006648957 0.02282893 -0.04383212 0.006648957 0.0248512 -0.04216116 0.006648957 0.0260058 -0.04033023 0.006648957 0.0251379 -0.04017424 0.006648957 0.02311503 -0.04200118 0.007816255 0.02398324 -0.0362572 0.006648957 0.02241802 -0.03807783 0.006648957 0.0232867 -0.03822904 0.006648957 0.02530992 -0.03655171 0.006648957 0.02646505 -0.03472709 0.006648957 0.02559661 -0.0345838 0.006648957 0.02357286 -0.03640443 0.007816255 0.02444154 -0.03067314 0.006648957 0.02287542 -0.03248739 0.006648957 0.02374452 -0.03262591 0.006648957 0.02576869 -0.0309422 0.006648957 0.02692425 -0.02912396 0.006648957 0.02605539 -0.02899336 0.006648957 0.02403074 -0.03080767 0.007816255 0.02489984 -0.02508908 0.006648957 0.02333295 -0.02689701 0.006648957 0.02420246 -0.02702277 0.006648957 0.02622741 -0.02533274 0.006648957 0.02738338 -0.02352058 0.006648898 0.02650845 -0.02340304 0.006648957 0.02448296 -0.02521091 0.007816255 0.0253582 -0.01951044 0.006648898 0.02364617 -0.02130699 0.006648898 0.02460283 -0.0214166 0.006648898 0.02662873 -0.01971119 0.006648898 0.02769899 -0.01790541 0.006648898 0.02674281 -0.01781427 0.006648898 0.02471601 -0.01961082 0.007816195 0.02567255 -0.01393687 0.006648898 0.02382683 -0.01571863 0.006648898 0.02478396 -0.01579868 0.006648898 0.02681124 -0.01407843 0.006648898 0.02788209 -0.01228743 0.006648898 0.02692532 -0.01222592 0.006648898 0.02489739 -0.01400768 0.007816195 0.02585446 -0.008363366 0.006648898 0.02400815 -0.01013028 0.006648898 0.02496558 -0.01018071 0.006648898 0.02699381 -0.00844568 0.006648898 0.02806508 -0.006669521 0.006648898 0.02710789 -0.006637573 0.006648898 0.0250793 -0.008404552 0.007816195 0.02603662 -0.002789795 0.006648898 0.02419018 -0.004541933 0.006648898 0.02514761 -0.004562795 0.006648898 0.02717638 -0.002812981 0.006648898 0.02824783 -0.001051545 0.006648898 0.02727437 -0.00104922 0.006648898 0.02524548 -0.002801358 0.007816195 0.02621901 0.002783656 0.006648898 0.02415037 0.001046299 0.006648898 0.02522176 0.001055121 0.006648898 0.02725058 0.002819716 0.006648898 0.02820795 0.004566311 0.006648898 0.0271365 0.004539012 0.006648898 0.0251078 0.002801716 0.007816195 0.02617913 0.008357167 0.006648898 0.02396851 0.006634652 0.006648898 0.02503949 0.006673038 0.006648898 0.02706801 0.008452475 0.006648898 0.02802515 0.01018428 0.006648898 0.02695393 0.01012736 0.006648898 0.02492582 0.00840485 0.007816195 0.0259968 0.01393073 0.006648898 0.02378731 0.012223 0.006648898 0.02485775 0.01229101 0.006648898 0.02688544 0.01408523 0.006648898 0.0278421 0.0158022 0.006648898 0.02677136 0.01571571 0.006648898 0.02474433 0.01400798 0.007816195 0.02581471 0.0195043 0.006648898 0.02360683 0.01781135 0.006648898 0.02467644 0.01790893 0.006648898 0.02670294 0.01971799 0.006648898 0.02765893 0.02141916 0.006648898 0.02657014 0.02130424 0.006648898 0.02454453 0.01961112 0.007816195 0.02563285 0.02508521 0.006648898 0.02323299 0.02340048 0.006648898 0.02438861 0.02352118 0.006648898 0.02641391 0.02533447 0.006648898 0.02728307 0.02702313 0.006648898 0.02612715 0.0268945 0.006648898 0.02410238 0.02520984 0.007816195 0.025258 0.03066927 0.006648898 0.02277547 0.02899092 0.006648898 0.02393066 0.02912425 0.006648898 0.02595514 0.03094393 0.006648898 0.02682393 0.03262621 0.006648898 0.02566844 0.03248494 0.006648898 0.0236445 0.0308066 0.007816195 0.0247997 0.03625333 0.006648898 0.02231806 0.03458136 0.006648898 0.02347278 0.03472739 0.006648898 0.02549642 0.03655338 0.006648898 0.02636474 0.03822934 0.006648898 0.02520972 0.03807538 0.006648898 0.02318668 0.03640335 0.007816195 0.0243414 0.04183739 0.006648898 0.02186083 0.04017174 0.006648898 0.02301502 0.04033052 0.006648898 0.02503764 0.04216283 0.006648898 0.02590548 0.04384309 0.006648898 0.02475011 0.0436775 0.006648898 0.02272796 0.04200011 0.007816195 0.02388316</float_array>
          <technique_common>
            <accessor count="112" source="#shiftlight_multi_led_mirror-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="shiftlight_multi_led_mirror-mesh-normals">
          <float_array count="306" id="shiftlight_multi_led_mirror-mesh-normals-array">0.04013299 0.866782 -0.4970698 0 1 -1.52593e-4 0.451348 0.866134 -0.2147023 0.4104217 0.8670188 0.2825464 -0.04013299 0.866782 0.4970698 -0.4510716 0.8664969 0.2138165 -0.4105435 0.8666522 -0.2834923 0.0388813 0.8667723 -0.4971863 0.4515392 0.8655391 -0.21669 0.410546 0.8676034 0.2805642 -0.0388813 0.8667723 0.4971863 -0.4512518 0.8659116 0.2157988 -0.4106723 0.8672461 -0.2814826 0.03766053 0.8667733 -0.4972783 0.4517135 0.8649424 -0.2187 0.410694 0.8681735 0.2785773 -0.03766053 0.8667733 0.4972783 -0.451439 0.865309 0.217815 -0.4108211 0.8678179 -0.2794963 0.03595101 0.8667621 -0.4974246 0 1 1.22074e-4 0.4511343 0.8646081 -0.2212029 0.4116794 0.8684363 0.2762943 -0.03592056 0.866763 0.4974251 -0.4515998 0.8647143 0.2198333 -0.4109392 0.8683896 -0.2775404 0.01953226 0.8667749 -0.498317 0 1 -4.27272e-4 0.4405692 0.867466 -0.2310882 0.4239726 0.8651275 0.2679585 -0.01956272 0.8667744 0.4983167 -0.439997 0.8683052 0.229017 -0.4244038 0.8642504 -0.2700973 0.01666355 0.8667804 -0.4984117 0.4407888 0.8661043 -0.2357302 0.4240978 0.8665074 0.2632605 -0.01666355 0.8667804 0.4984117 -0.4402129 0.8669667 0.2336266 -0.4245226 0.8656477 -0.2653953 0.0137642 0.866779 -0.4985024 0.4409785 0.8647135 -0.2404338 0.4241924 0.8678564 0.2586234 -0.0137642 0.866779 0.4985024 -0.4404239 0.865588 0.2382946 -0.4246106 0.8670139 -0.2607542 0.009552419 0.8667688 -0.4986187 0 0.9999999 4.57792e-4 0.4388685 0.8641558 -0.2462302 0.4266309 0.8683079 0.2530364 -0.009460866 0.8667696 0.4986191 -0.4406048 0.8641799 0.2430239 -0.4246717 0.8683568 -0.2561458 -0.01150584 0.8667566 -0.4985986 0.4246777 0.8680639 -0.2571261 0.4405748 0.8644862 0.2419865 0.01150554 0.8667643 0.4985855 -0.4242526 0.8688923 0.2550216 -0.4411256 0.863604 -0.2441236 -0.01440495 0.8667713 -0.498498 0.4245862 0.8667212 -0.2617651 0.4403883 0.8658834 0.2372847 0.01440495 0.8667713 0.498498 -0.4241578 0.8675733 0.2596281 -0.4409357 0.8650251 -0.2393895 -0.01727402 0.8667846 -0.4983836 0.4244926 0.8653436 -0.2664333 0.4401752 0.8672577 0.2326156 0.01730453 0.8667841 0.4983833 -0.4240723 0.8662124 0.2642708 -0.4407274 0.866409 -0.234723 -0.02182114 0.8667734 -0.4982246 0 0.9999999 6.10389e-4 0.4215341 0.8650421 -0.27205 0.4425543 0.8675909 0.2267858 0.02182114 0.8667734 0.4982246 -0.4239448 0.864828 0.2689673 -0.4405173 0.8677586 -0.2300859 -0.03668361 0.8667653 -0.4973654 0.4109086 0.8682674 -0.2779676 0.4515583 0.8648459 0.2194007 0.03668361 0.8667653 0.4973654 -0.4107918 0.868614 0.2770556 -0.451844 0.8644703 -0.2202911 -0.03793543 0.8667775 -0.4972503 0.4107866 0.8676876 -0.2799514 0.4513847 0.8654439 0.217391 0.03793543 0.8667775 0.4972503 -0.4106343 0.8680544 0.279036 -0.4516837 0.8650658 -0.2182731 -0.03915613 0.8667762 -0.4971579 0.4106628 0.8671041 -0.281934 0.451197 0.8660456 0.2153745 0.03915613 0.8667762 0.4971579 -0.4105149 0.8674802 0.280991 -0.451497 0.8656694 -0.2162571 -0.04098737 0.8667783 -0.4970066 -0.001190245 0.9999993 -3.05194e-5 0.4088674 0.8676033 -0.2830057 0.4493378 0.8676069 0.2129647 0.03988862 0.8667774 0.4970976 -0.4103866 0.8668872 0.283001 -0.4513202 0.8662615 -0.2142459</float_array>
          <technique_common>
            <accessor count="102" source="#shiftlight_multi_led_mirror-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="shiftlight_multi_led_mirror-mesh-map-0">
          <float_array count="576" id="shiftlight_multi_led_mirror-mesh-map-0-array">0.1484375 0.06820005 0.1484375 0.5000011 0.2419249 0.2841005 0.2419249 0.7158985 0.1484375 0.5000011 0.1484375 0.9317989 0.05495017 0.7158985 0.1484375 0.5000011 0.05495017 0.2841005 0.2419249 0.2841005 0.1484375 0.5000011 0.2419249 0.7158985 0.1484375 0.9317989 0.1484375 0.5000011 0.05495017 0.7158985 0.05495017 0.2841005 0.1484375 0.5000011 0.1484375 0.06820005 0.1484375 0.06820005 0.1484375 0.5000011 0.2419249 0.2841005 0.2419249 0.7158985 0.1484375 0.5000011 0.1484375 0.9317989 0.05495017 0.7158985 0.1484375 0.5000011 0.05495017 0.2841005 0.2419249 0.2841005 0.1484375 0.5000011 0.2419249 0.7158985 0.1484375 0.9317989 0.1484375 0.5000011 0.05495017 0.7158985 0.05495017 0.2841005 0.1484375 0.5000011 0.1484375 0.06820005 0.1484375 0.06820005 0.1484375 0.5000011 0.2419249 0.2841005 0.2419249 0.7158985 0.1484375 0.5000011 0.1484375 0.9317989 0.05495017 0.7158985 0.1484375 0.5000011 0.05495017 0.2841005 0.2419249 0.2841005 0.1484375 0.5000011 0.2419249 0.7158985 0.1484375 0.9317989 0.1484375 0.5000011 0.05495017 0.7158985 0.05495017 0.2841005 0.1484375 0.5000011 0.1484375 0.06820005 0.5 0.06820005 0.5 0.5000011 0.5934875 0.2841005 0.5934875 0.7158985 0.5 0.5000011 0.5 0.9317989 0.4065127 0.7158985 0.5 0.5000011 0.4065127 0.2841005 0.5934875 0.2841005 0.5 0.5000011 0.5934875 0.7158985 0.5 0.9317989 0.5 0.5000011 0.4065127 0.7158985 0.4065127 0.2841005 0.5 0.5000011 0.5 0.06820005 0.5 0.06820005 0.5 0.5000011 0.5934875 0.2841005 0.5934875 0.7158985 0.5 0.5000011 0.5 0.9317989 0.4065127 0.7158985 0.5 0.5000011 0.4065127 0.2841005 0.5934875 0.2841005 0.5 0.5000011 0.5934875 0.7158985 0.5 0.9317989 0.5 0.5000011 0.4065127 0.7158985 0.4065127 0.2841005 0.5 0.5000011 0.5 0.06820005 0.5 0.06820005 0.5 0.5000011 0.5934875 0.2841005 0.5934875 0.7158985 0.5 0.5000011 0.5 0.9317989 0.4065127 0.7158985 0.5 0.5000011 0.4065127 0.2841005 0.5934875 0.2841005 0.5 0.5000011 0.5934875 0.7158985 0.5 0.9317989 0.5 0.5000011 0.4065127 0.7158985 0.4065127 0.2841005 0.5 0.5000011 0.5 0.06820005 0.8515625 0.06820005 0.8515625 0.5000011 0.94505 0.2841005 0.94505 0.7158985 0.8515625 0.5000011 0.8515625 0.9317989 0.7580752 0.7158985 0.8515625 0.5000011 0.7580752 0.2841005 0.94505 0.2841005 0.8515625 0.5000011 0.94505 0.7158985 0.8515625 0.9317989 0.8515625 0.5000011 0.7580752 0.7158985 0.7580752 0.2841005 0.8515625 0.5000011 0.8515625 0.06820005 0.8515625 0.06820005 0.8515625 0.5000011 0.94505 0.2841005 0.94505 0.7158985 0.8515625 0.5000011 0.8515625 0.9317989 0.7580752 0.7158985 0.8515625 0.5000011 0.7580752 0.2841005 0.94505 0.2841005 0.8515625 0.5000011 0.94505 0.7158985 0.8515625 0.9317989 0.8515625 0.5000011 0.7580752 0.7158985 0.7580752 0.2841005 0.8515625 0.5000011 0.8515625 0.06820005 0.8515625 0.06820005 0.8515625 0.5000011 0.94505 0.2841005 0.94505 0.7158985 0.8515625 0.5000011 0.8515625 0.9317989 0.7580752 0.7158985 0.8515625 0.5000011 0.7580752 0.2841005 0.94505 0.2841005 0.8515625 0.5000011 0.94505 0.7158985 0.8515625 0.9317989 0.8515625 0.5000011 0.7580752 0.7158985 0.7580752 0.2841005 0.8515625 0.5000011 0.8515625 0.06820005 0.8515625 0.06820005 0.8515625 0.5000011 0.94505 0.2841005 0.94505 0.7158985 0.8515625 0.5000011 0.8515625 0.9317989 0.7580752 0.7158985 0.8515625 0.5000011 0.7580752 0.2841005 0.94505 0.2841005 0.8515625 0.5000011 0.94505 0.7158985 0.8515625 0.9317989 0.8515625 0.5000011 0.7580752 0.7158985 0.7580752 0.2841005 0.8515625 0.5000011 0.8515625 0.06820005 0.5 0.06820005 0.5 0.5000011 0.5934875 0.2841005 0.5934875 0.7158985 0.5 0.5000011 0.5 0.9317989 0.4065127 0.7158985 0.5 0.5000011 0.4065127 0.2841005 0.5934875 0.2841005 0.5 0.5000011 0.5934875 0.7158985 0.5 0.9317989 0.5 0.5000011 0.4065127 0.7158985 0.4065127 0.2841005 0.5 0.5000011 0.5 0.06820005 0.5 0.06820005 0.5 0.5000011 0.5934875 0.2841005 0.5934875 0.7158985 0.5 0.5000011 0.5 0.9317989 0.4065127 0.7158985 0.5 0.5000011 0.4065127 0.2841005 0.5934875 0.2841005 0.5 0.5000011 0.5934875 0.7158985 0.5 0.9317989 0.5 0.5000011 0.4065127 0.7158985 0.4065127 0.2841005 0.5 0.5000011 0.5 0.06820005 0.5 0.06820005 0.5 0.5000011 0.5934875 0.2841005 0.5934875 0.7158985 0.5 0.5000011 0.5 0.9317989 0.4065127 0.7158985 0.5 0.5000011 0.4065127 0.2841005 0.5934875 0.2841005 0.5 0.5000011 0.5934875 0.7158985 0.5 0.9317989 0.5 0.5000011 0.4065127 0.7158985 0.4065127 0.2841005 0.5 0.5000011 0.5 0.06820005 0.1484375 0.06820005 0.1484375 0.5000011 0.2419249 0.2841005 0.2419249 0.7158985 0.1484375 0.5000011 0.1484375 0.9317989 0.05495017 0.7158985 0.1484375 0.5000011 0.05495017 0.2841005 0.2419249 0.2841005 0.1484375 0.5000011 0.2419249 0.7158985 0.1484375 0.9317989 0.1484375 0.5000011 0.05495017 0.7158985 0.05495017 0.2841005 0.1484375 0.5000011 0.1484375 0.06820005 0.1484375 0.06820005 0.1484375 0.5000011 0.2419249 0.2841005 0.2419249 0.7158985 0.1484375 0.5000011 0.1484375 0.9317989 0.05495017 0.7158985 0.1484375 0.5000011 0.05495017 0.2841005 0.2419249 0.2841005 0.1484375 0.5000011 0.2419249 0.7158985 0.1484375 0.9317989 0.1484375 0.5000011 0.05495017 0.7158985 0.05495017 0.2841005 0.1484375 0.5000011 0.1484375 0.06820005 0.1484375 0.06820005 0.1484375 0.5000011 0.2419249 0.2841005 0.2419249 0.7158985 0.1484375 0.5000011 0.1484375 0.9317989 0.05495017 0.7158985 0.1484375 0.5000011 0.05495017 0.2841005 0.2419249 0.2841005 0.1484375 0.5000011 0.2419249 0.7158985 0.1484375 0.9317989 0.1484375 0.5000011 0.05495017 0.7158985 0.05495017 0.2841005 0.1484375 0.5000011 0.1484375 0.06820005</float_array>
          <technique_common>
            <accessor count="288" source="#shiftlight_multi_led_mirror-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="shiftlight_multi_led_mirror-mesh-vertices">
          <input semantic="POSITION" source="#shiftlight_multi_led_mirror-mesh-positions" />
        </vertices>
        <triangles count="12" material="shiftlight_multi_l0-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi_led_mirror-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi_led_mirror-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi_led_mirror-mesh-map-0" />
          <p>0 0 0 6 1 1 5 2 2 4 3 3 6 1 4 3 4 5 2 5 6 6 1 7 1 6 8 5 2 9 6 1 10 4 3 11 3 4 12 6 1 13 2 5 14 1 6 15 6 1 16 0 0 17 105 95 270 111 96 271 110 97 272 109 98 273 111 96 274 108 99 275 107 100 276 111 96 277 106 101 278 110 97 279 111 96 280 109 98 281 108 99 282 111 96 283 107 100 284 106 101 285 111 96 286 105 95 287</p>
        </triangles>
        <triangles count="12" material="shiftlight_multi_l1-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi_led_mirror-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi_led_mirror-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi_led_mirror-mesh-map-0" />
          <p>7 7 18 13 1 19 12 8 20 11 9 21 13 1 22 10 10 23 9 11 24 13 1 25 8 12 26 12 8 27 13 1 28 11 9 29 10 10 30 13 1 31 9 11 32 8 12 33 13 1 34 7 7 35 98 89 252 104 1 253 103 90 254 102 91 255 104 1 256 101 92 257 100 93 258 104 1 259 99 94 260 103 90 261 104 1 262 102 91 263 101 92 264 104 1 265 100 93 266 99 94 267 104 1 268 98 89 269</p>
        </triangles>
        <triangles count="12" material="shiftlight_multi_l2-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi_led_mirror-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi_led_mirror-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi_led_mirror-mesh-map-0" />
          <p>14 13 36 20 1 37 19 14 38 18 15 39 20 1 40 17 16 41 16 17 42 20 1 43 15 18 44 19 14 45 20 1 46 18 15 47 17 16 48 20 1 49 16 17 50 15 18 51 20 1 52 14 13 53 91 83 234 97 1 235 96 84 236 95 85 237 97 1 238 94 86 239 93 87 240 97 1 241 92 88 242 96 84 243 97 1 244 95 85 245 94 86 246 97 1 247 93 87 248 92 88 249 97 1 250 91 83 251</p>
        </triangles>
        <triangles count="12" material="shiftlight_multi_l3-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi_led_mirror-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi_led_mirror-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi_led_mirror-mesh-map-0" />
          <p>21 19 54 27 20 55 26 21 56 25 22 57 27 20 58 24 23 59 23 24 60 27 20 61 22 25 62 26 21 63 27 20 64 25 22 65 24 23 66 27 20 67 23 24 68 22 25 69 27 20 70 21 19 71 84 77 216 90 1 217 89 78 218 88 79 219 90 1 220 87 80 221 86 81 222 90 1 223 85 82 224 89 78 225 90 1 226 88 79 227 87 80 228 90 1 229 86 81 230 85 82 231 90 1 232 84 77 233</p>
        </triangles>
        <triangles count="12" material="shiftlight_multi_l4-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi_led_mirror-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi_led_mirror-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi_led_mirror-mesh-map-0" />
          <p>28 26 72 34 27 73 33 28 74 32 29 75 34 27 76 31 30 77 30 31 78 34 27 79 29 32 80 33 28 81 34 27 82 32 29 83 31 30 84 34 27 85 30 31 86 29 32 87 34 27 88 28 26 89 77 70 198 83 71 199 82 72 200 81 73 201 83 71 202 80 74 203 79 75 204 83 71 205 78 76 206 82 72 207 83 71 208 81 73 209 80 74 210 83 71 211 79 75 212 78 76 213 83 71 214 77 70 215</p>
        </triangles>
        <triangles count="12" material="shiftlight_multi_l5-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi_led_mirror-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi_led_mirror-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi_led_mirror-mesh-map-0" />
          <p>35 33 90 41 27 91 40 34 92 39 35 93 41 27 94 38 36 95 37 37 96 41 27 97 36 38 98 40 34 99 41 27 100 39 35 101 38 36 102 41 27 103 37 37 104 36 38 105 41 27 106 35 33 107 70 64 180 76 27 181 75 65 182 74 66 183 76 27 184 73 67 185 72 68 186 76 27 187 71 69 188 75 65 189 76 27 190 74 66 191 73 67 192 76 27 193 72 68 194 71 69 195 76 27 196 70 64 197</p>
        </triangles>
        <triangles count="12" material="shiftlight_multi_l6-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi_led_mirror-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi_led_mirror-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi_led_mirror-mesh-map-0" />
          <p>42 39 108 48 27 109 47 40 110 46 41 111 48 27 112 45 42 113 44 43 114 48 27 115 43 44 116 47 40 117 48 27 118 46 41 119 45 42 120 48 27 121 44 43 122 43 44 123 48 27 124 42 39 125 63 58 162 69 27 163 68 59 164 67 60 165 69 27 166 66 61 167 65 62 168 69 27 169 64 63 170 68 59 171 69 27 172 67 60 173 66 61 174 69 27 175 65 62 176 64 63 177 69 27 178 63 58 179</p>
        </triangles>
        <triangles count="12" material="shiftlight_multi_l7-material">
          <input offset="0" semantic="VERTEX" source="#shiftlight_multi_led_mirror-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#shiftlight_multi_led_mirror-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#shiftlight_multi_led_mirror-mesh-map-0" />
          <p>49 45 126 55 46 127 54 47 128 53 48 129 55 46 130 52 49 131 51 50 132 55 46 133 50 51 134 54 47 135 55 46 136 53 48 137 52 49 138 55 46 139 51 50 140 50 51 141 55 46 142 49 45 143 56 52 144 62 27 145 61 53 146 60 54 147 62 27 148 59 55 149 58 56 150 62 27 151 57 57 152 61 53 153 62 27 154 60 54 155 59 55 156 62 27 157 58 56 158 57 57 159 62 27 160 56 52 161</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers />
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="shiftlight_light" name="shiftlight_light" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry name="shiftlight_light" url="#shiftlight_light-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="shiftlight_single_l0-material" target="#shiftlight_single_l0-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="shiftlight" name="shiftlight" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry name="shiftlight" url="#shiftlight-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="shiftlight-material" target="#shiftlight-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="shiftlight_multi" name="shiftlight_multi" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry name="shiftlight_multi" url="#shiftlight_multi-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="shiftlight_multi-material" target="#shiftlight_multi-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="shiftlight_multi_led_mirror" name="shiftlight_multi_led_mirror" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry name="shiftlight_multi_led_mirror" url="#shiftlight_multi_led_mirror-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="shiftlight_multi_l0-material" target="#shiftlight_multi_l0-material" />
              <instance_material symbol="shiftlight_multi_l1-material" target="#shiftlight_multi_l1-material" />
              <instance_material symbol="shiftlight_multi_l2-material" target="#shiftlight_multi_l2-material" />
              <instance_material symbol="shiftlight_multi_l3-material" target="#shiftlight_multi_l3-material" />
              <instance_material symbol="shiftlight_multi_l4-material" target="#shiftlight_multi_l4-material" />
              <instance_material symbol="shiftlight_multi_l5-material" target="#shiftlight_multi_l5-material" />
              <instance_material symbol="shiftlight_multi_l6-material" target="#shiftlight_multi_l6-material" />
              <instance_material symbol="shiftlight_multi_l7-material" target="#shiftlight_multi_l7-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene" />
  </scene>
</COLLADA>