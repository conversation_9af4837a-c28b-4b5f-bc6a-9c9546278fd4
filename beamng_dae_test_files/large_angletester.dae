<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2017-09-11, commit time:10:43, hash:5bd8ac9</authoring_tool>
    </contributor>
    <created>2019-12-14T17:29:45</created>
    <modified>2019-12-14T17:29:45</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_lights>
    <light id="Lamp-light" name="Lamp">
      <technique_common>
        <point>
          <color sid="color">1 1 1</color>
          <constant_attenuation>1</constant_attenuation>
          <linear_attenuation>0</linear_attenuation>
          <quadratic_attenuation>0.00111109</quadratic_attenuation>
        </point>
      </technique_common>
      <extra>
        <technique profile="blender">
          <type sid="type" type="int">0</type>
          <flag sid="flag" type="int">0</flag>
          <mode sid="mode" type="int">8192</mode>
          <gamma sid="blender_gamma" type="float">1</gamma>
          <red sid="red" type="float">1</red>
          <green sid="green" type="float">1</green>
          <blue sid="blue" type="float">1</blue>
          <shadow_r sid="blender_shadow_r" type="float">0</shadow_r>
          <shadow_g sid="blender_shadow_g" type="float">0</shadow_g>
          <shadow_b sid="blender_shadow_b" type="float">0</shadow_b>
          <energy sid="blender_energy" type="float">1</energy>
          <dist sid="blender_dist" type="float">29.99998</dist>
          <spotsize sid="spotsize" type="float">75</spotsize>
          <spotblend sid="spotblend" type="float">0.15</spotblend>
          <halo_intensity sid="blnder_halo_intensity" type="float">1</halo_intensity>
          <att1 sid="att1" type="float">0</att1>
          <att2 sid="att2" type="float">1</att2>
          <falloff_type sid="falloff_type" type="int">2</falloff_type>
          <clipsta sid="clipsta" type="float">1.000799</clipsta>
          <clipend sid="clipend" type="float">30.002</clipend>
          <bias sid="bias" type="float">1</bias>
          <soft sid="soft" type="float">3</soft>
          <compressthresh sid="compressthresh" type="float">0.04999995</compressthresh>
          <bufsize sid="bufsize" type="int">2880</bufsize>
          <samp sid="samp" type="int">3</samp>
          <buffers sid="buffers" type="int">1</buffers>
          <filtertype sid="filtertype" type="int">0</filtertype>
          <bufflag sid="bufflag" type="int">0</bufflag>
          <buftype sid="buftype" type="int">2</buftype>
          <ray_samp sid="ray_samp" type="int">1</ray_samp>
          <ray_sampy sid="ray_sampy" type="int">1</ray_sampy>
          <ray_sampz sid="ray_sampz" type="int">1</ray_sampz>
          <ray_samp_type sid="ray_samp_type" type="int">0</ray_samp_type>
          <area_shape sid="area_shape" type="int">1</area_shape>
          <area_size sid="area_size" type="float">0.1</area_size>
          <area_sizey sid="area_sizey" type="float">0.1</area_sizey>
          <area_sizez sid="area_sizez" type="float">1</area_sizez>
          <adapt_thresh sid="adapt_thresh" type="float">0.000999987</adapt_thresh>
          <ray_samp_method sid="ray_samp_method" type="int">1</ray_samp_method>
          <shadhalostep sid="shadhalostep" type="int">0</shadhalostep>
          <sun_effect_type sid="sun_effect_type" type="int">0</sun_effect_type>
          <skyblendtype sid="skyblendtype" type="int">1</skyblendtype>
          <horizon_brightness sid="horizon_brightness" type="float">1</horizon_brightness>
          <spread sid="spread" type="float">1</spread>
          <sun_brightness sid="sun_brightness" type="float">1</sun_brightness>
          <sun_size sid="sun_size" type="float">1</sun_size>
          <backscattered_light sid="backscattered_light" type="float">1</backscattered_light>
          <sun_intensity sid="sun_intensity" type="float">1</sun_intensity>
          <atm_turbidity sid="atm_turbidity" type="float">2</atm_turbidity>
          <atm_extinction_factor sid="atm_extinction_factor" type="float">1</atm_extinction_factor>
          <atm_distance_factor sid="atm_distance_factor" type="float">1</atm_distance_factor>
          <skyblendfac sid="skyblendfac" type="float">1</skyblendfac>
          <sky_exposure sid="sky_exposure" type="float">1</sky_exposure>
          <sky_colorspace sid="sky_colorspace" type="int">0</sky_colorspace>
        </technique>
      </extra>
    </light>
  </library_lights>
  <library_images/>
  <library_effects>
    <effect id="angle_floor-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.0487171 0.512 0 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.125 0.125 0.125 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="angle_protractor-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.512 0.512 0.512 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.125 0.125 0.125 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="angle_floor-material" name="angle_floor">
      <instance_effect url="#angle_floor-effect"/>
    </material>
    <material id="angle_protractor-material" name="angle_protractor">
      <instance_effect url="#angle_protractor-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Plane_002-mesh" name="Plane.002">
      <mesh>
        <source id="Plane_002-mesh-positions">
          <float_array id="Plane_002-mesh-positions-array" count="36">-1.143473 -7 0.02999997 2.856527 -7 0.02999997 -1.143473 0 0.02999997 2.856527 0 0.02999997 0.8565267 -7 0.02999997 0.8565267 0 0.02999997 -1.143473 -7 -0.01999998 2.856527 -7 -0.01999998 -1.143473 0 -0.01999998 2.856527 0 -0.01999998 0.8565267 -7 -0.01999998 0.8565267 0 -0.01999998</float_array>
          <technique_common>
            <accessor source="#Plane_002-mesh-positions-array" count="12" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Plane_002-mesh-normals">
          <float_array id="Plane_002-mesh-normals-array" count="18">0 0 1 0 0 -1 0 -1 0 1 0 0 -1 0 0 0 1 0</float_array>
          <technique_common>
            <accessor source="#Plane_002-mesh-normals-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane_002-mesh-vertices">
          <input semantic="POSITION" source="#Plane_002-mesh-positions"/>
        </vertices>
        <triangles material="angle_floor-material" count="20">
          <input semantic="VERTEX" source="#Plane_002-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Plane_002-mesh-normals" offset="1"/>
          <p>4 0 1 0 3 0 4 0 5 0 2 0 11 1 9 1 7 1 6 1 8 1 11 1 1 2 4 2 10 2 3 3 1 3 7 3 0 4 2 4 8 4 5 5 3 5 9 5 4 2 0 2 6 2 2 5 5 5 11 5 5 0 4 0 3 0 0 0 4 0 2 0 10 1 11 1 7 1 10 1 6 1 11 1 7 2 1 2 10 2 9 3 3 3 7 3 6 4 0 4 8 4 11 5 5 5 9 5 10 2 4 2 6 2 8 5 2 5 11 5</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Circle_001-mesh" name="Circle.001">
      <mesh>
        <source id="Circle_001-mesh-positions">
          <float_array id="Circle_001-mesh-positions-array" count="234">-2 -4.88762e-7 3 -2 -0.294052 2.985554 -2 -0.5852717 2.942356 -2 -0.8708549 2.870821 -2 -1.148051 2.771638 -2 -1.414191 2.645763 -2 -1.666712 2.494408 -2 -1.903181 2.319031 -2 -2.121321 2.121319 -2 -2.319032 1.903179 -2 -2.49441 1.666709 -2 -2.645765 1.414189 -2 -2.771639 1.148048 -2 -2.870821 0.8708521 -2 -2.942356 0.5852688 -2 -2.985554 0.2940492 -2 -3 -2.40804e-6 -2 -4.88762e-7 0 -2.1179 -4.88762e-7 3 -2.1179 -0.294052 2.985554 -2.1179 -0.5852717 2.942356 -2.1179 -0.8708549 2.870821 -2.1179 -1.148051 2.771638 -2.1179 -1.414191 2.645763 -2.1179 -1.666712 2.494408 -2.1179 -1.903181 2.319031 -2.1179 -2.121321 2.121319 -2.1179 -2.319032 1.903179 -2.1179 -2.49441 1.666709 -2.1179 -2.645765 1.414189 -2.1179 -2.771639 1.148048 -2.1179 -2.870821 0.8708521 -2.1179 -2.942356 0.5852688 -2.1179 -2.985554 0.2940492 -2.1179 -3 -2.40804e-6 -2.1179 -4.88762e-7 0 -2 -0.294052 2.985554 -2 -4.88762e-7 3 -2 -4.88762e-7 3 -2 -0.5852717 2.942356 -2 -0.8708549 2.870821 -2 -1.148051 2.771638 -2 -1.414191 2.645763 -2 -1.666712 2.494408 -2 -1.903181 2.319031 -2 -2.121321 2.121319 -2 -2.319032 1.903179 -2 -2.49441 1.666709 -2 -2.645765 1.414189 -2 -2.771639 1.148048 -2 -2.870821 0.8708521 -2 -2.942356 0.5852688 -2 -2.985554 0.2940492 -2 -3 -2.40804e-6 -2 -3 -2.40804e-6 -2 -4.88762e-7 0 -2 -4.88762e-7 0 -2.1179 -0.294052 2.985554 -2.1179 -4.88762e-7 3 -2.1179 -4.88762e-7 3 -2.1179 -0.5852717 2.942356 -2.1179 -0.8708549 2.870821 -2.1179 -1.148051 2.771638 -2.1179 -1.414191 2.645763 -2.1179 -1.666712 2.494408 -2.1179 -1.903181 2.319031 -2.1179 -2.121321 2.121319 -2.1179 -2.319032 1.903179 -2.1179 -2.49441 1.666709 -2.1179 -2.645765 1.414189 -2.1179 -2.771639 1.148048 -2.1179 -2.870821 0.8708521 -2.1179 -2.942356 0.5852688 -2.1179 -2.985554 0.2940492 -2.1179 -3 -2.40804e-6 -2.1179 -3 -2.40804e-6 -2.1179 -4.88762e-7 0 -2.1179 -4.88762e-7 0</float_array>
          <technique_common>
            <accessor source="#Circle_001-mesh-positions-array" count="78" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Circle_001-mesh-normals">
          <float_array id="Circle_001-mesh-normals-array" count="93">-1 -1.68918e-7 0 0 -0.7730101 0.6343938 0 -0.8314677 0.5555732 0 -0.6343938 0.7730101 0 -0.7071068 0.7071068 0 1 0 0 -0.4714023 0.8819184 0 -0.5555732 0.8314677 0 -0.290264 0.9569466 0 -0.3826816 0.9238803 0 -0.9807879 0.1950772 0 -0.9951869 0.09799575 0 -0.09799575 0.9951869 0 -0.1950772 0.9807879 0 -0.9238803 0.3826816 0 -0.9569466 0.290264 0 -0.8819184 0.4714023 0 8.02681e-7 -1 0 -0.9987967 0.04904407 0 -0.04904407 0.9987967 -1 0 0 -1 -1.35134e-7 0 -1 1.35134e-7 0 -1 1.08108e-6 0 -1 -1.08107e-6 0 -1 1.35134e-7 0 -1 1.35134e-7 0 -1 4.39186e-7 0 -1 -1.35134e-7 0 -1 0 0 1 0 0</float_array>
          <technique_common>
            <accessor source="#Circle_001-mesh-normals-array" count="31" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Circle_001-mesh-map-0">
          <float_array id="Circle_001-mesh-map-0-array" count="408">1 7.87681e-7 0.8049098 0.9807851 0.901983 0.9951847 0.2269892 0.6343932 0.2269892 0.6343932 0.1685301 0.5555703 0.3656064 0.7730103 0.3656064 0.7730103 0.2928928 0.7071064 1 7.87681e-7 1 7.87681e-7 1 1 0.5286031 0.8819211 0.5286031 0.8819211 0.4444295 0.8314694 0.7097153 0.9569401 0.7097153 0.9569401 0.6173164 0.9238796 0.01921445 0.1950903 0.01921445 0.1950903 0.004815042 0.09801715 0.901983 0.9951847 0.901983 0.9951847 0.8049098 0.9807851 0.07612025 0.3826833 0.07612025 0.3826833 0.04305928 0.2902845 0.1685301 0.5555703 0.1685301 0.5555703 0.1180784 0.4713968 0.2928928 0.7071064 0.2928928 0.7071064 0.2269892 0.6343932 0 0 1 7.87681e-7 1 7.87681e-7 0.4444295 0.8314694 0.4444295 0.8314694 0.3656064 0.7730103 0.6173164 0.9238796 0.6173164 0.9238796 0.5286031 0.8819211 0.004815042 0.09801715 0.004815042 0.09801715 0 0 0.8049098 0.9807851 0.8049098 0.9807851 0.7097153 0.9569401 0.04305928 0.2902845 0.04305928 0.2902845 0.01921445 0.1950903 1 1 1 1 0.901983 0.9951847 0.1180784 0.4713968 0.1180784 0.4713968 0.07612025 0.3826833 1 1 1 7.87681e-7 0.901983 0.9951847 1 7.87681e-7 0.01921445 0.1950903 0.04305928 0.2902845 0.01921445 0.1950903 1 7.87681e-7 0.004815042 0.09801715 0.07612025 0.3826833 1 7.87681e-7 0.04305928 0.2902845 0.1685301 0.5555703 1 7.87681e-7 0.1180784 0.4713968 0.2928928 0.7071064 1 7.87681e-7 0.2269892 0.6343932 0.4444295 0.8314694 1 7.87681e-7 0.3656064 0.7730103 0.6173164 0.9238796 1 7.87681e-7 0.5286031 0.8819211 0.8049098 0.9807851 1 7.87681e-7 0.7097153 0.9569401 1 7.87681e-7 0.1685301 0.5555703 0.2269892 0.6343932 1 7.87681e-7 0.4444295 0.8314694 0.5286031 0.8819211 1 7.87681e-7 0.6173164 0.9238796 0.7097153 0.9569401 1 7.87681e-7 0.07612025 0.3826833 0.1180784 0.4713968 0.1685301 0.5555703 0.2269892 0.6343932 0.1685301 0.5555703 1 7.87681e-7 0 0 0.004815042 0.09801715 1 7.87681e-7 0.2928928 0.7071064 0.3656064 0.7730103 0.2928928 0.7071064 0.3656064 0.7730103 0.2928928 0.7071064 1 1 1 7.87681e-7 1 1 0.4444295 0.8314694 0.5286031 0.8819211 0.4444295 0.8314694 0.6173164 0.9238796 0.7097153 0.9569401 0.6173164 0.9238796 0.004815042 0.09801715 0.01921445 0.1950903 0.004815042 0.09801715 0.8049098 0.9807851 0.901983 0.9951847 0.8049098 0.9807851 0.04305928 0.2902845 0.07612025 0.3826833 0.04305928 0.2902845 0.1180784 0.4713968 0.1685301 0.5555703 0.1180784 0.4713968 0.2269892 0.6343932 0.2928928 0.7071064 0.2269892 0.6343932 0 0 0 0 1 7.87681e-7 0.3656064 0.7730103 0.4444295 0.8314694 0.3656064 0.7730103 0.5286031 0.8819211 0.6173164 0.9238796 0.5286031 0.8819211 0 0 0.004815042 0.09801715 0 0 0.7097153 0.9569401 0.8049098 0.9807851 0.7097153 0.9569401 0.01921445 0.1950903 0.04305928 0.2902845 0.01921445 0.1950903 0.901983 0.9951847 1 1 0.901983 0.9951847 0.07612025 0.3826833 0.1180784 0.4713968 0.07612025 0.3826833 1 7.87681e-7 0.4444295 0.8314694 0.3656064 0.7730103 0.8049098 0.9807851 1 7.87681e-7 0.901983 0.9951847 0.6173164 0.9238796 1 7.87681e-7 0.7097153 0.9569401 0.4444295 0.8314694 1 7.87681e-7 0.5286031 0.8819211 0.2928928 0.7071064 1 7.87681e-7 0.3656064 0.7730103 0.1685301 0.5555703 1 7.87681e-7 0.2269892 0.6343932 0.07612025 0.3826833 1 7.87681e-7 0.1180784 0.4713968 0.01921445 0.1950903 1 7.87681e-7 0.04305928 0.2902845 0 0 1 7.87681e-7 0.004815042 0.09801715 1 7.87681e-7 0.8049098 0.9807851 0.7097153 0.9569401 1 7.87681e-7 0.2928928 0.7071064 0.2269892 0.6343932 1 7.87681e-7 0.6173164 0.9238796 0.5286031 0.8819211 1 7.87681e-7 0.07612025 0.3826833 0.04305928 0.2902845 1 7.87681e-7 0.1685301 0.5555703 0.1180784 0.4713968 1 7.87681e-7 1 1 0.901983 0.9951847 1 7.87681e-7 0.01921445 0.1950903 0.004815042 0.09801715</float_array>
          <technique_common>
            <accessor source="#Circle_001-mesh-map-0-array" count="204" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Circle_001-mesh-vertices">
          <input semantic="POSITION" source="#Circle_001-mesh-positions"/>
        </vertices>
        <triangles material="angle_floor-material" count="52">
          <input semantic="VERTEX" source="#Circle_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Circle_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Circle_001-mesh-map-0" offset="2" set="0"/>
          <p>35 0 0 20 0 1 19 0 2 46 1 3 67 1 4 68 2 5 44 3 6 65 3 7 66 4 8 55 5 9 76 5 10 58 5 11 42 6 12 63 6 13 64 7 14 40 8 15 61 8 16 62 9 17 51 10 18 72 10 19 73 11 20 36 12 21 57 12 22 60 13 23 49 14 24 70 14 25 71 15 26 47 2 27 68 2 28 69 16 29 45 4 30 66 4 31 67 1 32 74 17 33 77 17 34 56 17 35 43 7 36 64 7 37 65 3 38 41 9 39 62 9 40 63 6 41 52 11 42 73 11 43 75 18 44 39 13 45 60 13 46 61 8 47 50 15 48 71 15 49 72 10 50 38 19 51 59 19 52 57 12 53 48 16 54 69 16 55 70 14 56 18 20 57 35 20 58 19 20 59 35 21 60 32 21 61 31 21 62 32 22 63 35 22 64 33 22 65 30 20 66 35 20 67 31 20 68 28 20 69 35 20 70 29 20 71 26 20 72 35 20 73 27 20 74 24 23 75 35 23 76 25 23 77 22 24 78 35 24 79 23 24 80 20 20 81 35 20 82 21 20 83 35 25 84 28 25 85 27 25 86 35 26 87 24 26 88 23 26 89 35 27 90 22 27 91 21 27 92 35 28 93 30 28 94 29 28 95 47 2 96 46 1 97 68 2 98 35 20 99 34 20 100 33 20 101 35 29 102 26 29 103 25 29 104 45 4 105 44 3 106 66 4 107 37 5 108 55 5 109 58 5 110 43 7 111 42 6 112 64 7 113 41 9 114 40 8 115 62 9 116 52 11 117 51 10 118 73 11 119 39 13 120 36 12 121 60 13 122 50 15 123 49 14 124 71 15 125 48 16 126 47 2 127 69 16 128 46 1 129 45 4 130 67 1 131 53 17 132 74 17 133 56 17 134 44 3 135 43 7 136 65 3 137 42 6 138 41 9 139 63 6 140 54 18 141 52 11 142 75 18 143 40 8 144 39 13 145 61 8 146 51 10 147 50 15 148 72 10 149 36 12 150 38 19 151 57 12 152 49 14 153 48 16 154 70 14 155</p>
        </triangles>
        <triangles material="angle_protractor-material" count="16">
          <input semantic="VERTEX" source="#Circle_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Circle_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Circle_001-mesh-map-0" offset="2" set="0"/>
          <p>17 30 156 6 30 157 7 30 158 2 30 159 17 30 160 1 30 161 4 30 162 17 30 163 3 30 164 6 30 165 17 30 166 5 30 167 8 30 168 17 30 169 7 30 170 10 30 171 17 30 172 9 30 173 12 30 174 17 30 175 11 30 176 14 30 177 17 30 178 13 30 179 16 30 180 17 30 181 15 30 182 17 30 183 2 30 184 3 30 185 17 30 186 8 30 187 9 30 188 17 30 189 4 30 190 5 30 191 17 30 192 12 30 193 13 30 194 17 30 195 10 30 196 11 30 197 17 30 198 0 30 199 1 30 200 17 30 201 14 30 202 15 30 203</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Lamp" name="Lamp" type="NODE">
        <matrix sid="transform">-0.2908646 -0.7711008 0.5663932 4.076245 0.9551712 -0.1998834 0.2183912 1.005454 -0.05518906 0.6045247 0.7946723 5.903862 0 0 0 1</matrix>
        <instance_light url="#Lamp-light"/>
      </node>
      <node id="angle_floor" name="angle_floor" type="NODE">
        <matrix sid="transform">1.749057 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane_002-mesh" name="angle_floor">
          <bind_material>
            <technique_common>
              <instance_material symbol="angle_floor-material" target="#angle_floor-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="angle_protractor" name="angle_protractor" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Circle_001-mesh" name="angle_protractor">
          <bind_material>
            <technique_common>
              <instance_material symbol="angle_floor-material" target="#angle_floor-material"/>
              <instance_material symbol="angle_protractor-material" target="#angle_protractor-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>