<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 4.3.0 commit date:2024-11-19, commit time:08:52, hash:2b18cad88b13</authoring_tool>
    </contributor>
    <created>2025-03-05T01:31:34</created>
    <modified>2025-03-05T01:31:34</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="tow_hitch-effect">
      <profile_COMMON>
        <newparam sid="tow_hitch_b_color_png-surface">
          <surface type="2D">
            <init_from>tow_hitch_b_color_png</init_from>
          </surface>
        </newparam>
        <newparam sid="tow_hitch_b_color_png-sampler">
          <sampler2D>
            <source>tow_hitch_b_color_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <texture texture="tow_hitch_b_color_png-sampler" texcoord="UVMap"/>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.5</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="tow_hitch_b_color_png" name="tow_hitch_b_color_png">
      <init_from>/D:/Work%20space/BeamNG%20Game/vehicles/common/towhitch/tow_hitch_b.color.png</init_from>
    </image>
  </library_images>
  <library_materials>
    <material id="tow_hitch-material" name="tow_hitch">
      <instance_effect url="#tow_hitch-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="tow_beam_c_R-mesh" name="tow_beam_c_R">
      <mesh>
        <source id="tow_beam_c_R-mesh-positions">
          <float_array id="tow_beam_c_R-mesh-positions-array" count="96">-0.4689809 -0.02549999 -0.02149999 -0.4689809 -0.02149999 -0.02549999 -0.4689809 -0.02149999 0.02549999 -0.4689809 -0.02549999 0.02149999 -0.03828036 -0.02149999 -0.02549999 -0.03828036 -0.02549993 -0.02149999 -0.03828036 -0.02149999 0.02549999 -0.4689809 0.02149999 -0.02549999 -0.4689809 0.02549999 0.02149999 -0.4689809 0.02149999 0.02549999 -0.03828036 0.02549999 -0.02149999 -0.03828036 0.02149999 -0.02549999 -0.03828036 0.02149999 0.02549999 -0.03828036 0.02549999 0.02149999 -0.468981 0.02549999 -0.02149999 -0.4741238 -0.03000974 -0.02530235 -0.4741238 -0.02530235 -0.03000974 -0.4741238 -0.02530235 0.03000974 -0.4741238 -0.03000974 0.02530235 -0.4741238 0.02530235 -0.03000974 -0.4741238 0.03000974 -0.02530235 -0.4741238 0.03000974 0.02530235 -0.4741238 0.02530235 0.03000974 -0.03828042 -0.02549993 0.02149999 -0.03313755 0.03000974 -0.02530235 -0.03313755 0.02530235 -0.03000974 -0.03313755 0.02530235 0.03000974 -0.03313755 0.03000974 0.02530235 -0.03313761 -0.02530235 -0.03000974 -0.03313761 -0.03000974 -0.02530235 -0.03313761 -0.03000974 0.02530235 -0.03313761 -0.02530235 0.03000974</float_array>
          <technique_common>
            <accessor source="#tow_beam_c_R-mesh-positions-array" count="32" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_c_R-mesh-normals">
          <float_array id="tow_beam_c_R-mesh-normals-array" count="102">-0.01203763 0.9963275 -0.08477509 0.01205188 0.9963285 -0.08476161 0.0120418 0.9963272 0.08477729 -0.01204532 0.9963286 0.08476102 -0.01203882 0.08477598 0.9963274 0.01204574 0.08476012 0.9963287 0.01203674 -0.08477598 0.9963274 -0.01204776 -0.08476096 0.9963285 0.01203727 -0.9963274 -0.08477592 -0.01204854 -0.9963285 -0.08476138 -0.01203989 -0.9963274 0.08477586 0.01204431 -0.9963285 0.08476132 -0.01201725 -0.0848146 -0.9963243 0.01203721 -0.08477467 -0.9963275 0.01201635 0.08481115 -0.9963247 -0.01203894 0.08477568 -0.9963274 0.6835555 0.7272657 0.0619412 0.6835472 0.06194067 0.7272734 -1 1.96314e-7 0 0.6835489 -0.06194078 0.7272719 0.6835459 -0.7272744 0.06194454 0.6835513 -0.7272694 -0.06194281 0.6835456 -0.0619409 -0.727275 0.6835521 0.7272686 -0.06194299 0.6835514 0.0619384 -0.7272696 -0.6835532 -0.7272679 0.06193959 -0.683549 -0.06194233 0.7272716 1 0 0 -0.6835516 0.06194037 0.7272694 -0.6835469 0.7272735 0.06194317 -0.6835506 0.7272703 -0.06194061 -0.683548 0.06194281 -0.7272725 -0.6835497 -0.727271 -0.06194198 -0.6835529 -0.06193965 -0.7272682</float_array>
          <technique_common>
            <accessor source="#tow_beam_c_R-mesh-normals-array" count="34" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_c_R-mesh-map-0">
          <float_array id="tow_beam_c_R-mesh-map-0-array" count="224">0.4242945 0.1090186 0.8484916 0.1090186 0.8484916 0.1506472 0.4242945 0.1506472 0.4242945 0.1561236 0.8484916 0.1561237 0.8484916 0.1977523 0.4242945 0.1977522 0.8484916 0.05643725 0.4242945 0.05643713 0.4242945 0.01480859 0.8484916 0.01480859 0.4242945 0.06191366 0.4242945 0.05643713 0.8484916 0.05643725 0.8484916 0.06191366 0.8484916 0.1977523 0.8484916 0.2032287 0.4242945 0.2032287 0.4242945 0.1977522 0.8484916 0.1035422 0.8484916 0.1090186 0.4242945 0.1090186 0.4242945 0.1035422 0.4242945 0.1561236 0.4242945 0.1506472 0.8484916 0.1506472 0.8484916 0.1561237 0.4242945 0.06191366 0.8484916 0.06191366 0.8484916 0.1035422 0.4242945 0.1035422 0.002330601 0.1686181 0.002298116 0.1570087 0.01255089 0.1584517 0.01255089 0.1669963 0.8723409 0.1713312 0.8684685 0.1674588 0.8684685 0.1258302 0.8723409 0.1219578 0.9139696 0.1219578 0.9178419 0.1258302 0.9178419 0.1674588 0.9139696 0.1713312 0.002514958 0.2420559 0.002371311 0.2305397 0.01255089 0.2319467 0.01255089 0.2404912 0.002371311 0.2305397 0.002330601 0.1686181 0.01255089 0.1669963 0.01255089 0.2319467 0.001077175 0.3105879 0.002998411 0.3021542 0.01255089 0.3054415 0.01255089 0.3139861 0.002298116 0.1570087 0.002448379 0.09498536 0.01255089 0.09350138 0.01255089 0.1584517 0.002633929 0.08355516 0.003215372 0.02508133 0.01255089 0.02000653 0.01255089 0.08495688 0.002448379 0.09498536 0.002633929 0.08355516 0.01255089 0.08495688 0.01255089 0.09350138 0.002998411 0.3021542 0.002514958 0.2420559 0.01255089 0.2404912 0.01255089 0.3054415 0.002330601 0.1686181 0.002298116 0.1570087 0.01255089 0.1584517 0.01255089 0.1669963 0.8723409 0.1713312 0.8684685 0.1674588 0.8684685 0.1258302 0.8723409 0.1219578 0.9139696 0.1219578 0.9178419 0.1258302 0.9178419 0.1674588 0.9139696 0.1713312 0.002514958 0.2420559 0.002371311 0.2305397 0.01255089 0.2319467 0.01255089 0.2404912 0.002371311 0.2305397 0.002330601 0.1686181 0.01255089 0.1669963 0.01255089 0.2319467 0.001077175 0.3105879 0.002998411 0.3021542 0.01255089 0.3054415 0.01255089 0.3139861 0.002298116 0.1570087 0.002448379 0.09498536 0.01255089 0.09350138 0.01255089 0.1584517 0.002633929 0.08355516 0.003215372 0.02508133 0.01255089 0.02000653 0.01255089 0.08495688 0.002448379 0.09498536 0.002633929 0.08355516 0.01255089 0.08495688 0.01255089 0.09350138 0.002998411 0.3021542 0.002514958 0.2420559 0.01255089 0.2404912 0.01255089 0.3054415</float_array>
          <technique_common>
            <accessor source="#tow_beam_c_R-mesh-map-0-array" count="112" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_beam_c_R-mesh-vertices">
          <input semantic="POSITION" source="#tow_beam_c_R-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="26">
          <input semantic="VERTEX" source="#tow_beam_c_R-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_beam_c_R-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_beam_c_R-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 8 4 4 4 4 4 4 4 4 8 4 4 4 4 4 4 4 </vcount>
          <p>10 0 0 14 1 1 8 2 2 13 3 3 12 4 4 9 5 5 2 6 6 6 7 7 0 8 8 5 9 9 23 10 10 3 11 11 4 12 12 5 9 13 0 8 14 1 13 15 2 6 16 3 11 17 23 10 18 6 7 19 7 14 20 14 1 21 10 0 22 11 15 23 12 4 24 13 3 25 8 2 26 9 5 27 4 12 28 1 13 29 7 14 30 11 15 31 9 5 32 8 2 33 21 16 34 22 17 35 16 18 36 15 18 37 18 18 38 17 18 39 22 18 40 21 18 41 20 18 42 19 18 43 3 11 44 2 6 45 17 19 46 18 20 47 2 6 48 9 5 49 22 17 50 17 19 51 1 13 52 0 8 53 15 21 54 16 22 55 8 2 56 14 1 57 20 23 58 21 16 59 7 14 60 1 13 61 16 22 62 19 24 63 14 1 64 7 14 65 19 24 66 20 23 67 0 8 68 3 11 69 18 20 70 15 21 71 6 7 72 23 10 73 30 25 74 31 26 75 25 27 76 24 27 77 27 27 78 26 27 79 31 27 80 30 27 81 29 27 82 28 27 83 13 3 84 12 4 85 26 28 86 27 29 87 12 4 88 6 7 89 31 26 90 26 28 91 11 15 92 10 0 93 24 30 94 25 31 95 23 10 96 5 9 97 29 32 98 30 25 99 4 12 100 11 15 101 25 31 102 28 33 103 5 9 104 4 12 105 28 33 106 29 32 107 10 0 108 13 3 109 27 29 110 24 30 111</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_mount_hz_b_R-mesh" name="tow_mount_hz_b_R">
      <mesh>
        <source id="tow_mount_hz_b_R-mesh-positions">
          <float_array id="tow_mount_hz_b_R-mesh-positions-array" count="144">-0.4744203 -0.107093 -0.02980327 -0.5019696 -0.06428033 0.05404877 -0.4744203 -0.02357435 -0.02980327 -0.5055459 -0.05808597 0.05404877 -0.4744203 -0.1220626 0.03100156 -0.5126985 -0.05808597 0.05404877 -0.4744203 -0.02357435 0.03100156 -0.5162748 -0.06428033 0.05404877 -0.5357343 -0.1364766 0.06812256 -0.5126985 -0.07047462 0.05404877 -0.5357343 -0.0439254 0.06812256 -0.5055459 -0.07047462 0.05404877 -0.5055459 -0.05808597 0.06003487 -0.4744203 -0.1364766 0.05909371 -0.5126985 -0.05808597 0.06003487 -0.4834492 -0.1364766 0.06812256 -0.4834492 -0.0439254 0.06812256 -0.5162748 -0.06428033 0.06003487 -0.4744203 -0.0439254 0.05909371 -0.5126985 -0.07047462 0.06003487 -0.4744203 -0.1270563 0.04207676 -0.5055459 -0.07047462 0.06003487 -0.4744203 -0.03524792 0.04207676 -0.5019696 -0.06428033 0.06003487 -0.4824203 -0.107093 -0.02980327 -0.5019696 -0.1122244 0.05404877 -0.4824203 -0.02357435 -0.02980327 -0.5055459 -0.1060301 0.05404877 -0.4824203 -0.1220626 0.03100156 -0.5126985 -0.1060301 0.05404877 -0.4824203 -0.02357435 0.03100156 -0.5162748 -0.1122244 0.05404877 -0.5357343 -0.1364766 0.0601226 -0.5126985 -0.1184186 0.05404877 -0.5357343 -0.0439254 0.0601226 -0.5055459 -0.1184186 0.05404877 -0.4824203 -0.1364766 0.05577999 -0.5055459 -0.1060301 0.06003487 -0.486763 -0.1364766 0.0601226 -0.5126985 -0.1060301 0.06003487 -0.4867629 -0.0439254 0.0601226 -0.5162748 -0.1122244 0.06003487 -0.4824203 -0.0439254 0.05577999 -0.5126985 -0.1184186 0.06003487 -0.4824203 -0.1270563 0.04207676 -0.5055459 -0.1184186 0.06003487 -0.4824203 -0.03524792 0.04207676 -0.5019696 -0.1122244 0.06003487</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_b_R-mesh-positions-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_hz_b_R-mesh-normals">
          <float_array id="tow_mount_hz_b_R-mesh-normals-array" count="186">1 0 0 1 0 5.08675e-7 1 0 2.4932e-7 0.9445669 0 0.328319 0.9445629 -1.73226e-6 0.3283305 0.1457549 0 0.9893208 0.1457548 -9.34228e-6 0.9893208 0 0 1 -1 0 0 -1 4.82552e-7 -6.4184e-7 -0.9720682 2.70145e-7 -0.2346986 -0.9720789 2.18302e-6 -0.2346545 -1 2.1894e-7 -2.91211e-7 -0.08121562 0 -0.9966966 -0.08121526 0 -0.9966967 0 0 -1 0 -0.9710066 -0.2390527 0 -0.9635251 -0.2676178 2.35845e-5 -0.9635251 -0.2676178 0 1 0 0 1 0 -0.06819689 0.9417608 0.3292961 -0.06819838 0.9417651 0.3292837 0 1 0 0.07329308 -0.9324036 -0.3539093 0 -1 9.59617e-7 0 -1 9.59617e-7 0.07329255 -0.9324012 -0.3539159 0 -1 1.14971e-6 0.06467038 -0.8761274 -0.4777222 0.06464856 -0.8761411 -0.4776999 -0.05379593 0.791251 0.6091206 -0.05378925 0.79124 0.6091355 0 0.6882699 0.7254549 0 -1 0 -0.866025 -0.5000008 0 -0.8660255 -0.4999998 0 -0.8660276 -0.4999962 0 -0.8660255 0.5000001 0 -0.8660248 0.5000011 0 -0.8660227 0.500005 0 0.8660221 0.5000058 0 0.8660231 0.500004 0 0.866018 0.5000131 0 0.8660225 -0.500005 0 0.8660237 -0.5000031 0 0.8660182 -0.5000125 0 -3.28445e-7 3.00704e-7 -1 -1.31377e-6 3.00707e-7 -1 -3.28444e-7 0 -1 -3.28445e-7 3.00703e-7 -1 -3.28444e-7 0 -1 -0.8660229 -0.5000044 0 -0.8660276 0.4999962 0 0.8660272 0.4999968 0 0.866028 -0.4999957 0 -1.31378e-6 -3.00701e-7 -1 6.56895e-7 0 -1 6.56892e-7 2.25527e-7 -1 -1.31378e-6 -3.00701e-7 -1 6.56896e-7 0 -1 6.56893e-7 2.25527e-7 -1</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_b_R-mesh-normals-array" count="62" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_hz_b_R-mesh-map-0">
          <float_array id="tow_mount_hz_b_R-mesh-map-0-array" count="368">0.3105007 0.796985 0.2269001 0.796985 0.2269001 0.7361206 0.3254848 0.7361206 0.3304828 0.7250344 0.2385849 0.7250344 0.2472706 0.7080008 0.3399125 0.7080008 0.2472706 0.6952194 0.3399125 0.6952193 0.3399125 0.7080008 0.2472706 0.7080008 0.3399125 0.6952193 0.2472706 0.6952194 0.2472706 0.6428833 0.3399125 0.6428832 0.3254848 0.7361206 0.2269001 0.7361206 0.2385849 0.7250344 0.3304828 0.7250344 0.851692 0.7782944 0.9125564 0.7633103 0.9125563 0.8618949 0.8516919 0.8618949 0.9236424 0.758312 0.9373592 0.7488819 0.937359 0.8415238 0.9236424 0.8502101 0.9435065 0.8415238 0.937359 0.8415238 0.9373592 0.7488819 0.9435065 0.7488819 0.9435065 0.7488819 0.992526 0.7488818 0.9925259 0.8415237 0.9435065 0.8415238 0.9125564 0.7633103 0.9236424 0.758312 0.9236424 0.8502101 0.9125563 0.8618949 0.81878 0.8547174 0.8187801 0.7920669 0.8267841 0.7920666 0.826784 0.8547174 0.6490427 0.9928947 0.565442 0.9928947 0.565442 0.9848867 0.6490427 0.9848867 0.7114206 0.5948878 0.7722853 0.5948878 0.7722853 0.6028949 0.7114206 0.6028949 0.5556774 0.9928917 0.4630354 0.9928917 0.4630354 0.9848836 0.5556774 0.9848836 0.545623 0.6518907 0.5546542 0.6428577 0.5579699 0.6508604 0.5536258 0.6552045 0.8185934 0.7604531 0.8270604 0.7508866 0.8308559 0.7586739 0.8267841 0.763275 0.8270604 0.7508866 0.8792729 0.7508864 0.8792729 0.7586737 0.8308559 0.7586739 0.5456264 0.7041946 0.545623 0.6518907 0.5536258 0.6552045 0.5536293 0.7041941 0.8187801 0.7920669 0.8187801 0.7799119 0.8267841 0.7799119 0.8267841 0.7920666 0.5546542 0.6428577 0.5737437 0.6420004 0.574172 0.6499916 0.5579699 0.6508604 0.8187801 0.7799119 0.8185934 0.7604531 0.8267841 0.763275 0.8267841 0.7799119 0.5737437 0.6420004 0.5898178 0.6411389 0.5902463 0.6491302 0.574172 0.6499916 0.9874209 0.7278694 0.9874209 0.7188813 0.9874209 0.7278694 0.05864334 0.6038709 0.05864334 0.5948836 0.05864334 0.6038709 0.02740067 0.6038752 0.03814023 0.5948878 0.03814023 0.6038752 0.9949766 0.5878716 0.9949766 0.5788835 0.9949766 0.5878716 0.9859873 0.707621 0.9949753 0.6968813 0.9949753 0.707621 0.9669165 0.7168877 0.9579277 0.7276272 0.9579277 0.7168877 0.747335 0.5410054 0.7473369 0.5517452 0.738036 0.5571167 0.9874209 0.7278694 0.9874209 0.7188813 0.9874209 0.7278694 0.04790395 0.6038709 0.05864334 0.5948836 0.05864334 0.6038709 0.03814023 0.6038752 0.03814023 0.5948878 0.03814023 0.6038752 0.9949766 0.5878716 0.9949766 0.5788835 0.9949766 0.5878716 0.9859873 0.707621 0.9949753 0.6968813 0.9949753 0.707621 0.9669165 0.7168877 0.9579277 0.7276272 0.9579277 0.7168877 0.747335 0.5410054 0.7473369 0.5517452 0.738036 0.5571167 0.9766814 0.7278694 0.9874209 0.7188813 0.9874209 0.7188813 0.04790395 0.6038709 0.04790395 0.5948836 0.05864334 0.5948836 0.02740067 0.6038752 0.03814023 0.5948878 0.03814023 0.5948878 0.9842375 0.5878716 0.9949766 0.5788835 0.9949766 0.5788835 0.9859873 0.707621 0.9859873 0.6968813 0.9949753 0.6968813 0.9669165 0.7168877 0.9669165 0.7276272 0.9579277 0.7276272 0.738036 0.5571167 0.7287347 0.5517488 0.7287328 0.541009 0.7287328 0.541009 0.7380322 0.5356379 0.738036 0.5571167 0.7380322 0.5356379 0.747335 0.5410054 0.738036 0.5571167 0.9766814 0.7278694 0.9874209 0.7188813 0.9874209 0.7188813 0.04790395 0.6038709 0.04790395 0.5948836 0.05864334 0.5948836 0.02740067 0.6038752 0.02740067 0.5948878 0.03814023 0.5948878 0.9842375 0.5878716 0.9949766 0.5788835 0.9949766 0.5788835 0.9859873 0.707621 0.9859873 0.6968813 0.9949753 0.6968813 0.9669165 0.7168877 0.9669165 0.7276272 0.9579277 0.7276272 0.738036 0.5571167 0.7287347 0.5517488 0.7287328 0.541009 0.7287328 0.541009 0.7380322 0.5356379 0.738036 0.5571167 0.7380322 0.5356379 0.747335 0.5410054 0.738036 0.5571167</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_b_R-mesh-map-0-array" count="184" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_mount_hz_b_R-mesh-vertices">
          <input semantic="POSITION" source="#tow_mount_hz_b_R-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="54">
          <input semantic="VERTEX" source="#tow_mount_hz_b_R-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_mount_hz_b_R-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_mount_hz_b_R-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>0 0 0 2 0 1 6 0 2 4 0 3 20 1 4 22 2 5 18 3 6 13 4 7 16 5 8 15 6 9 13 4 10 18 3 11 15 6 12 16 5 13 10 7 14 8 7 15 4 0 16 6 0 17 22 2 18 20 1 19 24 8 20 28 8 21 30 8 22 26 8 23 44 9 24 36 10 25 42 11 26 46 12 27 40 13 28 42 11 29 36 10 30 38 14 31 38 14 32 32 15 33 34 15 34 40 13 35 28 8 36 44 9 37 46 12 38 30 8 39 0 16 40 4 17 41 28 18 42 24 16 43 2 15 44 0 15 45 24 15 46 26 15 47 6 19 48 2 19 49 26 19 50 30 19 51 8 8 52 10 8 53 34 8 54 32 8 55 16 20 56 18 21 57 42 22 58 40 23 59 13 24 60 15 25 61 38 26 62 36 27 63 15 25 64 8 28 65 32 28 66 38 26 67 10 19 68 16 20 69 40 23 70 34 19 71 4 17 72 20 29 73 44 30 74 28 18 75 18 21 76 22 31 77 46 32 78 42 22 79 20 29 80 13 24 81 36 27 82 44 30 83 22 31 84 6 33 85 30 33 86 46 32 87 19 34 88 11 34 89 21 34 90 17 35 91 9 36 92 19 37 93 14 38 94 7 39 95 17 40 96 12 19 97 5 19 98 14 19 99 12 41 100 1 42 101 3 43 102 11 44 103 23 45 104 21 46 105 3 47 106 1 48 107 11 49 108 43 34 109 35 34 110 45 34 111 41 35 112 33 36 113 43 37 114 39 38 115 31 39 116 41 40 117 37 19 118 29 19 119 39 19 120 37 41 121 25 42 122 27 43 123 35 44 124 47 45 125 45 46 126 27 50 127 25 48 128 35 51 129 19 34 130 9 34 131 11 34 132 17 35 133 7 52 134 9 36 135 14 38 136 5 53 137 7 39 138 12 19 139 3 19 140 5 19 141 12 41 142 23 54 143 1 42 144 11 44 145 1 55 146 23 45 147 11 49 148 9 56 149 7 57 150 7 57 151 5 58 152 11 49 153 5 58 154 3 47 155 11 49 156 43 34 157 33 34 158 35 34 159 41 35 160 31 52 161 33 36 162 39 38 163 29 53 164 31 39 165 37 19 166 27 19 167 29 19 168 37 41 169 47 54 170 25 42 171 35 44 172 25 55 173 47 45 174 35 51 175 33 59 176 31 60 177 31 60 178 29 61 179 35 51 180 29 61 181 27 50 182 35 51 183</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_mount_hz_R-mesh" name="tow_mount_hz_R">
      <mesh>
        <source id="tow_mount_hz_R-mesh-positions">
          <float_array id="tow_mount_hz_R-mesh-positions-array" count="156">-0.4028258 -0.08484792 -0.02980327 -0.4320057 -0.07184338 0.05404877 -0.4028258 -0.03136479 -0.02980327 -0.435582 -0.06564909 0.05404877 -0.4028258 -0.160508 0.03100156 -0.4427345 -0.06564909 0.05404877 -0.4028258 -0.03136479 0.03100156 -0.4463109 -0.07184338 0.05404877 -0.4028258 0.03262549 -0.02980327 -0.4427345 -0.07803773 0.05404877 -0.4028258 0.03262549 0.03100156 -0.435582 -0.07803773 0.05404877 -0.4641398 -0.1659853 0.06812256 -0.435582 -0.06564909 0.06003487 -0.4641398 -0.0439254 0.06812256 -0.4427345 -0.06564909 0.06003487 -0.4028258 -0.1659853 0.05909371 -0.4463109 -0.07184338 0.06003487 -0.4427345 -0.07803773 0.06003487 -0.4118547 -0.1659853 0.06812256 -0.4118546 -0.0439254 0.06812256 -0.435582 -0.07803773 0.06003487 -0.4028258 -0.0439254 0.05909371 -0.4320057 -0.07184338 0.06003487 -0.4028258 -0.1659853 0.04207676 -0.4320057 -0.1313089 0.05404877 -0.4028258 -0.04392546 0.04207676 -0.435582 -0.1251146 0.05404877 -0.4427345 -0.1251146 0.05404877 -0.4108257 -0.08484792 -0.02980327 -0.4108257 -0.03136479 -0.02980327 -0.4463109 -0.1313089 0.05404877 -0.4108257 -0.160508 0.03100156 -0.4427345 -0.1375032 0.05404877 -0.4108257 -0.03136479 0.03100156 -0.435582 -0.1375032 0.05404877 -0.435582 -0.1251146 0.06003487 -0.4108257 0.03262549 -0.02980327 -0.4427345 -0.1251146 0.06003487 -0.4108257 0.03262549 0.03100156 -0.4463109 -0.1313089 0.06003487 -0.4641398 -0.1659853 0.0601226 -0.4427345 -0.1375032 0.06003487 -0.4641398 -0.0439254 0.0601226 -0.4108258 -0.1659853 0.05577999 -0.435582 -0.1375032 0.06003487 -0.4151684 -0.1659853 0.0601226 -0.4320057 -0.1313089 0.06003487 -0.4151684 -0.0439254 0.0601226 -0.4108257 -0.0439254 0.05577999 -0.4108257 -0.1659853 0.04207676 -0.4108257 -0.04392546 0.04207676</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_R-mesh-positions-array" count="52" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_hz_R-mesh-normals">
          <float_array id="tow_mount_hz_R-mesh-normals-array" count="189">1 0 0 1 0 5.12807e-7 1 0 3.4476e-7 0.9448387 0 0.3275362 0.9448388 0 0.327536 0.1457549 0 0.9893208 0.1457548 0 0.9893208 0 0 1 -1 0 0 -1 1.63038e-7 -6.36814e-7 -0.9722376 2.16694e-7 -0.2339959 -0.9722375 -7.39257e-5 -0.2339963 -1 0 -4.2813e-7 -0.08121567 0 -0.9966965 -0.08121544 0 -0.9966967 0 0 -1 0 -0.6264328 -0.7794754 0 -0.6631496 -0.7484869 0 -0.6631496 -0.7484869 0 1 0 8.26738e-6 0.1432399 0.989688 0 0.1432399 0.989688 -1 2.38439e-7 0 0 1 0 3.01379e-7 1 -1.51727e-6 3.0138e-7 1 -1.51727e-6 0 1 0 0 -1 2.33168e-7 0 -1 0 0 -1 2.33168e-7 0 -0.9791966 -0.2029142 9.69056e-7 -0.9791967 -0.2029139 3.35215e-7 0.9032391 0.4291377 3.27704e-7 0.9032391 0.4291377 -0.8660229 -0.5000044 0 -0.8660227 0.5000047 0 -0.8660227 0.5000048 0 -0.8660227 0.500005 0 0.866025 0.5000008 0 0.8660255 0.5 0 0.8660232 0.5000039 0 0.8660252 -0.5000005 0 0.8660257 -0.4999995 0 0.8660229 -0.5000044 0 -3.28447e-7 3.00701e-7 -1 -1.31379e-6 3.00702e-7 -1 -3.28446e-7 0 -1 -0.8660229 0.5000044 0 0.8660253 0.5000002 0 0.8660258 0.4999994 0 0.8660236 0.5000033 0 0 3.00701e-7 -1 0 3.00702e-7 -1 -6.56891e-7 0 -1 0.8660272 0.4999968 0 0.866028 -0.4999957 0 -1.31378e-6 -3.00706e-7 -1 6.56888e-7 0 -1 6.5689e-7 2.25527e-7 -1 0.8660276 0.4999962 0 -1.31378e-6 -3.00706e-7 -1 -1.31378e-6 0 -1 -6.56889e-7 2.25527e-7 -1</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_R-mesh-normals-array" count="63" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_hz_R-mesh-map-0">
          <float_array id="tow_mount_hz_R-mesh-map-0-array" count="400">0.8759952 0.5068858 0.9295307 0.5068858 0.9295308 0.5677496 0.8002611 0.5677496 0.9295308 0.5677496 0.9295307 0.5068858 0.9935839 0.5068858 0.9935839 0.5677496 0.7947784 0.5788364 0.916958 0.5788362 0.916958 0.5958702 0.7947784 0.5958704 0.916958 0.6086516 0.7947784 0.6086518 0.7947784 0.5958704 0.916958 0.5958702 0.7947784 0.6086518 0.916958 0.6086516 0.916958 0.660987 0.7947785 0.6609876 0.8002611 0.5677496 0.9295308 0.5677496 0.916958 0.5788362 0.7947784 0.5788364 0.6606852 0.6817209 0.5849509 0.6208565 0.7142207 0.6208565 0.7142207 0.6817209 0.7142207 0.6208565 0.7782738 0.6208565 0.7782738 0.6817209 0.7142207 0.6817209 0.5794683 0.6097702 0.5794683 0.5960536 0.7016478 0.5960536 0.7016477 0.6097702 0.7016478 0.5899061 0.7016478 0.5960536 0.5794683 0.5960536 0.5794684 0.5899061 0.5794684 0.5899061 0.5794684 0.5408869 0.7016479 0.5408869 0.7016478 0.5899061 0.5849509 0.6208565 0.5794683 0.6097702 0.7016477 0.6097702 0.7142207 0.6208565 0.5634347 0.8044806 0.5634348 0.7073202 0.5714427 0.7073202 0.5714426 0.8044806 0.3286633 0.9748931 0.2751278 0.9748931 0.2751278 0.9668852 0.3286633 0.9668852 0.7417697 0.9848848 0.8026342 0.9848848 0.8026342 0.9928923 0.7417697 0.9928923 0.8008952 0.8369411 0.8008952 0.7728881 0.8089022 0.7728881 0.8089022 0.8369411 0.3927164 0.974893 0.3286633 0.9748931 0.3286633 0.9668852 0.3927164 0.966885 0.9102249 0.6788898 0.7880452 0.67889 0.7880452 0.6708823 0.9102249 0.6708821 0.8099332 0.8797749 0.8008953 0.8707372 0.8089023 0.8674201 0.8132499 0.8717671 0.5634348 0.6779187 0.5724728 0.6688809 0.5757901 0.6768888 0.5714427 0.6812359 0.5724728 0.6688809 0.6248091 0.6688811 0.6248091 0.676889 0.5757901 0.6768888 0.8622681 0.8797749 0.8099332 0.8797749 0.8132499 0.8717671 0.8622681 0.8717671 0.5634348 0.7073202 0.5634348 0.6949524 0.5714427 0.6949524 0.5714427 0.7073202 0.8008953 0.8707372 0.8008953 0.8537035 0.8089023 0.8537035 0.8089023 0.8674201 0.5634348 0.6949524 0.5634348 0.6779187 0.5714427 0.6812359 0.5714427 0.6949524 0.8008953 0.8537035 0.8008952 0.8369411 0.8089022 0.8369411 0.8089023 0.8537035 0.9874209 0.7278694 0.9874209 0.7188813 0.9874209 0.7278694 0.04790395 0.6038709 0.05864334 0.5948836 0.05864334 0.6038709 0.03814023 0.6038752 0.03814023 0.5948878 0.03814023 0.6038752 0.9949766 0.5878716 0.9949766 0.5788835 0.9949766 0.5878716 0.9859873 0.707621 0.9949753 0.6968813 0.9949753 0.707621 0.9669165 0.7168877 0.9579277 0.7168877 0.9579277 0.7168877 0.747335 0.5410054 0.7473369 0.5517452 0.738036 0.5571167 0.9874209 0.7278694 0.9874209 0.7188813 0.9874209 0.7278694 0.05864334 0.6038709 0.05864334 0.5948836 0.05864334 0.6038709 0.03814023 0.6038752 0.03814023 0.5948878 0.03814023 0.6038752 0.9949766 0.5878716 0.9949766 0.5788835 0.9949766 0.5878716 0.9859873 0.707621 0.9949753 0.6968813 0.9949753 0.707621 0.9669165 0.7168877 0.9579277 0.7276272 0.9579277 0.7168877 0.747335 0.5410054 0.7473369 0.5517452 0.738036 0.5571167 0.9766814 0.7278694 0.9874209 0.7188813 0.9874209 0.7188813 0.04790395 0.6038709 0.04790395 0.5948836 0.05864334 0.5948836 0.02740067 0.6038752 0.03814023 0.5948878 0.03814023 0.5948878 0.9842375 0.5878716 0.9949766 0.5788835 0.9949766 0.5788835 0.9859873 0.707621 0.9859873 0.6968813 0.9949753 0.6968813 0.9669165 0.7168877 0.9669165 0.7168877 0.9579277 0.7276272 0.738036 0.5571167 0.7287347 0.5517488 0.7287328 0.541009 0.7287328 0.541009 0.7380322 0.5356379 0.738036 0.5571167 0.7380322 0.5356379 0.747335 0.5410054 0.738036 0.5571167 0.9766814 0.7278694 0.9874209 0.7188813 0.9874209 0.7188813 0.04790395 0.6038709 0.05864334 0.5948836 0.05864334 0.5948836 0.02740067 0.6038752 0.02740067 0.5948878 0.03814023 0.5948878 0.9842375 0.5878716 0.9949766 0.5788835 0.9949766 0.5788835 0.9859873 0.707621 0.9859873 0.6968813 0.9949753 0.6968813 0.9669165 0.7168877 0.9669165 0.7168877 0.9579277 0.7276272 0.738036 0.5571167 0.7287347 0.5517488 0.7287328 0.541009 0.7287328 0.541009 0.7380322 0.5356379 0.738036 0.5571167 0.7380322 0.5356379 0.747335 0.5410054 0.738036 0.5571167</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_R-mesh-map-0-array" count="200" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_mount_hz_R-mesh-vertices">
          <input semantic="POSITION" source="#tow_mount_hz_R-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="58">
          <input semantic="VERTEX" source="#tow_mount_hz_R-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_mount_hz_R-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_mount_hz_R-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>0 0 0 2 0 1 6 0 2 4 0 3 6 0 4 2 0 5 8 0 6 10 0 7 24 1 8 26 2 9 22 3 10 16 4 11 20 5 12 19 6 13 16 4 14 22 3 15 19 6 16 20 5 17 14 7 18 12 7 19 4 0 20 6 0 21 26 2 22 24 1 23 29 8 24 32 8 25 34 8 26 30 8 27 34 8 28 39 8 29 37 8 30 30 8 31 50 9 32 44 10 33 49 11 34 51 12 35 48 13 36 49 11 37 44 10 38 46 14 39 46 14 40 41 15 41 43 15 42 48 13 43 32 8 44 50 9 45 51 12 46 34 8 47 0 16 48 4 17 49 32 18 50 29 16 51 2 15 52 0 15 53 29 15 54 30 15 55 10 19 56 8 19 57 37 19 58 39 19 59 6 20 60 10 7 61 39 7 62 34 21 63 8 15 64 2 15 65 30 15 66 37 15 67 12 22 68 14 22 69 43 22 70 41 22 71 20 23 72 22 24 73 49 25 74 48 26 75 16 27 76 19 28 77 46 28 78 44 29 79 19 28 80 12 28 81 41 28 82 46 28 83 14 19 84 20 23 85 48 26 86 43 19 87 4 17 88 24 30 89 50 31 90 32 18 91 22 24 92 26 32 93 51 33 94 49 25 95 24 30 96 16 27 97 44 29 98 50 31 99 26 32 100 6 20 101 34 21 102 51 33 103 18 28 104 11 28 105 21 28 106 17 34 107 9 34 108 18 34 109 15 35 110 7 36 111 17 37 112 13 19 113 5 19 114 15 19 115 13 38 116 1 39 117 3 40 118 11 41 119 23 42 120 21 43 121 3 44 122 1 45 123 11 46 124 42 28 125 35 28 126 45 28 127 40 34 128 33 34 129 42 34 130 38 47 131 31 47 132 40 47 133 36 19 134 28 19 135 38 19 136 36 48 137 25 49 138 27 50 139 35 41 140 47 42 141 45 43 142 27 51 143 25 52 144 35 53 145 18 28 146 9 28 147 11 28 148 17 34 149 7 34 150 9 34 151 15 35 152 5 47 153 7 36 154 13 19 155 3 19 156 5 19 157 13 38 158 23 54 159 1 39 160 11 41 161 1 55 162 23 42 163 11 46 164 9 56 165 7 57 166 7 57 167 5 58 168 11 46 169 5 58 170 3 44 171 11 46 172 42 28 173 33 28 174 35 28 175 40 34 176 31 34 177 33 34 178 38 47 179 28 47 180 31 47 181 36 19 182 27 19 183 28 19 184 36 48 185 47 59 186 25 49 187 35 41 188 25 55 189 47 42 190 35 53 191 33 60 192 31 61 193 31 61 194 28 62 195 35 53 196 28 62 197 27 51 198 35 53 199</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_mount_vt_R-mesh" name="tow_mount_vt_R">
      <mesh>
        <source id="tow_mount_vt_R-mesh-positions">
          <float_array id="tow_mount_vt_R-mesh-positions-array" count="36">-0.2715895 -0.003957211 -0.03649359 -0.1970691 -0.003957211 -0.03649359 -0.312583 -0.003957211 0.1444737 -0.1970691 -0.003957211 0.1444737 -0.312583 -0.003957211 0.04570949 -0.1970691 -0.003957211 0.04570949 -0.2715895 0.006042718 -0.03649359 -0.1970691 0.006042718 -0.03649359 -0.312583 0.006042718 0.1444737 -0.1970691 0.006042718 0.1444737 -0.312583 0.006042718 0.04570949 -0.1970691 0.006042718 0.04570949</float_array>
          <technique_common>
            <accessor source="#tow_mount_vt_R-mesh-positions-array" count="12" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_vt_R-mesh-normals">
          <float_array id="tow_mount_vt_R-mesh-normals-array" count="45">0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 1 0 -0.8948974 0 -0.446272 -0.9733698 0 -0.2292407 -0.9733698 0 -0.2292407 0 0 -1 1 0 0 0 0 1 -1 0 0</float_array>
          <technique_common>
            <accessor source="#tow_mount_vt_R-mesh-normals-array" count="15" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_vt_R-mesh-map-0">
          <float_array id="tow_mount_vt_R-mesh-map-0-array" count="80">0.4874194 0.4888801 0.4874194 0.6045074 0.3885585 0.6045073 0.3885585 0.48888 0.5697035 0.5299133 0.5697035 0.6045063 0.4874194 0.6045074 0.4874194 0.4888801 0.4370017 0.614885 0.5358626 0.614885 0.5358626 0.7305122 0.4370015 0.7305121 0.3547179 0.6559183 0.4370017 0.614885 0.4370015 0.7305121 0.3547178 0.7305123 0.3192373 0.9488916 0.2272897 0.9488917 0.2272897 0.9388818 0.3192373 0.9388816 0.9075149 0.9508908 0.8329215 0.9508908 0.8329215 0.9408813 0.9075149 0.9408813 0.6698806 0.9508895 0.5710195 0.9508895 0.5710195 0.94088 0.6698805 0.94088 0.8008914 0.8908815 0.9165188 0.8908814 0.9165188 0.9008918 0.8008914 0.9008919 0.2272897 0.9488917 0.1284286 0.9488917 0.1284286 0.9388818 0.2272897 0.9388818 0.5710195 0.9508895 0.4887359 0.9508895 0.4887359 0.94088 0.5710195 0.94088</float_array>
          <technique_common>
            <accessor source="#tow_mount_vt_R-mesh-map-0-array" count="40" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_mount_vt_R-mesh-vertices">
          <input semantic="POSITION" source="#tow_mount_vt_R-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="10">
          <input semantic="VERTEX" source="#tow_mount_vt_R-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_mount_vt_R-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_mount_vt_R-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>4 0 0 5 1 1 3 2 2 2 2 3 0 3 4 1 3 5 5 1 6 4 0 7 10 4 8 8 5 9 9 5 10 11 6 11 6 7 12 10 4 13 11 6 14 7 7 15 0 8 16 4 9 17 10 10 18 6 8 19 1 11 20 0 11 21 6 11 22 7 11 23 3 12 24 5 12 25 11 12 26 9 12 27 2 13 28 3 13 29 9 13 30 8 13 31 4 9 32 2 14 33 8 14 34 10 10 35 5 12 36 1 12 37 7 12 38 11 12 39</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_beam_bend-mesh" name="tow_beam_bend">
      <mesh>
        <source id="tow_beam_bend-mesh-positions">
          <float_array id="tow_beam_bend-mesh-positions-array" count="312">0.468981 -0.08359777 -0.02149999 0.468981 -0.07959777 -0.02549999 0.468981 -0.07959777 0.02549999 0.468981 -0.08359777 0.02149999 -0.468981 -0.07959777 -0.02549999 -0.468981 -0.08359771 -0.02149999 -0.468981 -0.08359771 0.02149999 -0.468981 -0.07959777 0.02549999 0.468981 -0.03659778 -0.02549999 0.468981 -0.03259778 -0.02149999 0.468981 -0.03259778 0.02149999 0.468981 -0.03659778 0.02549999 -0.468981 -0.03259772 -0.02149999 -0.468981 -0.03659772 -0.02549999 -0.468981 -0.03659772 0.02549999 -0.468981 -0.03259772 0.02149999 -0.4741238 -0.08810752 -0.02530235 -0.4741238 -0.08340013 -0.03000974 -0.4741238 -0.08340013 0.03000974 -0.4741238 -0.08810752 0.02530235 -0.4741238 -0.03279536 -0.03000974 -0.4741238 -0.02808797 -0.02530235 -0.4741238 -0.02808797 0.02530235 -0.4741238 -0.03279536 0.03000974 0.4741238 -0.08810752 -0.02530235 0.4741238 -0.08340013 -0.03000974 0.4741238 -0.08340013 0.03000974 0.4741238 -0.08810752 0.02530235 0.4741238 -0.03279536 -0.03000974 0.4741238 -0.02808797 -0.02530235 0.4741238 -0.02808797 0.02530235 0.4741238 -0.03279536 0.03000974 0.3922321 -0.06076562 -0.02549999 -0.3922321 -0.06076562 -0.02549999 0.3081747 -0.04383879 -0.02549999 0.2122744 -0.03126102 -0.02549999 0.1082165 -0.02351564 -0.02549999 0 -0.02090036 -0.02549999 -0.1082165 -0.02351564 -0.02549999 -0.2122744 -0.03126096 -0.02549999 -0.3081747 -0.04383879 -0.02549999 0.3920118 -0.06471157 -0.02149999 -0.3920117 -0.06471157 -0.02149999 0.3080016 -0.04779422 -0.02149999 0.2121552 -0.03522354 -0.02149999 0.1081557 -0.0274825 -0.02149999 0 -0.02486866 -0.02149999 -0.1081557 -0.0274825 -0.02149999 -0.2121552 -0.03522348 -0.02149999 -0.3080016 -0.04779422 -0.02149999 0.3922321 -0.06076562 0.02549999 -0.3922321 -0.06076562 0.02549999 0.3081747 -0.04383879 0.02549999 0.2122744 -0.03126102 0.02549999 0.1082165 -0.02351564 0.02549999 0 -0.02090036 0.02549999 -0.1082165 -0.02351564 0.02549999 -0.2122744 -0.03126096 0.02549999 -0.3081747 -0.04383879 0.02549999 -0.3920117 -0.06471157 0.02149999 0.3920118 -0.06471157 0.02149999 -0.3080016 -0.04779422 0.02149999 -0.2121552 -0.03522348 0.02149999 -0.1081557 -0.0274825 0.02149999 0 -0.02486866 0.02149999 0.1081557 -0.0274825 0.02149999 0.2121552 -0.03522354 0.02149999 0.3080016 -0.04779422 0.02149999 -0.3922321 -0.01776564 -0.02549999 0.3922321 -0.01776564 -0.02549999 -0.3081747 -8.38804e-4 -0.02549999 -0.2122744 0.01173895 -0.02549999 -0.1082165 0.01948434 -0.02549999 0 0.02209961 -0.02549999 0.1082165 0.01948434 -0.02549999 0.2122744 0.01173895 -0.02549999 0.3081747 -8.38815e-4 -0.02549999 -0.3920117 -0.01371157 -0.02149999 0.3920117 -0.01371157 -0.02149999 -0.3080016 0.003205716 -0.02149999 -0.2121552 0.01577645 -0.02149999 -0.1081557 0.02351742 -0.02149999 0 0.02613127 -0.02149999 0.1081557 0.02351742 -0.02149999 0.2121552 0.01577645 -0.02149999 0.3080016 0.003205716 -0.02149999 -0.3922321 -0.01776564 0.02549999 0.3922321 -0.01776564 0.02549999 -0.3081747 -8.38802e-4 0.02549999 -0.2122744 0.01173895 0.02549999 -0.1082165 0.01948434 0.02549999 0 0.02209961 0.02549999 0.1082165 0.01948434 0.02549999 0.2122744 0.01173895 0.02549999 0.3081747 -8.38813e-4 0.02549999 0.3920117 -0.01371157 0.02149999 -0.3920117 -0.01371157 0.02149999 0.3080016 0.003205716 0.02149999 0.2121552 0.01577645 0.02149999 0.1081557 0.02351742 0.02149999 0 0.02613127 0.02149999 -0.1081557 0.02351742 0.02149999 -0.2121552 0.01577645 0.02149999 -0.3080016 0.003205716 0.02149999</float_array>
          <technique_common>
            <accessor source="#tow_beam_bend-mesh-positions-array" count="104" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_bend-mesh-normals">
          <float_array id="tow_beam_bend-mesh-normals-array" count="366">-0.2374951 0.9678933 -0.0823338 -0.2374829 0.9678963 0.08233386 -0.2161254 0.9728393 0.0829063 -0.2163419 0.9727913 -0.08290636 -0.0208745 -0.08493518 0.9961679 -0.01885801 -0.08485507 0.9962149 -0.2163476 -0.9728079 0.08269506 -0.2374844 -0.9678964 0.08232754 -0.2374953 -0.9678938 -0.08232754 -0.2163478 -0.9728079 -0.08269518 0.6835498 0.06192666 0.7272723 0.6835505 0.7272712 0.0619297 0.6835548 0.7272663 0.06194067 0.6835485 0.0619412 0.7272721 0.02087467 -0.08493584 -0.9961678 -0.02085173 0.08493709 -0.9961682 -0.01885807 0.0848667 -0.9962139 0.01887458 -0.0848518 -0.9962149 0.01885813 0.084867 0.9962138 0.02085196 0.08493727 0.9961681 0.02085614 0.08493626 -0.9961682 0.01885855 0.08486688 -0.9962138 0.2163419 0.9727913 -0.08290618 0.2374827 0.9678963 -0.08233344 -0.02085602 0.08493602 0.9961682 -0.01885849 0.0848667 0.9962139 0.2163476 -0.972808 -0.08269459 0.2374845 -0.9678964 -0.08232706 0.2163477 -0.972808 0.08269453 0.2374951 -0.9678938 0.082327 0.2163419 0.9727913 0.08290618 0.237495 0.9678933 0.08233344 -0.01887434 -0.08485126 -0.9962149 -0.02086663 -0.08493715 -0.9961678 0.02086704 -0.08493769 0.9961678 0.01886266 -0.08485448 0.9962149 -1 1.96314e-7 0 0.683552 -0.72727 0.06192779 0.6835476 -0.06192868 0.7272741 0.683552 -0.06193894 0.7272691 0.6835508 -0.7272699 0.06194323 0.6835498 -0.06192713 -0.7272722 0.6835498 -0.7272719 -0.06192934 0.6835542 -0.7272668 -0.06194067 0.6835485 -0.06194156 -0.7272722 0.6835527 0.7272693 -0.06192809 0.6835513 0.7272695 -0.06194323 0.6835476 0.06192827 -0.7272741 0.6835521 0.06193852 -0.727269 -0.6835476 0.06192904 0.7272741 -0.6835523 0.06193983 0.7272688 -0.6835504 0.7272704 0.06194227 -0.6835521 0.7272699 0.06192773 1 0 0 -0.6835474 -0.7272742 0.0619297 -0.6835525 -0.7272684 0.06194156 -0.6835477 -0.06194144 0.7272729 -0.6835494 -0.06192737 0.7272725 -0.6835471 -0.06192994 -0.7272744 -0.6835511 -0.06193995 -0.7272698 -0.683548 -0.7272725 -0.0619437 -0.6835489 -0.7272729 -0.06192785 -0.683555 0.7272663 -0.06194013 -0.6835507 0.7272711 -0.06192964 -0.68355 0.06192654 -0.7272721 -0.6835489 0.06194126 -0.7272717 -0.01393073 -0.08481186 -0.9962997 0.01391732 0.08480024 -0.9963008 -0.00861293 -0.08483988 -0.9963575 0.008621871 0.08478689 -0.9963619 -0.004170119 -0.08489209 -0.9963814 0.004106998 0.08479052 -0.9963904 0 -0.08485621 -0.9963933 -2.96007e-5 0.08478325 -0.9963995 0.004170238 -0.08489257 -0.9963814 -0.004145145 0.08478844 -0.9963904 0.008613049 -0.08484041 -0.9963574 -0.008632838 0.08478552 -0.9963619 0.01393103 -0.08481216 -0.9962996 -0.01392883 0.08479815 -0.9963008 0.161232 0.9833264 0.084104 0.01391732 0.08480018 0.9963008 0.1007714 0.9912768 0.08494371 0.00862199 0.08478695 0.9963618 0.04854434 0.9951635 0.08540123 0.004107177 0.0847907 0.9963904 -2.89511e-5 0.9963375 0.08550775 -2.94638e-5 0.08478349 0.9963994 -0.0485444 0.9951634 0.08540135 -0.004145085 0.08478868 0.9963904 -0.1007715 0.9912768 0.08494377 -0.008632898 0.08478575 0.9963619 -0.1612321 0.9833264 0.08410406 -0.01392883 0.08479833 0.9963008 -0.1612319 0.9833264 -0.08410412 -0.1007141 0.9912826 -0.08494412 -0.04854434 0.9951634 -0.08540159 2.89917e-5 0.9963375 -0.08550798 0.0485444 0.9951634 -0.08540135 0.1007715 0.9912768 -0.08494377 0.1612321 0.9833264 -0.08410406 0.1612117 -0.9833882 0.08341747 0.01391321 -0.08481514 0.9962996 0.1007302 -0.9913713 0.08388352 0.008612811 -0.08484035 0.9963574 0.04857605 -0.9952722 0.08410483 0.004121601 -0.08489483 0.9963814 -1.4566e-4 -0.99645 0.08418798 -1.9419e-5 -0.08485609 0.9963933 -0.04857516 -0.9952722 0.08410525 -0.004144608 -0.08489328 0.9963814 -0.1007285 -0.9913715 0.08388406 -0.008627831 -0.08483827 0.9963574 -0.1612099 -0.9833885 0.08341789 -0.01391792 -0.08481401 0.9962996 -0.16121 -0.9833885 -0.08341795 -0.1007285 -0.9913714 -0.083884 -0.04857522 -0.9952723 -0.08410525 -1.45713e-4 -0.99645 -0.08418798 0.04857599 -0.9952723 -0.08410471 0.1007301 -0.9913713 -0.0838834 0.1612116 -0.9833882 -0.08341747</float_array>
          <technique_common>
            <accessor source="#tow_beam_bend-mesh-normals-array" count="122" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_bend-mesh-map-0">
          <float_array id="tow_beam_bend-mesh-map-0-array" count="800">0.9713096 0.130266 0.9713096 0.1718945 0.8962086 0.1718945 0.8962086 0.130266 0.05611449 0.2189997 0.1310005 0.2189996 0.1312157 0.2244762 0.05611485 0.2244762 0.05611473 0.07768464 0.05611473 0.0360561 0.1312156 0.0360561 0.1312157 0.07768464 0.002330601 0.1686181 0.002298116 0.1570087 0.01255089 0.1584517 0.01255089 0.1669963 0.9713094 0.08316093 0.9713096 0.1247895 0.8964235 0.1247895 0.8964235 0.08316093 0.1310007 0.1773711 0.1310007 0.2189997 0.05611473 0.2189997 0.05611473 0.1773711 0.05611485 0.1247897 0.1310008 0.1247897 0.1312159 0.130266 0.05611497 0.130266 0.9713096 0.1773709 0.8964235 0.1773709 0.8962084 0.1718944 0.9713093 0.1718944 0.9713096 0.08316093 0.8964235 0.08316093 0.8962089 0.07768446 0.9713099 0.07768446 0.8962086 0.07768446 0.8962086 0.03605592 0.9713096 0.03605592 0.9713096 0.07768446 0.1312157 0.130266 0.1312157 0.1718946 0.05611467 0.1718946 0.05611467 0.130266 0.8964236 0.1247895 0.9713097 0.1247895 0.9713094 0.1302659 0.8962085 0.130266 0.1310005 0.08316111 0.05611449 0.08316117 0.05611479 0.07768464 0.1312157 0.07768464 0.9713096 0.1773709 0.9713096 0.2189995 0.8964235 0.2189995 0.8964236 0.1773709 0.8964236 0.2189995 0.9713097 0.2189995 0.9713098 0.224476 0.8962088 0.224476 0.1310008 0.177371 0.05611485 0.1773711 0.05611497 0.1718946 0.1312159 0.1718946 0.8723409 0.1713312 0.8684685 0.1674588 0.8684685 0.1258302 0.8723409 0.1219578 0.9139696 0.1219578 0.9178419 0.1258302 0.9178419 0.1674588 0.9139696 0.1713312 0.002514958 0.2420559 0.002371311 0.2305397 0.01255089 0.2319467 0.01255089 0.2404912 0.002371311 0.2305397 0.002330601 0.1686181 0.01255089 0.1669963 0.01255089 0.2319467 0.001077175 0.3105879 0.002998411 0.3021542 0.01255089 0.3054415 0.01255089 0.3139861 0.002298116 0.1570087 0.002448379 0.09498536 0.01255089 0.09350138 0.01255089 0.1584517 0.002633929 0.08355516 0.003215372 0.02508133 0.01255089 0.02000653 0.01255089 0.08495688 0.002448379 0.09498536 0.002633929 0.08355516 0.01255089 0.08495688 0.01255089 0.09350138 0.002998411 0.3021542 0.002514958 0.2420559 0.01255089 0.2404912 0.01255089 0.3054415 0.002330601 0.1686181 0.01255089 0.1669963 0.01255089 0.1584517 0.002298116 0.1570087 0.8723409 0.1713312 0.9139696 0.1713312 0.9178419 0.1674588 0.9178419 0.1258302 0.9139696 0.1219578 0.8723409 0.1219578 0.8684685 0.1258302 0.8684685 0.1674588 0.002514958 0.2420559 0.01255089 0.2404912 0.01255089 0.2319467 0.002371311 0.2305397 0.002371311 0.2305397 0.01255089 0.2319467 0.01255089 0.1669963 0.002330601 0.1686181 0.001077175 0.3105879 0.01255089 0.3139861 0.01255089 0.3054415 0.002998411 0.3021542 0.002298116 0.1570087 0.01255089 0.1584517 0.01255089 0.09350138 0.002448379 0.09498536 0.002633929 0.08355516 0.01255089 0.08495688 0.01255089 0.02000653 0.003215372 0.02508133 0.002448379 0.09498536 0.01255089 0.09350138 0.01255089 0.08495688 0.002633929 0.08355516 0.002998411 0.3021542 0.01255089 0.3054415 0.01255089 0.2404912 0.002514958 0.2420559 0.1310007 0.1247897 0.1310007 0.08316111 0.2128028 0.08014184 0.2128028 0.1217704 0.2128028 0.1217704 0.2128028 0.08014184 0.3057389 0.07120007 0.3057389 0.1128287 0.3057389 0.1128287 0.3057389 0.07120007 0.4062375 0.05667936 0.4062374 0.09830826 0.4062374 0.09830826 0.4062375 0.05667936 0.5137122 0.08042281 0.5137122 0.1039753 0.5137122 0.1039753 0.5137122 0.08042281 0.621187 0.05667936 0.621187 0.0983082 0.621187 0.0983082 0.621187 0.05667936 0.7216855 0.07119995 0.7216855 0.1128287 0.7216855 0.1128287 0.7216855 0.07119995 0.8146215 0.08014172 0.8146216 0.1217703 0.8146216 0.1217703 0.8146215 0.08014172 0.8964235 0.08316093 0.8964235 0.1247895 0.1310008 0.177371 0.1312159 0.1718946 0.2125546 0.1731228 0.2128028 0.1803903 0.2128028 0.1803903 0.2125546 0.1731228 0.3042023 0.1767604 0.3057388 0.189332 0.3057388 0.189332 0.3042023 0.1767604 0.4026371 0.1826675 0.4062374 0.2038527 0.4062374 0.2038527 0.4026371 0.1826675 0.5137122 0.1746328 0.5137122 0.1981853 0.5137122 0.1981853 0.5137122 0.1746328 0.6247874 0.1826675 0.6211871 0.2038526 0.6211871 0.2038526 0.6247874 0.1826675 0.7232221 0.1767603 0.7216855 0.189332 0.7216855 0.189332 0.7232221 0.1767603 0.8148698 0.1731228 0.8146215 0.1803902 0.8146215 0.1803902 0.8148698 0.1731228 0.8962084 0.1718944 0.8964235 0.1773709 0.8964236 0.1247895 0.8962085 0.130266 0.8148698 0.1290377 0.8146216 0.1217703 0.8146216 0.1217703 0.8148698 0.1290377 0.7232221 0.1254002 0.7216855 0.1128287 0.7216855 0.1128287 0.7232221 0.1254002 0.6247873 0.1194932 0.621187 0.0983082 0.621187 0.0983082 0.6247873 0.1194932 0.5137122 0.1275278 0.5137122 0.1039753 0.5137122 0.1039753 0.5137122 0.1275278 0.4026371 0.1194933 0.4062374 0.09830826 0.4062374 0.09830826 0.4026371 0.1194933 0.3042022 0.1254003 0.3057389 0.1128287 0.3057389 0.1128287 0.3042022 0.1254003 0.2125545 0.1290377 0.2128028 0.1217704 0.2128028 0.1217704 0.2125545 0.1290377 0.1312159 0.130266 0.1310008 0.1247897 0.8964236 0.2189995 0.8962088 0.224476 0.8146674 0.2269325 0.8146216 0.2220188 0.8146216 0.2220188 0.8146674 0.2269325 0.7224197 0.2342078 0.7216855 0.2309606 0.7216855 0.2309606 0.7224197 0.2342078 0.6230105 0.2460219 0.6211869 0.2454811 0.6211869 0.2454811 0.6230105 0.2460219 0.5137122 0.224476 0.5137122 0.2217378 0.5137122 0.2217378 0.5137122 0.224476 0.4044144 0.2460221 0.4062376 0.2454813 0.4062376 0.2454813 0.4044144 0.2460221 0.3050051 0.2342078 0.3057389 0.2309606 0.3057389 0.2309606 0.3050051 0.2342078 0.2127572 0.2269327 0.2128028 0.2220189 0.2128028 0.2220189 0.2127572 0.2269327 0.1312157 0.2244762 0.1310005 0.2189996 0.1310005 0.08316111 0.1312157 0.07768464 0.2125545 0.07645636 0.2128028 0.08014184 0.2128028 0.08014184 0.2125545 0.07645636 0.3042024 0.07281875 0.3057389 0.07120007 0.3057389 0.07120007 0.3042024 0.07281875 0.4026372 0.06691157 0.4062375 0.05667936 0.4062375 0.05667936 0.4026372 0.06691157 0.5137122 0.07768458 0.5137122 0.08042281 0.5137122 0.08042281 0.5137122 0.07768458 0.6247874 0.06691157 0.621187 0.05667936 0.621187 0.05667936 0.6247874 0.06691157 0.7232224 0.07281863 0.7216855 0.07119995 0.7216855 0.07119995 0.7232224 0.07281863 0.8148699 0.07645624 0.8146215 0.08014172 0.8146215 0.08014172 0.8148699 0.07645624 0.8962089 0.07768446 0.8964235 0.08316093 0.8962086 0.03605592 0.8962086 0.07768446 0.8148699 0.07645624 0.8150724 0.03605592 0.8150724 0.03605592 0.8148699 0.07645624 0.7232224 0.07281863 0.7240247 0.03605592 0.7240247 0.03605592 0.7232224 0.07281863 0.6247874 0.06691157 0.6265642 0.03605598 0.6265642 0.03605598 0.6247874 0.06691157 0.5137122 0.07768458 0.5137122 0.03605598 0.5137122 0.03605598 0.5137122 0.07768458 0.4026372 0.06691157 0.4008601 0.03605604 0.4008601 0.03605604 0.4026372 0.06691157 0.3042024 0.07281875 0.3033996 0.03605604 0.3033996 0.03605604 0.3042024 0.07281875 0.2125545 0.07645636 0.2123519 0.03605604 0.2123519 0.03605604 0.2125545 0.07645636 0.1312157 0.07768464 0.1312156 0.0360561 0.1310007 0.2189997 0.1310007 0.1773711 0.2128028 0.1803903 0.2128028 0.2220189 0.2128028 0.2220189 0.2128028 0.1803903 0.3057388 0.189332 0.3057389 0.2309606 0.3057389 0.2309606 0.3057388 0.189332 0.4062374 0.2038527 0.4062376 0.2454813 0.4062376 0.2454813 0.4062374 0.2038527 0.5137122 0.1981853 0.5137122 0.2217378 0.5137122 0.2217378 0.5137122 0.1981853 0.6211871 0.2038526 0.6211869 0.2454811 0.6211869 0.2454811 0.6211871 0.2038526 0.7216855 0.189332 0.7216855 0.2309606 0.7216855 0.2309606 0.7216855 0.189332 0.8146215 0.1803902 0.8146216 0.2220188 0.8146216 0.2220188 0.8146215 0.1803902 0.8964236 0.1773709 0.8964235 0.2189995 0.1312157 0.1718946 0.1312157 0.130266 0.2125545 0.1290377 0.2125546 0.1731228 0.2125546 0.1731228 0.2125545 0.1290377 0.3042022 0.1254003 0.3042023 0.1767604 0.3042023 0.1767604 0.3042022 0.1254003 0.4026371 0.1194933 0.4026371 0.1826675 0.4026371 0.1826675 0.4026371 0.1194933 0.5137122 0.1275278 0.5137122 0.1746328 0.5137122 0.1746328 0.5137122 0.1275278 0.6247873 0.1194932 0.6247874 0.1826675 0.6247874 0.1826675 0.6247873 0.1194932 0.7232221 0.1254002 0.7232221 0.1767603 0.7232221 0.1767603 0.7232221 0.1254002 0.8148698 0.1290377 0.8148698 0.1731228 0.8148698 0.1731228 0.8148698 0.1290377 0.8962086 0.130266 0.8962086 0.1718945 0.1310007 0.08316111 0.1310007 0.1247897 0.05611473 0.1247897 0.05611473 0.08316111</float_array>
          <technique_common>
            <accessor source="#tow_beam_bend-mesh-map-0-array" count="400" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_beam_bend-mesh-vertices">
          <input semantic="POSITION" source="#tow_beam_bend-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="98">
          <input semantic="VERTEX" source="#tow_beam_bend-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_beam_bend-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_beam_bend-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 8 4 4 4 4 4 4 4 4 8 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>12 0 0 15 1 1 96 2 2 77 3 3 2 4 4 50 5 5 60 6 6 3 7 7 0 8 8 3 7 9 60 6 10 41 9 11 14 10 12 15 11 13 22 12 14 23 13 15 4 14 16 13 15 17 68 16 18 33 17 19 87 18 20 50 5 21 2 4 22 11 19 23 8 20 24 69 21 25 78 22 26 9 23 27 14 24 28 86 25 29 96 2 30 15 1 31 4 14 32 33 17 33 42 26 34 5 27 35 42 26 36 59 28 37 6 29 38 5 27 39 78 22 40 95 30 41 10 31 42 9 23 43 68 16 44 13 15 45 12 0 46 77 3 47 32 32 48 1 33 49 0 8 50 41 9 51 14 24 52 7 34 53 51 35 54 86 25 55 51 35 56 7 34 57 6 29 58 59 28 59 87 18 60 11 19 61 10 31 62 95 30 63 17 36 64 16 36 65 19 36 66 18 36 67 23 36 68 22 36 69 21 36 70 20 36 71 6 37 72 7 38 73 18 39 74 19 40 75 7 38 76 14 10 77 23 13 78 18 39 79 4 41 80 5 42 81 16 43 82 17 44 83 15 11 84 12 45 85 21 46 86 22 12 87 13 47 88 4 41 89 17 44 90 20 48 91 12 45 92 13 47 93 20 48 94 21 46 95 5 42 96 6 37 97 19 40 98 16 43 99 11 49 100 31 50 101 30 51 102 10 52 103 25 53 104 28 53 105 29 53 106 30 53 107 31 53 108 26 53 109 27 53 110 24 53 111 3 54 112 27 55 113 26 56 114 2 57 115 2 57 116 26 56 117 31 50 118 11 49 119 1 58 120 25 59 121 24 60 122 0 61 123 10 52 124 30 51 125 29 62 126 9 63 127 8 64 128 28 65 129 25 59 130 1 58 131 9 63 132 29 62 133 28 65 134 8 64 135 0 61 136 24 60 137 27 55 138 3 54 139 69 21 140 32 32 141 34 66 142 76 67 143 76 67 144 34 66 145 35 68 146 75 69 147 75 69 148 35 68 149 36 70 150 74 71 151 74 71 152 36 70 153 37 72 154 73 73 155 73 73 156 37 72 157 38 74 158 72 75 159 72 75 160 38 74 161 39 76 162 71 77 163 71 77 164 39 76 165 40 78 166 70 79 167 70 79 168 40 78 169 33 17 170 68 16 171 87 18 172 95 30 173 97 80 174 94 81 175 94 81 176 97 80 177 98 82 178 93 83 179 93 83 180 98 82 181 99 84 182 92 85 183 92 85 184 99 84 185 100 86 186 91 87 187 91 87 188 100 86 189 101 88 190 90 89 191 90 89 192 101 88 193 102 90 194 89 91 195 89 91 196 102 90 197 103 92 198 88 93 199 88 93 200 103 92 201 96 2 202 86 25 203 68 16 204 77 3 205 79 94 206 70 79 207 70 79 208 79 94 209 80 95 210 71 77 211 71 77 212 80 95 213 81 96 214 72 75 215 72 75 216 81 96 217 82 97 218 73 73 219 73 73 220 82 97 221 83 98 222 74 71 223 74 71 224 83 98 225 84 99 226 75 69 227 75 69 228 84 99 229 85 100 230 76 67 231 76 67 232 85 100 233 78 22 234 69 21 235 51 35 236 59 28 237 61 101 238 58 102 239 58 102 240 61 101 241 62 103 242 57 104 243 57 104 244 62 103 245 63 105 246 56 106 247 56 106 248 63 105 249 64 107 250 55 108 251 55 108 252 64 107 253 65 109 254 54 110 255 54 110 256 65 109 257 66 111 258 53 112 259 53 112 260 66 111 261 67 113 262 52 114 263 52 114 264 67 113 265 60 6 266 50 5 267 32 32 268 41 9 269 43 115 270 34 66 271 34 66 272 43 115 273 44 116 274 35 68 275 35 68 276 44 116 277 45 117 278 36 70 279 36 70 280 45 117 281 46 118 282 37 72 283 37 72 284 46 118 285 47 119 286 38 74 287 38 74 288 47 119 289 48 120 290 39 76 291 39 76 292 48 120 293 49 121 294 40 78 295 40 78 296 49 121 297 42 26 298 33 17 299 59 28 300 42 26 301 49 121 302 61 101 303 61 101 304 49 121 305 48 120 306 62 103 307 62 103 308 48 120 309 47 119 310 63 105 311 63 105 312 47 119 313 46 118 314 64 107 315 64 107 316 46 118 317 45 117 318 65 109 319 65 109 320 45 117 321 44 116 322 66 111 323 66 111 324 44 116 325 43 115 326 67 113 327 67 113 328 43 115 329 41 9 330 60 6 331 50 5 332 87 18 333 94 81 334 52 114 335 52 114 336 94 81 337 93 83 338 53 112 339 53 112 340 93 83 341 92 85 342 54 110 343 54 110 344 92 85 345 91 87 346 55 108 347 55 108 348 91 87 349 90 89 350 56 106 351 56 106 352 90 89 353 89 91 354 57 104 355 57 104 356 89 91 357 88 93 358 58 102 359 58 102 360 88 93 361 86 25 362 51 35 363 95 30 364 78 22 365 85 100 366 97 80 367 97 80 368 85 100 369 84 99 370 98 82 371 98 82 372 84 99 373 83 98 374 99 84 375 99 84 376 83 98 377 82 97 378 100 86 379 100 86 380 82 97 381 81 96 382 101 88 383 101 88 384 81 96 385 80 95 386 102 90 387 102 90 388 80 95 389 79 94 390 103 92 391 103 92 392 79 94 393 77 3 394 96 2 395 32 32 396 69 21 397 8 20 398 1 33 399</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_shackle_mount-mesh" name="tow_shackle_mount">
      <mesh>
        <source id="tow_shackle_mount-mesh-positions">
          <float_array id="tow_shackle_mount-mesh-positions-array" count="174">0.01313596 0.1483177 0.02552717 -0.01313674 0.1483177 0.02547276 0.01313596 0.1819683 0.0195524 0.01313596 0.1905604 0.01155275 -0.01313674 0.1819683 0.01949799 -0.01313674 0.1905604 0.01149827 -0.01313668 0.1483177 -0.02547276 0.01313596 0.1483177 -0.02552717 0.01313596 0.1819683 -0.01955229 0.01313596 0.1905604 -0.01155257 0.01313596 0.1937043 0 -0.01313674 0.1819683 -0.01949787 -0.01313674 0.1905604 -0.01149815 -0.01313674 0.1937043 0 0.03410845 0.1600252 0 0.03410845 0.1721653 0.003944337 0.03410845 0.1625893 0.007888674 0.03410845 0.1692993 0.01276421 0.03410845 0.1775943 0.01276421 0.03410845 0.1843042 0.007888734 0.03410845 0.1867533 0 0.03410845 0.1843042 -0.007888674 0.03410845 0.1775943 -0.01276415 0.03410845 0.1692993 -0.01276415 0.03410845 0.1625893 -0.007888674 0.02996414 0.1600252 0 0.02996414 0.1625893 0.007888674 0.02996414 0.1692993 0.01276421 0.02996414 0.1775943 0.01276421 0.02996414 0.1843042 0.007888734 0.02996414 0.1867533 0 0.02996414 0.1843042 -0.007888674 0.02996414 0.1775943 -0.01276415 0.02996414 0.1692993 -0.01276415 0.02996414 0.1625893 -0.007888674 0.03410845 0.1747203 -0.003928065 0.04590946 0.1867533 0 0.04590934 0.1843042 0.007888734 0.04590946 0.1721653 0.003944337 0.04590946 0.1600252 0 0.04590946 0.1625893 -0.007888674 0.04590946 0.1747203 -0.003928065 -0.02549999 -0.02443265 -0.02149999 -0.02149999 -0.02443265 -0.02549999 -0.02149999 -0.02443265 0.02549993 -0.02549999 -0.02443265 0.02149993 0.02149999 -0.02443265 -0.02549999 0.02549999 -0.02443265 -0.02149999 0.02549999 -0.02443265 0.02149993 0.02149999 -0.02443265 0.02549993 -0.02549999 0.1417685 -0.02149999 -0.02149999 0.1417686 -0.02549999 0.02549999 0.1417686 -0.02149999 -0.02149999 0.1417686 0.02549993 0.02149999 0.1417685 -0.02549999 -0.02549999 0.1417685 0.02149993 0.02549999 0.1417685 0.02149993 0.02149999 0.1417685 0.02549993</float_array>
          <technique_common>
            <accessor source="#tow_shackle_mount-mesh-positions-array" count="58" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_shackle_mount-mesh-normals">
          <float_array id="tow_shackle_mount-mesh-normals-array" count="216">-9.31094e-4 0.8556446 0.5175631 -9.2969e-4 0.8559643 0.5170342 -0.001853764 0.44653 0.8947668 -0.001854062 0.4462321 0.8949154 -2.8266e-4 1 -1.76994e-7 -2.8266e-4 1 -1.47495e-7 -0.001280367 0.09774208 -0.995211 -0.001853346 0.4465329 -0.8947653 -0.001853644 0.4462351 -0.894914 0.01869666 0.05249434 -0.9984462 -1 -1.28228e-7 0 -0.8964467 0.4431517 -6.56865e-5 -0.8656181 0.5006346 -0.008392333 -9.29092e-4 0.8559646 -0.5170337 -9.30494e-4 0.8556452 -0.517562 -0.04412287 0.03458631 0.9984273 -0.001282155 0.119819 0.992795 1 0 0 0.9043412 0.4268105 -1.1444e-4 0.8770103 0.4804558 0.003902196 1 0 -1.27e-7 1 1.66161e-6 6.20532e-6 0 0.8720363 0.4894411 0 0.8128746 0.5824388 0 0.3090372 0.9510499 0 0.3090372 0.95105 1.35972e-7 -0.8675765 -0.4973039 1.35979e-7 -0.994182 -0.1077137 0 -1 0 0 -0.8089995 -0.5878093 0 0.9946883 0.1029336 0 1 7.15176e-7 0 -0.3090367 -0.9510501 0 -0.3090367 -0.9510502 0 0.8128746 -0.5824388 0 0.3103274 -0.9506298 0 0.3103646 -0.9506176 0 -0.8090015 0.5878067 0 -0.8090014 0.5878068 3.90227e-7 -0.9510252 -0.3091136 0 -0.3090398 0.9510492 1 -3.66994e-7 0 1 1.8326e-7 0 1 0 2.24629e-7 1 2.75211e-7 0 0 0.9550361 0.2964895 0 -0.3090323 0.9510515 -1.19546e-6 -0.3090016 0.9510616 0 0.3090366 -0.9510502 0 0.3090365 -0.9510502 0 -1 0 0.3826835 0 -0.9238796 0.3149296 0 -0.949115 0.9238798 0 -0.3826829 0.9238796 0 -0.3826833 -0.3826833 0 0.9238796 -0.3151912 0 0.9490282 -0.9238793 0 0.3826842 -0.9238796 0 0.3826835 -0.9238796 0 -0.3826833 -0.9238794 0 -0.382684 0.9238794 0 0.3826838 0.3149283 0 0.9491155 0.3826835 0 0.9238796 0.9238796 0 0.3826833 -0.3151915 0 -0.9490282 -0.3826835 0 -0.9238796 -7.45058e-6 1 0 -0.4317207 0.9020075 0 -0.4317208 0.9020074 0 0 1 6.1923e-7 0.4321368 0.9018081 2.16682e-7</float_array>
          <technique_common>
            <accessor source="#tow_shackle_mount-mesh-normals-array" count="72" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_shackle_mount-mesh-map-0">
          <float_array id="tow_shackle_mount-mesh-map-0-array" count="380">0.2237691 0.4506888 0.2237693 0.4769871 0.2120181 0.4769709 0.2120181 0.4506724 0.2357537 0.4507128 0.2357012 0.4770115 0.2237693 0.4769871 0.2237691 0.4506888 0.1778078 0.3532504 0.2120182 0.3532376 0.2120181 0.3795361 0.1778076 0.379549 0.9799143 0.920902 0.9885148 0.928909 0.9916618 0.9404188 0.9885148 0.9519279 0.9799143 0.9599361 0.9462308 0.9659161 0.9462308 0.9149208 0.2593842 0.4770652 0.2476332 0.4770354 0.2477382 0.4507367 0.2594892 0.4507668 0.2476332 0.4770354 0.2357012 0.4770115 0.2357537 0.4507128 0.2477382 0.4507367 0.2120181 0.4506724 0.2120181 0.4769709 0.1778077 0.4769584 0.1778076 0.45066 0.1976661 0.3988721 0.2062665 0.3908651 0.2399501 0.384884 0.2399502 0.4359884 0.2062665 0.4300077 0.1976661 0.4220002 0.1945191 0.4104365 0.8060572 0.5297504 0.8086338 0.5376437 0.799333 0.5376538 0.799333 0.5248802 0.04017806 0.5831864 0.0401746 0.574902 0.05284482 0.5749184 0.05295425 0.5831854 0.5865945 0.7283979 0.5824471 0.7283979 0.5824471 0.7200955 0.5865945 0.7200955 0.5865945 0.686884 0.5865945 0.6951869 0.5824471 0.6951869 0.5824471 0.686884 0.5865945 0.736666 0.5824471 0.736666 0.5824471 0.7283979 0.5865945 0.7283979 0.5865945 0.7615397 0.5865945 0.769842 0.5824471 0.769842 0.5824471 0.7615397 0.5865945 0.7449342 0.5824471 0.7449342 0.5824471 0.736666 0.5865945 0.736666 0.799333 0.5503242 0.7875206 0.5503242 0.7875206 0.5376538 0.799333 0.5376538 0.799333 0.5376538 0.7875206 0.5376538 0.7875206 0.5248801 0.799333 0.5248802 0.5865945 0.70349 0.5824471 0.70349 0.5824471 0.6951869 0.5865945 0.6951869 0.5865945 0.6951869 0.5865945 0.686884 0.5984073 0.686884 0.5984073 0.6951869 0.5865945 0.7200955 0.5824471 0.7200955 0.5824471 0.7117924 0.5865945 0.7117924 0.806079 0.5455433 0.799333 0.5503242 0.799333 0.5376538 0.8086338 0.5376437 0.2403195 0.3497608 0.2428848 0.3576576 0.2336024 0.3576573 0.2336024 0.3448811 0.2403195 0.3655534 0.2336024 0.3704345 0.2336024 0.3576573 0.2428848 0.3576576 0.04017806 0.5831864 0.02740085 0.5831884 0.02740097 0.5748856 0.0401746 0.574902 0.5865945 0.736666 0.5865945 0.7283979 0.5984073 0.7283979 0.5984073 0.736666 0.2336024 0.3576573 0.2217895 0.3576573 0.2217895 0.3448809 0.2336024 0.3448811 0.2336024 0.3576573 0.2336024 0.3704345 0.2217895 0.3704345 0.2217895 0.3576573 0.5865945 0.7615397 0.5824471 0.7615397 0.5824471 0.7532365 0.5865945 0.7532365 0.5865945 0.7532365 0.5824471 0.7532365 0.5824471 0.7449342 0.5865945 0.7449342 0.07213526 0.8448861 0.07613909 0.8408821 0.1191815 0.8408821 0.1231853 0.8448861 0.1231853 0.8879282 0.1191815 0.8919317 0.07613909 0.8919317 0.07213526 0.8879282 0.004887998 0.3879211 0.171252 0.3879211 0.1712521 0.3935835 0.004887998 0.3935834 0.004887938 0.4853299 0.171252 0.4853299 0.171252 0.4909924 0.004887938 0.4909923 0.004887938 0.5340341 0.004887938 0.4909923 0.171252 0.4909924 0.1712521 0.5340342 0.171252 0.4366253 0.171252 0.4422876 0.004887938 0.4422876 0.004887938 0.4366253 0.1712521 0.3448785 0.171252 0.3879211 0.004887998 0.3879211 0.004887998 0.3448785 0.1712521 0.5340342 0.1712521 0.5396966 0.004887938 0.5396965 0.004887938 0.5340341 0.1712521 0.3935835 0.171252 0.4366253 0.004887938 0.4366253 0.004887998 0.3935834 0.171252 0.4422876 0.171252 0.4853299 0.004887938 0.4853299 0.004887938 0.4422876 0.9315941 0.9619395 0.9315941 0.9188979 0.9355979 0.914894 0.9355979 0.9659434 0.2545875 0.4319575 0.2505837 0.4359614 0.2505835 0.3849114 0.2545875 0.3889159 0.1778077 0.4769584 0.171252 0.4853299 0.171252 0.4422876 0.1778076 0.45066 0.1778078 0.3532504 0.1778076 0.379549 0.171252 0.3879211 0.1712521 0.3448785 0.9462308 0.9149208 0.9462308 0.9659161 0.9355979 0.9659434 0.9355979 0.914894 0.2399501 0.384884 0.2505835 0.3849114 0.2505837 0.4359614 0.2399502 0.4359884 0.5865945 0.7117924 0.5824471 0.7117924 0.5824471 0.70349 0.5865945 0.70349</float_array>
          <technique_common>
            <accessor source="#tow_shackle_mount-mesh-map-0-array" count="190" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_shackle_mount-mesh-map-1">
          <float_array id="tow_shackle_mount-mesh-map-1-array" count="380">0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.375 0.7696079 0.3946079 0.75 0.6053922 0.75 0.6250001 0.7696079 0.625 0.9803922 0.6053922 1 0.3946079 1 0.375 0.9803922 0.3553921 1.169459 0.3553921 0.8544107 0.3946079 0.8551081 0.3946079 1.169459 0.855392 1.169459 0.855392 0.8615069 0.8749998 0.8613824 0.8749998 1.169459 0.3946079 -0.4194591 0.6053921 -0.4194592 0.6053921 -0.1110648 0.3946079 -0.1051082 0.6053922 0.8610649 0.6446077 0.8615069 0.6446077 1.169459 0.6053922 1.169459 0.1446078 0.8544107 0.3553921 0.8544107 0.3553921 1.169459 0.1446078 1.169459 0.125 0.861195 0.1446078 0.8544107 0.1446078 1.169459 0.125 1.169459 0.3946079 0.8551081 0.6053922 0.8610649 0.6053922 1.169459 0.3946079 1.169459 0.6446077 0.8615069 0.855392 0.8615069 0.855392 1.169459 0.6446077 1.169459 0.3946079 -0.1051082 0.6053921 -0.1110648 0.855392 0.8615069 0.1446078 0.8544107 0.3946079 0.8551081 0.3553921 0.8544107 0.6446077 0.8615069 0.6053922 0.8610649 0 0 0.855392 0.8615069 0.6446077 0.8615069 0 0 0 0 0 0 0.3553921 0.8544107 0.1446078 0.8544107 0 0 0 0 0.1446078 0.8544107 0.855392 0.8615069 0 0 0.6446077 0.8615069 0.3553921 0.8544107 0 0 0 0 0 0 0 0 0 0</float_array>
          <technique_common>
            <accessor source="#tow_shackle_mount-mesh-map-1-array" count="190" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_shackle_mount-mesh-vertices">
          <input semantic="POSITION" source="#tow_shackle_mount-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="45">
          <input semantic="VERTEX" source="#tow_shackle_mount-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_shackle_mount-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_shackle_mount-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#tow_shackle_mount-mesh-map-1" offset="2" set="1"/>
          <vcount>4 4 4 7 4 4 4 7 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 8 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>3 0 0 5 1 1 4 2 2 2 3 3 10 4 4 13 5 5 5 1 6 3 0 7 6 6 8 11 7 9 8 8 10 7 9 11 4 10 12 5 10 13 13 10 14 12 10 15 11 10 16 6 11 17 1 12 18 11 7 19 12 13 20 9 14 21 8 8 22 12 13 23 13 5 24 10 4 25 9 14 26 2 3 27 4 2 28 1 15 29 0 16 30 3 17 31 2 17 32 0 18 33 7 19 34 8 17 35 9 17 36 10 17 37 23 20 38 22 20 39 35 20 40 24 20 41 38 21 42 41 21 43 36 21 44 37 21 45 19 22 46 29 23 47 28 24 48 18 25 49 24 26 50 14 27 51 25 28 52 34 29 53 20 30 54 30 31 55 29 23 56 19 22 57 23 32 58 24 26 59 34 29 60 33 33 61 21 34 62 31 34 63 30 31 64 20 30 65 20 35 66 36 35 67 41 35 68 35 35 69 35 36 70 41 36 71 40 36 72 24 36 73 16 37 74 26 38 75 25 28 76 14 27 77 14 27 78 24 26 79 40 39 80 39 39 81 18 25 82 28 24 83 27 40 84 17 40 85 21 41 86 20 41 87 35 41 88 22 41 89 18 42 90 17 42 91 15 42 92 19 42 93 16 43 94 14 43 95 15 43 96 17 43 97 38 44 98 39 44 99 40 44 100 41 44 101 20 30 102 19 22 103 37 45 104 36 45 105 15 46 106 38 46 107 37 46 108 19 46 109 15 47 110 14 47 111 39 47 112 38 47 113 23 32 114 33 33 115 32 48 116 22 49 117 22 49 118 32 48 119 31 34 120 21 34 121 46 50 122 47 50 123 48 50 124 49 50 125 44 50 126 45 50 127 42 50 128 43 50 129 46 51 130 54 52 131 52 53 132 47 54 133 44 55 134 53 56 135 55 57 136 45 58 137 42 59 138 45 58 139 55 57 140 50 60 141 56 61 142 57 62 143 49 63 144 48 64 145 51 65 146 54 52 147 46 51 148 43 66 149 50 60 150 51 65 151 43 66 152 42 59 153 52 53 154 56 61 155 48 64 156 47 54 157 57 62 158 53 56 159 44 55 160 49 63 161 50 67 162 55 67 163 53 68 164 51 69 165 52 70 166 54 71 167 57 71 168 56 70 169 1 15 170 53 56 171 57 62 172 0 16 173 6 6 174 7 9 175 54 52 176 51 65 177 1 12 178 6 11 179 51 69 180 53 68 181 0 18 182 57 71 183 54 71 184 7 19 185 17 40 186 27 40 187 26 38 188 16 37 189</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_shackle-mesh" name="tow_shackle">
      <mesh>
        <source id="tow_shackle-mesh-positions">
          <float_array id="tow_shackle-mesh-positions-array" count="444">0.05664843 0.2318943 0 0.05070811 0.2326313 -0.01028877 0.03882753 0.2339373 -0.01028877 0.03288733 0.2345044 0 0.03882753 0.2339373 0.01028889 0.05070811 0.2326313 0.01028889 0.05296641 0.2542144 0 0.04747819 0.2524954 -0.01028877 0.03650212 0.2488144 -0.01028877 0.0310139 0.2468573 0 0.03650212 0.2488144 0.01028889 0.04747819 0.2524954 0.01028889 0.04248094 0.2736344 0 0.03828054 0.2693743 -0.01028877 0.02987974 0.2607303 -0.01028877 0.02567934 0.2563433 0 0.02987974 0.2607303 0.01028889 0.03828054 0.2693743 0.01028889 0.02678841 0.2876894 0 0.02451515 0.2815093 -0.01028877 0.01996874 0.2691524 -0.01028877 0.01769542 0.2629714 0 0.01996874 0.2691524 0.01028889 0.02451515 0.2815093 0.01028889 0.008277714 0.2931473 0 0.008277714 0.2862224 -0.01028877 0.008277714 0.2724103 -0.01028877 0.008277714 0.2655243 0 0.008277714 0.2724103 0.01028889 0.008277714 0.2862224 0.01028889 0.03022027 0.1881833 0 0.02253174 0.1912813 -0.01295602 0.01646804 0.1998253 0 0.02253174 0.1912813 0.01295608 0.04074633 0.1985363 0 0.03724515 0.1998713 -0.01028883 0.02998894 0.2062293 -0.01028883 0.02506256 0.2109614 0 0.02998894 0.2062293 0.01028889 0.03724509 0.1998713 0.01028889 0.05035054 0.2102172 0 0.04513305 0.2129994 -0.01028877 0.03469806 0.2186393 -0.01028877 0.02948057 0.2214813 0 0.034698 0.2186393 0.01028889 0.04513299 0.2129994 0.01028889 0.01357072 0.1917833 0 0.01357066 0.1882823 0.01077669 0.02253174 0.1514073 0 0.03022027 0.1551153 0 0.01357066 0.1551153 0 0.02253174 0.1556163 0.01295608 0.03022027 0.1586163 0.01077669 0.01357066 0.1586163 0.01077669 0.02253174 0.1666383 0.02096337 0.03022027 0.1677833 0.01743704 0.01357066 0.1677833 0.01743704 0.02253174 0.1802603 0.02096337 0.03022027 0.1791153 0.01743704 0.01357066 0.1791144 0.01743704 0.03022027 0.1882823 0.01077669 0.03022027 0.1882823 -0.01077669 0.01357066 0.1882823 -0.01077669 0.02253174 0.1802603 -0.02096331 0.03022027 0.1791153 -0.01743704 0.01357066 0.1791153 -0.01743704 0.02253174 0.1666383 -0.02096337 0.03022027 0.1677833 -0.01743704 0.01357066 0.1677833 -0.01743704 0.02253174 0.1556163 -0.01295608 0.03022027 0.1586163 -0.01077669 0.03022027 0.1734493 0 0.01357066 0.1734493 0 0.01357066 0.1586163 -0.01077669 -0.0568217 0.2318943 0 -0.0508815 0.2326313 -0.01028883 -0.04765158 0.2524954 -0.01028883 -0.0531398 0.2542144 0 -0.03900092 0.2339373 -0.01028883 -0.03667551 0.2488144 -0.01028883 -0.03306061 0.2345044 0 -0.03118729 0.2468573 0 -0.03900092 0.2339373 0.01028889 -0.03667551 0.2488144 0.01028889 -0.0508815 0.2326313 0.01028889 -0.04765158 0.2524954 0.01028889 -0.03845393 0.2693743 -0.01028877 -0.04265433 0.2736344 0 -0.03005313 0.2607303 -0.01028877 -0.02585273 0.2563433 0 -0.03005313 0.2607303 0.01028889 -0.03845393 0.2693743 0.01028889 -0.02468854 0.2815093 -0.01028877 -0.02696174 0.2876894 0 -0.02014189 0.2691524 -0.01028877 -0.01786869 0.2629714 0 -0.02014189 0.2691524 0.01028889 -0.02468854 0.2815093 0.01028889 -0.008451044 0.2862224 -0.01028877 -0.008451044 0.2931473 0 -0.008451044 0.2724103 -0.01028877 -0.008451044 0.2655243 0 -0.008451044 0.2724103 0.01028889 -0.008451044 0.2862224 0.01028889 -0.0303936 0.1881833 0 -0.0303936 0.1882823 -0.01077669 -0.03741854 0.1998713 -0.01028883 -0.04091972 0.1985363 0 -0.02270513 0.1912813 -0.01295602 -0.03016233 0.2062293 -0.01028883 -0.01664143 0.1998253 0 -0.02523589 0.2109614 0 -0.02270513 0.1912813 0.01295608 -0.03016233 0.2062293 0.01028889 -0.0303936 0.1882823 0.01077669 -0.0374186 0.1998713 0.01028883 -0.04530644 0.2129994 -0.01028883 -0.05052393 0.2102172 0 -0.03487133 0.2186393 -0.01028883 -0.02965384 0.2214813 0 -0.03487133 0.2186393 0.01028889 -0.04530644 0.2129994 0.01028889 -0.01374399 0.1882823 -0.01077669 -0.01374399 0.1917833 0 -0.01374393 0.1882823 0.01077669 -0.02270513 0.1514073 0 -0.0303936 0.1551153 0 -0.0303936 0.1586163 0.01077669 -0.02270513 0.1556163 0.01295608 -0.0303936 0.1734493 0 -0.01374399 0.1551153 0 -0.01374399 0.1586163 0.01077669 -0.01374399 0.1734493 0 -0.0303936 0.1677833 0.01743704 -0.02270513 0.1666383 0.02096337 -0.01374399 0.1677833 0.01743704 -0.0303936 0.1791144 0.01743704 -0.02270513 0.1802603 0.02096337 -0.01374399 0.1791144 0.01743704 -0.0303936 0.1791144 -0.01743704 -0.02270513 0.1802603 -0.02096337 -0.01374399 0.1791144 -0.01743704 -0.0303936 0.1677833 -0.01743704 -0.02270513 0.1666383 -0.02096337 -0.01374399 0.1677833 -0.01743704 -0.0303936 0.1586163 -0.01077669 -0.02270513 0.1556163 -0.01295608 -0.01374399 0.1586163 -0.01077669</float_array>
          <technique_common>
            <accessor source="#tow_shackle-mesh-positions-array" count="148" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_shackle-mesh-normals">
          <float_array id="tow_shackle-mesh-normals-array" count="444">-0.9982014 0.05995106 -7.82084e-7 -0.9489977 -0.315283 3.75025e-7 -0.4162849 -0.138297 -0.8986551 -0.4429061 0.02661579 -0.8961728 0.5309819 0.1799356 -0.8280588 -0.3260884 -0.2670524 -0.9068349 0.4349147 0.3420152 -0.8329915 -0.4429067 0.02661597 0.8961727 -0.4162842 -0.1382996 0.8986549 0.9982011 0.0599547 0 0.9489985 -0.315281 5.95239e-7 0.4162849 -0.1382962 0.8986551 0.4429047 0.02661794 0.8961735 -0.5309827 0.1799355 -0.8280584 0.4162852 -0.1382954 -0.8986552 0.4429045 0.02661752 -0.8961737 -0.5443354 -0.03234815 -0.8382437 0.5443332 -0.03234803 -0.8382452 0.9470989 0.3209422 -5.33435e-7 0.9982389 -0.05932295 -1.97062e-7 -0.4349163 0.3420143 0.832991 -0.7860455 0.6181687 -7.99256e-7 -0.9470993 0.3209409 -5.08595e-7 -0.5309837 0.1799345 0.828058 0.5309825 0.1799347 0.8280587 0.4349145 0.3420148 0.8329918 -0.3260898 -0.2670518 0.9068346 0.3260854 -0.2670541 -0.9068355 0.7736732 -0.633585 -5.57338e-7 0.3260878 -0.2670525 0.9068351 -0.4349142 0.3420158 -0.8329916 0.786046 0.6181681 -2.9283e-7 0.4927912 0.8701477 -3.19676e-7 0.2568461 0.4535416 0.8534227 -0.5443353 -0.03234732 0.838244 0.4715881 -0.8818191 2.32685e-7 0.1871098 -0.3498402 0.9179335 -0.7736756 -0.6335821 -4.72778e-7 -0.4716373 -0.8817927 -3.75876e-7 -0.1871096 -0.349837 -0.9179347 0.2568466 0.4535423 -0.8534221 -0.2568471 0.453543 0.8534215 -0.4927895 0.8701485 -4.4658e-7 0.1475139 0.9890601 6.82543e-7 0.06996822 0.4692093 0.8803108 -0.2568476 0.4535423 -0.8534218 -0.06997263 0.4692095 -0.8803104 0.04447114 -0.4174915 -0.9075919 0.187105 -0.3498412 -0.9179341 -0.1871058 -0.3498389 0.9179348 -0.04447412 -0.4174907 0.9075922 -0.1058928 -0.9943776 -7.83322e-7 -0.9510003 0.3091902 -1.82394e-7 -0.4452173 0.1447572 0.8836442 -0.04447424 -0.41749 -0.9075925 0.06996828 0.4692099 -0.8803105 0.1058937 -0.9943776 1.96928e-6 0.04447126 -0.4174921 0.9075917 0.7882964 0.6152958 -1.14857e-6 0.8702471 0.4926155 -1.23214e-6 0.4259953 0.2701274 0.8634577 0.3250368 0.4946783 0.8060052 -0.3250449 0.4946804 0.8060007 0.8337315 -0.07348954 0.5472577 0.533098 -0.3579909 0.7665828 -0.4259962 0.2701295 0.8634566 0.4259917 0.2701368 -0.8634566 0.3250395 0.494674 -0.8060068 -0.8426719 -0.4356001 -0.3164755 -1 -1.76758e-7 0 -0.8426678 -0.1663915 -0.5120792 0.9045134 -0.4264455 -3.17465e-7 0.8337303 -0.07348757 -0.54726 0.5330955 -0.3579927 -0.7665836 0.8007668 -0.5989764 4.24527e-7 -0.1475137 0.98906 8.70321e-7 0.9509838 0.3092406 0 0.4452164 0.1447596 0.8836442 -0.4452167 0.1447578 -0.8836444 0.5057466 -0.2245932 -0.8329337 0.913905 -0.4059282 0 -0.5057081 -0.2246323 0.8329465 -0.5057463 -0.2245916 -0.8329342 0.4452182 0.1447575 -0.8836436 -0.5330955 -0.3579947 -0.7665827 -0.8702451 0.492619 -1.60327e-6 -0.4259941 0.2701336 -0.8634564 -0.7882981 0.6152937 2.64718e-7 -0.9308817 0.3653207 -3.1637e-6 -0.8491561 0.3594934 0.386909 0.9308815 0.3653215 -9.02079e-6 0.849154 0.3594946 -0.3869122 0.8426721 -0.4355991 -0.3164764 0.8426823 -0.5384114 7.13733e-6 2.49185e-5 -1 2.20053e-6 2.3255e-5 -0.8090223 -0.587778 0.8426626 0.1663886 -0.5120885 1 0 0 -0.8020297 0.1845649 0.5680529 -1 8.39614e-7 0 -0.8020259 -0.1845753 0.5680549 1.65086e-5 0.3090176 0.9510564 -2.3596e-5 -0.3090177 -0.9510564 -2.31658e-5 -0.8090282 -0.5877699 2.25723e-5 0.3090178 -0.9510563 0.8426674 -0.1664032 -0.512076 2.35494e-5 -0.3090107 -0.9510586 0.802026 -0.1845824 -0.5680526 -2.27727e-5 0.3090149 -0.9510573 0.8020294 0.1845694 -0.5680519 -0.3250392 0.494687 -0.8059991 -0.5330994 -0.3579964 0.7665793 -0.8337358 -0.07348877 0.5472515 0.802029 0.1845707 0.5680523 0.8491558 0.3594875 0.3869152 -2.33576e-5 0.3090134 0.9510578 -0.8020209 0.1845867 -0.5680584 -0.8491546 0.3595003 -0.3869058 0.8426721 -0.4355987 0.3164769 0.8426676 -0.1663902 0.5120797 2.34637e-5 -0.3090175 0.9510564 2.29944e-5 -0.8090283 0.5877699 -0.8020303 -0.4832159 -0.3510697 -0.8020254 -0.1845715 -0.568057 0.8020304 -0.4832146 0.3510712 0.8020259 -0.1845762 0.5680546 -2.36072e-5 -0.3090175 0.9510564 -2.31117e-5 -0.8090261 0.5877728 -0.8426705 0.166383 -0.5120775 0.8426638 0.1663855 0.5120876 -0.8020302 -0.4832172 0.3510679 -2.47005e-5 -1 -2.25753e-6 -0.8426719 -0.4355994 0.3164766 -0.8426823 -0.5384114 -7.30582e-6 0.8020416 -0.5972683 2.64123e-6 -0.9982389 -0.05932301 -6.08765e-7 0.5443338 -0.03234809 0.8382448 -0.8007661 -0.5989772 -9.87315e-7 -0.9045128 -0.4264467 -2.50584e-7 -0.06997263 0.469209 0.8803107 0.5057079 -0.2246343 0.832946 -0.8337293 -0.07349348 -0.5472607 -0.9139055 -0.4059271 -4.01749e-7 1 -6.62851e-7 -5.10746e-7 -0.8426709 0.1663802 0.5120778 0.8020303 -0.4832161 -0.3510691 -0.8426677 -0.1664041 0.5120751 -0.8020416 -0.5972684 -2.52854e-6</float_array>
          <technique_common>
            <accessor source="#tow_shackle-mesh-normals-array" count="148" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_shackle-mesh-map-0">
          <float_array id="tow_shackle-mesh-map-0-array" count="1248">0.5019257 0.8359894 0.4872208 0.8359889 0.487282 0.8234393 0.5019506 0.823006 0.7230266 0.7070738 0.7230972 0.696509 0.7372259 0.6954451 0.738884 0.7069234 0.5019257 0.8359894 0.5019441 0.8489701 0.4872803 0.8485285 0.4872208 0.8359889 0.3761868 0.8359947 0.3909063 0.8359967 0.3908457 0.8485575 0.3761616 0.8489928 0.8304585 0.7062519 0.8302879 0.6952658 0.8464176 0.694155 0.8477567 0.7059842 0.7063865 0.7069638 0.7230266 0.7070738 0.7229171 0.7165746 0.7057254 0.7166568 0.8140611 0.7261154 0.8133272 0.7161942 0.8306614 0.716123 0.8305339 0.7259932 0.7231168 0.7260745 0.738981 0.7260723 0.7374297 0.7375788 0.7232835 0.7366414 0.3908495 0.8234463 0.4039121 0.8228462 0.4038439 0.8359907 0.3909063 0.8359967 0.4038439 0.8359907 0.4039194 0.8491685 0.3908457 0.8485575 0.3909063 0.8359967 0.8157538 0.7380574 0.8140611 0.7261154 0.8305339 0.7259932 0.8304443 0.7369757 0.8306614 0.716123 0.8133272 0.7161942 0.8139775 0.7062594 0.8304585 0.7062519 0.7396167 0.7164912 0.7551875 0.7163909 0.7541837 0.7260236 0.738981 0.7260723 0.8465789 0.7379693 0.8304443 0.7369757 0.8305339 0.7259932 0.8478312 0.7261314 0.4166848 0.8359563 0.4169992 0.8501561 0.4039194 0.8491685 0.4038439 0.8359907 0.4742186 0.8228262 0.474293 0.8359964 0.4614589 0.836031 0.4611459 0.8218367 0.738884 0.7069234 0.7372259 0.6954451 0.7515799 0.6940205 0.7540706 0.7068151 0.798319 0.726131 0.7972468 0.7162843 0.8133272 0.7161942 0.8140611 0.7261154 0.7551875 0.7163909 0.7695106 0.71621 0.7686557 0.7268185 0.7541837 0.7260236 0.7982181 0.7063813 0.783416 0.7058703 0.7858291 0.6907791 0.8005928 0.6929519 0.4612005 0.8500978 0.448364 0.8502908 0.4493849 0.8361451 0.4614589 0.836031 0.5173724 0.8359928 0.5183625 0.848888 0.5019441 0.8489701 0.5019257 0.8359894 0.7540706 0.7068151 0.7515799 0.6940205 0.7655596 0.6917398 0.7684429 0.7059002 0.4287623 0.8358425 0.4299965 0.8503982 0.4169992 0.8501561 0.4166848 0.8359563 0.3333839 0.8359773 0.3477416 0.8359837 0.345718 0.8494439 0.3275937 0.8516798 0.6557525 0.7424688 0.6540762 0.7319675 0.6715209 0.7293763 0.6753019 0.7401208 0.34573 0.822522 0.3477416 0.8359837 0.3333839 0.8359773 0.327608 0.82027 0.9356215 0.7056023 0.9200639 0.7157933 0.9275991 0.6985486 0.6536725 0.7169757 0.6538282 0.701962 0.6713681 0.7042887 0.6721574 0.7168357 0.7972468 0.7162843 0.7825993 0.7164599 0.783416 0.7058703 0.7982181 0.7063813 0.8139775 0.7062594 0.7982181 0.7063813 0.8005928 0.6929519 0.8155798 0.6942896 0.3607215 0.8359895 0.3761868 0.8359947 0.3761616 0.8489928 0.3597217 0.8489011 0.5173724 0.8359928 0.5019257 0.8359894 0.5019506 0.823006 0.51837 0.8230976 0.6903816 0.6933718 0.7075763 0.695576 0.7063865 0.7069638 0.6878693 0.7056564 0.6878693 0.7056564 0.7063865 0.7069638 0.7057254 0.7166568 0.6875073 0.7167478 0.8644534 0.7402829 0.8465789 0.7379693 0.8478312 0.7261314 0.8670831 0.7275187 0.8669984 0.7044554 0.8642753 0.6917107 0.8802119 0.6916652 0.8841397 0.7028664 0.5183625 0.848888 0.5173724 0.8359928 0.5303345 0.8359966 0.5323458 0.8494389 0.3477416 0.8359837 0.3607215 0.8359895 0.3597217 0.8489011 0.345718 0.8494439 0.6878693 0.7056564 0.6713681 0.7042887 0.6750435 0.6934589 0.6903816 0.6933718 0.6878693 0.7056564 0.6875073 0.7167478 0.6721574 0.7168357 0.6713681 0.7042887 0.5504408 0.8516831 0.5446706 0.8360011 0.5528184 0.8360036 0.5573784 0.8452532 0.3252235 0.8359737 0.3206578 0.8267085 0.327608 0.82027 0.3333839 0.8359773 0.6219717 0.7076767 0.6195602 0.7173681 0.6104226 0.7175053 0.6146999 0.7029489 0.6402355 0.6986481 0.6538282 0.701962 0.6370127 0.7171278 0.5649918 0.8505532 0.5692296 0.8360093 0.5740973 0.8500891 0.5626502 0.8590411 0.5504408 0.8516831 0.5573784 0.8452532 0.5649918 0.8505532 0.931397 0.690274 0.9431971 0.7003453 0.9356215 0.7056023 0.9275991 0.6985486 0.7396167 0.7164912 0.738884 0.7069234 0.7540706 0.7068151 0.7551875 0.7163909 0.6397177 0.6893141 0.6402355 0.6986481 0.6294165 0.7006191 0.6253811 0.6931548 0.6536725 0.7169757 0.6540762 0.7319675 0.6370127 0.7171278 0.5528184 0.8360036 0.5692296 0.8360093 0.5573784 0.8452532 0.3039122 0.8218606 0.3013113 0.8130573 0.3153804 0.8128969 0.313033 0.8213977 0.6553355 0.691291 0.6538282 0.701962 0.6402355 0.6986481 0.6397177 0.6893141 0.9007999 0.7425026 0.8803895 0.7402117 0.8842353 0.7289823 0.9024497 0.7315153 0.3130194 0.850538 0.3206492 0.8452343 0.3275937 0.8516798 0.3153588 0.8590412 0.565 0.8214622 0.5692296 0.8360093 0.557384 0.8267568 0.6222684 0.7269709 0.6299452 0.7336378 0.6263644 0.7415296 0.6151105 0.7320079 0.5812866 0.8273989 0.5692296 0.8360093 0.574106 0.8219313 0.2967069 0.8445883 0.3038989 0.8500657 0.3012898 0.8588678 0.2892222 0.8505197 0.9165557 0.6869645 0.931397 0.690274 0.9275991 0.6985486 0.9163352 0.6964001 0.6370127 0.7171278 0.6195602 0.7173681 0.6219717 0.7076767 0.3130194 0.850538 0.3153588 0.8590412 0.3012898 0.8588678 0.3038989 0.8500657 0.51837 0.8230976 0.5323542 0.8225557 0.5303345 0.8359966 0.5173724 0.8359928 0.6406977 0.7356391 0.6299452 0.7336378 0.6370127 0.7171278 0.5812813 0.8446261 0.5887486 0.8505532 0.5766958 0.858878 0.5740973 0.8500891 0.9480422 0.7156906 0.9433093 0.7310712 0.9356955 0.7258698 0.9383766 0.7157263 0.9356955 0.7258698 0.9200639 0.7157933 0.9383766 0.7157263 0.2967069 0.8445883 0.2892222 0.8505197 0.2840973 0.8359543 0.2942398 0.8359588 0.8478312 0.7261314 0.8305339 0.7259932 0.8306614 0.716123 0.8485307 0.7160553 0.7075763 0.695576 0.7230972 0.696509 0.7230266 0.7070738 0.7063865 0.7069638 0.4742277 0.8491317 0.4612005 0.8500978 0.4614589 0.836031 0.474293 0.8359964 0.3908495 0.8234463 0.3909063 0.8359967 0.3761868 0.8359947 0.3761711 0.8230001 0.7077724 0.7377136 0.7064789 0.7263408 0.7231168 0.7260745 0.7232835 0.7366414 0.7064789 0.7263408 0.7057254 0.7166568 0.7229171 0.7165746 0.7231168 0.7260745 0.6299452 0.7336378 0.6406977 0.7356391 0.6405109 0.7446467 0.6263644 0.7415296 0.8842353 0.7289823 0.8834497 0.715927 0.902697 0.7158566 0.9024497 0.7315153 0.7229171 0.7165746 0.7230266 0.7070738 0.738884 0.7069234 0.7396167 0.7164912 0.487282 0.8234393 0.4872208 0.8359889 0.474293 0.8359964 0.4742186 0.8228262 0.8304585 0.7062519 0.8139775 0.7062594 0.8155798 0.6942896 0.8302879 0.6952658 0.7229171 0.7165746 0.7396167 0.7164912 0.738981 0.7260723 0.7231168 0.7260745 0.4872803 0.8485285 0.4742277 0.8491317 0.474293 0.8359964 0.4872208 0.8359889 0.8008069 0.7395015 0.798319 0.726131 0.8140611 0.7261154 0.8157538 0.7380574 0.5626646 0.8129726 0.5767098 0.8131446 0.574106 0.8219313 0.565 0.8214622 0.4039121 0.8228462 0.416946 0.8218851 0.4166848 0.8359563 0.4038439 0.8359907 0.738981 0.7260723 0.7541837 0.7260236 0.7518221 0.7388755 0.7374297 0.7375788 0.8133272 0.7161942 0.7972468 0.7162843 0.7982181 0.7063813 0.8139775 0.7062594 0.7836136 0.7267529 0.7825993 0.7164599 0.7972468 0.7162843 0.798319 0.726131 0.7861276 0.7417891 0.7836136 0.7267529 0.798319 0.726131 0.8008069 0.7395015 0.4611459 0.8218367 0.4614589 0.836031 0.4493849 0.8361451 0.4481523 0.821592 0.416946 0.8218851 0.4297854 0.821695 0.4287623 0.8358425 0.4166848 0.8359563 0.6879832 0.7278271 0.6906085 0.7400707 0.6753019 0.7401208 0.6715209 0.7293763 0.7551875 0.7163909 0.7540706 0.7068151 0.7684429 0.7059002 0.7695106 0.71621 0.6536725 0.7169757 0.6721574 0.7168357 0.6715209 0.7293763 0.6540762 0.7319675 0.6553355 0.691291 0.6750435 0.6934589 0.6713681 0.7042887 0.6538282 0.701962 0.5446706 0.8360011 0.5504408 0.8516831 0.5323458 0.8494389 0.5303345 0.8359966 0.5446706 0.8360011 0.5303345 0.8359966 0.5323542 0.8225557 0.5504506 0.8203225 0.8841397 0.7028664 0.8802119 0.6916652 0.9006052 0.6892257 0.9023352 0.7001993 0.902697 0.7158566 0.8834497 0.715927 0.8841397 0.7028664 0.9023352 0.7001993 0.8670831 0.7275187 0.8478312 0.7261314 0.8485307 0.7160553 0.867478 0.7159852 0.6906085 0.7400707 0.6879832 0.7278271 0.7064789 0.7263408 0.7077724 0.7377136 0.8306614 0.716123 0.8304585 0.7062519 0.8477567 0.7059842 0.8485307 0.7160553 0.3761711 0.8230001 0.3761868 0.8359947 0.3607215 0.8359895 0.3597328 0.8230773 0.8477567 0.7059842 0.8464176 0.694155 0.8642753 0.6917107 0.8669984 0.7044554 0.6879832 0.7278271 0.6875073 0.7167478 0.7057254 0.7166568 0.7064789 0.7263408 0.6879832 0.7278271 0.6715209 0.7293763 0.6721574 0.7168357 0.6875073 0.7167478 0.8803895 0.7402117 0.8644534 0.7402829 0.8670831 0.7275187 0.8842353 0.7289823 0.7541837 0.7260236 0.7686557 0.7268185 0.7658789 0.741022 0.7518221 0.7388755 0.3597328 0.8230773 0.3607215 0.8359895 0.3477416 0.8359837 0.34573 0.822522 0.8670831 0.7275187 0.867478 0.7159852 0.8834497 0.715927 0.8842353 0.7289823 0.8834497 0.715927 0.867478 0.7159852 0.8669984 0.7044554 0.8841397 0.7028664 0.3275937 0.8516798 0.3206492 0.8452343 0.3252235 0.8359737 0.3333839 0.8359773 0.3087855 0.8359658 0.2967069 0.8445883 0.2942398 0.8359588 0.9480422 0.7156906 0.9383766 0.7157263 0.9356215 0.7056023 0.9431971 0.7003453 0.9200639 0.7157933 0.916477 0.7352127 0.9024497 0.7315153 0.3087855 0.8359658 0.3130194 0.850538 0.3038989 0.8500657 0.2942398 0.8359588 0.2840973 0.8359543 0.2892361 0.8213935 0.2967151 0.8273323 0.6253811 0.6931548 0.6294165 0.7006191 0.6219717 0.7076767 0.6146999 0.7029489 0.2967151 0.8273323 0.2892361 0.8213935 0.3013113 0.8130573 0.3039122 0.8218606 0.9167664 0.7446468 0.9007999 0.7425026 0.9024497 0.7315153 0.916477 0.7352127 0.9163352 0.6964001 0.9200639 0.7157933 0.9023352 0.7001993 0.3087855 0.8359658 0.3252235 0.8359737 0.3206492 0.8452343 0.5766958 0.858878 0.5626502 0.8590411 0.5649918 0.8505532 0.5740973 0.8500891 0.6104226 0.7175053 0.6195602 0.7173681 0.6222684 0.7269709 0.6151105 0.7320079 0.3206578 0.8267085 0.313033 0.8213977 0.3153804 0.8128969 0.327608 0.82027 0.3087855 0.8359658 0.313033 0.8213977 0.3206578 0.8267085 0.6406977 0.7356391 0.6540762 0.7319675 0.6557525 0.7424688 0.6405109 0.7446467 0.3087855 0.8359658 0.2967151 0.8273323 0.3039122 0.8218606 0.5504506 0.8203225 0.5626646 0.8129726 0.565 0.8214622 0.557384 0.8267568 0.9315829 0.7412287 0.9167664 0.7446468 0.916477 0.7352127 0.927725 0.7329817 0.5887578 0.8214763 0.5938756 0.8360168 0.5837506 0.8360133 0.5812866 0.8273989 0.9315829 0.7412287 0.927725 0.7329817 0.9356955 0.7258698 0.9433093 0.7310712 0.9200639 0.7157933 0.902697 0.7158566 0.9023352 0.7001993 0.6370127 0.7171278 0.6299452 0.7336378 0.6222684 0.7269709 0.5767098 0.8131446 0.5887578 0.8214763 0.5812866 0.8273989 0.574106 0.8219313 0.9165557 0.6869645 0.9163352 0.6964001 0.9023352 0.7001993 0.9006052 0.6892257 0.5938756 0.8360168 0.5887486 0.8505532 0.5812813 0.8446261 0.5837506 0.8360133 0.7695106 0.71621 0.7825993 0.7164599 0.7836136 0.7267529 0.7686557 0.7268185 0.8485307 0.7160553 0.8477567 0.7059842 0.8669984 0.7044554 0.867478 0.7159852 0.7684429 0.7059002 0.783416 0.7058703 0.7825993 0.7164599 0.7695106 0.71621 0.7658789 0.741022 0.7686557 0.7268185 0.7836136 0.7267529 0.7861276 0.7417891 0.7655596 0.6917398 0.7858291 0.6907791 0.783416 0.7058703 0.7684429 0.7059002 0.4493849 0.8361451 0.448364 0.8502908 0.4299965 0.8503982 0.4287623 0.8358425 0.4481523 0.821592 0.4493849 0.8361451 0.4287623 0.8358425 0.4297854 0.821695 0.5504506 0.8203225 0.557384 0.8267568 0.5528184 0.8360036 0.5446706 0.8360011 0.5812813 0.8446261 0.5692296 0.8360093 0.5837506 0.8360133 0.6195602 0.7173681 0.6370127 0.7171278 0.6222684 0.7269709 0.6540762 0.7319675 0.6406977 0.7356391 0.6370127 0.7171278 0.6538282 0.701962 0.6536725 0.7169757 0.6370127 0.7171278 0.6294165 0.7006191 0.6402355 0.6986481 0.6370127 0.7171278 0.6294165 0.7006191 0.6370127 0.7171278 0.6219717 0.7076767 0.9356215 0.7056023 0.9383766 0.7157263 0.9200639 0.7157933 0.5649918 0.8505532 0.5573784 0.8452532 0.5692296 0.8360093 0.5528184 0.8360036 0.557384 0.8267568 0.5692296 0.8360093 0.565 0.8214622 0.574106 0.8219313 0.5692296 0.8360093 0.5812866 0.8273989 0.5837506 0.8360133 0.5692296 0.8360093 0.9356955 0.7258698 0.927725 0.7329817 0.9200639 0.7157933 0.3087855 0.8359658 0.3038989 0.8500657 0.2967069 0.8445883 0.9200639 0.7157933 0.927725 0.7329817 0.916477 0.7352127 0.3087855 0.8359658 0.3206492 0.8452343 0.3130194 0.850538 0.9163352 0.6964001 0.9275991 0.6985486 0.9200639 0.7157933 0.3087855 0.8359658 0.3206578 0.8267085 0.3252235 0.8359737 0.3087855 0.8359658 0.3039122 0.8218606 0.313033 0.8213977 0.3087855 0.8359658 0.2942398 0.8359588 0.2967151 0.8273323 0.9200639 0.7157933 0.9024497 0.7315153 0.902697 0.7158566 0.5812813 0.8446261 0.5740973 0.8500891 0.5692296 0.8360093</float_array>
          <technique_common>
            <accessor source="#tow_shackle-mesh-map-0-array" count="624" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_shackle-mesh-vertices">
          <input semantic="POSITION" source="#tow_shackle-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="166">
          <input semantic="VERTEX" source="#tow_shackle-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_shackle-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_shackle-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 4 4 4 4 3 3 4 4 4 4 3 4 3 4 4 3 4 4 3 4 4 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 4 3 3 4 4 4 4 3 3 4 4 4 3 4 3 4 4 4 4 3 3 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>3 0 0 9 1 1 8 2 2 2 3 3 7 4 4 8 2 5 14 5 6 13 6 7 3 0 8 4 7 9 10 8 10 9 1 11 80 9 12 81 10 13 83 11 14 82 12 15 76 13 16 79 14 17 78 15 18 75 16 19 1 17 20 7 4 21 6 18 22 0 19 23 91 20 24 87 21 25 77 22 26 85 23 27 11 24 28 17 25 29 16 26 30 10 8 31 79 14 32 88 27 33 89 28 34 81 10 35 89 28 36 90 29 37 83 11 38 81 10 39 90 29 40 91 20 41 85 23 42 83 11 43 77 22 44 87 21 45 86 30 46 76 13 47 12 31 48 18 32 49 23 33 50 17 25 51 82 12 52 83 11 53 85 23 54 84 34 55 95 35 56 96 36 57 90 29 58 89 28 59 14 5 60 15 37 61 21 38 62 20 39 63 13 6 64 14 5 65 20 39 66 19 40 67 97 41 68 93 42 69 87 21 70 91 20 71 18 32 72 24 43 73 29 44 74 23 33 75 92 45 76 98 46 77 100 47 78 94 48 79 22 49 80 28 50 81 27 51 82 21 38 83 43 52 84 44 53 85 4 7 86 3 0 87 19 40 88 20 39 89 26 54 90 25 55 91 101 56 92 102 57 93 96 36 94 95 35 95 110 58 96 111 59 97 113 60 98 112 61 99 33 62 100 60 63 101 39 64 102 38 65 103 109 66 104 111 59 105 110 58 106 108 67 107 145 68 108 129 69 109 142 70 110 30 71 111 61 72 112 35 73 113 34 74 114 93 42 115 99 75 116 98 46 117 92 45 118 86 30 119 92 45 120 94 48 121 88 27 122 119 76 123 80 9 124 82 12 125 120 77 126 43 52 127 3 0 128 2 3 129 42 78 130 42 78 131 2 3 132 1 17 133 41 79 134 41 79 135 1 17 136 0 19 137 40 80 138 120 77 139 82 12 140 84 34 141 121 81 142 116 82 143 118 83 144 109 66 145 106 84 146 44 53 147 43 52 148 37 85 149 38 65 150 111 59 151 119 76 152 120 77 153 113 60 154 41 79 155 35 73 156 36 86 157 42 78 158 41 79 159 40 80 160 34 74 161 35 73 162 33 62 163 32 87 164 46 88 165 47 89 166 123 90 167 122 91 168 108 67 169 110 58 170 70 92 171 49 93 172 48 94 173 69 95 174 64 96 175 61 72 176 71 97 177 59 98 178 72 99 179 56 100 180 57 101 181 33 62 182 47 89 183 59 98 184 143 102 185 146 103 186 145 68 187 142 70 188 12 31 189 13 6 190 19 40 191 18 32 192 63 104 193 64 96 194 67 105 195 66 106 196 30 71 197 60 63 198 71 97 199 46 88 200 72 99 201 47 89 202 144 107 203 143 102 204 140 108 205 141 109 206 31 110 207 61 72 208 64 96 209 63 104 210 112 61 211 113 60 212 115 111 213 114 112 214 138 113 215 124 114 216 112 61 217 137 115 218 65 116 219 72 99 220 62 117 221 52 118 222 55 119 223 54 120 224 51 121 225 73 122 226 72 99 227 68 123 228 131 124 229 135 125 230 134 126 231 128 127 232 140 108 233 143 102 234 142 70 235 139 128 236 71 97 237 49 93 238 70 92 239 138 113 240 137 115 241 134 126 242 135 125 243 42 78 244 36 86 245 37 85 246 43 52 247 58 129 248 55 119 249 71 97 250 53 130 251 51 121 252 54 120 253 56 100 254 125 131 255 128 127 256 127 132 257 126 133 258 127 132 259 129 69 260 126 133 261 131 124 262 128 127 263 125 131 264 130 134 265 84 34 266 85 23 267 77 22 268 74 135 269 2 3 270 8 2 271 7 4 272 1 17 273 16 26 274 22 49 275 21 38 276 15 37 277 79 14 278 81 10 279 80 9 280 78 15 281 4 7 282 5 136 283 11 24 284 10 8 285 5 136 286 0 19 287 6 18 288 11 24 289 55 119 290 58 129 291 57 101 292 54 120 293 115 111 294 107 137 295 104 138 296 114 112 297 6 18 298 7 4 299 13 6 300 12 31 301 8 2 302 9 1 303 15 37 304 14 5 305 76 13 306 86 30 307 88 27 308 79 14 309 6 18 310 12 31 311 17 25 312 11 24 313 10 8 314 16 26 315 15 37 316 9 1 317 96 36 318 97 41 319 91 20 320 90 29 321 63 104 322 66 106 323 68 123 324 65 116 325 88 27 326 94 48 327 95 35 328 89 28 329 17 25 330 23 33 331 22 49 332 16 26 333 87 21 334 93 42 335 92 45 336 86 30 337 103 139 338 99 75 339 93 42 340 97 41 341 102 57 342 103 139 343 97 41 344 96 36 345 20 39 346 21 38 347 27 51 348 26 54 349 94 48 350 100 47 351 101 56 352 95 35 353 45 140 354 44 53 355 38 65 356 39 64 357 18 32 358 19 40 359 25 55 360 24 43 361 30 71 362 34 74 363 39 64 364 60 63 365 31 110 366 36 86 367 35 73 368 61 72 369 32 87 370 33 62 371 38 65 372 37 85 373 32 87 374 37 85 375 36 86 376 31 110 377 106 84 378 109 66 379 108 67 380 105 141 381 104 138 382 107 137 383 106 84 384 105 141 385 121 81 386 84 34 387 74 135 388 117 142 389 44 53 390 45 140 391 5 136 392 4 7 393 77 22 394 76 13 395 75 16 396 74 135 397 78 15 398 80 9 399 119 76 400 118 83 401 75 16 402 78 15 403 118 83 404 116 82 405 45 140 406 40 80 407 0 19 408 5 136 409 45 140 410 39 64 411 34 74 412 40 80 413 113 60 414 120 77 415 121 81 416 115 111 417 23 33 418 29 44 419 28 50 420 22 49 421 118 83 422 119 76 423 111 59 424 109 66 425 121 81 426 117 142 427 107 137 428 115 111 429 107 137 430 117 142 431 116 82 432 106 84 433 112 61 434 124 114 435 123 90 436 110 58 437 132 143 438 131 124 439 130 134 440 125 131 441 126 133 442 145 68 443 146 103 444 129 69 445 136 144 446 114 112 447 132 143 448 138 113 449 135 125 450 130 134 451 125 131 452 146 103 453 147 145 454 66 106 455 67 105 456 70 92 457 69 95 458 147 145 459 146 103 460 143 102 461 144 107 462 137 115 463 112 61 464 114 112 465 136 144 466 139 128 467 129 69 468 105 141 469 132 143 470 123 90 471 124 114 472 54 120 473 57 101 474 59 98 475 56 100 476 48 94 477 49 93 478 52 118 479 51 121 480 122 91 481 141 109 482 140 108 483 108 67 484 132 143 485 141 109 486 122 91 487 58 129 488 60 63 489 33 62 490 57 101 491 132 143 492 147 145 493 144 107 494 31 110 495 63 104 496 65 116 497 62 117 498 134 126 499 137 115 500 136 144 501 133 146 502 69 95 503 48 94 504 50 147 505 73 122 506 134 126 507 133 146 508 127 132 509 128 127 510 129 69 511 104 138 512 105 141 513 71 97 514 55 119 515 52 118 516 66 106 517 69 95 518 73 122 519 68 123 520 140 108 521 139 128 522 105 141 523 108 67 524 48 94 525 51 121 526 53 130 527 50 147 528 24 43 529 99 75 530 103 139 531 29 44 532 74 135 533 75 16 534 116 82 535 117 142 536 25 55 537 98 46 538 99 75 539 24 43 540 28 50 541 29 44 542 103 139 543 102 57 544 26 54 545 100 47 546 98 46 547 25 55 548 27 51 549 28 50 550 102 57 551 101 56 552 26 54 553 27 51 554 101 56 555 100 47 556 31 110 557 62 117 558 46 88 559 32 87 560 53 130 561 72 99 562 50 147 563 49 93 564 71 97 565 52 118 566 60 63 567 58 129 568 71 97 569 61 72 570 30 71 571 71 97 572 67 105 573 64 96 574 71 97 575 67 105 576 71 97 577 70 92 578 145 68 579 126 133 580 129 69 581 59 98 582 47 89 583 72 99 584 46 88 585 62 117 586 72 99 587 65 116 588 68 123 589 72 99 590 73 122 591 50 147 592 72 99 593 127 132 594 133 146 595 129 69 596 132 143 597 135 125 598 131 124 599 129 69 600 133 146 601 136 144 602 132 143 603 124 114 604 138 113 605 139 128 606 142 70 607 129 69 608 132 143 609 122 91 610 123 90 611 132 143 612 144 107 613 141 109 614 132 143 615 130 134 616 147 145 617 129 69 618 114 112 619 104 138 620 53 130 621 56 100 622 72 99 623</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_reciever_b-mesh" name="tow_reciever_b">
      <mesh>
        <source id="tow_reciever_b-mesh-positions">
          <float_array id="tow_reciever_b-mesh-positions-array" count="672">-0.03313761 -0.05972331 -0.02913761 0.0331496 -0.05434083 -0.02782183 0.0331496 0.04066145 -0.02782183 -0.02913761 -0.05972331 -0.03313761 0.07130378 -0.03656762 0.1039003 -0.02913761 -0.05972331 0.03313755 -0.03313761 -0.05972331 0.02913761 0.07130378 0.03656733 0.1039003 0.04060697 0.03656733 -0.02279621 -0.02913761 0.07286459 -0.03313761 -0.03313761 0.07286459 -0.02913761 0.04060697 -0.05024671 -0.02279621 -0.03313761 0.07286459 0.02913761 0.0787611 0.03656733 0.1002516 -0.02913761 0.07286459 0.03313755 0.0787611 -0.03656762 0.1002516 0.0331496 -0.05434083 0.03045791 0.02913761 -0.05972331 -0.03313761 0.0353012 -0.04956507 0.04242962 0.03313761 -0.05972331 -0.02913761 0.03313761 -0.05972331 0.02913761 0.03530168 0.03656733 0.04243052 0.0331496 0.04066145 0.03045791 0.02913761 -0.05972331 0.03313755 0.03313761 0.07286459 -0.02913761 0.04060697 0.03656733 0.03045791 0.02913761 0.07286459 -0.03313761 0.04275906 0.03656733 0.03878182 0.04275858 -0.04956507 0.03878092 0.02913761 0.07286459 0.03313755 0.04060697 -0.05024671 0.03045791 0.03313761 0.07286459 0.02913761 -0.0331496 -0.05434083 -0.02782183 -0.02559369 -0.05972331 -0.02153271 -0.0331496 0.04066145 -0.02782183 -0.02153271 -0.05972331 -0.02559369 -0.02153271 -0.05972331 0.02559369 -0.07130378 -0.03656762 0.1039003 -0.07130378 0.03656733 0.1039003 -0.02559369 -0.05972331 0.02153271 0.02153271 -0.05972331 -0.02559369 -0.04060697 0.03656733 -0.02279621 0.02559369 -0.05972331 -0.02153271 -0.04060697 -0.05024671 -0.02279621 0.02559369 -0.05972331 0.02153271 -0.0787611 0.03656733 0.1002516 0.02153271 -0.05972331 0.02559369 -0.0787611 -0.03656762 0.1002516 -0.0331496 -0.05434083 0.03045791 -0.02153271 0.08295559 -0.02559369 -0.02559369 0.08295559 -0.02153271 -0.0353012 -0.04956507 0.04242962 -0.03530168 0.03656733 0.04243052 -0.02559369 0.08295559 0.02153271 -0.02153271 0.08295559 0.02559369 -0.0331496 0.04066145 0.03045791 -0.04060697 0.03656733 0.03045791 0.02559369 0.08295559 -0.02153271 -0.04275906 0.03656733 0.03878182 0.02153271 0.08295559 -0.02559369 0.02153271 0.08295559 0.02559369 -0.04275858 -0.04956507 0.03878092 -0.04060697 -0.05024671 0.03045791 0.02559369 0.08295559 0.02153271 -0.03833019 0.07286459 -0.03108161 -0.03108155 0.07286459 -0.03833019 -0.03108155 0.07286459 0.03833013 -0.03833019 0.07286459 0.03108155 0.03108155 0.07286459 -0.03833019 0.03833019 0.07286459 -0.03108161 0.03833019 0.07286459 0.03108155 0.03108155 0.07286459 0.03833013 -0.03108155 0.08295559 0.03833013 0.03108155 0.08295559 0.03833013 0.03833019 0.08295559 0.03108155 -0.03833019 0.08295559 -0.03108161 -0.03833019 0.08295559 0.03108155 -0.03108155 0.08295559 -0.03833019 0.03108155 0.08295559 -0.03833019 0.03833019 0.08295559 -0.03108161 0.03605735 0.07286459 0.03605735 0.03605735 0.08295559 0.03605735 -0.03605735 0.07286459 0.03605735 -0.03605735 0.08295559 0.03605735 -0.03605735 0.07286459 -0.03605735 -0.03605735 0.08295559 -0.03605735 0.03605735 0.07286459 -0.03605735 0.03605735 0.08295559 -0.03605735 0.08202439 0.05119216 -0.01971381 0.07707071 0.05919212 -0.02599561 -0.04801297 0.06484901 0.005656838 -0.04801297 0.05919218 0.007999956 -0.04801297 0.06484901 -0.005656838 -0.04801297 0.05353528 0.005656838 -0.04801297 0.05119216 0 -0.04801297 0.05353528 -0.005656838 -0.04801297 0.05919218 -0.007999956 -0.04801297 0.06719213 0 0.04082924 0.06484901 0.005656599 0.04084867 0.05919218 0.007999718 0.04082924 0.05353528 0.005656659 0.04078221 0.05119216 0 0.04073524 0.05353528 -0.005656599 0.04071581 0.05919218 -0.007999718 0.04073524 0.06484901 -0.005656659 0.04078221 0.06719213 0 0.05160284 0.06484901 0.005435883 0.05192631 0.05919218 0.00775659 0.05160284 0.05353528 0.005435883 0.0508219 0.05119216 -1.66784e-4 0.0500409 0.05353528 -0.005769431 0.04971742 0.05919218 -0.008090138 0.0500409 0.06484901 -0.005769431 0.0508219 0.06719213 -1.66787e-4 0.0603342 0.06484901 0.003079175 0.06128817 0.05919218 0.00521934 0.0603342 0.05353528 0.003079175 0.05803114 0.05119216 -0.002087533 0.05572807 0.05353528 -0.007254362 0.0547741 0.05919218 -0.009394526 0.05572807 0.06484901 -0.007254362 0.05803114 0.06719213 -0.002087533 0.07021903 0.06484901 -0.003293097 0.07158285 0.05919218 -0.001387774 0.07021903 0.05353528 -0.003293097 0.06692647 0.05119216 -0.007893025 0.06363397 0.05353528 -0.01249295 0.0622701 0.05919218 -0.01439827 0.06363397 0.06484901 -0.01249295 0.06692647 0.06719213 -0.007893025 0.08552718 0.06484901 -0.01527196 0.08697807 0.05919218 -0.01343202 0.08552718 0.05353528 -0.01527196 0.0785216 0.05353528 -0.02415573 0.0785216 0.06484901 -0.02415573 0.08202439 0.06719213 -0.01971381 -0.0339998 0.04906332 -0.01868391 -0.03599983 0.04742503 -0.01753675 -0.0379998 0.04906332 -0.01868391 -0.03599983 0.05719214 -0.02037376 -0.03399986 0.05919218 -0.02037376 -0.03599983 0.06119215 -0.02037376 -0.03799986 0.05919218 -0.02037376 -0.03599983 0.05724221 0.01007467 -0.03399986 0.05919218 0.009629905 -0.03599983 0.06114208 0.009185194 -0.0379998 0.05919218 0.009629905 -0.03599983 0.05948114 0.01478177 -0.0339998 0.06111848 0.01363325 -0.03599983 0.06275588 0.01248478 -0.0379998 0.06111848 0.01363331 -0.03599983 0.06282657 0.01812559 -0.0339998 0.06458604 0.01717466 -0.03599983 0.06634557 0.01622378 -0.0379998 0.06458604 0.01717466 -0.03599983 0.06348288 0.02125763 -0.0339998 0.06548285 0.02126002 -0.03599983 0.06748282 0.02126246 -0.0379998 0.06548285 0.02126002 -0.03599983 0.06266283 0.02514755 -0.0339998 0.06439936 0.02613979 -0.03599983 0.06613582 0.02713203 -0.0379998 0.06439936 0.02613979 -0.03599983 0.06009966 0.02748394 -0.0339998 0.06111133 0.02920919 -0.03599983 0.062123 0.03093445 -0.0379998 0.06111133 0.02920919 -0.03599983 0.05695903 0.02852147 -0.0339998 0.05701249 0.03052079 -0.03599983 0.05706602 0.03252005 -0.0379998 0.05701249 0.03052079 -0.03599983 0.05369997 0.0276069 -0.0339998 0.05292183 0.02944928 -0.03599983 0.05214369 0.03129172 -0.0379998 0.05292183 0.02944928 -0.03599983 0.05098617 0.02605342 -0.0339998 0.04948467 0.02737456 -0.03599983 0.04798316 0.02869576 -0.0379998 0.04948467 0.02737456 -0.03599983 0.04997479 0.02364093 -0.0339998 0.04801619 0.02404588 -0.03599983 0.04605764 0.02445083 -0.0379998 0.04801619 0.02404588 -0.03599983 0.04997152 0.02013152 -0.0339998 0.04805064 0.01957452 -0.03599983 0.04612976 0.01901757 -0.0379998 0.04805064 0.01957452 -0.03599983 0.05143994 0.01789993 -0.0339998 0.04983931 0.01670068 -0.03599983 0.04823875 0.01550149 -0.0379998 0.04983931 0.01670068 -0.03599983 0.0539698 0.01502335 -0.0339998 0.05224859 0.01400476 -0.03599983 0.05052739 0.01298618 -0.0379998 0.05224859 0.01400476 -0.03599983 0.05535733 0.01114243 -0.0339998 0.05339419 0.01076012 -0.03599983 0.05143111 0.01037782 -0.0379998 0.05339419 0.01076012 -0.03599983 0.05538487 0.007992625 -0.0339998 0.05349051 0.00863415 -0.03599983 0.05159622 0.009275674 -0.0379998 0.05349051 0.00863415 -0.03599983 0.05250293 0.00439614 -0.0339998 0.05076724 0.005389869 -0.03599983 0.04903155 0.006383538 -0.0379998 0.05076724 0.005389869 -0.03599983 0.05090141 1.24241e-4 -0.0339998 0.04890149 1.42468e-4 -0.03599983 0.04690158 1.60694e-4 -0.0379998 0.04890149 1.42466e-4 -0.03599983 0.05275624 -0.005077242 -0.0339998 0.05090284 -0.005828917 -0.03599983 0.04904949 -0.00658065 -0.0379998 0.05090284 -0.005828917 -0.03599983 0.0548985 -0.009849965 -0.0339998 0.05290424 -0.01000142 -0.03599983 0.05090999 -0.01015287 -0.0379998 0.05290424 -0.01000142 -0.03599983 0.0533598 -0.01532095 -0.0339998 0.05152952 -0.01451474 -0.03599983 0.04969924 -0.01370853 -0.0379998 0.05152952 -0.01451474 -0.03599983 0.05070161 -0.01983106</float_array>
          <technique_common>
            <accessor source="#tow_reciever_b-mesh-positions-array" count="224" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_reciever_b-mesh-normals">
          <float_array id="tow_reciever_b-mesh-normals-array" count="894">0.06412827 0 0.9979417 -0.06412827 -1.1136e-4 0.9979417 -0.06412827 0 0.9979417 0.06412827 1.1136e-4 0.9979417 -0.9979417 0 -0.06412816 -0.9979417 1.23668e-4 0.06412804 -0.9979417 0 0.06412798 -0.9979417 -1.1136e-4 -0.06412822 0.9979417 0 -0.06412816 0.9979417 -1.23668e-4 0.06412804 0.9979417 0 0.06412798 0.9979417 1.1136e-4 -0.06412822 -0.06412816 0 -0.9979417 -0.06412822 1.1136e-4 -0.9979417 0.06412816 0 -0.9979417 0.06412822 -1.1136e-4 -0.9979417 0 -1 1.51501e-7 0 -1 2.74562e-7 0 -1 0 0 -1 -1.67084e-7 0 -1 -1.51501e-7 0 -1 -2.74562e-7 0 -1 0 0 -1 1.67084e-7 0 -1 -3.18586e-7 0 -1 -1.9018e-7 0 -1 0 0.08591431 0 -0.9963026 0.08591431 1.15619e-5 -0.9963026 -0.08591431 0 -0.9963026 -0.08591431 -1.15619e-5 -0.9963026 -0.9963026 0 -0.08591431 -0.9963026 0 -0.08591425 0 -1 -3.22361e-7 0 -1 -2.96537e-7 0 -1 4.34989e-7 0 -1 3.4318e-7 0 -1 -5.8284e-7 0 -1 1.9018e-7 0 -1 3.18586e-7 -0.9963026 0 0.08591431 -0.9963026 -1.15619e-5 0.08591431 -0.08591425 0 0.9963026 -0.08591431 0 0.9963026 0 -1 7.31525e-7 0 -1 6.65541e-7 0.9963026 0 -0.08591431 0.9963026 0 -0.08591425 0.08591425 0 0.9963026 0.08591431 0 0.9963026 0.9963026 0 0.08591431 0.9963026 1.15619e-5 0.08591431 0 1 2.89366e-7 0 1 2.59809e-7 0 1 0 0 1 0 0 1 -1.41035e-7 0 1 -2.1429e-7 0 1 -2.59809e-7 0 1 -2.89366e-7 0 1 0 0 1 0 0 1 0 0 1 0 0 1 1.41035e-7 0 1 2.1429e-7 -0.7071067 0 0.7071069 -0.03385764 0 0.9994267 -0.03385764 0 0.9994267 -0.7071067 0 0.707107 0.03385764 0 0.9994267 0.03385764 0 0.9994267 0.7071067 0 0.7071069 0.9994267 0 0.0338577 0.9994267 0 0.0338577 -0.9994267 0 -0.0338577 -0.9994267 0 0.0338577 -0.9994267 0 0.0338577 -0.9994267 0 -0.0338577 0.9994267 0 -0.0338577 0.9994267 0 -0.0338577 -0.7071067 0 -0.7071069 0.03385764 0 -0.9994267 -0.03385764 0 -0.9994267 -0.03385764 0 -0.9994267 0.03385764 0 -0.9994267 0.7071067 0 -0.7071069 0.7071067 0 -0.707107 0 -1 2.96537e-7 0 -1 3.22361e-7 0 -1 -6.65541e-7 0 -1 -7.31525e-7 0 -1 -4.34989e-7 0 -1 -3.4318e-7 0 -1 5.8284e-7 0.4560979 -0.8894689 0.02863216 0.4812495 -0.8765838 0 0.4560704 -0.8894822 0.02866017 0.07909679 -0.9836712 0.1616626 0.1072031 -0.9803969 0.1653161 0.1072056 -0.9803954 0.1653231 0.8785427 -1.10269e-6 -0.4776638 0.8785444 -7.66577e-7 -0.4776608 0.8628937 0 -0.5053858 -1 0 0 -0.9995672 -8.32602e-7 0.0294215 -0.9995665 8.35442e-7 0.02944213 0.4812501 0.8765833 0 0.4543957 0.8905857 0.0195406 0.4544214 0.890573 0.01951926 0.5588508 0 -0.8292682 1 0 0 0.9993926 -7.96195e-7 -0.03484922 0.9993935 2.95992e-6 -0.03482294 0.4394866 0 0.8982492 0.03662693 0.9991971 0.01623499 0 1 1.97394e-7 0.0366162 0.9991976 0.01623183 -0.8901045 1.1716e-6 0.4557564 -0.8901056 -4.54485e-6 0.4557543 -0.8628937 0 0.5053857 -0.4560958 -0.8894701 0.02862977 -0.4560732 -0.8894805 0.02866333 -0.4812495 -0.8765838 0 -0.07909679 -0.9836713 0.1616626 -0.1072062 -0.9803961 0.1653191 -0.1072013 -0.9803952 0.1653274 -0.8785427 -4.18723e-6 -0.4776638 -0.8628937 0 -0.5053858 -0.8785444 -7.53091e-7 -0.4776608 0.9995665 2.88989e-6 0.02944213 0.9995672 8.16251e-7 0.0294215 -0.4812501 0.8765835 0 -0.4544234 0.890572 0.01952272 -0.4543973 0.8905848 0.01954329 -0.5588509 0 -0.8292682 -0.9993935 4.83822e-6 -0.03482294 -0.9993926 3.42597e-5 -0.03484922 -0.4394866 0 0.8982492 -0.0366289 0.9991971 0.01623052 -0.03661936 0.9991976 0.01622474 0 1 0 0.8901056 1.04035e-6 0.4557543 0.8901046 7.31647e-6 0.4557563 0.8628937 0 0.5053857 0.7852237 0 -0.6192124 0.7852236 0 -0.6192123 0.7852246 0 -0.6192112 0.7852241 0 -0.6192117 0.7852242 0 -0.6192117 0.7852239 -1.31792e-7 -0.6192121 0.7852239 -1.31792e-7 -0.6192121 0.7852233 9.8254e-7 -0.6192128 -1 0 0 -1 0 0 -1 5.48928e-7 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1.55174e-6 0.7071025 0.7071111 0.001564085 0.7061607 0.7080499 2.28474e-4 0.9999985 0.001759707 4.54935e-7 1 -1.43752e-7 2.64549e-6 6.16078e-7 1 0.002332746 0 0.9999974 1.54721e-6 -0.7071018 0.7071118 0.001564025 -0.7061599 0.7080507 4.53124e-7 -1 2.87504e-7 2.28435e-4 -0.9999985 0.001760184 1.54947e-6 -0.7071022 -0.7071114 -7.7383e-4 -0.7077983 -0.7064142 2.64887e-6 0 -1 -0.001023411 -5.65009e-7 -0.9999995 1.554e-6 0.7071022 -0.7071114 -7.73918e-4 0.7077983 -0.7064142 0.09421998 0.6949923 0.7128173 0.005836009 0.9996225 0.02684885 0.1329504 -1.40495e-7 0.9911227 0.09422004 -0.6949921 0.7128174 0.005835711 -0.9996225 0.02684837 -0.07325237 -0.7226147 -0.6873587 -0.1024832 2.47608e-6 -0.9947348 -0.07325237 0.7226154 -0.687358 0.3054393 0.6945024 0.6514395 0.01157063 0.999513 0.02898234 0.4231468 0 0.9060612 0.3054408 -0.694501 0.6514402 0.01157033 -0.999513 0.02898216 -0.3047432 -0.7263411 -0.6160845 -0.4460602 -2.4009e-6 -0.895003 -0.3047435 0.726341 -0.6160846 0.4194677 0.7017924 0.5757902 0.005053222 0.9999523 0.008362591 0.5881478 1.25014e-6 0.8087536 0.4194679 -0.7017924 0.5757901 0.00512588 -0.9999513 0.008442699 -0.4185432 -0.7114533 -0.5644961 -0.5964211 -1.56451e-7 -0.8026718 -0.4185858 0.7114533 -0.5644645 0.436748 0.7055046 0.5581349 0.001685082 0.9999961 0.002242982 0.6162191 4.31632e-7 0.7875748 0.4367254 -0.7055442 0.5581024 0.001690208 -0.9999961 0.002239406 -0.4352825 -0.7083677 -0.5556479 -0.616734 -1.23115e-7 -0.7871717 -0.4352856 0.7083671 -0.5556461 -9.09493e-7 -9.31322e-7 -1 1.76121e-6 -0.9999997 8.28538e-4 -1.09403e-5 -0.9981449 0.06088358 -0.9998431 -0.01689887 0.005331814 -0.9999716 -0.007494032 8.51784e-4 0.9999716 -0.007494091 8.44541e-4 0.999843 -0.01689869 0.005331456 1.65436e-6 0.9999997 8.40427e-4 8.21799e-6 0.9986377 -0.05218201 7.86546e-7 -0.8178263 0.5754651 -0.9995674 -0.02753049 0.01035207 0.9995658 -0.02756983 0.01039487 -1.084e-6 0.8089757 -0.5878422 8.66764e-7 -0.8600719 0.510173 -0.9976976 0.06468349 -0.02039015 0.9976975 0.06468278 -0.020392 3.79184e-7 0.8746722 -0.4847148 5.80418e-7 -0.9997603 -0.02189725 -0.9940679 0.1087547 0.001284778 0.9940679 0.1087543 0.001285314 -1.28089e-6 0.9998018 0.01991391 -1.9682e-6 -0.8773059 -0.4799316 -0.9941849 0.09341061 0.05358213 0.9941852 0.0935747 0.05328923 1.28989e-6 0.8753772 0.4834405 4.89306e-7 -0.5096998 -0.8603524 -0.9934269 0.05877554 0.09822762 0.9934267 0.05877286 0.09822934 0 0.5104705 0.8598953 3.411e-7 -0.02494466 -0.9996889 -0.9941967 0.007024705 0.1073487 0.9941966 0.007025897 0.1073498 -1.05547e-6 0.0328384 0.9994607 1.02419e-6 0.3839694 -0.9233458 -0.9944625 -0.04200917 0.09633105 0.9944624 -0.04200619 0.09633308 -6.41382e-7 -0.3869713 0.9220918 -8.47829e-7 0.7354966 -0.6775285 -0.9922265 -0.09617823 0.07897096 0.9922265 -0.09617757 0.07897204 -1.1595e-6 -0.7441658 0.667995 2.30039e-6 0.9844472 -0.1756811 -0.9925724 -0.118732 0.02651238 0.9925723 -0.1187326 0.02651 -4.24048e-7 -0.9827599 0.1848865 2.87048e-6 0.9698519 0.2436955 -0.994284 -0.104205 -0.0232526 0.9942841 -0.1042035 -0.02325564 -3.27106e-6 -0.9710991 -0.2386767 0 0.7932357 0.6089147 -0.9993349 -0.03347831 -0.01446384 0.9993349 -0.03347754 -0.01446366 2.66984e-7 -0.8034572 -0.5953626 -2.35431e-7 0.8618243 0.5072069 -0.9979473 0.0585342 0.02597945 0.9979475 0.05853176 0.02598202 8.48582e-7 -0.8537864 -0.5206236 -6.51402e-7 0.9778119 0.2094851 -0.9884114 0.1509797 0.01574707 0.9884021 0.1511143 0.0150237 -2.11364e-6 -0.9691148 -0.2466101 1.47035e-6 0.9111043 -0.412176 -0.9935294 0.1120536 -0.01853609 0.9935298 0.1120491 -0.01853984 1.71475e-5 -0.8811277 0.4728786 -5.15467e-7 0.8745182 -0.4849926 -0.9994385 -0.03303784 0.005601227 0.9994387 -0.03303313 0.005606412 -1.75198e-6 -0.884548 0.4664494 2.01011e-6 0.999824 0.01876127 -0.9971166 -0.07571321 0.005111932 0.9971166 -0.07571339 0.005107641 1.62582e-6 -0.9999799 -0.006358385 0 0.9292331 0.3694941 -0.9999678 -0.008019208 -2.70635e-4 0.9999679 -0.008017957 -2.72206e-4 1.46172e-6 -0.9311491 -0.3646388 -9.22355e-7 0.9978953 0.06484675 -0.9962772 0.08619904 -0.001263737 0.9962771 0.08619982 -0.001266241 -3.9758e-7 -0.9967609 -0.08042371 2.71317e-7 0.9184439 -0.3955516 -0.997423 0.06919395 -0.0189678 0.9974229 0.06919461 -0.01896613 -6.53614e-7 -0.9098791 0.4148736 -4.43853e-7 0.8611109 -0.5084173 -0.9991766 0.03535252 -0.01991057 0.9991766 0.03535282 -0.01991021 -5.17858e-7 -0.8602349 0.5098981 6.82121e-7 -0.5735777 -0.8191514</float_array>
          <technique_common>
            <accessor source="#tow_reciever_b-mesh-normals-array" count="298" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_reciever_b-mesh-map-0">
          <float_array id="tow_reciever_b-mesh-map-0-array" count="1720">0.397257 0.4776004 0.3389247 0.4776004 0.3389247 0.3448827 0.397257 0.3448827 0.2749298 0.3448829 0.3332623 0.3448827 0.3332623 0.4776004 0.2749298 0.4776005 0.4612519 0.4776004 0.4029194 0.4776004 0.4029194 0.3448827 0.4612519 0.3448827 0.2692674 0.4776005 0.2692674 0.3448829 0.2749298 0.3448829 0.2749298 0.4776005 0.3389247 0.3448827 0.3389247 0.4776004 0.3332623 0.4776004 0.3332623 0.3448827 0.4669142 0.3448827 0.4669142 0.4776004 0.4612519 0.4776004 0.4612519 0.3448827 0.397257 0.4776004 0.397257 0.3448827 0.4029194 0.3448827 0.4029194 0.4776004 0.5252467 0.4776004 0.4669142 0.4776004 0.4669142 0.3448827 0.5252467 0.3448827 0.7154162 0.5832189 0.7230287 0.5756679 0.7661362 0.5756679 0.7737487 0.5832189 0.7737487 0.516879 0.7661362 0.5244303 0.7230287 0.5244303 0.7154162 0.516879 0.7777526 0.5792151 0.7702015 0.5716029 0.7702015 0.5284951 0.7777526 0.520883 0.2379744 0.6316979 0.2379744 0.488879 0.2810821 0.488879 0.2810821 0.6316979 0.7661362 0.5244303 0.7737487 0.516879 0.7777526 0.520883 0.7702015 0.5284951 0.2810821 0.6316979 0.2810821 0.488879 0.2868308 0.488879 0.2868309 0.6316979 0.09137868 0.6203598 0.09657627 0.6184141 0.1005802 0.6224176 0.09863448 0.6276159 0.09365373 0.625341 0.7189636 0.5284951 0.7114124 0.520883 0.7154162 0.516879 0.7230287 0.5244303 0.7114124 0.520883 0.7189636 0.5284951 0.7189636 0.5716029 0.7114124 0.5792151 0.7230287 0.5756679 0.7154162 0.5832189 0.7114124 0.5792151 0.7189636 0.5716029 0.7702015 0.5716029 0.7777526 0.5792151 0.7737487 0.5832189 0.7661362 0.5756679 0.2868309 0.6316979 0.2868308 0.488879 0.3299386 0.488879 0.3299386 0.6316974 0.3356873 0.6316974 0.3299386 0.6316974 0.3299386 0.488879 0.3356873 0.488879 0.1005802 0.6224176 0.1589126 0.6224176 0.1608585 0.6276159 0.09863448 0.6276159 0.2379744 0.6316979 0.2322257 0.6316979 0.2322257 0.488879 0.2379744 0.488879 0.3356873 0.6316974 0.3356873 0.488879 0.378795 0.488879 0.378795 0.6316974 0.189118 0.6316979 0.189118 0.488879 0.2322257 0.488879 0.2322257 0.6316979 0.06480604 0.6148713 0.074364 0.6276208 0.01213991 0.627621 0.02169835 0.6148715 0.004884243 0.5581411 0.007159531 0.55316 0.01213991 0.5508856 0.02169835 0.5636346 0.01763343 0.5676994 0.02169835 0.5636346 0.01213991 0.5508856 0.074364 0.5508854 0.06480604 0.5636345 0.01213991 0.627621 0.007159531 0.6253451 0.004884243 0.6203644 0.01763349 0.6108067 0.02169835 0.6148715 0.01763349 0.6108067 0.004884243 0.6203644 0.004884243 0.5581411 0.01763343 0.5676994 0.06887102 0.5676993 0.08161979 0.5581409 0.08161979 0.6203643 0.06887102 0.6108065 0.08161979 0.6203643 0.07934474 0.6253449 0.074364 0.6276208 0.06480604 0.6148713 0.06887102 0.6108065 0.1608585 0.6276159 0.1589126 0.6224176 0.1629165 0.6184141 0.1681144 0.6203598 0.1658391 0.625341 0.09657627 0.5600815 0.09657627 0.6184141 0.09137868 0.6203598 0.09137868 0.5581353 0.08352965 0.9188859 0.08900541 0.9188859 0.08900541 0.9289863 0.08352965 0.9289863 0.08900541 0.9188859 0.1512295 0.918886 0.1512295 0.9289864 0.08900541 0.9289863 0.1567052 0.918886 0.1621807 0.918886 0.1621808 0.9289864 0.1567052 0.9289864 0.01582998 0.918886 0.07805413 0.9188859 0.07805413 0.9289863 0.01582998 0.9289864 0.1621807 0.918886 0.2244049 0.918886 0.2244049 0.9289864 0.1621808 0.9289864 0.01035422 0.918886 0.01582998 0.918886 0.01582998 0.9289864 0.01035434 0.9289864 0.2353562 0.918886 0.2975803 0.9188855 0.2975804 0.9289864 0.2353562 0.9289864 0.2298806 0.918886 0.2353562 0.918886 0.2353562 0.9289864 0.2298806 0.9289864 0.1629165 0.6184141 0.1629165 0.5600815 0.1681141 0.5581353 0.1681144 0.6203598 0.09863436 0.5508792 0.1005802 0.5560775 0.09657627 0.5600815 0.09137868 0.5581353 0.09365373 0.5531547 0.1589126 0.5560775 0.1005802 0.5560775 0.09863436 0.5508792 0.1608585 0.5508792 0.1681141 0.5581353 0.1629165 0.5600815 0.1589126 0.5560775 0.1608585 0.5508792 0.1658391 0.5531547 0.074364 0.5508854 0.07934474 0.5531598 0.08161979 0.5581409 0.06887102 0.5676993 0.06480604 0.5636345 0.1512295 0.918886 0.1567052 0.918886 0.1567052 0.9289864 0.1512295 0.9289864 0.07805413 0.9188859 0.08352965 0.9188859 0.08352965 0.9289863 0.07805413 0.9289863 0.004878699 0.918886 0.01035422 0.918886 0.01035434 0.9289864 0.004878699 0.9289864 0.2244049 0.918886 0.2298806 0.918886 0.2298806 0.9289864 0.2244049 0.9289864 0.1833692 0.6316979 0.1833692 0.488879 0.189118 0.488879 0.189118 0.6316979 0.177356 0.9911798 0.1207667 0.9911798 0.1255844 0.982883 0.1787585 0.9828829 0.2590088 0.9911797 0.187922 0.9911797 0.1873692 0.9828824 0.2596742 0.9828828 0.210565 0.821565 0.210564 0.907782 0.1392573 0.907782 0.1392573 0.834575 0.3371492 0.9559813 0.3371492 0.8608852 0.395486 0.8608853 0.395486 0.9559814 0.8227303 0.9494546 0.7645666 0.9494546 0.7629332 0.940878 0.8176785 0.940878 0.3649722 0.99388 0.2698768 0.9938799 0.2739748 0.9848779 0.3608741 0.984878 0.2724765 0.907782 0.21917 0.907782 0.21917 0.820883 0.2724764 0.8208829 0.7320107 0.9931955 0.6588041 0.9931955 0.6588041 0.9848853 0.7320107 0.9848853 0.751757 0.9494547 0.6806619 0.9494547 0.6800241 0.940878 0.7524244 0.940878 0.395486 0.9559814 0.395486 0.8608853 0.4076615 0.8656662 0.4076625 0.9518827 0.210564 0.907782 0.210565 0.821565 0.21917 0.820883 0.21917 0.907782 0.177356 0.9911798 0.1787585 0.9828829 0.1873692 0.9828824 0.187922 0.9911797 0.7629332 0.940878 0.7645666 0.9494546 0.751757 0.9494547 0.7524244 0.940878 0.4076615 0.8656662 0.4789693 0.8786768 0.4789689 0.9518828 0.4076625 0.9518827 0.177356 0.9911798 0.1787585 0.9828829 0.1255844 0.982883 0.1207667 0.9911798 0.2590088 0.9911797 0.2596742 0.9828828 0.1873692 0.9828824 0.187922 0.9911797 0.210565 0.821565 0.1392573 0.834575 0.1392573 0.907782 0.210564 0.907782 0.3371492 0.9559813 0.395486 0.9559814 0.395486 0.8608853 0.3371492 0.8608852 0.8227303 0.9494546 0.8176785 0.940878 0.7629332 0.940878 0.7645666 0.9494546 0.3649722 0.99388 0.3608741 0.984878 0.2739748 0.9848779 0.2698768 0.9938799 0.2724765 0.907782 0.2724764 0.8208829 0.21917 0.820883 0.21917 0.907782 0.7320107 0.9931955 0.7320107 0.9848853 0.6588041 0.9848853 0.6588041 0.9931955 0.751757 0.9494547 0.7524244 0.940878 0.6800241 0.940878 0.6806619 0.9494547 0.395486 0.9559814 0.4076625 0.9518827 0.4076615 0.8656662 0.395486 0.8608853 0.210564 0.907782 0.21917 0.907782 0.21917 0.820883 0.210565 0.821565 0.177356 0.9911798 0.187922 0.9911797 0.1873692 0.9828824 0.1787585 0.9828829 0.7629332 0.940878 0.7524244 0.940878 0.751757 0.9494547 0.7645666 0.9494546 0.4076615 0.8656662 0.4076625 0.9518827 0.4789689 0.9518828 0.4789693 0.8786768 0.5456273 0.6253427 0.5560899 0.6296766 0.549961 0.6296766 0.549961 0.6148797 0.5604239 0.6192138 0.5604239 0.6253427 0.5456273 0.6192138 0.5560899 0.6148797 0.5604239 0.6192138 0.549961 0.6148797 0.9891931 0.6613423 0.9848592 0.6508801 0.9891931 0.6552138 0.9787303 0.665676 0.9743966 0.6552138 0.9787303 0.6508801 0.9848592 0.665676 0.5456273 0.6192138 0.5604239 0.6253427 0.5560899 0.6296766 0.5456273 0.6253427 0.9787303 0.665676 0.9743966 0.6613423 0.9743966 0.6552138 0.9680451 0.3448867 0.9680451 0.4331985 0.962077 0.4331985 0.962077 0.3448867 0.9740127 0.3448867 0.9740127 0.4331985 0.9680451 0.4331985 0.9680451 0.3448867 0.9799814 0.3448867 0.9799814 0.4331985 0.9740127 0.4331985 0.9740127 0.3448867 0.9859491 0.3448867 0.9859491 0.4331985 0.9799814 0.4331985 0.9799814 0.3448867 0.9919167 0.3448867 0.9919167 0.4331985 0.9859491 0.4331985 0.9859491 0.3448867 0.9501411 0.3448867 0.9501411 0.4331985 0.9441725 0.4331985 0.9441725 0.3448867 0.9561087 0.3448867 0.9561087 0.4331985 0.9501411 0.4331985 0.9501411 0.3448867 0.962077 0.3448867 0.962077 0.4331985 0.9561087 0.4331985 0.9561087 0.3448867 0.9680451 0.4331985 0.9680451 0.4431849 0.962077 0.4431849 0.962077 0.4331985 0.9740127 0.4331985 0.9740127 0.4431849 0.9680451 0.4431849 0.9680451 0.4331985 0.9799814 0.4331985 0.9799814 0.4431849 0.9740127 0.4431849 0.9740127 0.4331985 0.9859491 0.4331985 0.9859491 0.4431849 0.9799814 0.4431849 0.9799814 0.4331985 0.9919167 0.4331985 0.9919167 0.4431849 0.9859491 0.4431849 0.9859491 0.4331985 0.9501411 0.4331985 0.9501411 0.4431849 0.9441725 0.4431849 0.9441725 0.4331985 0.9561087 0.4331985 0.9561087 0.4431849 0.9501411 0.4431849 0.9501411 0.4331985 0.962077 0.4331985 0.962077 0.4431849 0.9561087 0.4431849 0.9561087 0.4331985 0.9680451 0.4431849 0.9680451 0.4506052 0.962077 0.4506052 0.962077 0.4431849 0.9740127 0.4431849 0.9740127 0.4506052 0.9680451 0.4506052 0.9680451 0.4431849 0.9799814 0.4431849 0.9799814 0.4506052 0.9740127 0.4506052 0.9740127 0.4431849 0.9859491 0.4431849 0.9859491 0.4506052 0.9799814 0.4506052 0.9799814 0.4431849 0.9919167 0.4431849 0.9919167 0.4506052 0.9859491 0.4506052 0.9859491 0.4431849 0.9501411 0.4431849 0.9501411 0.4506052 0.9441725 0.4506052 0.9441725 0.4431849 0.9561087 0.4431849 0.9561087 0.4506052 0.9501411 0.4506052 0.9501411 0.4431849 0.962077 0.4431849 0.962077 0.4506052 0.9561087 0.4506052 0.9561087 0.4431849 0.9680451 0.4506052 0.9680451 0.4611697 0.962077 0.4611697 0.962077 0.4506052 0.9740127 0.4506052 0.9740127 0.4611697 0.9680451 0.4611697 0.9680451 0.4506052 0.9799814 0.4506052 0.9799814 0.4611697 0.9740127 0.4611697 0.9740127 0.4506052 0.9859491 0.4506052 0.9859491 0.4611697 0.9799814 0.4611697 0.9799814 0.4506052 0.9919167 0.4506052 0.9919167 0.4611697 0.9859491 0.4611697 0.9859491 0.4506052 0.9501411 0.4506052 0.9501411 0.4611697 0.9441725 0.4611697 0.9441725 0.4506052 0.9561087 0.4506052 0.9561087 0.4611697 0.9501411 0.4611697 0.9501411 0.4506052 0.962077 0.4506052 0.962077 0.4611697 0.9561087 0.4611697 0.9561087 0.4506052 0.9680451 0.4611697 0.9680451 0.4802401 0.962077 0.4802401 0.962077 0.4611697 0.9740127 0.4611697 0.9740127 0.4802401 0.9680451 0.4802401 0.9680451 0.4611697 0.9799814 0.4611697 0.9799814 0.4802401 0.9740127 0.4802401 0.9740127 0.4611697 0.9859491 0.4611697 0.9859491 0.4802401 0.9799814 0.4802401 0.9799814 0.4611697 0.9919167 0.4611697 0.9919167 0.4802401 0.9859491 0.4802401 0.9859491 0.4611697 0.9501411 0.4611697 0.9501411 0.4802401 0.9441725 0.4802401 0.9441725 0.4611697 0.9561087 0.4611697 0.9561087 0.4802401 0.9501411 0.4802401 0.9501411 0.4611697 0.962077 0.4611697 0.962077 0.4802401 0.9561087 0.4802401 0.9561087 0.4611697 0.9848592 0.665676 0.9787303 0.6508801 0.9848592 0.6508801 0.9891931 0.6613423 0.2539069 0.3448848 0.2539069 0.347716 0.2510758 0.347716 0.2510758 0.3448848 0.1310737 0.9588853 0.1639157 0.9588851 0.1639878 0.9621087 0.1311113 0.9621089 0.1311113 0.9681732 0.1639878 0.968173 0.1639157 0.9713976 0.1310737 0.9713978 0.1311255 0.9651418 0.1640332 0.9651417 0.1639878 0.968173 0.1311113 0.9681732 0.1311113 0.9621089 0.1639878 0.9621087 0.1640332 0.9651417 0.1311255 0.9651418 0.1639157 0.9588851 0.1688311 0.9588851 0.1688311 0.9621087 0.1639878 0.9621087 0.1639878 0.968173 0.1688311 0.968173 0.1688311 0.9713976 0.1639157 0.9713976 0.1640332 0.9651417 0.1688311 0.9651417 0.1688311 0.968173 0.1639878 0.968173 0.1639878 0.9621087 0.1688311 0.9621087 0.1688311 0.9651417 0.1640332 0.9651417 0.1688311 0.9588851 0.1746547 0.9589205 0.1746547 0.9621087 0.1688311 0.9621087 0.1688311 0.968173 0.1746547 0.968173 0.1746547 0.9713618 0.1688311 0.9713976 0.1688311 0.9651417 0.1746547 0.9651417 0.1746547 0.968173 0.1688311 0.968173 0.1688311 0.9621087 0.1746547 0.9621087 0.1746547 0.9651417 0.1688311 0.9651417 0.1746547 0.9589205 0.1795691 0.9589205 0.1795691 0.9621087 0.1746547 0.9621087 0.1746547 0.968173 0.1795691 0.968173 0.1795691 0.9713618 0.1746547 0.9713618 0.1746547 0.9651417 0.1795691 0.9651417 0.1795691 0.968173 0.1746547 0.968173 0.1746547 0.9621087 0.1795691 0.9621087 0.1795691 0.9651417 0.1746547 0.9651417 0.1795691 0.9589205 0.185442 0.9589206 0.185442 0.9621089 0.1795691 0.9621087 0.1795691 0.968173 0.185442 0.9681732 0.185442 0.9713619 0.1795691 0.9713618 0.1795691 0.9651417 0.185442 0.9651418 0.185442 0.9681732 0.1795691 0.968173 0.1795691 0.9621087 0.185442 0.9621089 0.185442 0.9651418 0.1795691 0.9651417 0.185442 0.9589206 0.1907271 0.9589206 0.1907271 0.9621089 0.185442 0.9621089 0.185442 0.9681732 0.1907271 0.9681732 0.1907271 0.9713619 0.185442 0.9713619 0.185442 0.9651418 0.1907271 0.9651418 0.1907271 0.9681732 0.185442 0.9681732 0.185442 0.9621089 0.1907271 0.9621089 0.1907271 0.9651418 0.185442 0.9651418 0.1907271 0.9589206 0.1957836 0.9589206 0.1957836 0.9621089 0.1907271 0.9621089 0.1907271 0.9681732 0.1957836 0.9681732 0.1957836 0.9713619 0.1907271 0.9713619 0.1907271 0.9651418 0.1957836 0.9651418 0.1957836 0.9681732 0.1907271 0.9681732 0.1907271 0.9621089 0.1957836 0.9621089 0.1957836 0.9651418 0.1907271 0.9651418 0.1957836 0.9589206 0.2007522 0.9589206 0.2007522 0.9621089 0.1957836 0.9621089 0.1957836 0.9681732 0.2007522 0.9681732 0.2007522 0.9713619 0.1957836 0.9713619 0.1957836 0.9651418 0.2007522 0.9651418 0.2007522 0.9681732 0.1957836 0.9681732 0.1957836 0.9621089 0.2007522 0.9621089 0.2007522 0.9651418 0.1957836 0.9651418 0.2007522 0.9589206 0.2054696 0.9589205 0.2054696 0.9621087 0.2007522 0.9621089 0.2007522 0.9681732 0.2054696 0.968173 0.2054696 0.9713618 0.2007522 0.9713619 0.2007522 0.9651418 0.2054696 0.9651417 0.2054696 0.968173 0.2007522 0.9681732 0.2007522 0.9621089 0.2054696 0.9621087 0.2054696 0.9651417 0.2007522 0.9651418 0.2054696 0.9589205 0.2097443 0.9589205 0.2097443 0.9621087 0.2054696 0.9621087 0.2054696 0.968173 0.2097443 0.968173 0.2097443 0.9713618 0.2054696 0.9713618 0.2054696 0.9651417 0.2097443 0.9651417 0.2097443 0.968173 0.2054696 0.968173 0.2054696 0.9621087 0.2097443 0.9621087 0.2097443 0.9651417 0.2054696 0.9651417 0.2097443 0.9589205 0.2149977 0.9589205 0.2149977 0.9621087 0.2097443 0.9621087 0.2097443 0.968173 0.2149977 0.968173 0.2149977 0.9713618 0.2097443 0.9713618 0.2097443 0.9651417 0.2149977 0.9651417 0.2149977 0.968173 0.2097443 0.968173 0.2097443 0.9621087 0.2149977 0.9621087 0.2149977 0.9651417 0.2097443 0.9651417 0.2149977 0.9589205 0.2189754 0.9589205 0.2189754 0.9621087 0.2149977 0.9621087 0.2149977 0.968173 0.2189754 0.968173 0.2189754 0.9713618 0.2149977 0.9713618 0.2149977 0.9651417 0.2189754 0.9651417 0.2189754 0.968173 0.2149977 0.968173 0.2149977 0.9621087 0.2189754 0.9621087 0.2189754 0.9651417 0.2149977 0.9651417 0.2189754 0.9589205 0.2232233 0.9589205 0.2232233 0.9621087 0.2189754 0.9621087 0.2189754 0.968173 0.2232233 0.968173 0.2232233 0.9713618 0.2189754 0.9713618 0.2189754 0.9651417 0.2232233 0.9651417 0.2232233 0.968173 0.2189754 0.968173 0.2189754 0.9621087 0.2232233 0.9621087 0.2232233 0.9651417 0.2189754 0.9651417 0.2232233 0.9589205 0.227266 0.9589205 0.227266 0.9621087 0.2232233 0.9621087 0.2232233 0.968173 0.227266 0.968173 0.227266 0.9713618 0.2232233 0.9713618 0.2232233 0.9651417 0.227266 0.9651417 0.227266 0.968173 0.2232233 0.968173 0.2232233 0.9621087 0.227266 0.9621087 0.227266 0.9651417 0.2232233 0.9651417 0.227266 0.9589205 0.2297688 0.9589205 0.2297688 0.9621087 0.227266 0.9621087 0.227266 0.968173 0.2297688 0.968173 0.2297688 0.9713618 0.227266 0.9713618 0.227266 0.9651417 0.2297688 0.9651417 0.2297688 0.968173 0.227266 0.968173 0.227266 0.9621087 0.2297688 0.9621087 0.2297688 0.9651417 0.227266 0.9651417 0.2297688 0.9589205 0.2347464 0.9589205 0.2347464 0.9621087 0.2297688 0.9621087 0.2297688 0.968173 0.2347464 0.968173 0.2347464 0.9713618 0.2297688 0.9713618 0.2297688 0.9651417 0.2347464 0.9651417 0.2347464 0.968173 0.2297688 0.968173 0.2297688 0.9621087 0.2347464 0.9621087 0.2347464 0.9651417 0.2297688 0.9651417 0.2347464 0.9589205 0.2412898 0.95892 0.2412898 0.9621087 0.2347464 0.9621087 0.2347464 0.968173 0.2412898 0.968173 0.2412898 0.9713608 0.2347464 0.9713618 0.2347464 0.9651417 0.2412898 0.9651417 0.2412898 0.968173 0.2347464 0.968173 0.2347464 0.9621087 0.2412898 0.9621087 0.2412898 0.9651417 0.2347464 0.9651417 0.2412898 0.95892 0.2486898 0.95892 0.2486898 0.9621087 0.2412898 0.9621087 0.2412898 0.968173 0.2486898 0.968173 0.2486898 0.9713608 0.2412898 0.9713608 0.2412898 0.9651417 0.2486898 0.9651417 0.2486898 0.968173 0.2412898 0.968173 0.2412898 0.9621087 0.2486898 0.9621087 0.2486898 0.9651417 0.2412898 0.9651417 0.2486898 0.95892 0.2541277 0.95892 0.2541277 0.9621087 0.2486898 0.9621087 0.2486898 0.968173 0.2541277 0.968173 0.2541277 0.9713608 0.2486898 0.9713608 0.2486898 0.9651417 0.2541277 0.9651417 0.2541277 0.968173 0.2486898 0.968173 0.2486898 0.9621087 0.2541277 0.9621087 0.2541277 0.9651417 0.2486898 0.9651417 0.2541277 0.95892 0.2596721 0.95892 0.2596721 0.9621087 0.2541277 0.9621087 0.2541277 0.968173 0.2596721 0.968173 0.2596721 0.9713608 0.2541277 0.9713608 0.2541277 0.9651417 0.2596721 0.9651417 0.2596721 0.968173 0.2541277 0.968173 0.2541277 0.9621087 0.2596721 0.9621087 0.2596721 0.9651417 0.2541277 0.9651417 0.2596721 0.95892 0.2653632 0.95892 0.2653632 0.9621087 0.2596721 0.9621087 0.2596721 0.968173 0.2653632 0.968173 0.2653632 0.9713608 0.2596721 0.9713608 0.2596721 0.9651417 0.2653632 0.9651417 0.2653632 0.968173 0.2596721 0.968173 0.2596721 0.9621087 0.2653632 0.9621087 0.2653632 0.9651417 0.2596721 0.9651417 0.2527809 0.3617179 0.2527809 0.3588872 0.2556121 0.3588872 0.2556121 0.3617179</float_array>
          <technique_common>
            <accessor source="#tow_reciever_b-mesh-map-0-array" count="860" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_reciever_b-mesh-vertices">
          <input semantic="POSITION" source="#tow_reciever_b-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="214">
          <input semantic="VERTEX" source="#tow_reciever_b-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_reciever_b-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_reciever_b-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 5 4 4 4 4 4 4 4 4 4 4 4 5 4 5 4 4 5 5 4 4 4 4 4 4 4 4 4 4 5 4 5 5 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 4 3 3 4 4 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>29 0 0 14 1 1 5 2 2 23 3 3 0 4 4 6 5 5 12 6 6 10 7 7 24 8 8 31 9 9 20 10 10 19 11 11 9 12 12 3 13 13 0 4 14 10 7 15 5 2 16 14 1 17 12 6 18 6 5 19 17 14 20 26 15 21 24 8 22 19 11 23 29 0 24 23 3 25 20 10 26 31 9 27 9 12 28 26 15 29 17 14 30 3 13 31 5 16 32 36 17 33 46 18 34 23 19 35 17 20 36 40 21 37 35 22 38 3 23 39 20 24 40 44 25 41 42 26 42 19 26 43 36 27 44 54 28 45 60 29 46 46 30 47 40 21 48 17 20 49 19 26 50 42 26 51 46 30 52 60 29 53 63 31 54 44 32 55 67 33 56 12 34 57 14 35 58 66 36 59 82 37 60 33 38 61 0 39 62 3 23 63 35 22 64 0 39 65 33 38 66 39 26 67 6 26 68 36 17 69 5 16 70 6 26 71 39 26 72 44 25 73 20 24 74 23 19 75 46 18 76 44 32 77 63 31 78 57 40 79 42 41 80 40 42 81 42 41 82 57 40 83 59 43 84 14 35 85 29 44 86 71 45 87 66 36 88 36 27 89 39 46 90 53 47 91 54 28 92 40 42 93 59 43 94 49 48 95 35 49 96 33 50 97 50 51 98 53 47 99 39 46 100 54 52 101 72 53 102 73 54 103 60 55 104 79 56 105 87 57 106 78 58 107 59 59 108 57 60 109 59 59 110 78 58 111 77 61 112 49 62 113 73 54 114 81 57 115 74 56 116 63 60 117 60 55 118 63 60 119 74 56 120 79 56 121 57 60 122 50 63 123 75 64 124 76 64 125 53 63 126 76 64 127 83 65 128 72 53 129 54 52 130 53 63 131 71 45 132 29 44 133 31 26 134 70 26 135 80 26 136 10 26 137 12 34 138 67 33 139 64 26 140 82 66 141 66 67 142 72 68 143 83 69 144 66 67 145 71 70 146 73 71 147 72 68 148 80 72 149 70 73 150 74 74 151 81 72 152 64 75 153 67 76 154 76 77 155 75 78 156 70 73 157 69 79 158 79 80 159 74 74 160 84 81 161 64 75 162 75 78 163 85 81 164 68 82 165 65 83 166 77 84 167 78 85 168 86 86 169 68 82 170 78 85 171 87 87 172 31 26 173 24 88 174 69 89 175 70 26 176 65 90 177 9 91 178 10 26 179 64 26 180 84 26 181 26 92 182 9 91 183 65 90 184 68 93 185 69 89 186 24 88 187 26 92 188 68 93 189 86 94 190 77 61 191 85 65 192 75 64 193 50 63 194 49 62 195 71 70 196 80 72 197 81 72 198 73 71 199 67 76 200 82 66 201 83 69 202 76 77 203 65 83 204 84 81 205 85 81 206 77 84 207 69 79 208 86 86 209 87 87 210 79 80 211 35 49 212 49 48 213 50 51 214 33 50 215 16 95 216 1 96 217 11 96 218 30 97 219 4 98 220 18 99 221 28 100 222 15 98 223 28 101 224 27 102 225 13 103 226 15 103 227 2 104 228 1 104 229 16 105 230 22 106 231 2 107 232 22 108 233 25 109 234 8 107 235 1 110 236 2 110 237 8 110 238 11 110 239 8 111 240 25 112 241 30 113 242 11 111 243 7 114 244 4 114 245 15 114 246 13 114 247 21 115 248 7 116 249 13 116 250 27 117 251 22 106 252 16 105 253 18 118 254 21 119 255 27 102 256 28 101 257 30 113 258 25 112 259 16 95 260 30 97 261 28 100 262 18 99 263 25 109 264 22 108 265 21 115 266 27 117 267 18 118 268 4 120 269 7 120 270 21 119 271 48 121 272 62 122 273 43 123 274 32 123 275 37 124 276 47 124 277 61 125 278 51 126 279 61 127 280 47 128 281 45 128 282 58 129 283 34 111 284 55 130 285 48 131 286 32 111 287 34 132 288 41 132 289 56 133 290 55 134 291 32 135 292 43 135 293 41 135 294 34 135 295 41 104 296 43 104 297 62 136 298 56 137 299 38 138 300 45 138 301 47 138 302 37 138 303 52 139 304 58 140 305 45 141 306 38 141 307 55 130 308 52 142 309 51 143 310 48 131 311 58 129 312 56 137 313 62 136 314 61 127 315 48 121 316 51 126 317 61 125 318 62 122 319 56 133 320 58 140 321 52 139 322 55 134 323 51 143 324 52 142 325 38 144 326 37 144 327 134 145 328 133 146 329 89 147 330 130 148 331 132 149 332 88 150 333 135 151 334 131 152 335 132 149 336 130 148 337 95 153 338 92 154 339 96 155 340 93 156 341 90 157 342 97 158 343 94 159 344 135 151 345 88 150 346 133 146 347 134 145 348 93 156 349 91 104 350 90 157 351 90 160 352 98 161 353 105 162 354 97 163 355 91 164 356 99 165 357 98 161 358 90 160 359 93 166 360 100 167 361 99 165 362 91 164 363 94 168 364 101 169 365 100 167 366 93 166 367 95 170 368 102 171 369 101 169 370 94 168 371 96 172 372 103 173 373 102 171 374 95 170 375 92 174 376 104 175 377 103 173 378 96 172 379 97 163 380 105 162 381 104 175 382 92 174 383 98 161 384 106 176 385 113 177 386 105 162 387 99 165 388 107 178 389 106 176 390 98 161 391 100 167 392 108 179 393 107 178 394 99 165 395 101 169 396 109 180 397 108 179 398 100 167 399 102 171 400 110 181 401 109 180 402 101 169 403 103 173 404 111 182 405 110 181 406 102 171 407 104 175 408 112 183 409 111 182 410 103 173 411 105 162 412 113 177 413 112 183 414 104 175 415 106 176 416 114 184 417 121 185 418 113 177 419 107 178 420 115 186 421 114 184 422 106 176 423 108 179 424 116 187 425 115 186 426 107 178 427 109 180 428 117 188 429 116 187 430 108 179 431 110 181 432 118 189 433 117 188 434 109 180 435 111 182 436 119 190 437 118 189 438 110 181 439 112 183 440 120 191 441 119 190 442 111 182 443 113 177 444 121 185 445 120 191 446 112 183 447 114 184 448 122 192 449 129 193 450 121 185 451 115 186 452 123 194 453 122 192 454 114 184 455 116 187 456 124 195 457 123 194 458 115 186 459 117 188 460 125 196 461 124 195 462 116 187 463 118 189 464 126 197 465 125 196 466 117 188 467 119 190 468 127 198 469 126 197 470 118 189 471 120 191 472 128 199 473 127 198 474 119 190 475 121 185 476 129 193 477 128 199 478 120 191 479 122 192 480 130 200 481 135 201 482 129 193 483 123 194 484 131 202 485 130 200 486 122 192 487 124 195 488 132 203 489 131 202 490 123 194 491 125 196 492 88 204 493 132 203 494 124 195 495 126 197 496 133 205 497 88 204 498 125 196 499 127 198 500 89 206 501 133 205 502 126 197 503 128 199 504 134 207 505 89 206 506 127 198 507 129 193 508 135 201 509 134 207 510 128 199 511 94 159 512 97 158 513 92 154 514 95 153 515 139 208 516 142 208 517 141 208 518 140 208 519 139 209 520 143 210 521 146 211 522 142 212 523 140 213 524 144 214 525 143 210 526 139 209 527 141 215 528 145 216 529 144 214 530 140 213 531 142 212 532 146 211 533 145 216 534 141 215 535 143 210 536 147 217 537 150 218 538 146 211 539 144 214 540 148 219 541 147 217 542 143 210 543 145 216 544 149 220 545 148 219 546 144 214 547 146 211 548 150 218 549 149 220 550 145 216 551 147 217 552 151 221 553 154 222 554 150 218 555 148 219 556 152 223 557 151 221 558 147 217 559 149 220 560 153 224 561 152 223 562 148 219 563 150 218 564 154 222 565 153 224 566 149 220 567 151 221 568 155 225 569 158 226 570 154 222 571 152 223 572 156 227 573 155 225 574 151 221 575 153 224 576 157 228 577 156 227 578 152 223 579 154 222 580 158 226 581 157 228 582 153 224 583 155 225 584 159 229 585 162 230 586 158 226 587 156 227 588 160 231 589 159 229 590 155 225 591 157 228 592 161 232 593 160 231 594 156 227 595 158 226 596 162 230 597 161 232 598 157 228 599 159 229 600 163 233 601 166 234 602 162 230 603 160 231 604 164 235 605 163 233 606 159 229 607 161 232 608 165 236 609 164 235 610 160 231 611 162 230 612 166 234 613 165 236 614 161 232 615 163 233 616 167 237 617 170 238 618 166 234 619 164 235 620 168 239 621 167 237 622 163 233 623 165 236 624 169 240 625 168 239 626 164 235 627 166 234 628 170 238 629 169 240 630 165 236 631 167 237 632 171 241 633 174 242 634 170 238 635 168 239 636 172 243 637 171 241 638 167 237 639 169 240 640 173 244 641 172 243 642 168 239 643 170 238 644 174 242 645 173 244 646 169 240 647 171 241 648 175 245 649 178 246 650 174 242 651 172 243 652 176 247 653 175 245 654 171 241 655 173 244 656 177 248 657 176 247 658 172 243 659 174 242 660 178 246 661 177 248 662 173 244 663 175 245 664 179 249 665 182 250 666 178 246 667 176 247 668 180 251 669 179 249 670 175 245 671 177 248 672 181 252 673 180 251 674 176 247 675 178 246 676 182 250 677 181 252 678 177 248 679 179 249 680 183 253 681 186 254 682 182 250 683 180 251 684 184 255 685 183 253 686 179 249 687 181 252 688 185 256 689 184 255 690 180 251 691 182 250 692 186 254 693 185 256 694 181 252 695 183 253 696 187 257 697 190 258 698 186 254 699 184 255 700 188 259 701 187 257 702 183 253 703 185 256 704 189 260 705 188 259 706 184 255 707 186 254 708 190 258 709 189 260 710 185 256 711 187 257 712 191 261 713 194 262 714 190 258 715 188 259 716 192 263 717 191 261 718 187 257 719 189 260 720 193 264 721 192 263 722 188 259 723 190 258 724 194 262 725 193 264 726 189 260 727 191 261 728 195 265 729 198 266 730 194 262 731 192 263 732 196 267 733 195 265 734 191 261 735 193 264 736 197 268 737 196 267 738 192 263 739 194 262 740 198 266 741 197 268 742 193 264 743 195 265 744 199 269 745 202 270 746 198 266 747 196 267 748 200 271 749 199 269 750 195 265 751 197 268 752 201 272 753 200 271 754 196 267 755 198 266 756 202 270 757 201 272 758 197 268 759 199 269 760 203 273 761 206 274 762 202 270 763 200 271 764 204 275 765 203 273 766 199 269 767 201 272 768 205 276 769 204 275 770 200 271 771 202 270 772 206 274 773 205 276 774 201 272 775 203 273 776 207 277 777 210 278 778 206 274 779 204 275 780 208 279 781 207 277 782 203 273 783 205 276 784 209 280 785 208 279 786 204 275 787 206 274 788 210 278 789 209 280 790 205 276 791 207 277 792 211 281 793 214 282 794 210 278 795 208 279 796 212 283 797 211 281 798 207 277 799 209 280 800 213 284 801 212 283 802 208 279 803 210 278 804 214 282 805 213 284 806 209 280 807 211 281 808 215 285 809 218 286 810 214 282 811 212 283 812 216 287 813 215 285 814 211 281 815 213 284 816 217 288 817 216 287 818 212 283 819 214 282 820 218 286 821 217 288 822 213 284 823 215 285 824 219 289 825 222 290 826 218 286 827 216 287 828 220 291 829 219 289 830 215 285 831 217 288 832 221 292 833 220 291 834 216 287 835 218 286 836 222 290 837 221 292 838 217 288 839 219 289 840 223 293 841 138 294 842 222 290 843 220 291 844 136 295 845 223 293 846 219 289 847 221 292 848 137 296 849 136 295 850 220 291 851 222 290 852 138 294 853 137 296 854 221 292 855 138 297 856 223 297 857 136 297 858 137 297 859</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_reciever_a-mesh" name="tow_reciever_a">
      <mesh>
        <source id="tow_reciever_a-mesh-positions">
          <float_array id="tow_reciever_a-mesh-positions-array" count="576">-0.03313761 -0.05972331 -0.02913761 -0.02913761 -0.05972331 -0.03313761 -0.02913761 -0.05972331 0.03313755 -0.03313761 -0.05972331 0.02913761 -0.02913761 0.07286459 -0.03313761 -0.03313761 0.07286459 -0.02913761 -0.03313761 0.07286459 0.02913761 -0.02913761 0.07286459 0.03313755 0.02913761 -0.05972331 -0.03313761 0.03313761 -0.05972331 -0.02913761 0.03313761 -0.05972331 0.02913761 0.02913761 -0.05972331 0.03313755 0.03313761 0.07286459 -0.02913761 0.02913761 0.07286459 -0.03313761 0.02913761 0.07286459 0.03313755 0.03313761 0.07286459 0.02913761 -0.02559369 -0.05972331 -0.02153271 -0.02153271 -0.05972331 -0.02559369 -0.02153271 -0.05972331 0.02559369 -0.02559369 -0.05972331 0.02153271 0.02153271 -0.05972331 -0.02559369 0.02559369 -0.05972331 -0.02153271 0.02559369 -0.05972331 0.02153271 0.02153271 -0.05972331 0.02559369 -0.02153271 0.08295559 -0.02559369 -0.02559369 0.08295559 -0.02153271 -0.02559369 0.08295559 0.02153271 -0.02153271 0.08295559 0.02559369 0.02559369 0.08295559 -0.02153271 0.02153271 0.08295559 -0.02559369 0.02153271 0.08295559 0.02559369 0.02559369 0.08295559 0.02153271 -0.03833019 0.07286459 -0.03108161 -0.03108155 0.07286459 -0.03833019 -0.03108155 0.07286459 0.03833013 -0.03833019 0.07286459 0.03108155 0.03108155 0.07286459 -0.03833019 0.03833019 0.07286459 -0.03108161 0.03833019 0.07286459 0.03108155 0.03108155 0.07286459 0.03833013 -0.03108155 0.08295559 0.03833013 0.03108155 0.08295559 0.03833013 0.03833019 0.08295559 0.03108155 -0.03833019 0.08295559 -0.03108161 -0.03833019 0.08295559 0.03108155 -0.03108155 0.08295559 -0.03833019 0.03108155 0.08295559 -0.03833019 0.03833019 0.08295559 -0.03108161 0.03605735 0.07286459 0.03605735 0.03605735 0.08295559 0.03605735 -0.03605735 0.07286459 0.03605735 -0.03605735 0.08295559 0.03605735 -0.03605735 0.07286459 -0.03605735 -0.03605735 0.08295559 -0.03605735 0.03605735 0.07286459 -0.03605735 0.03605735 0.08295559 -0.03605735 0.08202439 0.05119216 -0.01971381 0.07707071 0.05919212 -0.02599561 -0.04801297 0.06484901 0.005656838 -0.04801297 0.05919218 0.007999956 -0.04801297 0.06484901 -0.005656838 -0.04801297 0.05353528 0.005656838 -0.04801297 0.05119216 0 -0.04801297 0.05353528 -0.005656838 -0.04801297 0.05919218 -0.007999956 -0.04801297 0.06719213 0 0.04082924 0.06484901 0.005656599 0.04084867 0.05919218 0.007999718 0.04082924 0.05353528 0.005656659 0.04078221 0.05119216 0 0.04073524 0.05353528 -0.005656599 0.04071581 0.05919218 -0.007999718 0.04073524 0.06484901 -0.005656659 0.04078221 0.06719213 0 0.05160284 0.06484901 0.005435883 0.05192631 0.05919218 0.00775659 0.05160284 0.05353528 0.005435883 0.0508219 0.05119216 -1.66784e-4 0.0500409 0.05353528 -0.005769431 0.04971742 0.05919218 -0.008090138 0.0500409 0.06484901 -0.005769431 0.0508219 0.06719213 -1.66787e-4 0.0603342 0.06484901 0.003079175 0.06128817 0.05919218 0.00521934 0.0603342 0.05353528 0.003079175 0.05803114 0.05119216 -0.002087533 0.05572807 0.05353528 -0.007254362 0.0547741 0.05919218 -0.009394526 0.05572807 0.06484901 -0.007254362 0.05803114 0.06719213 -0.002087533 0.07021903 0.06484901 -0.003293097 0.07158285 0.05919218 -0.001387774 0.07021903 0.05353528 -0.003293097 0.06692647 0.05119216 -0.007893025 0.06363397 0.05353528 -0.01249295 0.0622701 0.05919218 -0.01439827 0.06363397 0.06484901 -0.01249295 0.06692647 0.06719213 -0.007893025 0.08552718 0.06484901 -0.01527196 0.08697807 0.05919218 -0.01343202 0.08552718 0.05353528 -0.01527196 0.0785216 0.05353528 -0.02415573 0.0785216 0.06484901 -0.02415573 0.08202439 0.06719213 -0.01971381 -0.0339998 0.04906332 -0.01868391 -0.03599983 0.04742503 -0.01753675 -0.0379998 0.04906332 -0.01868391 -0.03599983 0.05719214 -0.02037376 -0.03399986 0.05919218 -0.02037376 -0.03599983 0.06119215 -0.02037376 -0.03799986 0.05919218 -0.02037376 -0.03599983 0.05724221 0.01007467 -0.03399986 0.05919218 0.009629905 -0.03599983 0.06114208 0.009185194 -0.0379998 0.05919218 0.009629905 -0.03599983 0.05948114 0.01478177 -0.0339998 0.06111848 0.01363325 -0.03599983 0.06275588 0.01248478 -0.0379998 0.06111848 0.01363331 -0.03599983 0.06282657 0.01812559 -0.0339998 0.06458604 0.01717466 -0.03599983 0.06634557 0.01622378 -0.0379998 0.06458604 0.01717466 -0.03599983 0.06348288 0.02125763 -0.0339998 0.06548285 0.02126002 -0.03599983 0.06748282 0.02126246 -0.0379998 0.06548285 0.02126002 -0.03599983 0.06266283 0.02514755 -0.0339998 0.06439936 0.02613979 -0.03599983 0.06613582 0.02713203 -0.0379998 0.06439936 0.02613979 -0.03599983 0.06009966 0.02748394 -0.0339998 0.06111133 0.02920919 -0.03599983 0.062123 0.03093445 -0.0379998 0.06111133 0.02920919 -0.03599983 0.05695903 0.02852147 -0.0339998 0.05701249 0.03052079 -0.03599983 0.05706602 0.03252005 -0.0379998 0.05701249 0.03052079 -0.03599983 0.05369997 0.0276069 -0.0339998 0.05292183 0.02944928 -0.03599983 0.05214369 0.03129172 -0.0379998 0.05292183 0.02944928 -0.03599983 0.05098617 0.02605342 -0.0339998 0.04948467 0.02737456 -0.03599983 0.04798316 0.02869576 -0.0379998 0.04948467 0.02737456 -0.03599983 0.04997479 0.02364093 -0.0339998 0.04801619 0.02404588 -0.03599983 0.04605764 0.02445083 -0.0379998 0.04801619 0.02404588 -0.03599983 0.04997152 0.02013152 -0.0339998 0.04805064 0.01957452 -0.03599983 0.04612976 0.01901757 -0.0379998 0.04805064 0.01957452 -0.03599983 0.05143994 0.01789993 -0.0339998 0.04983931 0.01670068 -0.03599983 0.04823875 0.01550149 -0.0379998 0.04983931 0.01670068 -0.03599983 0.0539698 0.01502335 -0.0339998 0.05224859 0.01400476 -0.03599983 0.05052739 0.01298618 -0.0379998 0.05224859 0.01400476 -0.03599983 0.05535733 0.01114243 -0.0339998 0.05339419 0.01076012 -0.03599983 0.05143111 0.01037782 -0.0379998 0.05339419 0.01076012 -0.03599983 0.05538487 0.007992625 -0.0339998 0.05349051 0.00863415 -0.03599983 0.05159622 0.009275674 -0.0379998 0.05349051 0.00863415 -0.03599983 0.05250293 0.00439614 -0.0339998 0.05076724 0.005389869 -0.03599983 0.04903155 0.006383538 -0.0379998 0.05076724 0.005389869 -0.03599983 0.05090141 1.24241e-4 -0.0339998 0.04890149 1.42468e-4 -0.03599983 0.04690158 1.60694e-4 -0.0379998 0.04890149 1.42466e-4 -0.03599983 0.05275624 -0.005077242 -0.0339998 0.05090284 -0.005828917 -0.03599983 0.04904949 -0.00658065 -0.0379998 0.05090284 -0.005828917 -0.03599983 0.0548985 -0.009849965 -0.0339998 0.05290424 -0.01000142 -0.03599983 0.05090999 -0.01015287 -0.0379998 0.05290424 -0.01000142 -0.03599983 0.0533598 -0.01532095 -0.0339998 0.05152952 -0.01451474 -0.03599983 0.04969924 -0.01370853 -0.0379998 0.05152952 -0.01451474 -0.03599983 0.05070161 -0.01983106</float_array>
          <technique_common>
            <accessor source="#tow_reciever_a-mesh-positions-array" count="192" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_reciever_a-mesh-normals">
          <float_array id="tow_reciever_a-mesh-normals-array" count="738">0.06412827 0 0.9979417 -0.06412827 -1.1136e-4 0.9979417 -0.06412827 0 0.9979417 0.06412827 1.1136e-4 0.9979417 -0.9979417 0 -0.06412816 -0.9979417 1.23668e-4 0.06412804 -0.9979417 0 0.06412798 -0.9979417 -1.1136e-4 -0.06412822 0.9979417 0 -0.06412816 0.9979417 -1.23668e-4 0.06412804 0.9979417 0 0.06412798 0.9979417 1.1136e-4 -0.06412822 -0.06412816 0 -0.9979417 -0.06412822 1.1136e-4 -0.9979417 0.06412816 0 -0.9979417 0.06412822 -1.1136e-4 -0.9979417 0 -1 1.51501e-7 0 -1 2.74562e-7 0 -1 0 0 -1 -1.67084e-7 0 -1 -1.51501e-7 0 -1 -2.74562e-7 0 -1 0 0 -1 1.67084e-7 0 -1 -3.18586e-7 0 -1 -1.9018e-7 0 -1 0 0.08591431 0 -0.9963026 0.08591431 1.15619e-5 -0.9963026 -0.08591431 0 -0.9963026 -0.08591431 -1.15619e-5 -0.9963026 -0.9963026 0 -0.08591431 -0.9963026 0 -0.08591425 0 -1 -2.96537e-7 0 -1 4.34989e-7 0 -1 3.4318e-7 0 -1 -5.82841e-7 0 -1 -3.22361e-7 0 -1 1.9018e-7 0 -1 3.18586e-7 -0.9963026 0 0.08591431 -0.9963026 -1.15619e-5 0.08591431 -0.08591425 0 0.9963026 -0.08591431 0 0.9963026 0 -1 4.34989e-7 0 -1 3.4318e-7 0.9963026 0 -0.08591431 0.9963026 0 -0.08591425 0.08591425 0 0.9963026 0.08591431 0 0.9963026 0.9963026 0 0.08591431 0.9963026 1.15619e-5 0.08591431 0 1 1.95898e-7 0 1 0 0 1 0 0 1 0 0 1 -1.95898e-7 0 -1 -2.96537e-7 0 -1 -3.22361e-7 0 -1 -5.82841e-7 0 -1 2.96537e-7 0 -1 3.22361e-7 -0.7071067 0 0.7071069 -0.03385764 0 0.9994267 -0.03385764 0 0.9994267 -0.7071067 0 0.707107 0.03385764 0 0.9994267 0.03385764 0 0.9994267 0.7071067 0 0.7071069 0.9994267 0 0.0338577 0.9994267 0 0.0338577 -0.9994267 0 -0.0338577 -0.9994267 0 0.0338577 -0.9994267 0 0.0338577 -0.9994267 0 -0.0338577 0.9994267 0 -0.0338577 0.9994267 0 -0.0338577 -0.7071067 0 -0.7071069 0.03385764 0 -0.9994267 -0.03385764 0 -0.9994267 -0.03385764 0 -0.9994267 0.03385764 0 -0.9994267 0.7071067 0 -0.7071069 0.7071067 0 -0.707107 0 -1 2.96537e-7 0 -1 3.22361e-7 0 -1 -4.34989e-7 0 -1 5.82841e-7 0 -1 -3.4318e-7 0 -1 -4.34989e-7 0 -1 -3.4318e-7 0 -1 5.82841e-7 0.7852237 0 -0.6192124 0.7852236 0 -0.6192123 0.7852246 0 -0.6192112 0.7852241 0 -0.6192117 0.7852242 0 -0.6192117 0.7852239 -1.31792e-7 -0.6192121 0.7852239 -1.31792e-7 -0.6192121 0.7852233 9.8254e-7 -0.6192128 -1 0 0 -1 0 0 -1 5.48928e-7 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1.55174e-6 0.7071025 0.7071111 0.001564085 0.7061607 0.7080499 2.28474e-4 0.9999985 0.001759707 4.54935e-7 1 -1.43752e-7 2.64549e-6 6.16078e-7 1 0.002332746 0 0.9999974 1.54721e-6 -0.7071018 0.7071118 0.001564025 -0.7061599 0.7080507 4.53124e-7 -1 2.87504e-7 2.28435e-4 -0.9999985 0.001760184 1.54947e-6 -0.7071022 -0.7071114 -7.7383e-4 -0.7077983 -0.7064142 2.64887e-6 0 -1 -0.001023411 -5.65009e-7 -0.9999995 1.554e-6 0.7071022 -0.7071114 -7.73918e-4 0.7077983 -0.7064142 0.09421998 0.6949923 0.7128173 0.005836009 0.9996225 0.02684885 0.1329504 -1.40495e-7 0.9911227 0.09422004 -0.6949921 0.7128174 0.005835711 -0.9996225 0.02684837 -0.07325237 -0.7226147 -0.6873587 -0.1024832 2.47608e-6 -0.9947348 -0.07325237 0.7226154 -0.687358 0.3054393 0.6945024 0.6514395 0.01157063 0.999513 0.02898234 0.4231468 0 0.9060612 0.3054408 -0.694501 0.6514402 0.01157033 -0.999513 0.02898216 -0.3047432 -0.7263411 -0.6160845 -0.4460602 -2.4009e-6 -0.895003 -0.3047435 0.726341 -0.6160846 0.4194677 0.7017924 0.5757902 0.005053222 0.9999523 0.008362591 0.5881478 1.25014e-6 0.8087536 0.4194679 -0.7017924 0.5757901 0.00512588 -0.9999513 0.008442699 -0.4185432 -0.7114533 -0.5644961 -0.5964211 -1.56451e-7 -0.8026718 -0.4185858 0.7114533 -0.5644645 0.436748 0.7055046 0.5581349 0.001685082 0.9999961 0.002242982 0.6162191 4.31632e-7 0.7875748 0.4367254 -0.7055442 0.5581024 0.001690208 -0.9999961 0.002239406 -0.4352825 -0.7083677 -0.5556479 -0.616734 -1.23115e-7 -0.7871717 -0.4352856 0.7083671 -0.5556461 -9.09493e-7 -9.31322e-7 -1 1.76121e-6 -0.9999997 8.28538e-4 -1.09403e-5 -0.9981449 0.06088358 -0.9998431 -0.01689887 0.005331814 -0.9999716 -0.007494032 8.51784e-4 0.9999716 -0.007494091 8.44541e-4 0.999843 -0.01689869 0.005331456 1.65436e-6 0.9999997 8.40427e-4 8.21799e-6 0.9986377 -0.05218201 7.86546e-7 -0.8178263 0.5754651 -0.9995674 -0.02753049 0.01035207 0.9995658 -0.02756983 0.01039487 -1.084e-6 0.8089757 -0.5878422 8.66764e-7 -0.8600719 0.510173 -0.9976976 0.06468349 -0.02039015 0.9976975 0.06468278 -0.020392 3.79184e-7 0.8746722 -0.4847148 5.80418e-7 -0.9997603 -0.02189725 -0.9940679 0.1087547 0.001284778 0.9940679 0.1087543 0.001285314 -1.28089e-6 0.9998018 0.01991391 -1.9682e-6 -0.8773059 -0.4799316 -0.9941849 0.09341061 0.05358213 0.9941852 0.0935747 0.05328923 1.28989e-6 0.8753772 0.4834405 4.89306e-7 -0.5096998 -0.8603524 -0.9934269 0.05877554 0.09822762 0.9934267 0.05877286 0.09822934 0 0.5104705 0.8598953 3.411e-7 -0.02494466 -0.9996889 -0.9941967 0.007024705 0.1073487 0.9941966 0.007025897 0.1073498 -1.05547e-6 0.0328384 0.9994607 1.02419e-6 0.3839694 -0.9233458 -0.9944625 -0.04200917 0.09633105 0.9944624 -0.04200619 0.09633308 -6.41382e-7 -0.3869713 0.9220918 -8.47829e-7 0.7354966 -0.6775285 -0.9922265 -0.09617823 0.07897096 0.9922265 -0.09617757 0.07897204 -1.1595e-6 -0.7441658 0.667995 2.30039e-6 0.9844472 -0.1756811 -0.9925724 -0.118732 0.02651238 0.9925723 -0.1187326 0.02651 -4.24048e-7 -0.9827599 0.1848865 2.87048e-6 0.9698519 0.2436955 -0.994284 -0.104205 -0.0232526 0.9942841 -0.1042035 -0.02325564 -3.27106e-6 -0.9710991 -0.2386767 0 0.7932357 0.6089147 -0.9993349 -0.03347831 -0.01446384 0.9993349 -0.03347754 -0.01446366 2.66984e-7 -0.8034572 -0.5953626 -2.35431e-7 0.8618243 0.5072069 -0.9979473 0.0585342 0.02597945 0.9979475 0.05853176 0.02598202 8.48582e-7 -0.8537864 -0.5206236 -6.51402e-7 0.9778119 0.2094851 -0.9884114 0.1509797 0.01574707 0.9884021 0.1511143 0.0150237 -2.11364e-6 -0.9691148 -0.2466101 1.47035e-6 0.9111043 -0.412176 -0.9935294 0.1120536 -0.01853609 0.9935298 0.1120491 -0.01853984 1.71475e-5 -0.8811277 0.4728786 -5.15467e-7 0.8745182 -0.4849926 -0.9994385 -0.03303784 0.005601227 0.9994387 -0.03303313 0.005606412 -1.75198e-6 -0.884548 0.4664494 2.01011e-6 0.999824 0.01876127 -0.9971166 -0.07571321 0.005111932 0.9971166 -0.07571339 0.005107641 1.62582e-6 -0.9999799 -0.006358385 0 0.9292331 0.3694941 -0.9999678 -0.008019208 -2.70635e-4 0.9999679 -0.008017957 -2.72206e-4 1.46172e-6 -0.9311491 -0.3646388 -9.22355e-7 0.9978953 0.06484675 -0.9962772 0.08619904 -0.001263737 0.9962771 0.08619982 -0.001266241 -3.9758e-7 -0.9967609 -0.08042371 2.71317e-7 0.9184439 -0.3955516 -0.997423 0.06919395 -0.0189678 0.9974229 0.06919461 -0.01896613 -6.53614e-7 -0.9098791 0.4148736 -4.43853e-7 0.8611109 -0.5084173 -0.9991766 0.03535252 -0.01991057 0.9991766 0.03535282 -0.01991021 -5.17858e-7 -0.8602349 0.5098981 6.82121e-7 -0.5735777 -0.8191514</float_array>
          <technique_common>
            <accessor source="#tow_reciever_a-mesh-normals-array" count="246" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_reciever_a-mesh-map-0">
          <float_array id="tow_reciever_a-mesh-map-0-array" count="1496">0.397257 0.4776004 0.3389247 0.4776004 0.3389247 0.3448827 0.397257 0.3448827 0.2749298 0.3448829 0.3332623 0.3448827 0.3332623 0.4776004 0.2749298 0.4776005 0.4612519 0.4776004 0.4029194 0.4776004 0.4029194 0.3448827 0.4612519 0.3448827 0.2692674 0.4776005 0.2692674 0.3448829 0.2749298 0.3448829 0.2749298 0.4776005 0.3389247 0.3448827 0.3389247 0.4776004 0.3332623 0.4776004 0.3332623 0.3448827 0.4669142 0.3448827 0.4669142 0.4776004 0.4612519 0.4776004 0.4612519 0.3448827 0.397257 0.4776004 0.397257 0.3448827 0.4029194 0.3448827 0.4029194 0.4776004 0.5252467 0.4776004 0.4669142 0.4776004 0.4669142 0.3448827 0.5252467 0.3448827 0.7154162 0.5832189 0.7230287 0.5756679 0.7661362 0.5756679 0.7737487 0.5832189 0.7737487 0.516879 0.7661362 0.5244303 0.7230287 0.5244303 0.7154162 0.516879 0.7777526 0.5792151 0.7702015 0.5716029 0.7702015 0.5284951 0.7777526 0.520883 0.2379744 0.6316979 0.2379744 0.488879 0.2810821 0.488879 0.2810821 0.6316979 0.7661362 0.5244303 0.7737487 0.516879 0.7777526 0.520883 0.7702015 0.5284951 0.2810821 0.6316979 0.2810821 0.488879 0.2868308 0.488879 0.2868309 0.6316979 0.09657627 0.6184141 0.1005802 0.6224176 0.09863448 0.6276159 0.09365373 0.625341 0.09137868 0.6203598 0.7189636 0.5284951 0.7114124 0.520883 0.7154162 0.516879 0.7230287 0.5244303 0.7114124 0.520883 0.7189636 0.5284951 0.7189636 0.5716029 0.7114124 0.5792151 0.7230287 0.5756679 0.7154162 0.5832189 0.7114124 0.5792151 0.7189636 0.5716029 0.7702015 0.5716029 0.7777526 0.5792151 0.7737487 0.5832189 0.7661362 0.5756679 0.2868309 0.6316979 0.2868308 0.488879 0.3299386 0.488879 0.3299386 0.6316974 0.3356873 0.6316974 0.3299386 0.6316974 0.3299386 0.488879 0.3356873 0.488879 0.1005802 0.6224176 0.1589126 0.6224176 0.1608585 0.6276159 0.09863448 0.6276159 0.2379744 0.6316979 0.2322257 0.6316979 0.2322257 0.488879 0.2379744 0.488879 0.3356873 0.6316974 0.3356873 0.488879 0.378795 0.488879 0.378795 0.6316974 0.189118 0.6316979 0.189118 0.488879 0.2322257 0.488879 0.2322257 0.6316979 0.06480604 0.6148713 0.074364 0.6276208 0.01213991 0.627621 0.02169835 0.6148715 0.01763343 0.5676994 0.004884243 0.5581411 0.007159531 0.55316 0.01213991 0.5508856 0.02169835 0.5636346 0.02169835 0.5636346 0.01213991 0.5508856 0.074364 0.5508854 0.06480604 0.5636345 0.02169835 0.6148715 0.01213991 0.627621 0.007159531 0.6253451 0.004884243 0.6203644 0.01763349 0.6108067 0.01763349 0.6108067 0.004884243 0.6203644 0.004884243 0.5581411 0.01763343 0.5676994 0.06887102 0.5676993 0.08161979 0.5581409 0.08161979 0.6203643 0.06887102 0.6108065 0.06887102 0.6108065 0.08161979 0.6203643 0.07934474 0.6253449 0.074364 0.6276208 0.06480604 0.6148713 0.1589126 0.6224176 0.1629165 0.6184141 0.1681144 0.6203598 0.1658391 0.625341 0.1608585 0.6276159 0.09657627 0.5600815 0.09657627 0.6184141 0.09137868 0.6203598 0.09137868 0.5581353 0.08352965 0.9188859 0.08900541 0.9188859 0.08900541 0.9289863 0.08352965 0.9289863 0.08900541 0.9188859 0.1512295 0.918886 0.1512295 0.9289864 0.08900541 0.9289863 0.1567052 0.918886 0.1621807 0.918886 0.1621808 0.9289864 0.1567052 0.9289864 0.01582998 0.918886 0.07805413 0.9188859 0.07805413 0.9289863 0.01582998 0.9289864 0.1621807 0.918886 0.2244049 0.918886 0.2244049 0.9289864 0.1621808 0.9289864 0.01035422 0.918886 0.01582998 0.918886 0.01582998 0.9289864 0.01035434 0.9289864 0.2353562 0.918886 0.2975803 0.9188855 0.2975804 0.9289864 0.2353562 0.9289864 0.2298806 0.918886 0.2353562 0.918886 0.2353562 0.9289864 0.2298806 0.9289864 0.1629165 0.6184141 0.1629165 0.5600815 0.1681141 0.5581353 0.1681144 0.6203598 0.1005802 0.5560775 0.09657627 0.5600815 0.09137868 0.5581353 0.09365373 0.5531547 0.09863436 0.5508792 0.1589126 0.5560775 0.1005802 0.5560775 0.09863436 0.5508792 0.1608585 0.5508792 0.1629165 0.5600815 0.1589126 0.5560775 0.1608585 0.5508792 0.1658391 0.5531547 0.1681141 0.5581353 0.06480604 0.5636345 0.074364 0.5508854 0.07934474 0.5531598 0.08161979 0.5581409 0.06887102 0.5676993 0.1512295 0.918886 0.1567052 0.918886 0.1567052 0.9289864 0.1512295 0.9289864 0.07805413 0.9188859 0.08352965 0.9188859 0.08352965 0.9289863 0.07805413 0.9289863 0.004878699 0.918886 0.01035422 0.918886 0.01035434 0.9289864 0.004878699 0.9289864 0.2244049 0.918886 0.2298806 0.918886 0.2298806 0.9289864 0.2244049 0.9289864 0.1833692 0.6316979 0.1833692 0.488879 0.189118 0.488879 0.189118 0.6316979 0.5456273 0.6253427 0.5560899 0.6296766 0.549961 0.6296766 0.549961 0.6148797 0.5604239 0.6192138 0.5604239 0.6253427 0.5456273 0.6192138 0.5560899 0.6148797 0.5604239 0.6192138 0.549961 0.6148797 0.9891931 0.6613423 0.9848592 0.6508801 0.9891931 0.6552138 0.9787303 0.665676 0.9743966 0.6552138 0.9787303 0.6508801 0.9848592 0.665676 0.5456273 0.6192138 0.5604239 0.6253427 0.5560899 0.6296766 0.5456273 0.6253427 0.9787303 0.665676 0.9743966 0.6613423 0.9743966 0.6552138 0.9680451 0.3448867 0.9680451 0.4331985 0.962077 0.4331985 0.962077 0.3448867 0.9740127 0.3448867 0.9740127 0.4331985 0.9680451 0.4331985 0.9680451 0.3448867 0.9799814 0.3448867 0.9799814 0.4331985 0.9740127 0.4331985 0.9740127 0.3448867 0.9859491 0.3448867 0.9859491 0.4331985 0.9799814 0.4331985 0.9799814 0.3448867 0.9919167 0.3448867 0.9919167 0.4331985 0.9859491 0.4331985 0.9859491 0.3448867 0.9501411 0.3448867 0.9501411 0.4331985 0.9441725 0.4331985 0.9441725 0.3448867 0.9561087 0.3448867 0.9561087 0.4331985 0.9501411 0.4331985 0.9501411 0.3448867 0.962077 0.3448867 0.962077 0.4331985 0.9561087 0.4331985 0.9561087 0.3448867 0.9680451 0.4331985 0.9680451 0.4431849 0.962077 0.4431849 0.962077 0.4331985 0.9740127 0.4331985 0.9740127 0.4431849 0.9680451 0.4431849 0.9680451 0.4331985 0.9799814 0.4331985 0.9799814 0.4431849 0.9740127 0.4431849 0.9740127 0.4331985 0.9859491 0.4331985 0.9859491 0.4431849 0.9799814 0.4431849 0.9799814 0.4331985 0.9919167 0.4331985 0.9919167 0.4431849 0.9859491 0.4431849 0.9859491 0.4331985 0.9501411 0.4331985 0.9501411 0.4431849 0.9441725 0.4431849 0.9441725 0.4331985 0.9561087 0.4331985 0.9561087 0.4431849 0.9501411 0.4431849 0.9501411 0.4331985 0.962077 0.4331985 0.962077 0.4431849 0.9561087 0.4431849 0.9561087 0.4331985 0.9680451 0.4431849 0.9680451 0.4506052 0.962077 0.4506052 0.962077 0.4431849 0.9740127 0.4431849 0.9740127 0.4506052 0.9680451 0.4506052 0.9680451 0.4431849 0.9799814 0.4431849 0.9799814 0.4506052 0.9740127 0.4506052 0.9740127 0.4431849 0.9859491 0.4431849 0.9859491 0.4506052 0.9799814 0.4506052 0.9799814 0.4431849 0.9919167 0.4431849 0.9919167 0.4506052 0.9859491 0.4506052 0.9859491 0.4431849 0.9501411 0.4431849 0.9501411 0.4506052 0.9441725 0.4506052 0.9441725 0.4431849 0.9561087 0.4431849 0.9561087 0.4506052 0.9501411 0.4506052 0.9501411 0.4431849 0.962077 0.4431849 0.962077 0.4506052 0.9561087 0.4506052 0.9561087 0.4431849 0.9680451 0.4506052 0.9680451 0.4611697 0.962077 0.4611697 0.962077 0.4506052 0.9740127 0.4506052 0.9740127 0.4611697 0.9680451 0.4611697 0.9680451 0.4506052 0.9799814 0.4506052 0.9799814 0.4611697 0.9740127 0.4611697 0.9740127 0.4506052 0.9859491 0.4506052 0.9859491 0.4611697 0.9799814 0.4611697 0.9799814 0.4506052 0.9919167 0.4506052 0.9919167 0.4611697 0.9859491 0.4611697 0.9859491 0.4506052 0.9501411 0.4506052 0.9501411 0.4611697 0.9441725 0.4611697 0.9441725 0.4506052 0.9561087 0.4506052 0.9561087 0.4611697 0.9501411 0.4611697 0.9501411 0.4506052 0.962077 0.4506052 0.962077 0.4611697 0.9561087 0.4611697 0.9561087 0.4506052 0.9680451 0.4611697 0.9680451 0.4802401 0.962077 0.4802401 0.962077 0.4611697 0.9740127 0.4611697 0.9740127 0.4802401 0.9680451 0.4802401 0.9680451 0.4611697 0.9799814 0.4611697 0.9799814 0.4802401 0.9740127 0.4802401 0.9740127 0.4611697 0.9859491 0.4611697 0.9859491 0.4802401 0.9799814 0.4802401 0.9799814 0.4611697 0.9919167 0.4611697 0.9919167 0.4802401 0.9859491 0.4802401 0.9859491 0.4611697 0.9501411 0.4611697 0.9501411 0.4802401 0.9441725 0.4802401 0.9441725 0.4611697 0.9561087 0.4611697 0.9561087 0.4802401 0.9501411 0.4802401 0.9501411 0.4611697 0.962077 0.4611697 0.962077 0.4802401 0.9561087 0.4802401 0.9561087 0.4611697 0.9848592 0.665676 0.9787303 0.6508801 0.9848592 0.6508801 0.9891931 0.6613423 0.2539069 0.3448848 0.2539069 0.347716 0.2510758 0.347716 0.2510758 0.3448848 0.1310737 0.9588853 0.1639157 0.9588851 0.1639878 0.9621087 0.1311113 0.9621089 0.1311113 0.9681732 0.1639878 0.968173 0.1639157 0.9713976 0.1310737 0.9713978 0.1311255 0.9651418 0.1640332 0.9651417 0.1639878 0.968173 0.1311113 0.9681732 0.1311113 0.9621089 0.1639878 0.9621087 0.1640332 0.9651417 0.1311255 0.9651418 0.1639157 0.9588851 0.1688311 0.9588851 0.1688311 0.9621087 0.1639878 0.9621087 0.1639878 0.968173 0.1688311 0.968173 0.1688311 0.9713976 0.1639157 0.9713976 0.1640332 0.9651417 0.1688311 0.9651417 0.1688311 0.968173 0.1639878 0.968173 0.1639878 0.9621087 0.1688311 0.9621087 0.1688311 0.9651417 0.1640332 0.9651417 0.1688311 0.9588851 0.1746547 0.9589205 0.1746547 0.9621087 0.1688311 0.9621087 0.1688311 0.968173 0.1746547 0.968173 0.1746547 0.9713618 0.1688311 0.9713976 0.1688311 0.9651417 0.1746547 0.9651417 0.1746547 0.968173 0.1688311 0.968173 0.1688311 0.9621087 0.1746547 0.9621087 0.1746547 0.9651417 0.1688311 0.9651417 0.1746547 0.9589205 0.1795691 0.9589205 0.1795691 0.9621087 0.1746547 0.9621087 0.1746547 0.968173 0.1795691 0.968173 0.1795691 0.9713618 0.1746547 0.9713618 0.1746547 0.9651417 0.1795691 0.9651417 0.1795691 0.968173 0.1746547 0.968173 0.1746547 0.9621087 0.1795691 0.9621087 0.1795691 0.9651417 0.1746547 0.9651417 0.1795691 0.9589205 0.185442 0.9589206 0.185442 0.9621089 0.1795691 0.9621087 0.1795691 0.968173 0.185442 0.9681732 0.185442 0.9713619 0.1795691 0.9713618 0.1795691 0.9651417 0.185442 0.9651418 0.185442 0.9681732 0.1795691 0.968173 0.1795691 0.9621087 0.185442 0.9621089 0.185442 0.9651418 0.1795691 0.9651417 0.185442 0.9589206 0.1907271 0.9589206 0.1907271 0.9621089 0.185442 0.9621089 0.185442 0.9681732 0.1907271 0.9681732 0.1907271 0.9713619 0.185442 0.9713619 0.185442 0.9651418 0.1907271 0.9651418 0.1907271 0.9681732 0.185442 0.9681732 0.185442 0.9621089 0.1907271 0.9621089 0.1907271 0.9651418 0.185442 0.9651418 0.1907271 0.9589206 0.1957836 0.9589206 0.1957836 0.9621089 0.1907271 0.9621089 0.1907271 0.9681732 0.1957836 0.9681732 0.1957836 0.9713619 0.1907271 0.9713619 0.1907271 0.9651418 0.1957836 0.9651418 0.1957836 0.9681732 0.1907271 0.9681732 0.1907271 0.9621089 0.1957836 0.9621089 0.1957836 0.9651418 0.1907271 0.9651418 0.1957836 0.9589206 0.2007522 0.9589206 0.2007522 0.9621089 0.1957836 0.9621089 0.1957836 0.9681732 0.2007522 0.9681732 0.2007522 0.9713619 0.1957836 0.9713619 0.1957836 0.9651418 0.2007522 0.9651418 0.2007522 0.9681732 0.1957836 0.9681732 0.1957836 0.9621089 0.2007522 0.9621089 0.2007522 0.9651418 0.1957836 0.9651418 0.2007522 0.9589206 0.2054696 0.9589205 0.2054696 0.9621087 0.2007522 0.9621089 0.2007522 0.9681732 0.2054696 0.968173 0.2054696 0.9713618 0.2007522 0.9713619 0.2007522 0.9651418 0.2054696 0.9651417 0.2054696 0.968173 0.2007522 0.9681732 0.2007522 0.9621089 0.2054696 0.9621087 0.2054696 0.9651417 0.2007522 0.9651418 0.2054696 0.9589205 0.2097443 0.9589205 0.2097443 0.9621087 0.2054696 0.9621087 0.2054696 0.968173 0.2097443 0.968173 0.2097443 0.9713618 0.2054696 0.9713618 0.2054696 0.9651417 0.2097443 0.9651417 0.2097443 0.968173 0.2054696 0.968173 0.2054696 0.9621087 0.2097443 0.9621087 0.2097443 0.9651417 0.2054696 0.9651417 0.2097443 0.9589205 0.2149977 0.9589205 0.2149977 0.9621087 0.2097443 0.9621087 0.2097443 0.968173 0.2149977 0.968173 0.2149977 0.9713618 0.2097443 0.9713618 0.2097443 0.9651417 0.2149977 0.9651417 0.2149977 0.968173 0.2097443 0.968173 0.2097443 0.9621087 0.2149977 0.9621087 0.2149977 0.9651417 0.2097443 0.9651417 0.2149977 0.9589205 0.2189754 0.9589205 0.2189754 0.9621087 0.2149977 0.9621087 0.2149977 0.968173 0.2189754 0.968173 0.2189754 0.9713618 0.2149977 0.9713618 0.2149977 0.9651417 0.2189754 0.9651417 0.2189754 0.968173 0.2149977 0.968173 0.2149977 0.9621087 0.2189754 0.9621087 0.2189754 0.9651417 0.2149977 0.9651417 0.2189754 0.9589205 0.2232233 0.9589205 0.2232233 0.9621087 0.2189754 0.9621087 0.2189754 0.968173 0.2232233 0.968173 0.2232233 0.9713618 0.2189754 0.9713618 0.2189754 0.9651417 0.2232233 0.9651417 0.2232233 0.968173 0.2189754 0.968173 0.2189754 0.9621087 0.2232233 0.9621087 0.2232233 0.9651417 0.2189754 0.9651417 0.2232233 0.9589205 0.227266 0.9589205 0.227266 0.9621087 0.2232233 0.9621087 0.2232233 0.968173 0.227266 0.968173 0.227266 0.9713618 0.2232233 0.9713618 0.2232233 0.9651417 0.227266 0.9651417 0.227266 0.968173 0.2232233 0.968173 0.2232233 0.9621087 0.227266 0.9621087 0.227266 0.9651417 0.2232233 0.9651417 0.227266 0.9589205 0.2297688 0.9589205 0.2297688 0.9621087 0.227266 0.9621087 0.227266 0.968173 0.2297688 0.968173 0.2297688 0.9713618 0.227266 0.9713618 0.227266 0.9651417 0.2297688 0.9651417 0.2297688 0.968173 0.227266 0.968173 0.227266 0.9621087 0.2297688 0.9621087 0.2297688 0.9651417 0.227266 0.9651417 0.2297688 0.9589205 0.2347464 0.9589205 0.2347464 0.9621087 0.2297688 0.9621087 0.2297688 0.968173 0.2347464 0.968173 0.2347464 0.9713618 0.2297688 0.9713618 0.2297688 0.9651417 0.2347464 0.9651417 0.2347464 0.968173 0.2297688 0.968173 0.2297688 0.9621087 0.2347464 0.9621087 0.2347464 0.9651417 0.2297688 0.9651417 0.2347464 0.9589205 0.2412898 0.95892 0.2412898 0.9621087 0.2347464 0.9621087 0.2347464 0.968173 0.2412898 0.968173 0.2412898 0.9713608 0.2347464 0.9713618 0.2347464 0.9651417 0.2412898 0.9651417 0.2412898 0.968173 0.2347464 0.968173 0.2347464 0.9621087 0.2412898 0.9621087 0.2412898 0.9651417 0.2347464 0.9651417 0.2412898 0.95892 0.2486898 0.95892 0.2486898 0.9621087 0.2412898 0.9621087 0.2412898 0.968173 0.2486898 0.968173 0.2486898 0.9713608 0.2412898 0.9713608 0.2412898 0.9651417 0.2486898 0.9651417 0.2486898 0.968173 0.2412898 0.968173 0.2412898 0.9621087 0.2486898 0.9621087 0.2486898 0.9651417 0.2412898 0.9651417 0.2486898 0.95892 0.2541277 0.95892 0.2541277 0.9621087 0.2486898 0.9621087 0.2486898 0.968173 0.2541277 0.968173 0.2541277 0.9713608 0.2486898 0.9713608 0.2486898 0.9651417 0.2541277 0.9651417 0.2541277 0.968173 0.2486898 0.968173 0.2486898 0.9621087 0.2541277 0.9621087 0.2541277 0.9651417 0.2486898 0.9651417 0.2541277 0.95892 0.2596721 0.95892 0.2596721 0.9621087 0.2541277 0.9621087 0.2541277 0.968173 0.2596721 0.968173 0.2596721 0.9713608 0.2541277 0.9713608 0.2541277 0.9651417 0.2596721 0.9651417 0.2596721 0.968173 0.2541277 0.968173 0.2541277 0.9621087 0.2596721 0.9621087 0.2596721 0.9651417 0.2541277 0.9651417 0.2596721 0.95892 0.2653632 0.95892 0.2653632 0.9621087 0.2596721 0.9621087 0.2596721 0.968173 0.2653632 0.968173 0.2653632 0.9713608 0.2596721 0.9713608 0.2596721 0.9651417 0.2653632 0.9651417 0.2653632 0.968173 0.2596721 0.968173 0.2596721 0.9621087 0.2653632 0.9621087 0.2653632 0.9651417 0.2596721 0.9651417 0.2527809 0.3617179 0.2527809 0.3588872 0.2556121 0.3588872 0.2556121 0.3617179</float_array>
          <technique_common>
            <accessor source="#tow_reciever_a-mesh-map-0-array" count="748" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_reciever_a-mesh-vertices">
          <input semantic="POSITION" source="#tow_reciever_a-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="186">
          <input semantic="VERTEX" source="#tow_reciever_a-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_reciever_a-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_reciever_a-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 5 4 4 4 4 4 4 4 4 4 4 4 5 4 5 4 4 5 5 4 4 4 4 4 4 4 4 4 4 5 4 5 5 4 4 4 4 4 3 4 3 3 4 4 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>14 0 0 7 1 1 2 2 2 11 3 3 0 4 4 3 5 5 6 6 6 5 7 7 12 8 8 15 9 9 10 10 10 9 11 11 4 12 12 1 13 13 0 4 14 5 7 15 2 2 16 7 1 17 6 6 18 3 5 19 8 14 20 13 15 21 12 8 22 9 11 23 14 0 24 11 3 25 10 10 26 15 9 27 4 12 28 13 15 29 8 14 30 1 13 31 2 16 32 18 17 33 23 18 34 11 19 35 8 20 36 20 21 37 17 22 38 1 23 39 10 24 40 22 25 41 21 26 42 9 26 43 18 27 44 27 28 45 30 29 46 23 30 47 20 21 48 8 20 49 9 26 50 21 26 51 23 30 52 30 29 53 31 31 54 22 32 55 6 33 56 7 34 57 34 35 58 50 36 59 35 37 60 16 38 61 0 39 62 1 23 63 17 22 64 0 39 65 16 38 66 19 26 67 3 26 68 18 17 69 2 16 70 3 26 71 19 26 72 22 25 73 10 24 74 11 19 75 23 18 76 22 32 77 31 31 78 28 40 79 21 41 80 20 42 81 21 41 82 28 40 83 29 43 84 7 34 85 14 44 86 39 45 87 34 35 88 18 27 89 19 46 90 26 47 91 27 28 92 20 42 93 29 43 94 24 48 95 17 49 96 16 50 97 25 51 98 26 47 99 19 46 100 27 52 101 40 53 102 41 53 103 30 52 104 28 54 105 47 54 106 55 54 107 46 55 108 29 56 109 29 56 110 46 55 111 45 55 112 24 56 113 30 52 114 41 53 115 49 54 116 42 54 117 31 54 118 31 54 119 42 54 120 47 54 121 28 54 122 25 54 123 43 54 124 44 54 125 26 54 126 26 54 127 44 54 128 51 54 129 40 53 130 27 52 131 14 44 132 15 57 133 38 58 134 48 59 135 39 45 136 5 60 137 6 33 138 35 37 139 32 61 140 50 62 141 34 63 142 40 64 143 51 65 144 34 63 145 39 66 146 41 67 147 40 64 148 48 68 149 38 69 150 42 70 151 49 68 152 32 71 153 35 72 154 44 73 155 43 74 156 38 69 157 37 75 158 47 76 159 42 70 160 52 77 161 32 71 162 43 74 163 53 77 164 36 78 165 33 79 166 45 80 167 46 81 168 54 82 169 36 78 170 46 81 171 55 83 172 15 57 173 12 84 174 37 85 175 38 58 176 4 86 177 5 60 178 32 61 179 52 87 180 33 88 181 13 89 182 4 86 183 33 88 184 36 90 185 12 84 186 13 89 187 36 90 188 54 91 189 37 85 190 24 56 191 45 55 192 53 54 193 43 54 194 25 54 195 39 66 196 48 68 197 49 68 198 41 67 199 35 72 200 50 62 201 51 65 202 44 73 203 33 79 204 52 77 205 53 77 206 45 80 207 37 75 208 54 82 209 55 83 210 47 76 211 17 49 212 24 48 213 25 51 214 16 50 215 102 92 216 101 93 217 57 94 218 98 95 219 100 96 220 56 97 221 103 98 222 99 99 223 100 96 224 98 95 225 63 100 226 60 101 227 64 102 228 61 103 229 58 104 230 65 105 231 62 106 232 103 98 233 56 97 234 101 93 235 102 92 236 61 103 237 59 107 238 58 104 239 58 108 240 66 109 241 73 110 242 65 111 243 59 112 244 67 113 245 66 109 246 58 108 247 61 114 248 68 115 249 67 113 250 59 112 251 62 116 252 69 117 253 68 115 254 61 114 255 63 118 256 70 119 257 69 117 258 62 116 259 64 120 260 71 121 261 70 119 262 63 118 263 60 122 264 72 123 265 71 121 266 64 120 267 65 111 268 73 110 269 72 123 270 60 122 271 66 109 272 74 124 273 81 125 274 73 110 275 67 113 276 75 126 277 74 124 278 66 109 279 68 115 280 76 127 281 75 126 282 67 113 283 69 117 284 77 128 285 76 127 286 68 115 287 70 119 288 78 129 289 77 128 290 69 117 291 71 121 292 79 130 293 78 129 294 70 119 295 72 123 296 80 131 297 79 130 298 71 121 299 73 110 300 81 125 301 80 131 302 72 123 303 74 124 304 82 132 305 89 133 306 81 125 307 75 126 308 83 134 309 82 132 310 74 124 311 76 127 312 84 135 313 83 134 314 75 126 315 77 128 316 85 136 317 84 135 318 76 127 319 78 129 320 86 137 321 85 136 322 77 128 323 79 130 324 87 138 325 86 137 326 78 129 327 80 131 328 88 139 329 87 138 330 79 130 331 81 125 332 89 133 333 88 139 334 80 131 335 82 132 336 90 140 337 97 141 338 89 133 339 83 134 340 91 142 341 90 140 342 82 132 343 84 135 344 92 143 345 91 142 346 83 134 347 85 136 348 93 144 349 92 143 350 84 135 351 86 137 352 94 145 353 93 144 354 85 136 355 87 138 356 95 146 357 94 145 358 86 137 359 88 139 360 96 147 361 95 146 362 87 138 363 89 133 364 97 141 365 96 147 366 88 139 367 90 140 368 98 148 369 103 149 370 97 141 371 91 142 372 99 150 373 98 148 374 90 140 375 92 143 376 100 151 377 99 150 378 91 142 379 93 144 380 56 152 381 100 151 382 92 143 383 94 145 384 101 153 385 56 152 386 93 144 387 95 146 388 57 154 389 101 153 390 94 145 391 96 147 392 102 155 393 57 154 394 95 146 395 97 141 396 103 149 397 102 155 398 96 147 399 62 106 400 65 105 401 60 101 402 63 100 403 107 156 404 110 156 405 109 156 406 108 156 407 107 157 408 111 158 409 114 159 410 110 160 411 108 161 412 112 162 413 111 158 414 107 157 415 109 163 416 113 164 417 112 162 418 108 161 419 110 160 420 114 159 421 113 164 422 109 163 423 111 158 424 115 165 425 118 166 426 114 159 427 112 162 428 116 167 429 115 165 430 111 158 431 113 164 432 117 168 433 116 167 434 112 162 435 114 159 436 118 166 437 117 168 438 113 164 439 115 165 440 119 169 441 122 170 442 118 166 443 116 167 444 120 171 445 119 169 446 115 165 447 117 168 448 121 172 449 120 171 450 116 167 451 118 166 452 122 170 453 121 172 454 117 168 455 119 169 456 123 173 457 126 174 458 122 170 459 120 171 460 124 175 461 123 173 462 119 169 463 121 172 464 125 176 465 124 175 466 120 171 467 122 170 468 126 174 469 125 176 470 121 172 471 123 173 472 127 177 473 130 178 474 126 174 475 124 175 476 128 179 477 127 177 478 123 173 479 125 176 480 129 180 481 128 179 482 124 175 483 126 174 484 130 178 485 129 180 486 125 176 487 127 177 488 131 181 489 134 182 490 130 178 491 128 179 492 132 183 493 131 181 494 127 177 495 129 180 496 133 184 497 132 183 498 128 179 499 130 178 500 134 182 501 133 184 502 129 180 503 131 181 504 135 185 505 138 186 506 134 182 507 132 183 508 136 187 509 135 185 510 131 181 511 133 184 512 137 188 513 136 187 514 132 183 515 134 182 516 138 186 517 137 188 518 133 184 519 135 185 520 139 189 521 142 190 522 138 186 523 136 187 524 140 191 525 139 189 526 135 185 527 137 188 528 141 192 529 140 191 530 136 187 531 138 186 532 142 190 533 141 192 534 137 188 535 139 189 536 143 193 537 146 194 538 142 190 539 140 191 540 144 195 541 143 193 542 139 189 543 141 192 544 145 196 545 144 195 546 140 191 547 142 190 548 146 194 549 145 196 550 141 192 551 143 193 552 147 197 553 150 198 554 146 194 555 144 195 556 148 199 557 147 197 558 143 193 559 145 196 560 149 200 561 148 199 562 144 195 563 146 194 564 150 198 565 149 200 566 145 196 567 147 197 568 151 201 569 154 202 570 150 198 571 148 199 572 152 203 573 151 201 574 147 197 575 149 200 576 153 204 577 152 203 578 148 199 579 150 198 580 154 202 581 153 204 582 149 200 583 151 201 584 155 205 585 158 206 586 154 202 587 152 203 588 156 207 589 155 205 590 151 201 591 153 204 592 157 208 593 156 207 594 152 203 595 154 202 596 158 206 597 157 208 598 153 204 599 155 205 600 159 209 601 162 210 602 158 206 603 156 207 604 160 211 605 159 209 606 155 205 607 157 208 608 161 212 609 160 211 610 156 207 611 158 206 612 162 210 613 161 212 614 157 208 615 159 209 616 163 213 617 166 214 618 162 210 619 160 211 620 164 215 621 163 213 622 159 209 623 161 212 624 165 216 625 164 215 626 160 211 627 162 210 628 166 214 629 165 216 630 161 212 631 163 213 632 167 217 633 170 218 634 166 214 635 164 215 636 168 219 637 167 217 638 163 213 639 165 216 640 169 220 641 168 219 642 164 215 643 166 214 644 170 218 645 169 220 646 165 216 647 167 217 648 171 221 649 174 222 650 170 218 651 168 219 652 172 223 653 171 221 654 167 217 655 169 220 656 173 224 657 172 223 658 168 219 659 170 218 660 174 222 661 173 224 662 169 220 663 171 221 664 175 225 665 178 226 666 174 222 667 172 223 668 176 227 669 175 225 670 171 221 671 173 224 672 177 228 673 176 227 674 172 223 675 174 222 676 178 226 677 177 228 678 173 224 679 175 225 680 179 229 681 182 230 682 178 226 683 176 227 684 180 231 685 179 229 686 175 225 687 177 228 688 181 232 689 180 231 690 176 227 691 178 226 692 182 230 693 181 232 694 177 228 695 179 229 696 183 233 697 186 234 698 182 230 699 180 231 700 184 235 701 183 233 702 179 229 703 181 232 704 185 236 705 184 235 706 180 231 707 182 230 708 186 234 709 185 236 710 181 232 711 183 233 712 187 237 713 190 238 714 186 234 715 184 235 716 188 239 717 187 237 718 183 233 719 185 236 720 189 240 721 188 239 722 184 235 723 186 234 724 190 238 725 189 240 726 185 236 727 187 237 728 191 241 729 106 242 730 190 238 731 188 239 732 104 243 733 191 241 734 187 237 735 189 240 736 105 244 737 104 243 738 188 239 739 190 238 740 106 242 741 105 244 742 189 240 743 106 245 744 191 245 745 104 245 746 105 245 747</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_mount_vt_L-mesh" name="tow_mount_vt_L">
      <mesh>
        <source id="tow_mount_vt_L-mesh-positions">
          <float_array id="tow_mount_vt_L-mesh-positions-array" count="36">0.2715895 -0.003957211 -0.03649359 0.1970691 -0.003957211 -0.03649359 0.312583 -0.003957211 0.1444737 0.1970691 -0.003957211 0.1444737 0.312583 -0.003957211 0.04570949 0.1970691 -0.003957211 0.04570949 0.2715895 0.006042718 -0.03649359 0.1970691 0.006042718 -0.03649359 0.312583 0.006042718 0.1444737 0.1970691 0.006042718 0.1444737 0.312583 0.006042718 0.04570949 0.1970691 0.006042718 0.04570949</float_array>
          <technique_common>
            <accessor source="#tow_mount_vt_L-mesh-positions-array" count="12" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_vt_L-mesh-normals">
          <float_array id="tow_mount_vt_L-mesh-normals-array" count="45">0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 1 0 0.8948974 0 -0.446272 0.9733698 0 -0.2292407 0.9733698 0 -0.2292407 0 0 -1 -1 0 0 0 0 1 1 0 0</float_array>
          <technique_common>
            <accessor source="#tow_mount_vt_L-mesh-normals-array" count="15" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_vt_L-mesh-map-0">
          <float_array id="tow_mount_vt_L-mesh-map-0-array" count="80">0.4874194 0.4888801 0.3885585 0.48888 0.3885585 0.6045073 0.4874194 0.6045074 0.5697035 0.5299133 0.4874194 0.4888801 0.4874194 0.6045074 0.5697035 0.6045063 0.4370017 0.614885 0.4370015 0.7305121 0.5358626 0.7305122 0.5358626 0.614885 0.3547179 0.6559183 0.3547178 0.7305123 0.4370015 0.7305121 0.4370017 0.614885 0.3192373 0.9488916 0.3192373 0.9388816 0.2272897 0.9388818 0.2272897 0.9488917 0.9075149 0.9508908 0.9075149 0.9408813 0.8329215 0.9408813 0.8329215 0.9508908 0.6698806 0.9508895 0.6698805 0.94088 0.5710195 0.94088 0.5710195 0.9508895 0.8008914 0.8908815 0.8008914 0.9008919 0.9165188 0.9008918 0.9165188 0.8908814 0.2272897 0.9488917 0.2272897 0.9388818 0.1284286 0.9388818 0.1284286 0.9488917 0.5710195 0.9508895 0.5710195 0.94088 0.4887359 0.94088 0.4887359 0.9508895</float_array>
          <technique_common>
            <accessor source="#tow_mount_vt_L-mesh-map-0-array" count="40" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_mount_vt_L-mesh-vertices">
          <input semantic="POSITION" source="#tow_mount_vt_L-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="10">
          <input semantic="VERTEX" source="#tow_mount_vt_L-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_mount_vt_L-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_mount_vt_L-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>4 0 0 2 1 1 3 1 2 5 2 3 0 3 4 4 0 5 5 2 6 1 3 7 10 4 8 11 5 9 9 6 10 8 6 11 6 7 12 7 7 13 11 5 14 10 4 15 0 8 16 6 8 17 10 9 18 4 10 19 1 11 20 7 11 21 6 11 22 0 11 23 3 12 24 9 12 25 11 12 26 5 12 27 2 13 28 8 13 29 9 13 30 3 13 31 4 10 32 10 9 33 8 14 34 2 14 35 5 12 36 11 12 37 7 12 38 1 12 39</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_mount_hz_L-mesh" name="tow_mount_hz_L">
      <mesh>
        <source id="tow_mount_hz_L-mesh-positions">
          <float_array id="tow_mount_hz_L-mesh-positions-array" count="156">0.4028258 -0.08484792 -0.02980327 0.4320057 -0.07184338 0.05404877 0.4028258 -0.03136479 -0.02980327 0.435582 -0.06564909 0.05404877 0.4028258 -0.160508 0.03100156 0.4427345 -0.06564909 0.05404877 0.4028258 -0.03136479 0.03100156 0.4463109 -0.07184338 0.05404877 0.4028258 0.03262549 -0.02980327 0.4427345 -0.07803773 0.05404877 0.4028258 0.03262549 0.03100156 0.435582 -0.07803773 0.05404877 0.4641398 -0.1659853 0.06812256 0.435582 -0.06564909 0.06003487 0.4641398 -0.0439254 0.06812256 0.4427345 -0.06564909 0.06003487 0.4028258 -0.1659853 0.05909371 0.4463109 -0.07184338 0.06003487 0.4427345 -0.07803773 0.06003487 0.4118547 -0.1659853 0.06812256 0.4118546 -0.0439254 0.06812256 0.435582 -0.07803773 0.06003487 0.4028258 -0.0439254 0.05909371 0.4320057 -0.07184338 0.06003487 0.4028258 -0.1659853 0.04207676 0.4320057 -0.1313089 0.05404877 0.4028258 -0.04392546 0.04207676 0.435582 -0.1251146 0.05404877 0.4427345 -0.1251146 0.05404877 0.4108257 -0.08484792 -0.02980327 0.4108257 -0.03136479 -0.02980327 0.4463109 -0.1313089 0.05404877 0.4108257 -0.160508 0.03100156 0.4427345 -0.1375032 0.05404877 0.4108257 -0.03136479 0.03100156 0.435582 -0.1375032 0.05404877 0.435582 -0.1251146 0.06003487 0.4108257 0.03262549 -0.02980327 0.4427345 -0.1251146 0.06003487 0.4108257 0.03262549 0.03100156 0.4463109 -0.1313089 0.06003487 0.4641398 -0.1659853 0.0601226 0.4427345 -0.1375032 0.06003487 0.4641398 -0.0439254 0.0601226 0.4108258 -0.1659853 0.05577999 0.435582 -0.1375032 0.06003487 0.4151684 -0.1659853 0.0601226 0.4320057 -0.1313089 0.06003487 0.4151684 -0.0439254 0.0601226 0.4108257 -0.0439254 0.05577999 0.4108257 -0.1659853 0.04207676 0.4108257 -0.04392546 0.04207676</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_L-mesh-positions-array" count="52" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_hz_L-mesh-normals">
          <float_array id="tow_mount_hz_L-mesh-normals-array" count="186">-1 0 0 -1 0 5.12807e-7 -0.9448387 2.26029e-5 0.327536 -0.9448387 -1.6768e-7 0.3275362 -1 0 3.4476e-7 -0.1458103 -9.34776e-5 0.9893127 -0.1458101 0 0.9893126 0 0 1 1 0 0 1 1.39165e-7 -6.36814e-7 1 0 -4.2813e-7 0.9722282 1.64184e-7 -0.2340351 0.9722282 7.48913e-5 -0.2340348 0.08125555 1.17473e-4 -0.9966933 0.08125519 0 -0.9966934 0 0 -1 0 -0.6264328 -0.7794754 0 -0.6631888 -0.7484522 0 -0.6631888 -0.7484522 0 1 0 0 0.14328 0.9896823 -7.44923e-5 0.14328 0.9896823 1 2.38439e-7 0 0 1 0 0 1 0 -3.0138e-7 1 -1.61346e-6 -3.01379e-7 1 -1.61346e-6 0 -1 0 0 1 2.1557e-7 0 -0.9791966 -0.2029141 8.73317e-6 -0.9791966 -0.2029144 7.1241e-6 0.9032391 0.4291377 -3.27704e-7 0.9032391 0.4291377 0.8660229 -0.5000044 0 0.8660227 0.5000047 0 0.8660227 0.500005 0 0.8660227 0.5000048 0 -0.866025 0.5000008 0 -0.8660232 0.5000039 0 -0.8660255 0.5 0 -0.8660252 -0.5000005 0 -0.8660229 -0.5000044 0 -0.8660257 -0.4999995 0 3.28447e-7 3.00701e-7 -1 3.28446e-7 0 -1 1.31379e-6 3.00702e-7 -1 0.8660229 0.5000044 0 -0.8660253 0.5000002 0 -0.8660236 0.5000033 0 -0.8660258 0.4999994 0 0 3.00701e-7 -1 6.56891e-7 0 -1 0 3.00702e-7 -1 -0.8660272 0.4999968 0 -0.866028 -0.4999957 0 -6.56888e-7 0 -1 1.31378e-6 -3.00706e-7 -1 -6.5689e-7 2.25527e-7 -1 -0.8660276 0.4999962 0 1.31378e-6 0 -1 1.31378e-6 -3.00706e-7 -1 6.56889e-7 2.25527e-7 -1</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_L-mesh-normals-array" count="62" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_hz_L-mesh-map-0">
          <float_array id="tow_mount_hz_L-mesh-map-0-array" count="400">0.8759952 0.5068858 0.8002611 0.5677496 0.9295308 0.5677496 0.9295307 0.5068858 0.9295308 0.5677496 0.9935839 0.5677496 0.9935839 0.5068858 0.9295307 0.5068858 0.7947784 0.5788364 0.7947784 0.5958704 0.916958 0.5958702 0.916958 0.5788362 0.916958 0.6086516 0.916958 0.5958702 0.7947784 0.5958704 0.7947784 0.6086518 0.7947784 0.6086518 0.7947785 0.6609876 0.916958 0.660987 0.916958 0.6086516 0.8002611 0.5677496 0.7947784 0.5788364 0.916958 0.5788362 0.9295308 0.5677496 0.6606852 0.6817209 0.7142207 0.6817209 0.7142207 0.6208565 0.5849509 0.6208565 0.7142207 0.6208565 0.7142207 0.6817209 0.7782738 0.6817209 0.7782738 0.6208565 0.5794683 0.6097702 0.7016477 0.6097702 0.7016478 0.5960536 0.5794683 0.5960536 0.7016478 0.5899061 0.5794684 0.5899061 0.5794683 0.5960536 0.7016478 0.5960536 0.5794684 0.5899061 0.7016478 0.5899061 0.7016479 0.5408869 0.5794684 0.5408869 0.5849509 0.6208565 0.7142207 0.6208565 0.7016477 0.6097702 0.5794683 0.6097702 0.5634347 0.8044806 0.5714426 0.8044806 0.5714427 0.7073202 0.5634348 0.7073202 0.3286633 0.9748931 0.3286633 0.9668852 0.2751278 0.9668852 0.2751278 0.9748931 0.7417697 0.9848848 0.7417697 0.9928923 0.8026342 0.9928923 0.8026342 0.9848848 0.8008952 0.8369411 0.8089022 0.8369411 0.8089022 0.7728881 0.8008952 0.7728881 0.3927164 0.974893 0.3927164 0.966885 0.3286633 0.9668852 0.3286633 0.9748931 0.9102249 0.6788898 0.9102249 0.6708821 0.7880452 0.6708823 0.7880452 0.67889 0.8099332 0.8797749 0.8132499 0.8717671 0.8089023 0.8674201 0.8008953 0.8707372 0.5634348 0.6779187 0.5714427 0.6812359 0.5757901 0.6768888 0.5724728 0.6688809 0.5724728 0.6688809 0.5757901 0.6768888 0.6248091 0.676889 0.6248091 0.6688811 0.8622681 0.8797749 0.8622681 0.8717671 0.8132499 0.8717671 0.8099332 0.8797749 0.5634348 0.7073202 0.5714427 0.7073202 0.5714427 0.6949524 0.5634348 0.6949524 0.8008953 0.8707372 0.8089023 0.8674201 0.8089023 0.8537035 0.8008953 0.8537035 0.5634348 0.6949524 0.5714427 0.6949524 0.5714427 0.6812359 0.5634348 0.6779187 0.8008953 0.8537035 0.8089023 0.8537035 0.8089022 0.8369411 0.8008952 0.8369411 0.9874209 0.7278694 0.9874209 0.7278694 0.9874209 0.7188813 0.04790395 0.6038709 0.05864334 0.6038709 0.05864334 0.5948836 0.03814023 0.6038752 0.03814023 0.6038752 0.03814023 0.5948878 0.9949766 0.5878716 0.9949766 0.5878716 0.9949766 0.5788835 0.9859873 0.707621 0.9949753 0.707621 0.9949753 0.6968813 0.9669165 0.7168877 0.9579277 0.7168877 0.9579277 0.7168877 0.747335 0.5410054 0.738036 0.5571167 0.7473369 0.5517452 0.9874209 0.7278694 0.9874209 0.7278694 0.9874209 0.7188813 0.05864334 0.6038709 0.05864334 0.6038709 0.05864334 0.5948836 0.03814023 0.6038752 0.03814023 0.6038752 0.03814023 0.5948878 0.9949766 0.5878716 0.9949766 0.5878716 0.9949766 0.5788835 0.9859873 0.707621 0.9949753 0.707621 0.9949753 0.6968813 0.9669165 0.7168877 0.9579277 0.7168877 0.9579277 0.7276272 0.747335 0.5410054 0.738036 0.5571167 0.7473369 0.5517452 0.9766814 0.7278694 0.9874209 0.7188813 0.9874209 0.7188813 0.04790395 0.6038709 0.05864334 0.5948836 0.04790395 0.5948836 0.02740067 0.6038752 0.03814023 0.5948878 0.03814023 0.5948878 0.9842375 0.5878716 0.9949766 0.5788835 0.9949766 0.5788835 0.9859873 0.707621 0.9949753 0.6968813 0.9859873 0.6968813 0.9669165 0.7168877 0.9579277 0.7276272 0.9669165 0.7168877 0.738036 0.5571167 0.7287328 0.541009 0.7287347 0.5517488 0.7287328 0.541009 0.738036 0.5571167 0.7380322 0.5356379 0.7380322 0.5356379 0.738036 0.5571167 0.747335 0.5410054 0.9766814 0.7278694 0.9874209 0.7188813 0.9874209 0.7188813 0.04790395 0.6038709 0.05864334 0.5948836 0.05864334 0.5948836 0.02740067 0.6038752 0.03814023 0.5948878 0.02740067 0.5948878 0.9842375 0.5878716 0.9949766 0.5788835 0.9949766 0.5788835 0.9859873 0.707621 0.9949753 0.6968813 0.9859873 0.6968813 0.9669165 0.7168877 0.9579277 0.7276272 0.9669165 0.7168877 0.738036 0.5571167 0.7287328 0.541009 0.7287347 0.5517488 0.7287328 0.541009 0.738036 0.5571167 0.7380322 0.5356379 0.7380322 0.5356379 0.738036 0.5571167 0.747335 0.5410054</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_L-mesh-map-0-array" count="200" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_mount_hz_L-mesh-vertices">
          <input semantic="POSITION" source="#tow_mount_hz_L-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="58">
          <input semantic="VERTEX" source="#tow_mount_hz_L-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_mount_hz_L-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_mount_hz_L-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>0 0 0 4 0 1 6 0 2 2 0 3 6 0 4 10 0 5 8 0 6 2 0 7 24 1 8 16 2 9 22 3 10 26 4 11 20 5 12 22 3 13 16 2 14 19 6 15 19 6 16 12 7 17 14 7 18 20 5 19 4 0 20 24 1 21 26 4 22 6 0 23 29 8 24 30 8 25 34 8 26 32 8 27 34 8 28 30 8 29 37 8 30 39 8 31 50 9 32 51 10 33 49 11 34 44 12 35 48 13 36 46 14 37 44 12 38 49 11 39 46 14 40 48 13 41 43 15 42 41 15 43 32 8 44 34 8 45 51 10 46 50 9 47 0 16 48 29 16 49 32 17 50 4 18 51 2 15 52 30 15 53 29 15 54 0 15 55 10 19 56 39 19 57 37 19 58 8 19 59 6 20 60 34 21 61 39 7 62 10 7 63 8 15 64 37 15 65 30 15 66 2 15 67 12 22 68 41 22 69 43 22 70 14 22 71 20 23 72 48 24 73 49 25 74 22 26 75 16 27 76 44 27 77 46 27 78 19 27 79 19 27 80 46 27 81 41 27 82 12 27 83 14 28 84 43 28 85 48 24 86 20 23 87 4 18 88 32 17 89 50 29 90 24 30 91 22 26 92 49 25 93 51 31 94 26 32 95 24 30 96 50 29 97 44 27 98 16 27 99 26 32 100 51 31 101 34 21 102 6 20 103 18 27 104 21 27 105 11 27 106 17 33 107 18 33 108 9 33 109 15 34 110 17 35 111 7 36 112 13 19 113 15 19 114 5 19 115 13 37 116 3 38 117 1 39 118 11 40 119 21 41 120 23 42 121 3 43 122 11 44 123 1 45 124 42 27 125 45 27 126 35 27 127 40 33 128 42 33 129 33 33 130 38 46 131 40 46 132 31 46 133 36 19 134 38 19 135 28 19 136 36 47 137 27 48 138 25 49 139 35 40 140 45 41 141 47 42 142 27 50 143 35 51 144 25 52 145 18 27 146 11 27 147 9 27 148 17 33 149 9 33 150 7 33 151 15 34 152 7 36 153 5 46 154 13 19 155 5 19 156 3 19 157 13 37 158 1 39 159 23 53 160 11 40 161 23 42 162 1 54 163 11 44 164 7 55 165 9 56 166 7 55 167 11 44 168 5 57 169 5 57 170 11 44 171 3 43 172 42 27 173 35 27 174 33 27 175 40 33 176 33 33 177 31 33 178 38 46 179 31 46 180 28 46 181 36 19 182 28 19 183 27 19 184 36 47 185 25 49 186 47 58 187 35 40 188 47 42 189 25 54 190 35 51 191 31 59 192 33 60 193 31 59 194 35 51 195 28 61 196 28 61 197 35 51 198 27 50 199</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_mount_hz_b_L-mesh" name="tow_mount_hz_b_L">
      <mesh>
        <source id="tow_mount_hz_b_L-mesh-positions">
          <float_array id="tow_mount_hz_b_L-mesh-positions-array" count="144">0.4744203 -0.107093 -0.02980327 0.5019696 -0.06428033 0.05404877 0.4744203 -0.02357435 -0.02980327 0.5055459 -0.05808597 0.05404877 0.4744203 -0.1220626 0.03100156 0.5126985 -0.05808597 0.05404877 0.4744203 -0.02357435 0.03100156 0.5162748 -0.06428033 0.05404877 0.5357343 -0.1364766 0.06812256 0.5126985 -0.07047462 0.05404877 0.5357343 -0.0439254 0.06812256 0.5055459 -0.07047462 0.05404877 0.5055459 -0.05808597 0.06003487 0.4744203 -0.1364766 0.05909371 0.5126985 -0.05808597 0.06003487 0.4834492 -0.1364766 0.06812256 0.4834492 -0.0439254 0.06812256 0.5162748 -0.06428033 0.06003487 0.4744203 -0.0439254 0.05909371 0.5126985 -0.07047462 0.06003487 0.4744203 -0.1270563 0.04207676 0.5055459 -0.07047462 0.06003487 0.4744203 -0.03524792 0.04207676 0.5019696 -0.06428033 0.06003487 0.4824203 -0.107093 -0.02980327 0.5019696 -0.1122244 0.05404877 0.4824203 -0.02357435 -0.02980327 0.5055459 -0.1060301 0.05404877 0.4824203 -0.1220626 0.03100156 0.5126985 -0.1060301 0.05404877 0.4824203 -0.02357435 0.03100156 0.5162748 -0.1122244 0.05404877 0.5357343 -0.1364766 0.0601226 0.5126985 -0.1184186 0.05404877 0.5357343 -0.0439254 0.0601226 0.5055459 -0.1184186 0.05404877 0.4824203 -0.1364766 0.05577999 0.5055459 -0.1060301 0.06003487 0.486763 -0.1364766 0.0601226 0.5126985 -0.1060301 0.06003487 0.4867629 -0.0439254 0.0601226 0.5162748 -0.1122244 0.06003487 0.4824203 -0.0439254 0.05577999 0.5126985 -0.1184186 0.06003487 0.4824203 -0.1270563 0.04207676 0.5055459 -0.1184186 0.06003487 0.4824203 -0.03524792 0.04207676 0.5019696 -0.1122244 0.06003487</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_b_L-mesh-positions-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_hz_b_L-mesh-normals">
          <float_array id="tow_mount_hz_b_L-mesh-normals-array" count="189">-1 0 0 -1 0 5.08675e-7 -0.9445447 -3.14568e-7 0.3283827 -0.9445669 0 0.328319 -1 0 2.4932e-7 -0.1458102 -8.41743e-5 0.9893126 -0.14581 0 0.9893127 0 0 1 1 0 0 1 3.77006e-7 -6.4184e-7 1 1.71052e-7 -2.91211e-7 0.9720695 3.18398e-7 -0.2346938 0.9720587 8.97795e-6 -0.2347383 0.08125543 0 -0.9966934 0.08125507 0 -0.9966934 0 0 -1 0 -0.9710066 -0.2390527 0 -0.9635251 -0.2676178 2.35845e-5 -0.9635251 -0.2676178 0 1 0 0 1 0 0 1 0 0.06819212 0.9417651 0.3292849 0.06819355 0.9417608 0.3292967 -0.07328689 -0.9324037 -0.3539104 -0.07328832 -0.9324011 -0.3539167 0 -1 7.67236e-7 0 -1 7.67235e-7 0 -1 1.14971e-6 0 1 2.1557e-7 -0.06464856 -0.8761413 -0.4776997 -0.0646702 -0.8761276 -0.4777221 0.05378925 0.79124 0.6091355 0.05379593 0.791251 0.6091206 0 0.6882699 0.7254549 0 -1 0 0.866025 -0.5000008 0 0.8660276 -0.4999962 0 0.8660255 -0.4999998 0 0.8660255 0.5000001 0 0.8660227 0.500005 0 0.8660248 0.5000011 0 -0.8660221 0.5000058 0 -0.866018 0.5000131 0 -0.8660231 0.500004 0 -0.8660225 -0.500005 0 -0.8660182 -0.5000125 0 -0.8660237 -0.5000031 0 3.28445e-7 3.00704e-7 -1 3.28444e-7 0 -1 1.31377e-6 3.00707e-7 -1 3.28445e-7 3.00703e-7 -1 3.28444e-7 0 -1 0.8660229 -0.5000044 0 0.8660276 0.4999962 0 -0.8660272 0.4999968 0 -0.866028 -0.4999957 0 -6.56895e-7 0 -1 1.31378e-6 -3.00701e-7 -1 -6.56892e-7 2.25527e-7 -1 -6.56896e-7 0 -1 1.31378e-6 -3.00701e-7 -1 -6.56893e-7 2.25527e-7 -1</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_b_L-mesh-normals-array" count="63" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_mount_hz_b_L-mesh-map-0">
          <float_array id="tow_mount_hz_b_L-mesh-map-0-array" count="368">0.3105007 0.796985 0.3254848 0.7361206 0.2269001 0.7361206 0.2269001 0.796985 0.3304828 0.7250344 0.3399125 0.7080008 0.2472706 0.7080008 0.2385849 0.7250344 0.2472706 0.6952194 0.2472706 0.7080008 0.3399125 0.7080008 0.3399125 0.6952193 0.3399125 0.6952193 0.3399125 0.6428832 0.2472706 0.6428833 0.2472706 0.6952194 0.3254848 0.7361206 0.3304828 0.7250344 0.2385849 0.7250344 0.2269001 0.7361206 0.851692 0.7782944 0.8516919 0.8618949 0.9125563 0.8618949 0.9125564 0.7633103 0.9236424 0.758312 0.9236424 0.8502101 0.937359 0.8415238 0.9373592 0.7488819 0.9435065 0.8415238 0.9435065 0.7488819 0.9373592 0.7488819 0.937359 0.8415238 0.9435065 0.7488819 0.9435065 0.8415238 0.9925259 0.8415237 0.992526 0.7488818 0.9125564 0.7633103 0.9125563 0.8618949 0.9236424 0.8502101 0.9236424 0.758312 0.81878 0.8547174 0.826784 0.8547174 0.8267841 0.7920666 0.8187801 0.7920669 0.6490427 0.9928947 0.6490427 0.9848867 0.565442 0.9848867 0.565442 0.9928947 0.7114206 0.5948878 0.7114206 0.6028949 0.7722853 0.6028949 0.7722853 0.5948878 0.5556774 0.9928917 0.5556774 0.9848836 0.4630354 0.9848836 0.4630354 0.9928917 0.545623 0.6518907 0.5536258 0.6552045 0.5579699 0.6508604 0.5546542 0.6428577 0.8185934 0.7604531 0.8267841 0.763275 0.8308559 0.7586739 0.8270604 0.7508866 0.8270604 0.7508866 0.8308559 0.7586739 0.8792729 0.7586737 0.8792729 0.7508864 0.5456264 0.7041946 0.5536293 0.7041941 0.5536258 0.6552045 0.545623 0.6518907 0.8187801 0.7920669 0.8267841 0.7920666 0.8267841 0.7799119 0.8187801 0.7799119 0.5546542 0.6428577 0.5579699 0.6508604 0.574172 0.6499916 0.5737437 0.6420004 0.8187801 0.7799119 0.8267841 0.7799119 0.8267841 0.763275 0.8185934 0.7604531 0.5737437 0.6420004 0.574172 0.6499916 0.5902463 0.6491302 0.5898178 0.6411389 0.9874209 0.7278694 0.9874209 0.7278694 0.9874209 0.7188813 0.05864334 0.6038709 0.05864334 0.6038709 0.05864334 0.5948836 0.02740067 0.6038752 0.03814023 0.6038752 0.03814023 0.5948878 0.9949766 0.5878716 0.9949766 0.5878716 0.9949766 0.5788835 0.9859873 0.707621 0.9949753 0.707621 0.9949753 0.6968813 0.9669165 0.7168877 0.9579277 0.7168877 0.9579277 0.7276272 0.747335 0.5410054 0.738036 0.5571167 0.7473369 0.5517452 0.9874209 0.7278694 0.9874209 0.7278694 0.9874209 0.7188813 0.04790395 0.6038709 0.05864334 0.6038709 0.05864334 0.5948836 0.03814023 0.6038752 0.03814023 0.6038752 0.03814023 0.5948878 0.9949766 0.5878716 0.9949766 0.5878716 0.9949766 0.5788835 0.9859873 0.707621 0.9949753 0.707621 0.9949753 0.6968813 0.9669165 0.7168877 0.9579277 0.7168877 0.9579277 0.7276272 0.747335 0.5410054 0.738036 0.5571167 0.7473369 0.5517452 0.9766814 0.7278694 0.9874209 0.7188813 0.9874209 0.7188813 0.04790395 0.6038709 0.05864334 0.5948836 0.04790395 0.5948836 0.02740067 0.6038752 0.03814023 0.5948878 0.03814023 0.5948878 0.9842375 0.5878716 0.9949766 0.5788835 0.9949766 0.5788835 0.9859873 0.707621 0.9949753 0.6968813 0.9859873 0.6968813 0.9669165 0.7168877 0.9579277 0.7276272 0.9669165 0.7276272 0.738036 0.5571167 0.7287328 0.541009 0.7287347 0.5517488 0.7287328 0.541009 0.738036 0.5571167 0.7380322 0.5356379 0.7380322 0.5356379 0.738036 0.5571167 0.747335 0.5410054 0.9766814 0.7278694 0.9874209 0.7188813 0.9874209 0.7188813 0.04790395 0.6038709 0.05864334 0.5948836 0.04790395 0.5948836 0.02740067 0.6038752 0.03814023 0.5948878 0.02740067 0.5948878 0.9842375 0.5878716 0.9949766 0.5788835 0.9949766 0.5788835 0.9859873 0.707621 0.9949753 0.6968813 0.9859873 0.6968813 0.9669165 0.7168877 0.9579277 0.7276272 0.9669165 0.7276272 0.738036 0.5571167 0.7287328 0.541009 0.7287347 0.5517488 0.7287328 0.541009 0.738036 0.5571167 0.7380322 0.5356379 0.7380322 0.5356379 0.738036 0.5571167 0.747335 0.5410054</float_array>
          <technique_common>
            <accessor source="#tow_mount_hz_b_L-mesh-map-0-array" count="184" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_mount_hz_b_L-mesh-vertices">
          <input semantic="POSITION" source="#tow_mount_hz_b_L-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="54">
          <input semantic="VERTEX" source="#tow_mount_hz_b_L-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_mount_hz_b_L-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_mount_hz_b_L-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>0 0 0 4 0 1 6 0 2 2 0 3 20 1 4 13 2 5 18 3 6 22 4 7 16 5 8 18 3 9 13 2 10 15 6 11 15 6 12 8 7 13 10 7 14 16 5 15 4 0 16 20 1 17 22 4 18 6 0 19 24 8 20 26 8 21 30 8 22 28 8 23 44 9 24 46 10 25 42 11 26 36 12 27 40 13 28 38 14 29 36 12 30 42 11 31 38 14 32 40 13 33 34 15 34 32 15 35 28 8 36 30 8 37 46 10 38 44 9 39 0 16 40 24 16 41 28 17 42 4 18 43 2 15 44 26 15 45 24 15 46 0 15 47 6 19 48 30 19 49 26 19 50 2 19 51 8 8 52 32 8 53 34 8 54 10 8 55 16 20 56 40 21 57 42 22 58 18 23 59 13 24 60 36 25 61 38 26 62 15 27 63 15 27 64 38 26 65 32 28 66 8 28 67 10 29 68 34 29 69 40 21 70 16 20 71 4 18 72 28 17 73 44 30 74 20 31 75 18 23 76 42 22 77 46 32 78 22 33 79 20 31 80 44 30 81 36 25 82 13 24 83 22 33 84 46 32 85 30 34 86 6 34 87 19 35 88 21 35 89 11 35 90 17 36 91 19 37 92 9 38 93 14 39 94 17 40 95 7 41 96 12 19 97 14 19 98 5 19 99 12 42 100 3 43 101 1 44 102 11 45 103 21 46 104 23 47 105 3 48 106 11 49 107 1 50 108 43 35 109 45 35 110 35 35 111 41 36 112 43 37 113 33 38 114 39 39 115 41 40 116 31 41 117 37 19 118 39 19 119 29 19 120 37 42 121 27 43 122 25 44 123 35 45 124 45 46 125 47 47 126 27 51 127 35 52 128 25 50 129 19 35 130 11 35 131 9 35 132 17 36 133 9 38 134 7 53 135 14 39 136 7 41 137 5 54 138 12 19 139 5 19 140 3 19 141 12 42 142 1 44 143 23 55 144 11 45 145 23 47 146 1 56 147 11 49 148 7 57 149 9 58 150 7 57 151 11 49 152 5 59 153 5 59 154 11 49 155 3 48 156 43 35 157 35 35 158 33 35 159 41 36 160 33 38 161 31 53 162 39 39 163 31 41 164 29 54 165 37 19 166 29 19 167 27 19 168 37 42 169 25 44 170 47 55 171 35 45 172 47 47 173 25 56 174 35 52 175 31 60 176 33 61 177 31 60 178 35 52 179 29 62 180 29 62 181 35 52 182 27 51 183</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_hitch_b-mesh" name="tow_hitch_b">
      <mesh>
        <source id="tow_hitch_b-mesh-positions">
          <float_array id="tow_hitch_b-mesh-positions-array" count="180">-0.02549999 -0.02443265 -0.02149999 -0.02149999 -0.02443265 -0.02549999 -0.02149999 -0.02443265 0.02549993 -0.02549999 -0.02443265 0.02149993 -0.02420973 0.1230819 -0.02786171 -0.02871388 0.1251611 -0.02349126 -0.02871388 0.1475123 0.0234912 -0.02420973 0.1495915 0.02786165 0.02149999 -0.02443265 -0.02549999 0.02549999 -0.02443265 -0.02149999 0.02549999 -0.02443265 0.02149993 0.02149999 -0.02443265 0.02549993 0.02871388 0.1251611 -0.02349126 0.02420973 0.1230819 -0.02786171 0.02420973 0.1495915 0.02786165 0.02871388 0.1475123 0.0234912 0.03549665 0.1325283 -0.03150838 0.02816224 0.1295746 -0.03741997 -0.03549665 0.1325283 -0.03150838 -0.02816224 0.1295746 -0.03741997 0.03549665 0.2603567 0.09226197 0.024378 0.2714754 0.09226197 0.03224009 0.2682188 0.09226197 -0.03549665 0.2603567 0.09226197 -0.02437794 0.2714754 0.09226197 -0.03224009 0.2682188 0.09226197 0.03549665 0.1852162 0.07963848 0.03549665 0.2023897 0.0922994 0.03549665 0.1893873 0.08555871 0.03549665 0.1960211 0.09039354 -0.03549665 0.2023897 0.0922994 -0.03549665 0.1852162 0.07963848 -0.03549665 0.1960211 0.0903936 -0.03549665 0.1893873 0.08555871 0.03549665 0.1235976 -0.02700906 0.02816224 0.120644 -0.03292065 -0.03549665 0.1235976 -0.02700906 -0.02816224 0.120644 -0.03292065 0.03549665 0.2603567 0.102262 0.024378 0.2714754 0.102262 0.03224009 0.2682188 0.102262 -0.03549665 0.2603567 0.102262 -0.02437794 0.2714754 0.102262 -0.03224009 0.2682188 0.102262 0.03549665 0.1763911 0.08434134 0.03549665 0.201525 0.1022619 0.03549665 0.1822342 0.09254682 0.03549665 0.1918168 0.0994668 -0.03549665 0.201525 0.102262 -0.03549665 0.1763911 0.08434134 -0.03549665 0.1918168 0.09946686 -0.03549665 0.1822342 0.09254688 -0.02149999 0.1198064 -0.02549999 -0.02549999 0.1217094 -0.02149999 -0.02149999 0.1440688 0.02549993 -0.02549999 0.1421659 0.02149993 0.02149999 0.1198064 -0.02549999 0.02549999 0.1217094 -0.02149999 0.02149999 0.1440688 0.02549993 0.02549999 0.142166 0.02149993</float_array>
          <technique_common>
            <accessor source="#tow_hitch_b-mesh-positions-array" count="60" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_hitch_b-mesh-normals">
          <float_array id="tow_hitch_b-mesh-normals-array" count="228">0 -1 0 0.9973716 -0.0220713 -0.06901323 0.9947147 -0.02173244 0.1003515 0.9959115 0 0.09033554 0.9968508 1.35918e-4 -0.07930141 0.08371436 -0.01579844 0.9963645 -0.08373588 -0.01578241 0.9963631 -0.0844298 0 0.9964295 0.08442962 -1.16096e-5 0.9964295 -0.9973716 -0.02207136 -0.06901335 -0.9947159 -0.02175104 0.1003362 -0.7463908 -0.5735954 0.3374747 -0.7741766 -0.594964 0.2160292 0 0.90302 -0.4295986 -0.08599191 -0.01863509 -0.9961216 -0.08537727 -1.42368e-4 -0.9963487 -0.9968507 0 -0.07930135 -0.0593822 -0.4175451 0.9067138 0.08599811 -0.01862961 -0.9961212 0.0896877 -0.631359 -0.7702869 0.7741751 -0.5949655 0.2160301 0.08537715 0 -0.9963487 -2.90295e-7 0.02511668 -0.9996845 -2.40976e-7 0.02511668 -0.9996845 -2.04778e-7 -5.50155e-4 -1 -1.74739e-7 0.4602373 -0.887796 -1.75229e-7 0.4602373 -0.8877959 -1.29291e-7 0.7054877 -0.7087221 4.85302e-6 0.7054563 -0.7087535 0 0.8992918 -0.4373492 0 0.8992919 -0.4373491 0 0.9032061 -0.4292071 1.31217e-7 -0.03553324 0.9993685 0 0 1 1.03289e-6 -0.03553324 0.9993685 1.88428e-7 -0.4497775 0.8931407 1.83623e-7 -0.4497775 0.8931407 -6.09306e-6 -0.7001378 0.7140078 1.88332e-7 -0.7001377 0.7140079 0 -0.8976901 0.4406274 0 -0.8976901 0.4406275 0 -0.9031798 0.4292626 0.1033002 -0.4475234 -0.8882859 0.6693767 -0.334264 -0.6634775 0.1032915 -0.447524 -0.8882867 -0.1033006 -0.4475236 -0.8882858 -0.1032919 -0.4475297 -0.8882838 -0.6693772 -0.3342638 -0.6634772 -0.7071083 0.7071053 -1.48071e-6 -0.9987894 0.04919159 9.6591e-5 -0.9987888 0.04920262 5.61434e-6 -0.05745023 0.9983484 5.17133e-5 -0.05745023 0.9983484 -1.41978e-6 0.05745005 0.9983485 0 0.05745011 0.9983485 -5.30562e-5 0.7071081 0.7071056 0 0.9987894 0.04919177 3.33436e-6 0.9987889 0.04920279 0 -1 0 0 1 -2.60678e-7 0 1 -2.69508e-7 0 1 -2.50259e-7 0 1 -2.68282e-7 0 -1 1.69453e-7 3.46568e-7 -1 1.70818e-7 3.4936e-7 1 -5.0836e-7 -6.93136e-7 1 -5.12455e-7 -6.9872e-7 -1 1.73768e-7 3.55393e-7 -1 1.65098e-7 3.3766e-7 1 -5.21304e-7 -6.97658e-7 1 -4.95293e-7 -6.61506e-7 1 0 0 -0.08970636 -0.6313369 -0.770303 0.05937504 -0.4175599 0.9067075 0.7463883 -0.5735998 0.3374727 -0.9959115 1.02745e-4 0.09033536</float_array>
          <technique_common>
            <accessor source="#tow_hitch_b-mesh-normals-array" count="76" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_hitch_b-mesh-map-0">
          <float_array id="tow_hitch_b-mesh-map-0-array" count="408">0.004878401 0.9428833 0.008882284 0.9388793 0.05192428 0.9388793 0.05592834 0.9428833 0.05592834 0.9859253 0.05192428 0.9899293 0.008882284 0.9899293 0.004878401 0.9859253 0.8856988 0.4887027 0.8426565 0.5091789 0.8426565 0.342417 0.8856988 0.342417 0.8369942 0.5110836 0.793952 0.5110836 0.793952 0.3424171 0.8369942 0.342417 0.7452476 0.4887026 0.7882898 0.5091789 0.7882664 0.5150626 0.7438827 0.4933423 -0.1261352 0.1087219 -0.1299952 0.1045743 -0.1299952 0.05998778 -0.1261352 0.05584019 -0.08464121 0.05584025 -0.08078122 0.05998778 -0.08078122 0.1045743 -0.08464121 0.1087219 0.7395851 0.4867972 0.7395851 0.3424171 0.7452475 0.3424171 0.7452475 0.4887026 0.793952 0.5110836 0.792541 0.5167666 0.7882664 0.5150626 0.7882898 0.5091789 0.891361 0.4867973 0.8903846 0.490767 0.8868331 0.4928566 0.8856988 0.4887026 0.8369942 0.5110836 0.8369942 0.342417 0.8426565 0.342417 0.8426565 0.5091789 0.9344032 0.4867972 0.891361 0.4867973 0.8913609 0.342417 0.9344032 0.3424171 0.484517 0.8119421 0.484517 0.7408789 0.5425406 0.7408789 0.5504105 0.7441389 0.5536703 0.7520084 0.5536703 0.8008123 0.5504105 0.8086817 0.5425406 0.8119421 0.484517 0.7408789 0.484517 0.8119421 0.4778628 0.8119421 0.4778628 0.7408789 0.4778628 0.7408789 0.4778628 0.8119421 0.469646 0.8119421 0.469646 0.7408789 0.469646 0.7408789 0.469646 0.8119421 0.4623969 0.8119421 0.462397 0.7408789 0.3392739 0.8119421 0.3326591 0.8046004 0.3326591 0.7482211 0.3392739 0.7408789 0.462397 0.7408789 0.4623969 0.8119421 0.07490313 0.7139414 0.01601392 0.7139414 0.008144021 0.7106814 0.004884302 0.7028121 0.004884302 0.6540083 0.008144021 0.6461385 0.01601392 0.6428788 0.07490313 0.6428788 0.07490313 0.6428788 0.08501547 0.6428788 0.08501547 0.7139414 0.07490313 0.7139414 0.08501547 0.6428788 0.09684711 0.6428788 0.09684711 0.7139414 0.08501547 0.7139414 0.09684711 0.6428788 0.1069303 0.6428788 0.1069303 0.7139414 0.09684711 0.7139414 0.2302827 0.7139413 0.1069303 0.7139414 0.1069303 0.6428788 0.2302826 0.6428788 0.2368975 0.6502205 0.2368975 0.7065997 0.3870032 0.9949169 0.3771213 0.9948956 0.3771213 0.9848861 0.3870034 0.9849075 0.4433829 0.9949169 0.3870032 0.9949169 0.3870034 0.9849075 0.4433829 0.9849075 0.4532651 0.9948955 0.4433829 0.9949169 0.4433829 0.9849075 0.4532651 0.984886 0.6985774 0.9628859 0.7071459 0.9628859 0.7071034 0.973329 0.6985661 0.973329 0.6900919 0.9628859 0.6985774 0.9628859 0.6985661 0.973329 0.6900768 0.973329 0.6413586 0.9628859 0.6900919 0.9628859 0.6900768 0.973329 0.6413738 0.973329 0.6328731 0.9628859 0.6413586 0.9628859 0.6413738 0.973329 0.6328843 0.973329 0.6243047 0.9628859 0.6328731 0.9628859 0.6328843 0.973329 0.624347 0.973329 0.7671911 0.9628859 0.7766851 0.9628859 0.7760545 0.973329 0.7664953 0.973329 0.5547654 0.9628859 0.5642594 0.9628859 0.5649552 0.973329 0.555396 0.973329 0.7766851 0.9628859 0.7878773 0.9628859 0.7879872 0.973329 0.7760545 0.973329 0.5435733 0.9628859 0.5547654 0.9628859 0.555396 0.973329 0.5434632 0.973329 0.7878773 0.9628859 0.7973042 0.9628859 0.7977812 0.973329 0.7879872 0.973329 0.5341461 0.9628859 0.5435733 0.9628859 0.5434632 0.973329 0.5336691 0.973329 0.4097323 0.9628857 0.5341461 0.9628859 0.5336691 0.973329 0.4099612 0.9733289 0.7973042 0.9628859 0.9217184 0.9628857 0.9214891 0.9733289 0.7977812 0.973329 0.5642594 0.9628859 0.6243047 0.9628859 0.624347 0.973329 0.5649552 0.973329 0.7071459 0.9628859 0.7671911 0.9628859 0.7664953 0.973329 0.7071034 0.973329 0.9370202 0.4906818 0.8903846 0.490767 0.891361 0.4867973 0.9344032 0.4867972 0.8384264 0.5167825 0.8369942 0.5110836 0.8426564 0.5091789 0.8427207 0.5150639 0.8913609 0.342417 0.891361 0.4867973 0.8856988 0.4887027 0.8856988 0.342417 0.793952 0.3424171 0.793952 0.5110836 0.7882898 0.5091789 0.7882898 0.3424171 0.7391813 0.4907093 0.7395851 0.4867972 0.7452476 0.4887026 0.7438827 0.4933423 0.7452475 0.3424171 0.7882898 0.3424171 0.7882898 0.5091789 0.7452475 0.4887026 0.8384264 0.5167825 0.792541 0.5167666 0.793952 0.5110836 0.8369942 0.5110836 0.8868331 0.4928566 0.8427207 0.5150639 0.8426564 0.5091789 0.8856988 0.4887026</float_array>
          <technique_common>
            <accessor source="#tow_hitch_b-mesh-map-0-array" count="204" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_hitch_b-mesh-vertices">
          <input semantic="POSITION" source="#tow_hitch_b-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="46">
          <input semantic="VERTEX" source="#tow_hitch_b-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_hitch_b-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_hitch_b-mesh-map-0" offset="2" set="0"/>
          <vcount>8 4 4 4 8 4 4 4 4 4 8 4 4 4 6 8 4 4 4 6 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>8 0 0 9 0 1 10 0 2 11 0 3 2 0 4 3 0 5 0 0 6 1 0 7 57 1 8 59 2 9 10 3 10 9 4 11 58 5 12 54 6 13 2 7 14 11 8 15 53 9 16 55 10 17 6 11 18 5 12 19 4 13 20 5 13 21 6 13 22 7 13 23 14 13 24 15 13 25 12 13 26 13 13 27 52 14 28 1 15 29 0 16 30 53 9 31 54 6 32 7 17 33 6 11 34 55 10 35 56 18 36 13 19 37 12 20 38 57 1 39 58 5 40 11 8 41 10 3 42 59 2 43 52 14 44 56 18 45 8 21 46 1 15 47 27 22 48 30 23 49 23 24 50 25 24 51 24 24 52 21 24 53 22 24 54 20 24 55 30 23 56 27 22 57 29 25 58 32 26 59 32 26 60 29 25 61 28 27 62 33 28 63 33 28 64 28 27 65 26 29 66 31 30 67 16 31 68 17 31 69 19 31 70 18 31 71 31 30 72 26 29 73 45 32 74 38 33 75 40 33 76 39 33 77 42 33 78 43 33 79 41 33 80 48 34 81 48 34 82 50 35 83 47 36 84 45 32 85 50 35 86 51 37 87 46 38 88 47 36 89 51 37 90 49 39 91 44 40 92 46 38 93 34 41 94 44 40 95 49 39 96 36 41 97 37 41 98 35 41 99 17 42 100 16 43 101 34 43 102 35 44 103 19 45 104 17 42 105 35 44 106 37 46 107 18 47 108 19 45 109 37 46 110 36 47 111 25 48 112 23 49 113 41 50 114 43 48 115 24 51 116 25 48 117 43 48 118 42 52 119 21 53 120 24 51 121 42 52 122 39 54 123 22 55 124 21 53 125 39 54 126 40 55 127 20 56 128 22 55 129 40 55 130 38 57 131 30 58 132 32 58 133 50 58 134 48 58 135 29 59 136 27 60 137 45 61 138 47 62 139 32 58 140 33 63 141 51 64 142 50 58 143 28 65 144 29 59 145 47 62 146 46 66 147 33 63 148 31 67 149 49 68 150 51 64 151 26 69 152 28 65 153 46 66 154 44 70 155 16 71 156 26 69 157 44 70 158 34 71 159 31 67 160 18 58 161 36 58 162 49 68 163 27 60 164 20 56 165 38 57 166 45 61 167 23 49 168 30 58 169 48 58 170 41 50 171 4 72 172 13 19 173 56 18 174 52 14 175 14 73 176 58 5 177 59 2 178 15 74 179 8 21 180 56 18 181 57 1 182 9 4 183 2 7 184 54 6 185 55 10 186 3 75 187 4 72 188 52 14 189 53 9 190 5 12 191 0 16 192 3 75 193 55 10 194 53 9 195 14 73 196 7 17 197 54 6 198 58 5 199 12 20 200 15 74 201 59 2 202 57 1 203</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_hitch_a-mesh" name="tow_hitch_a">
      <mesh>
        <source id="tow_hitch_a-mesh-positions">
          <float_array id="tow_hitch_a-mesh-positions-array" count="180">-0.02549999 -0.02443265 -0.02149999 -0.02149999 -0.02443265 -0.02549999 -0.02149999 -0.02443265 0.02549993 -0.02549999 -0.02443265 0.02149993 -0.02420973 0.1230819 -0.02786171 -0.02871388 0.1265687 -0.02349126 -0.02871388 0.164051 0.0234912 -0.02420973 0.1675378 0.02786165 0.02149999 -0.02443265 -0.02549999 0.02549999 -0.02443265 -0.02149999 0.02549999 -0.02443265 0.02149993 0.02149999 -0.02443265 0.02549993 0.02871388 0.1265687 -0.02349126 0.02420973 0.1230819 -0.02786171 0.02420973 0.1675378 0.02786165 0.02871388 0.164051 0.0234912 0.03549665 0.132551 -0.03269183 0.02816224 0.1284915 -0.0379064 -0.03549665 0.132551 -0.03269183 -0.02816224 0.1284915 -0.0379064 0.03549665 0.2603567 0.04226195 0.024378 0.2714754 0.04226195 0.03224009 0.2682188 0.04226195 -0.03549665 0.2603567 0.04226195 -0.02437794 0.2714754 0.04226195 -0.03224009 0.2682188 0.04226195 0.03549665 0.1847972 0.03399783 0.03549665 0.2023897 0.04229938 0.03549665 0.1895136 0.03821176 0.03549665 0.1958325 0.04120683 -0.03549665 0.2023897 0.04229938 -0.03549665 0.1847972 0.03399783 -0.03549665 0.1958325 0.04120683 -0.03549665 0.1895136 0.03821176 0.03549665 0.1246807 -0.02652263 0.02816224 0.1206213 -0.0317372 -0.03549665 0.1246807 -0.02652263 -0.02816224 0.1206213 -0.0317372 0.03549665 0.2603567 0.05226194 0.024378 0.2714754 0.05226194 0.03224009 0.2682188 0.05226194 -0.03549665 0.2603567 0.05226194 -0.02437794 0.2714754 0.05226194 -0.03224009 0.2682188 0.05226194 0.03549665 0.1775311 0.04086828 0.03549665 0.201525 0.05226194 0.03549665 0.1840162 0.04656511 0.03549665 0.1927986 0.05073547 -0.03549665 0.201525 0.05226194 -0.03549665 0.1775311 0.04086828 -0.03549665 0.1927986 0.05073547 -0.03549665 0.1840162 0.04656511 -0.02149999 0.120567 -0.02549999 -0.02549999 0.1237581 -0.02149999 -0.02149999 0.1612545 0.02549993 -0.02549999 0.1580633 0.02149993 0.02149999 0.120567 -0.02549999 0.02549999 0.1237581 -0.02149999 0.02149999 0.1612545 0.02549993 0.02549999 0.1580633 0.02149993</float_array>
          <technique_common>
            <accessor source="#tow_hitch_a-mesh-positions-array" count="60" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_hitch_a-mesh-normals">
          <float_array id="tow_hitch_a-mesh-normals-array" count="240">0 -1 0 0.9980018 -0.02102285 -0.05958533 0.9937866 -0.02047902 0.1094025 0.9956184 0 0.09350997 0.997107 -1.19241e-5 -0.07601177 0.08317565 -0.01430064 0.9964323 -0.08318889 -0.01429092 0.9964314 -0.08420681 0 0.9964483 0.08420675 1.0476e-4 0.9964483 -0.9980018 -0.02102273 -0.05958551 -0.9937846 -0.02043414 0.1094295 -0.6933812 -0.5328377 0.4850842 -0.7316814 -0.5623074 0.3852958 0 0.7817093 -0.6236431 -0.08678734 -0.01865851 -0.9960522 -0.08571159 -1.13264e-4 -0.9963201 -0.997107 0 -0.07601147 -0.05285227 -0.371691 0.9268509 0.08686971 -0.01859354 -0.9960463 0.1051203 -0.7399051 -0.6644473 0.7316887 -0.5623009 0.3852913 0.0857113 0 -0.99632 -1.73019e-7 0.01414591 -0.9999001 -1.80943e-7 0.01414591 -0.9998999 -1.62618e-7 -5.50209e-4 -1 -1.2275e-7 0.3025636 -0.9531293 -1.22936e-7 0.3025636 -0.9531293 0 0.5469399 -0.8371719 0 0.54694 -0.8371719 0 0.7801582 -0.6255825 0 0.7801582 -0.6255823 0 0.7873175 -0.6165478 1.65122e-7 -0.01968979 0.9998062 1.42736e-7 -1.62133e-7 1 1.57369e-7 -0.01968979 0.9998062 1.57365e-7 -0.3094238 0.9509243 0 -0.3093756 0.95094 1.42119e-7 -0.5428122 0.8398542 1.35945e-7 -0.5428121 0.8398542 0 -0.7771026 0.6293741 0 -0.7771025 0.6293742 0 -0.787026 0.6169198 0.1033003 -0.6136191 -0.7828159 0.669377 -0.458324 -0.5846996 0.1032916 -0.6136201 -0.7828163 -0.1033006 -0.6136195 -0.7828155 -0.1032919 -0.613625 -0.7828124 -0.6693773 -0.4583238 -0.5846995 -0.7071082 0.7071054 -1.48071e-6 -0.9987894 0.04919165 9.66102e-5 -0.9987888 0.04920268 -5.61888e-5 -0.05745017 0.9983484 5.17133e-5 -0.05745017 0.9983484 -1.44135e-6 0.05745017 0.9983484 0 0.05745023 0.9983484 -5.30562e-5 0.707108 0.7071056 0 0.9987894 0.04919189 3.33436e-6 0.9987888 0.04920297 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 -1 5.01541e-7 3.20879e-7 -1 5.00023e-7 3.20159e-7 1 -7.85633e-7 -6.41756e-7 1 -7.83871e-7 -6.40318e-7 -1 4.49044e-7 3.19321e-7 -1 4.50884e-7 3.20134e-7 1 -7.81819e-7 -6.38641e-7 1 -7.83809e-7 -6.40267e-7 1 0 0 -1 -2.73925e-7 0 -0.1051483 -0.7398797 -0.6644712 0.05284827 -0.3717025 0.9268465 0.6933652 -0.5328686 0.485073 -0.9956184 1.01684e-4 0.09350997</float_array>
          <technique_common>
            <accessor source="#tow_hitch_a-mesh-normals-array" count="80" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_hitch_a-mesh-map-0">
          <float_array id="tow_hitch_a-mesh-map-0-array" count="408">0.9382635 0.9039358 0.9342597 0.8999323 0.9342597 0.8568902 0.9382635 0.8528861 0.9813059 0.8528861 0.9853098 0.8568902 0.9853098 0.8999323 0.9813059 0.9039358 0.6811218 0.4907623 0.6380797 0.5251009 0.6380797 0.3424264 0.6811218 0.3424264 0.6324173 0.5282956 0.5893752 0.5282956 0.5893752 0.3424264 0.6324173 0.3424264 0.5406705 0.4907626 0.5837128 0.5251009 0.5833215 0.5315776 0.5389198 0.4948415 0.05786061 0.836884 0.06236904 0.8424805 0.06236904 0.9026418 0.05786061 0.9082382 0.009393453 0.9082382 0.004884958 0.9026418 0.004884958 0.8424805 0.009393453 0.836884 0.5350082 0.4875679 0.5350082 0.3424264 0.5406705 0.3424264 0.5406705 0.4907626 0.5893752 0.5282956 0.5878114 0.5346678 0.5833215 0.5315776 0.5837128 0.5251009 0.6867842 0.4875677 0.6860315 0.4917064 0.6827981 0.4943895 0.6811218 0.4907623 0.6324173 0.5282956 0.6324173 0.3424264 0.6380797 0.3424264 0.6380797 0.5251009 0.7298264 0.4875677 0.6867842 0.4875677 0.6867842 0.3424264 0.7298264 0.3424264 0.6657086 0.7548831 0.6657087 0.8259463 0.607685 0.8259463 0.5998151 0.8226863 0.5965554 0.8148167 0.5965554 0.7660131 0.5998151 0.7581431 0.607685 0.7548831 0.6657087 0.8259463 0.6657086 0.7548831 0.6723628 0.7548831 0.6723628 0.8259463 0.6723628 0.8259463 0.6723628 0.7548831 0.6793625 0.7548831 0.6793625 0.8259463 0.6793625 0.8259463 0.6793625 0.7548831 0.6856932 0.7548831 0.6856933 0.8259463 0.7704945 0.7548831 0.7771096 0.7622247 0.7771096 0.8186045 0.7704945 0.8259463 0.6856933 0.8259463 0.6856932 0.7548831 0.7211106 0.8368811 0.78 0.8368811 0.7878698 0.8401405 0.7911296 0.8480107 0.7911297 0.8968142 0.7878699 0.9046841 0.7800001 0.9079437 0.7211106 0.9079437 0.7211106 0.9079437 0.7122432 0.9079437 0.7122431 0.8368811 0.7211106 0.8368811 0.7122432 0.9079437 0.7025114 0.9079437 0.7025113 0.8368811 0.7122431 0.8368811 0.7025114 0.9079437 0.6938709 0.9079437 0.6938709 0.8368811 0.7025113 0.8368811 0.608144 0.8368811 0.6938709 0.8368811 0.6938709 0.9079437 0.608144 0.9079437 0.6015293 0.9006021 0.6015293 0.8442227 0.1990928 0.7347628 0.1991152 0.7248809 0.2091248 0.7248809 0.2091033 0.7347628 0.1990928 0.7911427 0.1990928 0.7347628 0.2091033 0.7347628 0.2091033 0.7911427 0.1991152 0.8010245 0.1990928 0.7911427 0.2091033 0.7911427 0.2091248 0.8010245 0.6692304 0.929208 0.6606975 0.929208 0.6606624 0.918884 0.6691806 0.918884 0.6777214 0.929208 0.6692304 0.929208 0.6691806 0.918884 0.6776989 0.918884 0.7264801 0.929208 0.6777214 0.929208 0.6776989 0.918884 0.7265026 0.918884 0.7349709 0.929208 0.7264801 0.929208 0.7265026 0.918884 0.7350209 0.918884 0.7435039 0.9292079 0.7349709 0.929208 0.7350209 0.918884 0.743539 0.9188839 0.6014517 0.929208 0.5928328 0.929208 0.5929055 0.918884 0.6017731 0.918884 0.8113687 0.9292079 0.8027497 0.9292079 0.8024284 0.9188839 0.8112961 0.9188839 0.5928328 0.929208 0.5833491 0.929208 0.5831736 0.918884 0.5929055 0.918884 0.8208524 0.9292079 0.8113687 0.9292079 0.8112961 0.9188839 0.8210278 0.9188839 0.5833491 0.929208 0.5749336 0.929208 0.5745333 0.918884 0.5831736 0.918884 0.8292678 0.9292079 0.8208524 0.9292079 0.8210278 0.9188839 0.8296681 0.9188839 0.9154409 0.9292079 0.8292678 0.9292079 0.8296681 0.9188839 0.915395 0.9188839 0.5749336 0.929208 0.4887604 0.929208 0.4888061 0.918884 0.5745333 0.918884 0.8027497 0.9292079 0.7435039 0.9292079 0.743539 0.9188839 0.8024284 0.9188839 0.6606975 0.929208 0.6014517 0.929208 0.6017731 0.918884 0.6606624 0.918884 0.7324426 0.4914411 0.6860315 0.4917064 0.6867842 0.4875677 0.7298264 0.4875677 0.6339643 0.5346772 0.6324173 0.5282956 0.6380797 0.5251009 0.638488 0.531597 0.6867842 0.3424264 0.6867842 0.4875677 0.6811218 0.4907623 0.6811218 0.3424264 0.5893752 0.3424264 0.5893752 0.5282956 0.5837128 0.5251009 0.5837128 0.3424264 0.5343552 0.4912 0.5350082 0.4875679 0.5406705 0.4907626 0.5389198 0.4948415 0.5406705 0.3424264 0.5837128 0.3424264 0.5837128 0.5251009 0.5406705 0.4907626 0.6339643 0.5346772 0.5878114 0.5346678 0.5893752 0.5282956 0.6324173 0.5282956 0.6827981 0.4943895 0.638488 0.531597 0.6380797 0.5251009 0.6811218 0.4907623</float_array>
          <technique_common>
            <accessor source="#tow_hitch_a-mesh-map-0-array" count="204" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_hitch_a-mesh-vertices">
          <input semantic="POSITION" source="#tow_hitch_a-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="46">
          <input semantic="VERTEX" source="#tow_hitch_a-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_hitch_a-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_hitch_a-mesh-map-0" offset="2" set="0"/>
          <vcount>8 4 4 4 8 4 4 4 4 4 8 4 4 4 6 8 4 4 4 6 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>8 0 0 9 0 1 10 0 2 11 0 3 2 0 4 3 0 5 0 0 6 1 0 7 57 1 8 59 2 9 10 3 10 9 4 11 58 5 12 54 6 13 2 7 14 11 8 15 53 9 16 55 10 17 6 11 18 5 12 19 4 13 20 5 13 21 6 13 22 7 13 23 14 13 24 15 13 25 12 13 26 13 13 27 52 14 28 1 15 29 0 16 30 53 9 31 54 6 32 7 17 33 6 11 34 55 10 35 56 18 36 13 19 37 12 20 38 57 1 39 58 5 40 11 8 41 10 3 42 59 2 43 52 14 44 56 18 45 8 21 46 1 15 47 27 22 48 30 23 49 23 24 50 25 24 51 24 24 52 21 24 53 22 24 54 20 24 55 30 23 56 27 22 57 29 25 58 32 26 59 32 26 60 29 25 61 28 27 62 33 28 63 33 28 64 28 27 65 26 29 66 31 30 67 16 31 68 17 31 69 19 31 70 18 31 71 31 30 72 26 29 73 45 32 74 38 33 75 40 33 76 39 33 77 42 33 78 43 33 79 41 33 80 48 34 81 48 34 82 50 35 83 47 36 84 45 32 85 50 35 86 51 37 87 46 38 88 47 36 89 51 37 90 49 39 91 44 40 92 46 38 93 34 41 94 44 40 95 49 39 96 36 41 97 37 41 98 35 41 99 17 42 100 16 43 101 34 43 102 35 44 103 19 45 104 17 42 105 35 44 106 37 46 107 18 47 108 19 45 109 37 46 110 36 47 111 25 48 112 23 49 113 41 50 114 43 48 115 24 51 116 25 48 117 43 48 118 42 52 119 21 53 120 24 51 121 42 52 122 39 54 123 22 55 124 21 53 125 39 54 126 40 55 127 20 56 128 22 55 129 40 55 130 38 57 131 30 58 132 32 59 133 50 60 134 48 61 135 29 62 136 27 63 137 45 64 138 47 65 139 32 59 140 33 66 141 51 67 142 50 60 143 28 68 144 29 62 145 47 65 146 46 69 147 33 66 148 31 70 149 49 71 150 51 67 151 26 72 152 28 68 153 46 69 154 44 73 155 16 74 156 26 72 157 44 73 158 34 74 159 31 70 160 18 75 161 36 75 162 49 71 163 27 63 164 20 56 165 38 57 166 45 64 167 23 49 168 30 58 169 48 61 170 41 50 171 4 76 172 13 19 173 56 18 174 52 14 175 14 77 176 58 5 177 59 2 178 15 78 179 8 21 180 56 18 181 57 1 182 9 4 183 2 7 184 54 6 185 55 10 186 3 79 187 4 76 188 52 14 189 53 9 190 5 12 191 0 16 192 3 79 193 55 10 194 53 9 195 14 77 196 7 17 197 54 6 198 58 5 199 12 20 200 15 78 201 59 2 202 57 1 203</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_beam_c_L-mesh" name="tow_beam_c_L">
      <mesh>
        <source id="tow_beam_c_L-mesh-positions">
          <float_array id="tow_beam_c_L-mesh-positions-array" count="96">0.4689809 -0.02549999 -0.02149999 0.4689809 -0.02149999 -0.02549999 0.4689809 -0.02149999 0.02549999 0.4689809 -0.02549999 0.02149999 0.03828036 -0.02149999 -0.02549999 0.03828036 -0.02549993 -0.02149999 0.03828036 -0.02149999 0.02549999 0.4689809 0.02149999 -0.02549999 0.4689809 0.02549999 0.02149999 0.4689809 0.02149999 0.02549999 0.03828036 0.02549999 -0.02149999 0.03828036 0.02149999 -0.02549999 0.03828036 0.02149999 0.02549999 0.03828036 0.02549999 0.02149999 0.468981 0.02549999 -0.02149999 0.4741238 -0.03000974 -0.02530235 0.4741238 -0.02530235 -0.03000974 0.4741238 -0.02530235 0.03000974 0.4741238 -0.03000974 0.02530235 0.4741238 0.02530235 -0.03000974 0.4741238 0.03000974 -0.02530235 0.4741238 0.03000974 0.02530235 0.4741238 0.02530235 0.03000974 0.03828042 -0.02549993 0.02149999 0.03313755 0.03000974 -0.02530235 0.03313755 0.02530235 -0.03000974 0.03313755 0.02530235 0.03000974 0.03313755 0.03000974 0.02530235 0.03313761 -0.02530235 -0.03000974 0.03313761 -0.03000974 -0.02530235 0.03313761 -0.03000974 0.02530235 0.03313761 -0.02530235 0.03000974</float_array>
          <technique_common>
            <accessor source="#tow_beam_c_L-mesh-positions-array" count="32" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_c_L-mesh-normals">
          <float_array id="tow_beam_c_L-mesh-normals-array" count="102">0.01204544 0.9963285 -0.08476114 0.01203745 0.9963275 0.08477491 -0.01204913 0.9963284 0.08476239 -0.01204264 0.9963273 -0.08477669 0.01204699 0.08476173 0.9963285 0.01203972 -0.08477509 0.9963275 -0.01204466 -0.08476191 0.9963285 -0.01203775 0.08477443 0.9963275 -0.01204419 -0.9963285 -0.0847615 -0.0120359 -0.9963275 0.08477509 0.01204812 -0.9963285 0.08476155 0.01204031 -0.9963274 -0.0847758 0.01203972 -0.08477503 -0.9963275 -0.0120148 -0.08481419 -0.9963244 -0.01203751 0.08477401 -0.9963275 0.01201659 0.0848149 -0.9963243 -0.6835506 0.06193917 0.7272703 -0.6835505 0.7272701 0.06194335 1 0 0 -0.6835489 -0.7272717 0.06194186 -0.6835453 -0.06194341 0.727275 -0.6835489 -0.06193953 -0.7272719 -0.6835463 -0.727274 -0.06194496 -0.6835551 0.727266 -0.06194037 -0.683548 0.06194108 -0.7272727 0.6835526 -0.06193971 0.7272684 0.6835496 -0.727271 0.06194221 -1 -1.30876e-7 0 0.6835504 0.7272704 0.06194049 0.683548 0.06194305 0.7272726 0.6835516 0.06194019 -0.7272693 0.6835471 0.7272733 -0.06194323 0.6835533 -0.7272678 -0.06193941 0.6835493 -0.06194233 -0.7272713</float_array>
          <technique_common>
            <accessor source="#tow_beam_c_L-mesh-normals-array" count="34" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_c_L-mesh-map-0">
          <float_array id="tow_beam_c_L-mesh-map-0-array" count="224">0.4748943 0.1082435 0.4748943 0.1498721 0.0506972 0.1498721 0.0506972 0.1082435 0.4748943 0.1553485 0.4748943 0.1969771 0.0506972 0.1969772 0.0506972 0.1553486 0.0506972 0.05566215 0.0506972 0.01403349 0.4748943 0.01403349 0.4748943 0.05566203 0.4748943 0.06113857 0.0506972 0.06113857 0.0506972 0.05566215 0.4748943 0.05566203 0.0506972 0.1969772 0.4748943 0.1969771 0.4748943 0.2024536 0.0506972 0.2024536 0.0506972 0.1027671 0.4748943 0.1027671 0.4748943 0.1082435 0.0506972 0.1082435 0.4748943 0.1553485 0.0506972 0.1553486 0.0506972 0.1498721 0.4748943 0.1498721 0.4748943 0.06113857 0.4748943 0.1027671 0.0506972 0.1027671 0.0506972 0.06113857 0.002330601 0.1686181 0.01255089 0.1669963 0.01255089 0.1584517 0.002298116 0.1570087 0.8723409 0.1713312 0.9139696 0.1713312 0.9178419 0.1674588 0.9178419 0.1258302 0.9139696 0.1219578 0.8723409 0.1219578 0.8684685 0.1258302 0.8684685 0.1674588 0.002514958 0.2420559 0.01255089 0.2404912 0.01255089 0.2319467 0.002371311 0.2305397 0.002371311 0.2305397 0.01255089 0.2319467 0.01255089 0.1669963 0.002330601 0.1686181 0.001077175 0.3105879 0.01255089 0.3139861 0.01255089 0.3054415 0.002998411 0.3021542 0.002298116 0.1570087 0.01255089 0.1584517 0.01255089 0.09350138 0.002448379 0.09498536 0.002633929 0.08355516 0.01255089 0.08495688 0.01255089 0.02000653 0.003215372 0.02508133 0.002448379 0.09498536 0.01255089 0.09350138 0.01255089 0.08495688 0.002633929 0.08355516 0.002998411 0.3021542 0.01255089 0.3054415 0.01255089 0.2404912 0.002514958 0.2420559 0.002330601 0.1686181 0.01255089 0.1669963 0.01255089 0.1584517 0.002298116 0.1570087 0.8723409 0.1713312 0.9139696 0.1713312 0.9178419 0.1674588 0.9178419 0.1258302 0.9139696 0.1219578 0.8723409 0.1219578 0.8684685 0.1258302 0.8684685 0.1674588 0.002514958 0.2420559 0.01255089 0.2404912 0.01255089 0.2319467 0.002371311 0.2305397 0.002371311 0.2305397 0.01255089 0.2319467 0.01255089 0.1669963 0.002330601 0.1686181 0.001077175 0.3105879 0.01255089 0.3139861 0.01255089 0.3054415 0.002998411 0.3021542 0.002298116 0.1570087 0.01255089 0.1584517 0.01255089 0.09350138 0.002448379 0.09498536 0.002633929 0.08355516 0.01255089 0.08495688 0.01255089 0.02000653 0.003215372 0.02508133 0.002448379 0.09498536 0.01255089 0.09350138 0.01255089 0.08495688 0.002633929 0.08355516 0.002998411 0.3021542 0.01255089 0.3054415 0.01255089 0.2404912 0.002514958 0.2420559</float_array>
          <technique_common>
            <accessor source="#tow_beam_c_L-mesh-map-0-array" count="112" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_beam_c_L-mesh-vertices">
          <input semantic="POSITION" source="#tow_beam_c_L-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="26">
          <input semantic="VERTEX" source="#tow_beam_c_L-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_beam_c_L-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_beam_c_L-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 8 4 4 4 4 4 4 4 4 8 4 4 4 4 4 4 4 </vcount>
          <p>10 0 0 13 1 1 8 2 2 14 3 3 12 4 4 6 5 5 2 6 6 9 7 7 0 8 8 3 9 9 23 10 10 5 11 11 4 12 12 1 13 13 0 8 14 5 11 15 2 6 16 6 5 17 23 10 18 3 9 19 7 14 20 11 15 21 10 0 22 14 3 23 12 4 24 9 7 25 8 2 26 13 1 27 4 12 28 11 15 29 7 14 30 1 13 31 9 7 32 22 16 33 21 17 34 8 2 35 16 18 36 19 18 37 20 18 38 21 18 39 22 18 40 17 18 41 18 18 42 15 18 43 3 9 44 18 19 45 17 20 46 2 6 47 2 6 48 17 20 49 22 16 50 9 7 51 1 13 52 16 21 53 15 22 54 0 8 55 8 2 56 21 17 57 20 23 58 14 3 59 7 14 60 19 24 61 16 21 62 1 13 63 14 3 64 20 23 65 19 24 66 7 14 67 0 8 68 15 22 69 18 19 70 3 9 71 6 5 72 31 25 73 30 26 74 23 10 75 25 27 76 28 27 77 29 27 78 30 27 79 31 27 80 26 27 81 27 27 82 24 27 83 13 1 84 27 28 85 26 29 86 12 4 87 12 4 88 26 29 89 31 25 90 6 5 91 11 15 92 25 30 93 24 31 94 10 0 95 23 10 96 30 26 97 29 32 98 5 11 99 4 12 100 28 33 101 25 30 102 11 15 103 5 11 104 29 32 105 28 33 106 4 12 107 10 0 108 24 31 109 27 28 110 13 1 111</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_beam_b-mesh" name="tow_beam_b">
      <mesh>
        <source id="tow_beam_b-mesh-positions">
          <float_array id="tow_beam_b-mesh-positions-array" count="48">0.4060216 -0.02549999 -0.02149999 0.4060216 -0.02149999 -0.02549999 0.4060216 -0.02149999 0.02549999 0.4060216 -0.02549999 0.02149999 -0.4060216 -0.02149993 -0.02549999 -0.4060216 -0.02549993 -0.02149999 -0.4060216 -0.02549993 0.02149999 -0.4060216 -0.02149993 0.02549999 0.4060216 0.02149993 -0.02549999 0.4060216 0.02549993 -0.02149999 0.4060216 0.02549993 0.02149999 0.4060216 0.02149993 0.02549999 -0.4060216 0.02549999 -0.02149999 -0.4060216 0.02149999 -0.02549999 -0.4060216 0.02149999 0.02549999 -0.4060216 0.02549999 0.02149999</float_array>
          <technique_common>
            <accessor source="#tow_beam_b-mesh-positions-array" count="16" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_b-mesh-normals">
          <float_array id="tow_beam_b-mesh-normals-array" count="54">1 0 0 0 0.9963964 -0.08481979 1.33621e-4 0.9963963 0.08481979 0 0.9963964 0.08481979 1.33621e-4 0.9963963 -0.08481979 0 0.08481979 0.9963964 0 -0.08481973 0.9963963 0 -0.08481979 0.9963964 -1.56839e-4 0.08481979 0.9963963 0 -0.9963963 -0.08481973 0 -0.9963963 0.08481973 0 -0.9963964 0.08481979 -1.04646e-4 -0.9963964 -0.08481985 -1 0 0 0 -0.08481973 -0.9963963 9.86927e-5 -0.08481985 -0.9963964 0 0.08481979 -0.9963964 -1.56839e-4 0.08481979 -0.9963963</float_array>
          <technique_common>
            <accessor source="#tow_beam_b-mesh-normals-array" count="18" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_b-mesh-map-0">
          <float_array id="tow_beam_b-mesh-map-0-array" count="96">0.8684685 0.1258302 0.8723409 0.1219578 0.9139696 0.1219578 0.9178419 0.1258302 0.9178419 0.1674588 0.9139696 0.1713312 0.8723409 0.1713312 0.8684685 0.1674588 0.8368416 0.1082434 0.8368416 0.149872 0.0506972 0.149872 0.0506972 0.1082434 0.8368416 0.1553485 0.8368416 0.1969771 0.0506972 0.1969771 0.0506972 0.1553485 0.0506972 0.05566203 0.0506972 0.01403349 0.8368416 0.01403349 0.8368416 0.05566203 0.8684685 0.1258302 0.8723409 0.1219577 0.9139696 0.1219577 0.9178419 0.1258302 0.9178419 0.1674588 0.9139696 0.1713312 0.8723409 0.1713312 0.8684685 0.1674588 0.8368416 0.06113857 0.0506972 0.06113857 0.0506972 0.05566203 0.8368416 0.05566203 0.0506972 0.1969771 0.8368416 0.1969771 0.8368416 0.2024535 0.0506972 0.2024536 0.0506972 0.1027671 0.8368416 0.1027671 0.8368416 0.1082434 0.0506972 0.1082434 0.8368416 0.1553485 0.0506972 0.1553485 0.0506972 0.149872 0.8368416 0.149872 0.8368416 0.06113857 0.8368416 0.1027671 0.0506972 0.1027671 0.0506972 0.06113857</float_array>
          <technique_common>
            <accessor source="#tow_beam_b-mesh-map-0-array" count="48" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_beam_b-mesh-vertices">
          <input semantic="POSITION" source="#tow_beam_b-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="10">
          <input semantic="VERTEX" source="#tow_beam_b-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_beam_b-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_beam_b-mesh-map-0" offset="2" set="0"/>
          <vcount>8 4 4 4 8 4 4 4 4 4 </vcount>
          <p>8 0 0 9 0 1 10 0 2 11 0 3 2 0 4 3 0 5 0 0 6 1 0 7 12 1 8 15 2 9 10 3 10 9 4 11 14 5 12 7 6 13 2 7 14 11 8 15 0 9 16 3 10 17 6 11 18 5 12 19 4 13 20 5 13 21 6 13 22 7 13 23 14 13 24 15 13 25 12 13 26 13 13 27 4 14 28 1 15 29 0 9 30 5 12 31 2 7 32 7 6 33 6 11 34 3 10 35 8 16 36 13 17 37 12 1 38 9 4 39 14 5 40 11 8 41 10 3 42 15 2 43 4 14 44 13 17 45 8 16 46 1 15 47</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_beam_a-mesh" name="tow_beam_a">
      <mesh>
        <source id="tow_beam_a-mesh-positions">
          <float_array id="tow_beam_a-mesh-positions-array" count="96">0.2714611 -0.02264493 -0.02031737 0.268606 -0.02549999 -0.02149999 0.2714611 -0.02031737 -0.02264493 0.268606 -0.02149999 -0.02549999 0.2714611 -0.02031737 0.02264493 0.268606 -0.02149999 0.02549999 0.2714611 -0.02264493 0.02031737 0.268606 -0.02549999 0.02149999 -0.2714611 -0.02031737 -0.02264493 -0.268606 -0.02149999 -0.02549999 -0.2714611 -0.02264493 -0.02031737 -0.268606 -0.02549993 -0.02149999 -0.2714611 -0.02264493 0.02031737 -0.268606 -0.02549993 0.02149999 -0.2714611 -0.02031737 0.02264493 -0.268606 -0.02149999 0.02549999 0.2714611 0.02031737 -0.02264493 0.268606 0.02149999 -0.02549999 0.2714611 0.02264493 -0.02031737 0.268606 0.02549999 -0.02149999 0.2714611 0.02264493 0.02031737 0.268606 0.02549999 0.02149999 0.2714611 0.02031737 0.02264493 0.268606 0.02149999 0.02549999 -0.2714611 0.02264493 -0.02031737 -0.268606 0.02549999 -0.02149999 -0.2714611 0.02031737 -0.02264493 -0.268606 0.02149999 -0.02549999 -0.2714611 0.02031737 0.02264493 -0.268606 0.02149999 0.02549999 -0.2714611 0.02264493 0.02031737 -0.268606 0.02549999 0.02149999</float_array>
          <technique_common>
            <accessor source="#tow_beam_a-mesh-positions-array" count="32" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_a-mesh-normals">
          <float_array id="tow_beam_a-mesh-normals-array" count="96">-0.005203843 -0.08469444 -0.9963935 -0.005182623 0.08472174 -0.9963912 0.005203723 0.08469444 -0.9963934 0.005182504 -0.08472174 -0.9963912 -0.005182564 0.0847218 0.9963912 0.005160927 0.08474957 0.9963889 0.005183041 0.9963909 0.08472496 -0.005161821 0.9963887 0.08475232 -0.9982472 -0.004182994 -0.05903345 -0.9982473 -0.05903244 -0.00418204 -0.9982474 -0.05903083 0.004185616 -0.9982472 -0.004179298 0.05903518 -0.9982473 0.004183173 0.05903351 -0.9982473 0.05903267 0.0041821 -0.9982474 0.05903095 -0.004185676 -0.9982472 0.004179418 -0.0590353 0.9982473 0.004183053 -0.05903369 0.9982473 0.05903244 -0.00418204 0.9982474 0.05903083 0.004185616 0.9982472 0.004179298 0.05903548 0.9982473 -0.004183173 0.05903381 0.9982474 -0.05903255 0.00418204 0.9982474 -0.05903095 -0.004185616 0.9982472 -0.004179477 -0.05903542 -0.005183041 0.9963909 -0.08472496 0.005161821 0.9963887 -0.08475226 0.005204677 -0.9963932 -0.084697 0.0051831 -0.9963909 0.0847249 -0.005204737 -0.9963932 0.084697 -0.00518316 -0.9963909 -0.0847249 -0.005182623 -0.0847218 0.9963912 0.005203604 -0.08469444 0.9963934</float_array>
          <technique_common>
            <accessor source="#tow_beam_a-mesh-normals-array" count="32" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_beam_a-mesh-map-0">
          <float_array id="tow_beam_a-mesh-map-0-array" count="224">0.659325 0.08439135 0.659325 0.12602 0.1392465 0.12602 0.1392465 0.08439147 0.659325 0.1786014 0.1392465 0.1786015 0.1392465 0.173125 0.6593251 0.1731249 0.8734858 0.1685672 0.8712324 0.1663139 0.8712325 0.1269751 0.8734858 0.1247218 0.9128248 0.1247218 0.9150781 0.1269751 0.9150781 0.1663139 0.9128248 0.1685672 0.9128248 0.1247217 0.9150779 0.1269751 0.9150781 0.166314 0.9128248 0.1685673 0.8734859 0.1685673 0.8712325 0.166314 0.8712325 0.1269751 0.8734858 0.1247217 0.1392465 0.1260201 0.6593251 0.12602 0.659325 0.1314963 0.1392465 0.1314964 0.1392465 0.07891499 0.1392465 0.03728646 0.659325 0.03728634 0.659325 0.07891488 0.659325 0.1786013 0.659325 0.2202299 0.1392465 0.2202301 0.1392465 0.1786015 0.659325 0.1314963 0.659325 0.1731249 0.1392465 0.173125 0.1392465 0.1314964 0.1392465 0.2202301 0.6593251 0.2202299 0.659325 0.2257064 0.1392465 0.2257065 0.6620889 0.08005976 0.6620889 0.08324646 0.659325 0.08439135 0.6593251 0.07891488 0.1364825 0.08324658 0.1364825 0.08005988 0.1392465 0.07891499 0.1392465 0.08439147 0.1364825 0.2245616 0.1364825 0.2213749 0.1392465 0.2202301 0.1392465 0.2257065 0.662089 0.2213748 0.6620889 0.2245615 0.659325 0.2257064 0.6593251 0.2202299 0.1364825 0.1303516 0.1364825 0.1271649 0.1392465 0.1260201 0.1392465 0.1314964 0.662089 0.1271648 0.6620889 0.1303515 0.659325 0.1314963 0.6593251 0.12602 0.662089 0.1742697 0.6620889 0.1774564 0.659325 0.1786014 0.6593251 0.1731249 0.1364825 0.1774566 0.1364825 0.1742699 0.1392465 0.173125 0.1392465 0.1786015 0.6620889 0.08553624 0.6620889 0.1248751 0.659325 0.12602 0.659325 0.08439135 0.1364825 0.1248752 0.1364825 0.08553636 0.1392465 0.08439147 0.1392465 0.12602 0.1364825 0.07777011 0.1364825 0.03843134 0.1392465 0.03728646 0.1392465 0.07891499 0.6620889 0.03843122 0.6620889 0.07776999 0.659325 0.07891488 0.659325 0.03728634 0.1392465 0.1314964 0.1392465 0.173125 0.1364825 0.1719801 0.1364825 0.1326413 0.1392465 0.1786015 0.1392465 0.2202301 0.1364825 0.2190852 0.1364825 0.1797463 0.6620889 0.1797462 0.6620889 0.219085 0.659325 0.2202299 0.659325 0.1786013 0.659325 0.1731249 0.659325 0.1314963 0.6620889 0.1326412 0.6620889 0.17198 0.659325 0.08439135 0.1392465 0.08439147 0.1392465 0.07891499 0.6593251 0.07891488</float_array>
          <technique_common>
            <accessor source="#tow_beam_a-mesh-map-0-array" count="112" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_beam_a-mesh-vertices">
          <input semantic="POSITION" source="#tow_beam_a-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="26">
          <input semantic="VERTEX" source="#tow_beam_a-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_beam_a-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_beam_a-mesh-map-0" offset="2" set="0"/>
          <vcount>4 4 8 8 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>9 0 0 27 1 1 17 2 2 3 3 3 29 4 4 23 5 5 21 6 6 31 7 7 8 8 8 10 9 9 12 10 10 14 11 11 28 12 12 30 13 13 24 14 14 26 15 15 16 16 16 18 17 17 20 18 18 22 19 19 4 20 20 6 21 21 0 22 22 2 23 23 17 2 24 27 1 25 25 24 26 19 25 27 1 26 28 7 27 29 13 28 30 11 29 31 29 4 32 15 30 33 5 31 34 23 5 35 25 24 36 31 7 37 21 6 38 19 25 39 5 31 40 15 30 41 13 28 42 7 27 43 10 9 44 8 8 45 9 0 46 11 29 47 2 23 48 0 22 49 1 26 50 3 3 51 6 21 52 4 20 53 5 31 54 7 27 55 14 11 56 12 10 57 13 28 58 15 30 59 18 17 60 16 16 61 17 2 62 19 25 63 26 15 64 24 14 65 25 24 66 27 1 67 30 13 68 28 12 69 29 4 70 31 7 71 22 19 72 20 18 73 21 6 74 23 5 75 8 8 76 26 15 77 27 1 78 9 0 79 16 16 80 2 23 81 3 3 82 17 2 83 0 22 84 6 21 85 7 27 86 1 26 87 12 10 88 10 9 89 11 29 90 13 28 91 19 25 92 21 6 93 20 18 94 18 17 95 23 5 96 5 31 97 4 20 98 22 19 99 28 12 100 14 11 101 15 30 102 29 4 103 31 7 104 25 24 105 24 14 106 30 13 107 9 0 108 3 3 109 1 26 110 11 29 111</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="tow_ball-mesh" name="tow_ball">
      <mesh>
        <source id="tow_ball-mesh-positions">
          <float_array id="tow_ball-mesh-positions-array" count="582">0 0.252784 0.01924693 0.00441575 0.244854 0.02343142 0.007789134 0.2506969 0.01924693 0.01017695 0.2548328 0.01209956 0.01101547 0.2562851 0.003668665 0.01017695 0.2548328 -0.004762172 0.007789134 0.2506969 -0.01190954 0.007648289 0.2416214 0.02343142 0.01349115 0.2449948 0.01924693 0.01762706 0.2473827 0.01209956 0.01907938 0.2482212 0.003668665 0.01762706 0.2473827 -0.004762172 0.01349115 0.2449948 -0.01190954 0.008831501 0.2372057 0.02343142 0.01557826 0.2372057 0.01924693 0.02035397 0.2372057 0.01209956 0.02203094 0.2372057 0.003668665 0.02035397 0.2372057 -0.004762172 0.01557826 0.2372057 -0.01190954 0.007648289 0.2327899 0.02343142 0.01349115 0.2294166 0.01924693 0.01762706 0.2270287 0.01209956 0.01907938 0.2261902 0.003668665 0.01762706 0.2270287 -0.004762172 0.01349115 0.2294166 -0.01190954 0.00441575 0.2295574 0.02343142 0.007789134 0.2237145 0.01924693 0.01017695 0.2195786 0.01209956 0.01101547 0.2181263 0.003668665 0.01017695 0.2195786 -0.004762172 0.007789134 0.2237145 -0.01190954 0 0.2283742 0.02343142 0 0.2216274 0.01924693 0 0.2168517 0.01209956 0 0.2151747 0.003668665 0 0.2168517 -0.004762172 0 0.2216274 -0.01190954 -0.00441575 0.2295574 0.02343142 -0.007789134 0.2237145 0.01924693 -0.01017695 0.2195786 0.01209956 -0.01101547 0.2181263 0.003668665 -0.01017695 0.2195786 -0.004762172 -0.007789134 0.2237145 -0.01190954 0 0.2372057 0.02343142 -0.007648289 0.2327899 0.02343142 -0.01349115 0.2294166 0.01924693 -0.01762706 0.2270287 0.01209956 -0.01907938 0.2261902 0.003668665 -0.01762706 0.2270287 -0.004762172 -0.01349115 0.2294166 -0.01190954 -0.008831501 0.2372057 0.02343142 -0.01557826 0.2372057 0.01924693 -0.02035397 0.2372057 0.01209956 -0.02203094 0.2372057 0.003668665 -0.02035397 0.2372057 -0.004762172 -0.01557826 0.2372057 -0.01190954 -0.007648289 0.2416214 0.02343142 -0.01349115 0.2449948 0.01924693 -0.01762706 0.2473827 0.01209956 -0.01907938 0.2482212 0.003668665 -0.01762706 0.2473827 -0.004762172 -0.01349115 0.2449948 -0.01190954 -0.00441575 0.244854 0.02343142 -0.007789075 0.2506969 0.01924693 -0.01017695 0.2548328 0.01209956 -0.01101547 0.2562851 0.003668665 -0.01017695 0.2548328 -0.004762172 -0.007789075 0.2506969 -0.01190954 0 0.2460372 0.02343142 0 0.2575597 0.01209956 0 0.2592368 0.003668665 0 0.2575597 -0.004762172 0 0.252784 -0.01190954 0.007789134 0.2506969 -0.01589101 0.01349115 0.2449948 -0.01589101 0.01557826 0.2372057 -0.01589101 0.01349115 0.2294166 -0.01589101 0.007789134 0.2237145 -0.01589101 0 0.2216274 -0.01589101 -0.007789134 0.2237145 -0.01589101 -0.01349115 0.2294166 -0.01589101 -0.01557826 0.2372057 -0.01589101 -0.01349115 0.2449948 -0.01589101 -0.007789075 0.2506969 -0.01589101 0 0.252784 -0.01589101 0.01161915 0.2573307 -0.04230266 0.02012497 0.2488249 -0.04230266 0.02012497 0.2372057 -0.04230266 0.02012497 0.2255865 -0.04230266 0.01161915 0.2170807 -0.04230266 0 0.2139674 -0.04230266 -0.01161915 0.2170807 -0.04230266 -0.02012497 0.2255865 -0.04230266 -0.02012497 0.2372057 -0.04230266 -0.02012497 0.2488249 -0.04230266 -0.01161915 0.2573307 -0.04230266 0 0.2604441 -0.04230266 0.02012497 0.2372057 -0.03017276 -0.01161915 0.2573307 -0.03622096 -0.02012497 0.2488249 -0.03622096 -0.02012497 0.2255865 -0.03622096 -0.01161915 0.2170807 -0.03622096 0.01161915 0.2170807 -0.03622096 0.02012497 0.2255865 -0.03622096 0 0.2604441 -0.03622096 0.02012497 0.2488249 -0.03622096 0.01161915 0.2573307 -0.03622096 -0.02012497 0.2372057 -0.03017276 0 0.2139674 -0.03622096 0 0.2211775 -0.05662339 0 0.2211775 -0.07067036 0.01388084 0.2291916 -0.05662339 0.01388084 0.2291916 -0.07067036 0.01388084 0.2452198 -0.05662345 0.01388084 0.2452198 -0.07067036 0 0.2532339 -0.05662345 0 0.2532339 -0.07067036 -0.01388072 0.2452198 -0.05662345 -0.01388072 0.2452198 -0.07067036 -0.01388072 0.2291916 -0.05662339 -0.01388072 0.2291916 -0.07067036 0.006940484 0.2251846 -0.05781334 0.006940484 0.2251846 -0.07295072 0.01388084 0.2372057 -0.05781334 0.01388084 0.2372057 -0.07295072 0.006940484 0.2492269 -0.05781334 0.006940484 0.2492268 -0.07295072 -0.006940305 0.2492269 -0.05781334 -0.006940305 0.2492268 -0.07295072 -0.01388072 0.2372057 -0.05781334 -0.01388072 0.2372057 -0.07295072 -0.006940305 0.2251846 -0.05781334 -0.006940305 0.2251846 -0.07295072 0 0.2241442 -0.07295066 0.01131159 0.230675 -0.07295072 0.01131159 0.2437365 -0.07295072 0 0.2502672 -0.07295072 -0.01131147 0.2437365 -0.07295072 -0.01131147 0.230675 -0.07295072 0.006940484 0.2251846 -0.07295072 0.01388084 0.2372057 -0.07295072 0.006940484 0.2492268 -0.07295072 -0.006940305 0.2492268 -0.07295072 -0.01388072 0.2372057 -0.07295072 -0.006940305 0.2251846 -0.07295072 0 0.2160018 -0.05478495 0.01836323 0.2266038 -0.05478495 0.01836323 0.2478077 -0.05478495 0 0.2584097 -0.05478495 -0.01836305 0.2478077 -0.05478495 -0.01836305 0.2266038 -0.05478495 0.01060205 0.2188426 -0.05478495 0.02120405 0.2372057 -0.05478495 0.01060205 0.2555689 -0.05478495 -0.01060187 0.2555689 -0.05478495 -0.02120387 0.2372057 -0.05478495 -0.01060187 0.2188426 -0.05478495 0 0.2160018 -0.05263805 0.01836323 0.2266038 -0.05263811 0.01836323 0.2478077 -0.05263811 0 0.2584097 -0.05263811 -0.01836305 0.2478077 -0.05263811 -0.01836305 0.2266038 -0.05263811 0.01060205 0.2188426 -0.05263805 0.02120405 0.2372057 -0.05263811 0.01060205 0.2555689 -0.05263811 -0.01060187 0.2555689 -0.05263811 -0.02120387 0.2372057 -0.05263811 -0.01060187 0.2188426 -0.05263805 0 0.2372057 -0.0769729 0.00793755 0.232623 -0.07294422 0.004582762 0.2292682 -0.07294422 -0.004582583 0.2451432 -0.07294422 0 0.2463712 -0.07294422 -0.007937371 0.232623 -0.07294422 -0.009165346 0.2372057 -0.07294422 0 0.2280403 -0.07294422 0.00793755 0.2417884 -0.07294422 0.009165465 0.2372057 -0.07294422 -0.007937371 0.2417884 -0.07294422 -0.004582583 0.2292682 -0.07294422 0.004582762 0.2451432 -0.07294422 0.004126429 0.2300586 -0.07696634 0 0.228953 -0.07696634 0.008252799 0.2372057 -0.07696634 0.007147133 0.2330793 -0.07696634 0.004126429 0.2443528 -0.0769664 0.007147133 0.2413321 -0.0769664 -0.00412625 0.2443528 -0.0769664 0 0.2454584 -0.0769664 -0.00825262 0.2372057 -0.07696634 -0.007146954 0.2413321 -0.0769664 -0.00412625 0.2300586 -0.07696634 -0.007146954 0.2330793 -0.07696634</float_array>
          <technique_common>
            <accessor source="#tow_ball-mesh-positions-array" count="194" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_ball-mesh-normals">
          <float_array id="tow_ball-mesh-normals-array" count="747">-1.4882e-7 0.9283308 -0.3717555 0 1 0 0.5000001 0.8660255 3.15147e-7 0.4641658 0.8039576 -0.3717557 0 0.9283307 0.3717557 0 0.7123124 0.7018627 0.3561562 0.6168807 0.7018625 0.4641659 0.8039578 0.3717553 0 -8.43639e-7 1 0 0 1 -1.86574e-7 -7.30613e-7 1 0 0.9525097 -0.3045082 0.4762549 0.8248974 -0.3045089 1.5951e-7 0.5270749 0.8498189 0.2635366 0.4564597 0.8498194 0.8039579 0.4641656 -0.3717553 0.8248975 0.4762547 -0.3045088 0.8039581 0.4641658 0.3717549 0.8660254 0.5 2.32485e-7 0.4564594 0.2635365 0.8498196 0.6168805 0.3561561 0.7018628 -5.59722e-7 -4.21818e-7 1 0.9283308 0 0.3717551 1 0 0 0.5270747 0 0.849819 0.7123122 0 0.7018628 0.9283308 0 -0.3717554 -7.46298e-7 0 1 0.9525095 0 -0.3045092 0.8660255 -0.5 -2.37651e-7 0.8039579 -0.4641657 -0.3717554 0.6168803 -0.3561564 0.7018628 0.8039579 -0.4641658 0.371755 -7.46298e-7 4.21819e-7 1 0.8248972 -0.4762552 -0.3045086 0.4564594 -0.2635369 0.8498195 0.3561562 -0.6168805 0.7018626 0.4641655 -0.8039582 0.371755 -3.73148e-7 7.30613e-7 1 0.4641654 -0.8039579 -0.3717555 0.4762551 -0.8248975 -0.3045082 0.4999995 -0.8660257 -3.15147e-7 0.2635368 -0.4564593 0.8498197 0 -0.9283307 -0.3717554 0 -0.9525097 -0.3045086 0 -0.9283309 0.371755 0 -1 0 0 -0.5270738 0.8498196 0 -0.7123124 0.7018626 0 8.43641e-7 1 -0.2635368 -0.4564589 0.8498198 -0.3561562 -0.6168806 0.7018626 -0.4999995 -0.8660257 -3.15147e-7 -0.4641653 -0.8039581 -0.3717553 -0.4641655 -0.8039583 0.3717547 1.86574e-7 7.30613e-7 1 -0.476255 -0.8248976 -0.304508 -0.8660254 -0.5 -2.37651e-7 -0.8039577 -0.4641657 -0.3717555 -0.6168801 -0.3561562 0.7018631 -0.8039579 -0.4641658 0.3717551 5.59722e-7 4.21818e-7 1 -0.8248972 -0.4762552 -0.3045089 -0.4564595 -0.2635369 0.8498195 7.46295e-7 0 1 -0.9283307 0 -0.3717555 -0.9525094 0 -0.3045094 -0.9283308 0 0.3717553 -1 0 1.60156e-7 -0.5270747 0 0.849819 -0.7123122 0 0.7018629 -0.803958 0.4641657 -0.3717551 -0.8248975 0.476255 -0.3045085 -0.803958 0.4641657 0.3717549 -0.8660255 0.5 0 -0.4564596 0.2635368 0.8498195 -0.6168805 0.3561561 0.7018628 7.46295e-7 -4.21818e-7 1 -0.5000001 0.8660253 0 -0.4641659 0.803958 -0.3717548 -0.356156 0.6168805 0.7018628 -0.4641659 0.8039578 0.3717553 3.73148e-7 -7.30611e-7 1 -0.4762548 0.8248978 -0.3045076 -0.2635367 0.4564591 0.8498197 0 -0.9828081 0.18463 0.4914044 -0.8511367 0.1846298 0.8582258 0.4808776 0.1794585 0.4914042 0.8511369 0.1846297 -0.8582257 0.4808776 0.1794589 -0.9851986 0 0.1714173 3.38785e-7 0.9828082 0.1846297 -0.4914039 0.8511371 0.1846296 -0.8582256 -0.4808779 0.179459 0.9851986 0 0.1714171 0.8582255 -0.4808779 0.179459 -0.4914044 -0.8511368 0.1846297 0.9767787 0 0.214251 0.9006178 -0.4096627 0.1451355 -0.9006178 0.4096624 0.1451354 -0.4923548 0.8527842 0.1742013 -0.4923554 -0.8527837 0.1742014 -0.9006178 -0.4096627 0.1451354 0.4923555 -0.8527837 0.1742016 9.23449e-7 0.9847102 0.1742012 0.9006177 0.4096627 0.1451351 0.4923553 0.8527838 0.1742012 -0.9767787 0 0.2142512 0 -0.9847101 0.1742016 -0.5000005 -0.8660252 -2.40035e-7 0 -1 -2.40035e-7 0.9238796 -0.3826834 0 1 0 0 -0.4999997 0.8660256 0 -0.9238798 0.3826832 0 -0.9238796 -0.3826834 0 0.5000005 -0.8660252 0 1.25709e-6 1 -6.74841e-7 0.9238795 0.3826836 0 0.5000003 0.8660253 -6.74841e-7 -1 0 0 -0.6197885 -0.3578359 -0.698438 -0.8160497 -0.4711465 -0.3347894 -0.435102 -0.7536213 -0.492688 -0.4246615 -0.7355352 -0.5278737 -0.8493223 -4.35961e-7 -0.5278747 -0.8702069 -2.78215e-7 -0.4926865 -0.6197882 0.3578349 -0.6984388 -0.8160495 0.4711464 -0.3347902 -0.4246614 0.7355341 -0.5278752 -0.4351035 0.7536193 -0.4926896 2.87032e-7 0.715669 -0.6984397 2.16833e-7 0.9422921 -0.334792 0.4246615 0.735534 -0.5278755 0.435104 0.753619 -0.4926898 0.6197875 0.3578346 -0.6984395 0.8160496 0.4711459 -0.3347902 0.849322 -4.78949e-7 -0.5278752 0.8702069 -2.66376e-7 -0.4926865 0.6197881 -0.3578354 -0.6984388 0.8160501 -0.4711465 -0.3347885 0.4351033 -0.7536223 -0.4926853 0.2930228 -0.5075331 -0.8102764 0.5277693 -0.3047068 -0.7928514 0.5860461 -4.38086e-7 -0.8102777 0.293022 0.5075252 -0.8102817 0 0.6094074 -0.7928574 0.5277673 0.3047055 -0.7928534 -0.2930219 0.5075258 -0.8102813 -0.5277675 -0.3047059 -0.7928529 -0.2930205 -0.5075299 -0.8102792 0.3981494 -0.864386 -0.3071057 0.09096807 -0.6068934 -0.7895603 -0.398149 -0.8643853 -0.3071085 -0.09096741 -0.6068896 -0.7895632 -0.5860461 -4.57556e-7 -0.8102777 -0.5277671 0.3047059 -0.7928532 -0.2997677 -0.6827583 -0.6663187 -0.2132307 -0.8328925 -0.510708 -0.4252216 -0.7365055 -0.5260668 0.4246618 -0.735535 -0.5278739 0.7382836 -0.4262492 -0.5227323 0.4252213 -0.7365043 -0.5260686 2.28388e-7 0.8524976 -0.5227311 0.4252217 0.7365046 -0.526068 -0.7382849 -0.4262499 -0.5227299 -0.8504426 -3.05341e-7 -0.5260679 -0.7382849 0.4262492 -0.5227304 -0.4252218 0.7365049 -0.5260677 0.2997685 -0.682758 -0.6663186 0.2132308 -0.8328921 -0.5107088 0.7382842 0.426249 -0.5227316 0.8504416 -3.59412e-7 -0.5260695 -0.4999983 0.8660264 0 -1.47315e-7 1 0 0.5 -0.8660255 -2.55736e-6 0.2588191 -0.9659259 0 -0.8660251 0.5000005 0 -1 1.37494e-7 0 0.8660251 -0.5000006 -3.83604e-6 1 1.27673e-7 -2.55736e-6 -0.8660252 -0.5000005 0 -0.4999999 -0.8660255 0 0.8660251 0.5000005 -1.27868e-6 0.4999983 0.8660265 0 -0.2588191 -0.9659258 0 -1.7299e-7 -0.9752069 -0.2212952 9.64922e-7 -0.9752069 -0.2212952 0.4876047 -0.8445543 -0.2212911 0.4876039 -0.8445547 -0.2212911 0 -1.12315e-6 -1 0 7.87242e-4 -0.9999998 3.93564e-4 6.81772e-4 -0.9999998 -6.83289e-4 3.92059e-4 -0.9999997 -3.93565e-4 6.81774e-4 -0.9999997 6.8286e-4 -3.94526e-4 -0.9999997 3.94846e-4 -6.83336e-4 -0.9999997 -6.8286e-4 -3.94523e-4 -0.9999998 -7.88837e-4 -1.80591e-6 -0.9999998 7.88837e-4 -1.80464e-6 -0.9999998 6.8329e-4 3.92059e-4 -0.9999998 0 -7.89048e-4 -0.9999997 -3.94844e-4 -6.83337e-4 -0.9999997 0.001324474 -7.64134e-4 -0.9999989 7.37474e-4 -0.001276433 -0.9999989 7.37474e-4 -0.001276433 -0.999999 0.001324474 -7.64135e-4 -0.9999989 -7.36876e-4 -0.001276433 -0.9999989 -0.001323878 -7.64137e-4 -0.9999989 -0.001323878 -7.64137e-4 -0.9999989 -7.36877e-4 -0.001276433 -0.999999 7.37477e-4 0.00127536 -0.9999989 0.001323878 7.64137e-4 -0.9999989 0.001323878 7.64138e-4 -0.9999989 7.37477e-4 0.00127536 -0.999999 0 -0.001528263 -0.9999989 0 -0.001528263 -0.9999989 -0.001474022 0 -0.9999989 -0.001324474 7.64137e-4 -0.9999989 -0.001324474 7.64137e-4 -0.9999989 -0.001474022 0 -0.999999 0 0.001526117 -0.9999989 0 0.001526117 -0.9999989 0.001474022 0 -0.9999989 0.001474022 0 -0.999999 -7.38072e-4 0.00127536 -0.9999989 -7.38073e-4 0.00127536 -0.999999 0.8445536 -0.4876049 -0.2212932 0.8445541 -0.4876039 -0.2212932 0.9752077 0 -0.221292 0.9752076 -2.44221e-7 -0.221292 0.8445544 0.4876047 -0.2212911 0.8445543 0.4876048 -0.2212911 0.4876037 0.8445543 -0.2212936 0.4876031 0.8445546 -0.2212936 -8.64949e-7 0.9752069 -0.2212951 3.02721e-7 0.975207 -0.2212951 -0.4876021 0.8445547 -0.2212956 -0.4876033 0.844554 -0.2212955 -0.8445534 0.4876031 -0.2212979 -0.8445529 0.487604 -0.2212979 -0.9752062 2.5542e-7 -0.2212985 -0.9752062 0 -0.2212985 -0.8445528 -0.4876036 -0.221299 -0.8445529 -0.4876035 -0.221299 -0.4876027 -0.8445531 -0.2213001 -0.4876036 -0.8445526 -0.2213001 0 0 -1 0 3.19993e-7 1</float_array>
          <technique_common>
            <accessor source="#tow_ball-mesh-normals-array" count="249" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_ball-mesh-map-0">
          <float_array id="tow_ball-mesh-map-0-array" count="1536">0.09603077 0.7751753 0.09603935 0.767118 0.1055894 0.7666179 0.1065556 0.774416 0.09601145 0.7599837 0.09590643 0.7523682 0.1035482 0.7516336 0.1047161 0.7594068 0.7016894 0.5083833 0.6914426 0.51113 0.6989437 0.5036287 0.09637379 0.7859765 0.09603077 0.7751753 0.1065556 0.774416 0.1079199 0.7855637 0.09603935 0.767118 0.09601145 0.7599837 0.1047161 0.7594068 0.1055894 0.7666179 0.09590643 0.7523682 0.09550154 0.7430536 0.1017819 0.7416745 0.1035482 0.7516336 0.1079199 0.7855637 0.1065556 0.774416 0.1170677 0.7727499 0.1194055 0.7830698 0.1055894 0.7666179 0.1047161 0.7594068 0.1133317 0.7575452 0.1152117 0.7647196 0.1035482 0.7516336 0.1017819 0.7416745 0.1082184 0.7406005 0.1112205 0.7499942 0.1065556 0.774416 0.1055894 0.7666179 0.1152117 0.7647196 0.1170677 0.7727499 0.1047161 0.7594068 0.1035482 0.7516336 0.1112205 0.7499942 0.1133317 0.7575452 0.6989437 0.5036287 0.6914426 0.51113 0.6941882 0.500882 0.1152117 0.7647196 0.1133317 0.7575452 0.1219396 0.7546133 0.1247224 0.7617615 0.1112205 0.7499942 0.1082184 0.7406005 0.1142568 0.7385974 0.1186623 0.7472878 0.1170677 0.7727499 0.1152117 0.7647196 0.1247224 0.7617615 0.1274479 0.7697492 0.1133317 0.7575452 0.1112205 0.7499942 0.1186623 0.7472878 0.1219396 0.7546133 0.6941882 0.500882 0.6914426 0.51113 0.688697 0.500882 0.1194055 0.7830698 0.1170677 0.7727499 0.1274479 0.7697492 0.1311348 0.7803159 0.1274479 0.7697492 0.1247224 0.7617615 0.1342197 0.7574449 0.1380461 0.7655565 0.1219396 0.7546133 0.1186623 0.7472878 0.1257482 0.7431725 0.1304413 0.7503389 0.688697 0.500882 0.6914426 0.51113 0.6839414 0.5036287 0.1311348 0.7803159 0.1274479 0.7697492 0.1380461 0.7655565 0.1425322 0.7764298 0.1247224 0.7617615 0.1219396 0.7546133 0.1304413 0.7503389 0.1342197 0.7574449 0.1186623 0.7472878 0.1142568 0.7385974 0.1197457 0.7355453 0.1257482 0.7431725 0.1304413 0.7503389 0.1257482 0.7431725 0.1322795 0.7374512 0.1389872 0.7441574 0.6839414 0.5036287 0.6914426 0.51113 0.681196 0.5083833 0.1425322 0.7764298 0.1380461 0.7655565 0.1490048 0.7605291 0.1535631 0.771246 0.1342197 0.7574449 0.1304413 0.7503389 0.1389872 0.7441574 0.1445172 0.7517369 0.1257482 0.7431725 0.1197457 0.7355453 0.124374 0.7313603 0.1322795 0.7374512 0.1380461 0.7655565 0.1342197 0.7574449 0.1445172 0.7517369 0.1490048 0.7605291 0.1535631 0.771246 0.1490048 0.7605291 0.1615237 0.7547109 0.1644948 0.7667193 0.1445172 0.7517369 0.1389872 0.7441574 0.1482976 0.7351844 0.1560218 0.7440721 0.1322795 0.7374512 0.124374 0.7313603 0.127431 0.7257606 0.1380863 0.7287998 0.1490048 0.7605291 0.1445172 0.7517369 0.1560218 0.7440721 0.1615237 0.7547109 0.1389872 0.7441574 0.1322795 0.7374512 0.1380863 0.7287998 0.1482976 0.7351844 0.681196 0.5083833 0.6914426 0.51113 0.681196 0.5138753 0.05257922 0.7302888 0.06320804 0.7260419 0.06662935 0.731469 0.05909144 0.738447 0.03196507 0.7568589 0.03642922 0.7465704 0.04787945 0.753248 0.04400533 0.7618032 0.04315346 0.7374555 0.05257922 0.7302888 0.05909144 0.738447 0.05285739 0.7455716 0.681196 0.5138753 0.6914426 0.51113 0.6839414 0.5186305 0.02971673 0.7680551 0.03196507 0.7568589 0.04400533 0.7618032 0.04014188 0.772008 0.03642922 0.7465704 0.04315346 0.7374555 0.05285739 0.7455716 0.04787945 0.753248 0.04400533 0.7618032 0.04787945 0.753248 0.05801594 0.758329 0.05455446 0.7662515 0.05285739 0.7455716 0.05909144 0.738447 0.06608623 0.7438953 0.06173998 0.751219 0.6839414 0.5186305 0.6914426 0.51113 0.688697 0.5213753 0.04014188 0.772008 0.04400533 0.7618032 0.05455446 0.7662515 0.05097186 0.776567 0.04787945 0.753248 0.05285739 0.7455716 0.06173998 0.751219 0.05801594 0.758329 0.05909144 0.738447 0.06662935 0.731469 0.07185244 0.7352793 0.06608623 0.7438953 0.688697 0.5213753 0.6914426 0.51113 0.6941882 0.5213753 0.05097186 0.776567 0.05455446 0.7662515 0.06487447 0.7700835 0.06206512 0.7805587 0.05801594 0.758329 0.06173998 0.751219 0.07032477 0.7552454 0.06758505 0.7621659 0.06608623 0.7438953 0.07185244 0.7352793 0.07735335 0.7386357 0.0734297 0.747731 0.05455446 0.7662515 0.05801594 0.758329 0.06758505 0.7621659 0.06487447 0.7700835 0.06173998 0.751219 0.06608623 0.7438953 0.0734297 0.747731 0.07032477 0.7552454 0.06206512 0.7805587 0.06487447 0.7700835 0.07523989 0.7728193 0.07340806 0.783457 0.06758505 0.7621659 0.07032477 0.7552454 0.07884109 0.7579122 0.07698005 0.7650591 0.0734297 0.747731 0.07735335 0.7386357 0.08324497 0.7410128 0.08085918 0.7504444 0.06487447 0.7700835 0.06758505 0.7621659 0.07698005 0.7650591 0.07523989 0.7728193 0.07032477 0.7552454 0.0734297 0.747731 0.08085918 0.7504444 0.07884109 0.7579122 0.6941882 0.5213753 0.6914426 0.51113 0.6989437 0.5186305 0.07523989 0.7728193 0.07698005 0.7650591 0.08654111 0.7666312 0.08558118 0.7745469 0.07884109 0.7579122 0.08085918 0.7504444 0.08837324 0.7518937 0.08740627 0.7595193 0.6989437 0.5186305 0.6914426 0.51113 0.7016894 0.5138753 0.07340806 0.783457 0.07523989 0.7728193 0.08558118 0.7745469 0.0848369 0.7852408 0.07698005 0.7650591 0.07884109 0.7579122 0.08740627 0.7595193 0.08654111 0.7666312 0.08085918 0.7504444 0.08324497 0.7410128 0.08933824 0.7424624 0.08837324 0.7518937 0.08558118 0.7745469 0.08654111 0.7666312 0.09603935 0.767118 0.09603077 0.7751753 0.08740627 0.7595193 0.08837324 0.7518937 0.09590643 0.7523682 0.09601145 0.7599837 0.7016894 0.5138753 0.6914426 0.51113 0.7016894 0.5083833 0.0848369 0.7852408 0.08558118 0.7745469 0.09603077 0.7751753 0.09637379 0.7859765 0.08654111 0.7666312 0.08740627 0.7595193 0.09601145 0.7599837 0.09603935 0.767118 0.08837324 0.7518937 0.08933824 0.7424624 0.09550154 0.7430536 0.09590643 0.7523682 0.1535631 0.771246 0.1644948 0.7667193 0.1667733 0.7717744 0.1559423 0.7768734 0.1079199 0.7855637 0.1194055 0.7830698 0.1205551 0.7889294 0.1084942 0.7912729 0.06206512 0.7805587 0.07340806 0.783457 0.07217293 0.7890995 0.06027287 0.7861086 0.0848369 0.7852408 0.09637379 0.7859765 0.09622746 0.7917219 0.08414226 0.7909002 0.05097186 0.776567 0.06206512 0.7805587 0.06027287 0.7861086 0.04878336 0.7819084 0.07340806 0.783457 0.0848369 0.7852408 0.08414226 0.7909002 0.07217293 0.7890995 0.1194055 0.7830698 0.1311348 0.7803159 0.132752 0.786068 0.1205551 0.7889294 0.1311348 0.7803159 0.1425322 0.7764298 0.1446411 0.7819016 0.132752 0.786068 0.1425322 0.7764298 0.1535631 0.771246 0.1559423 0.7768734 0.1446411 0.7819016 0.09637379 0.7859765 0.1079199 0.7855637 0.1084942 0.7912729 0.09622746 0.7917219 0.04014188 0.772008 0.05097186 0.776567 0.04878336 0.7819084 0.03780764 0.7772881 0.02971673 0.7680551 0.04014188 0.772008 0.03780764 0.7772881 0.02781337 0.7726866 0.1390568 0.8072789 0.132752 0.786068 0.1446411 0.7819016 0.1560236 0.8108375 0.06585794 0.8186343 0.07217293 0.7890995 0.08414226 0.7909002 0.08088076 0.8212972 0.02416855 0.8035229 0.03780764 0.7772881 0.04878336 0.7819084 0.03763741 0.8099343 0.1560236 0.8108375 0.1446411 0.7819016 0.1559423 0.7768734 0.1703008 0.8043364 0.08088076 0.8212972 0.08414226 0.7909002 0.09622746 0.7917219 0.09616166 0.8222725 0.1268138 0.819351 0.1115537 0.8214038 0.1084942 0.7912729 0.1205551 0.7889294 0.03763741 0.8099343 0.04878336 0.7819084 0.06027287 0.7861086 0.05399996 0.8066975 0.1703008 0.8043364 0.1559423 0.7768734 0.1667733 0.7717744 0.1844812 0.7964019 0.09616166 0.8222725 0.09622746 0.7917219 0.1084942 0.7912729 0.1115537 0.8214038 0.1268138 0.819351 0.1205551 0.7889294 0.132752 0.786068 0.1390568 0.8072789 0.06027287 0.7861086 0.07217293 0.7890995 0.06585794 0.8186343 0.05399996 0.8066975 0.01052492 0.7958936 0.02781337 0.7726866 0.03780764 0.7772881 0.02416855 0.8035229 0.02416855 0.8035229 0.02056193 0.8106135 0.006640911 0.8029316 0.01052492 0.7958936 0.1560236 0.8108375 0.1588696 0.8183507 0.1437408 0.8232794 0.1390568 0.8072789 0.08000618 0.8292281 0.06412732 0.826452 0.06585794 0.8186343 0.08088076 0.8212972 0.03476399 0.8170783 0.02056193 0.8106135 0.02416855 0.8035229 0.03763741 0.8099343 0.1703008 0.8043364 0.1739053 0.8117927 0.1588696 0.8183507 0.1560236 0.8108375 0.09607982 0.8301871 0.08000618 0.8292281 0.08088076 0.8212972 0.09616166 0.8222725 0.1284015 0.8271617 0.1123339 0.8295952 0.1115537 0.8214038 0.1268138 0.819351 0.04914414 0.8221914 0.03476399 0.8170783 0.03763741 0.8099343 0.05399996 0.8066975 0.1844812 0.7964019 0.1886042 0.8039067 0.1739053 0.8117927 0.1703008 0.8043364 0.1115537 0.8214038 0.1123339 0.8295952 0.09607982 0.8301871 0.09616166 0.8222725 0.1390568 0.8072789 0.1437408 0.8232794 0.1284015 0.8271617 0.1268138 0.819351 0.06585794 0.8186343 0.06412732 0.826452 0.04914414 0.8221914 0.05399996 0.8066975 0.9551785 0.6846179 0.966791 0.6725842 0.9745815 0.6754709 0.9649737 0.6898568 0.9492881 0.6749742 0.9634343 0.6650733 0.966791 0.6725842 0.9551785 0.6846179 0.9420784 0.6660459 0.9575751 0.6593409 0.9634343 0.6650733 0.9492881 0.6749742 0.9404466 0.6546789 0.95742 0.6511499 0.9575751 0.6593409 0.9420784 0.6660459 0.9373386 0.6437538 0.9542498 0.6435998 0.95742 0.6511499 0.9404466 0.6546789 0.9401995 0.6327602 0.9572989 0.6359832 0.9542498 0.6435998 0.9373386 0.6437538 0.9415166 0.6213386 0.9573162 0.6277436 0.9572989 0.6359832 0.9401995 0.6327602 0.9485717 0.61211 0.9631568 0.6218362 0.9573162 0.6277436 0.9415166 0.6213386 0.9543523 0.6020728 0.9664455 0.6141319 0.9631568 0.6218362 0.9485717 0.61211 0.9664455 0.6141319 0.9743562 0.6110417 0.9743601 0.61105 0.9689984 0.6167692 0.9631568 0.6218362 0.9664455 0.6141319 0.9689984 0.6167692 0.9631645 0.6218417 0.9542498 0.6435998 0.9572989 0.6359832 0.9573084 0.6359849 0.9578145 0.6435703 0.9573162 0.6277436 0.9631568 0.6218362 0.9631645 0.6218417 0.9606428 0.6291157 0.95742 0.6511499 0.9542498 0.6435998 0.9578145 0.6435703 0.9574294 0.6511478 0.9745815 0.6754709 0.966791 0.6725842 0.9692494 0.6699573 0.9745849 0.6754624 0.9743562 0.6110417 0.9817584 0.6050395 0.9821951 0.6092974 0.9743601 0.61105 0.9572989 0.6359832 0.9573162 0.6277436 0.9606428 0.6291157 0.9573084 0.6359849 0.9819157 0.6812053 0.9745815 0.6754709 0.9745849 0.6754624 0.9822685 0.6770385 0.966791 0.6725842 0.9634343 0.6650733 0.9634426 0.6650681 0.9692494 0.6699573 0.9634343 0.6650733 0.9575751 0.6593409 0.9608474 0.6579276 0.9634426 0.6650681 0.9575751 0.6593409 0.95742 0.6511499 0.9574294 0.6511478 0.9608474 0.6579276 0.9649737 0.6898568 0.9728815 0.6961978 0.9717065 0.7030506 0.9601385 0.6976284 0.9642751 0.5965078 0.9543523 0.6020728 0.9491035 0.5966124 0.9591944 0.5886638 0.9401995 0.6327602 0.9373386 0.6437538 0.9298559 0.6438301 0.9309747 0.631116 0.9551785 0.6846179 0.9649737 0.6898568 0.9601385 0.6976284 0.9501764 0.6900445 0.9420784 0.6660459 0.9492881 0.6749742 0.9416692 0.6804482 0.9351577 0.6690556 0.9492881 0.6749742 0.9551785 0.6846179 0.9501764 0.6900445 0.9416692 0.6804482 0.9373386 0.6437538 0.9404466 0.6546789 0.9312502 0.6565362 0.9298559 0.6438301 0.9722285 0.589854 0.9642751 0.5965078 0.9591944 0.5886638 0.9708619 0.582868 0.9485717 0.61211 0.9415166 0.6213386 0.9344325 0.6185611 0.9406451 0.6068144 0.9415166 0.6213386 0.9401995 0.6327602 0.9309747 0.631116 0.9344325 0.6185611 0.9543523 0.6020728 0.9485717 0.61211 0.9406451 0.6068144 0.9491035 0.5966124 0.9404466 0.6546789 0.9420784 0.6660459 0.9351577 0.6690556 0.9312502 0.6565362 0.9298559 0.6438301 0.9312502 0.6565362 0.9286051 0.6570356 0.9273203 0.6438566 0.9708619 0.582868 0.9591944 0.5886638 0.9578421 0.58648 0.9697018 0.5805482 0.9312502 0.6565362 0.9351577 0.6690556 0.9325397 0.6701955 0.9286051 0.6570356 0.9351577 0.6690556 0.9416692 0.6804482 0.939497 0.6820621 0.9325397 0.6701955 0.9591944 0.5886638 0.9491035 0.5966124 0.947245 0.5946322 0.9578421 0.58648 0.9491035 0.5966124 0.9406451 0.6068144 0.938241 0.6052053 0.947245 0.5946322 0.9416692 0.6804482 0.9501764 0.6900445 0.9484228 0.6919998 0.939497 0.6820621 0.9501764 0.6900445 0.9601385 0.6976284 0.9588412 0.6997548 0.9484228 0.6919998 0.9406451 0.6068144 0.9344325 0.6185611 0.9318683 0.6175939 0.938241 0.6052053 0.9344325 0.6185611 0.9309747 0.631116 0.9283375 0.6306864 0.9318683 0.6175939 0.9601385 0.6976284 0.9717065 0.7030506 0.9705282 0.7054998 0.9588412 0.6997548 0.9309747 0.631116 0.9298559 0.6438301 0.9273203 0.6438566 0.9283375 0.6306864 0.986242 0.9861934 0.9830974 0.9900217 0.9789524 0.9869972 0.9816386 0.9828336 0.9833197 0.6304618 0.9858798 0.6208852 0.9903253 0.6234484 0.9833197 0.6304618 0.976306 0.6234561 0.9807485 0.6208878 0.9833197 0.6304618 0.9903332 0.637467 0.9858909 0.6400353 0.9833197 0.6304618 0.9737462 0.6330333 0.9737432 0.6279019 0.9833197 0.6304618 0.9928963 0.6330217 0.9903332 0.637467 0.9833197 0.6304618 0.9928936 0.627891 0.9928963 0.6330217 0.9642751 0.5965078 0.9743562 0.6110417 0.9664455 0.6141319 0.9543523 0.6020728 0.9833197 0.6304618 0.9807596 0.6400384 0.9763146 0.6374759 0.9833197 0.6304618 0.9903253 0.6234484 0.9928936 0.627891 0.9833197 0.6304618 0.9807485 0.6208878 0.9858798 0.6208852 0.9833197 0.6304618 0.9737432 0.6279019 0.976306 0.6234561 0.9833197 0.6304618 0.9763146 0.6374759 0.9737462 0.6330333 0.9642751 0.5965078 0.9722285 0.589854 0.9817584 0.6050395 0.9743562 0.6110417 0.9649737 0.6898568 0.9745815 0.6754709 0.9819157 0.6812053 0.9728815 0.6961978 0.1063357 0.5813563 0.1088058 0.5732968 0.1132379 0.5768235 0.1109637 0.5820497 0.1243206 0.5671899 0.1316217 0.5714035 0.1287075 0.5750649 0.1234819 0.5727915 0.1193832 0.600169 0.112082 0.5959553 0.1149962 0.5922939 0.1202217 0.5945672 0.1161055 0.5690805 0.1243206 0.5671899 0.1234819 0.5727915 0.1178197 0.5734347 0.1373664 0.5775727 0.1373682 0.5860022 0.1327397 0.5853096 0.1320961 0.5796473 0.1275981 0.5982783 0.1193832 0.600169 0.1202217 0.5945672 0.1258841 0.593924 0.1063372 0.5897861 0.1063357 0.5813563 0.1109637 0.5820497 0.1116077 0.5877115 0.1316217 0.5714035 0.1373664 0.5775727 0.1320961 0.5796473 0.1287075 0.5750649 0.1348978 0.5940624 0.1275981 0.5982783 0.1258841 0.593924 0.1304659 0.5905348 0.112082 0.5959553 0.1063372 0.5897861 0.1116077 0.5877115 0.1149962 0.5922939 0.1088058 0.5732968 0.1161055 0.5690805 0.1178197 0.5734347 0.1132379 0.5768235 0.1373682 0.5860022 0.1348978 0.5940624 0.1304659 0.5905348 0.1327397 0.5853096 0.9766812 0.980023 0.9744888 0.9844657 0.9697647 0.9824625 0.9714347 0.9777982 0.9659683 0.9761877 0.9648424 0.9810127 0.9597867 0.9801348 0.9603534 0.9752134 0.9546639 0.974886 0.9546639 0.9798402 0.9495409 0.9801348 0.9489744 0.9752122 0.9433594 0.9761876 0.9444853 0.9810127 0.939563 0.9824625 0.9378932 0.9777981 0.9326466 0.9800229 0.9348393 0.9844655 0.9303754 0.9869971 0.9276891 0.9828335 0.9816386 0.9828336 0.9789524 0.9869972 0.9744888 0.9844657 0.9766812 0.980023 0.9714347 0.9777982 0.9697647 0.9824625 0.9648424 0.9810127 0.9659683 0.9761877 0.9603534 0.9752134 0.9597867 0.9801348 0.9546639 0.9798402 0.9546639 0.974886 0.9489744 0.9752122 0.9495409 0.9801348 0.9444853 0.9810127 0.9433594 0.9761876 0.9378932 0.9777981 0.939563 0.9824625 0.9348393 0.9844655 0.9326466 0.9800229 0.9276891 0.9828335 0.9303754 0.9869971 0.9262303 0.9900215 0.9230855 0.9861932 0.9833197 0.6304618 0.9858909 0.6400353 0.9807596 0.6400384 0.1177018 0.9770106 0.1074847 0.9872277 0.09352797 0.9872277 0.07957124 0.9872277 0.06935459 0.9770106 0.06561475 0.9630537 0.06935459 0.9490976 0.07957124 0.9388804 0.09352797 0.9388804 0.1074847 0.9388804 0.1177018 0.9490976 0.1214414 0.9630537 0.4887377 0.8768966 0.4953271 0.8654766 0.506743 0.8588819 0.5199273 0.8588786 0.5313466 0.8654673 0.5379417 0.8768838 0.5379452 0.8900674 0.5313558 0.9014873 0.5199398 0.9080826 0.5067559 0.9080855 0.4953365 0.9014964 0.4887412 0.8900802</float_array>
          <technique_common>
            <accessor source="#tow_ball-mesh-map-0-array" count="768" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="tow_ball-mesh-colors-TexTools_colorID" name="TexTools_colorID">
          <float_array id="tow_ball-mesh-colors-TexTools_colorID-array" count="3072">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1 1 0 0 1</float_array>
          <technique_common>
            <accessor source="#tow_ball-mesh-colors-TexTools_colorID-array" count="768" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="tow_ball-mesh-vertices">
          <input semantic="POSITION" source="#tow_ball-mesh-positions"/>
        </vertices>
        <polylist material="tow_hitch-material" count="194">
          <input semantic="VERTEX" source="#tow_ball-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#tow_ball-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#tow_ball-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#tow_ball-mesh-colors-TexTools_colorID" offset="3" set="0"/>
          <vcount>4 4 3 4 4 4 4 4 4 4 4 3 4 4 4 4 3 4 4 4 3 4 4 4 4 3 4 4 4 4 4 4 4 4 4 3 4 4 4 3 4 4 4 4 3 4 4 4 3 4 4 4 4 4 4 4 4 4 4 3 4 4 3 4 4 4 4 4 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 4 3 3 3 3 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 12 12 </vcount>
          <p>71 0 0 0 70 1 1 1 4 2 2 2 5 3 3 3 69 4 4 4 0 5 5 5 2 6 6 6 3 7 7 7 68 8 8 8 43 9 9 9 1 10 10 10 72 11 11 11 71 0 12 12 5 3 13 13 6 12 14 14 70 1 15 15 69 4 16 16 3 7 17 17 4 2 18 18 0 5 19 19 68 13 20 20 1 14 21 21 2 6 22 22 6 12 23 23 5 3 24 24 11 15 25 25 12 16 26 26 4 2 27 27 3 7 28 28 9 17 29 29 10 18 30 30 2 6 31 31 1 14 32 32 7 19 33 33 8 20 34 34 5 3 35 35 4 2 36 36 10 18 37 37 11 15 38 38 3 7 39 39 2 6 40 40 8 20 41 41 9 17 42 42 1 10 43 43 43 9 44 44 7 21 45 45 10 18 46 46 9 17 47 47 15 22 48 48 16 23 49 49 8 20 50 50 7 19 51 51 13 24 52 52 14 25 53 53 11 15 54 54 10 18 55 55 16 23 56 56 17 26 57 57 9 17 58 58 8 20 59 59 14 25 60 60 15 22 61 61 7 21 62 62 43 9 63 63 13 27 64 64 12 16 65 65 11 15 66 66 17 26 67 67 18 28 68 68 17 26 69 69 16 23 70 70 22 29 71 71 23 30 72 72 15 22 73 73 14 25 74 74 20 31 75 75 21 32 76 76 13 27 77 77 43 9 78 78 19 33 79 79 18 28 80 80 17 26 81 81 23 30 82 82 24 34 83 83 16 23 84 84 15 22 85 85 21 32 86 86 22 29 87 87 14 25 88 88 13 24 89 89 19 35 90 90 20 31 91 91 21 32 92 92 20 31 93 93 26 36 94 94 27 37 95 95 19 33 96 96 43 9 97 97 25 38 98 98 24 34 99 99 23 30 100 100 29 39 101 101 30 40 102 102 22 29 103 103 21 32 104 104 27 37 105 105 28 41 106 106 20 31 107 107 19 35 108 108 25 42 109 109 26 36 110 110 23 30 111 111 22 29 112 112 28 41 113 113 29 39 114 114 30 40 115 115 29 39 116 116 35 43 117 117 36 44 118 118 28 41 119 119 27 37 120 120 33 45 121 121 34 46 122 122 26 36 123 123 25 42 124 124 31 47 125 125 32 48 126 126 29 39 127 127 28 41 128 128 34 46 129 129 35 43 130 130 27 37 131 131 26 36 132 132 32 48 133 133 33 45 134 134 25 38 135 135 43 9 136 136 31 49 137 137 32 48 138 138 31 47 139 139 37 50 140 140 38 51 141 141 35 43 142 142 34 46 143 143 40 52 144 144 41 53 145 145 33 45 146 146 32 48 147 147 38 51 148 148 39 54 149 149 31 49 150 150 43 9 151 151 37 55 152 152 36 44 153 153 35 43 154 154 41 53 155 155 42 56 156 156 34 46 157 157 33 45 158 158 39 54 159 159 40 52 160 160 41 53 161 161 40 52 162 162 47 57 163 163 48 58 164 164 39 54 165 165 38 51 166 166 45 59 167 167 46 60 168 168 37 55 169 169 43 9 170 170 44 61 171 171 42 56 172 172 41 53 173 173 48 58 174 174 49 62 175 175 40 52 176 176 39 54 177 177 46 60 178 178 47 57 179 179 38 51 180 180 37 50 181 181 44 63 182 182 45 59 183 183 44 61 184 184 43 9 185 185 50 64 186 186 49 62 187 187 48 58 188 188 54 65 189 189 55 66 190 190 47 57 191 191 46 60 192 192 52 67 193 193 53 68 194 194 45 59 195 195 44 63 196 196 50 69 197 197 51 70 198 198 48 58 199 199 47 57 200 200 53 68 201 201 54 65 202 202 46 60 203 203 45 59 204 204 51 70 205 205 52 67 206 206 55 66 207 207 54 65 208 208 60 71 209 209 61 72 210 210 53 68 211 211 52 67 212 212 58 73 213 213 59 74 214 214 51 70 215 215 50 69 216 216 56 75 217 217 57 76 218 218 54 65 219 219 53 68 220 220 59 74 221 221 60 71 222 222 52 67 223 223 51 70 224 224 57 76 225 225 58 73 226 226 50 64 227 227 43 9 228 228 56 77 229 229 60 71 230 230 59 74 231 231 65 78 232 232 66 79 233 233 58 73 234 234 57 76 235 235 63 80 236 236 64 81 237 237 56 77 238 238 43 9 239 239 62 82 240 240 61 72 241 241 60 71 242 242 66 79 243 243 67 83 244 244 59 74 245 245 58 73 246 246 64 81 247 247 65 78 248 248 57 76 249 249 56 75 250 250 62 84 251 251 63 80 252 252 66 79 253 253 65 78 254 254 70 1 255 255 71 0 256 256 64 81 257 257 63 80 258 258 0 5 259 259 69 4 260 260 62 82 261 261 43 9 262 262 68 8 263 263 67 83 264 264 66 79 265 265 71 0 266 266 72 11 267 267 65 78 268 268 64 81 269 269 69 4 270 270 70 1 271 271 63 80 272 272 62 84 273 273 68 13 274 274 0 5 275 275 30 40 276 276 36 44 277 277 78 85 278 278 77 86 279 279 6 12 280 280 12 16 281 281 74 87 282 282 73 88 283 283 55 66 284 284 61 72 285 285 82 89 286 286 81 90 287 287 67 83 288 288 72 11 289 289 84 91 290 290 83 92 291 291 49 62 292 292 55 66 293 293 81 90 294 294 80 93 295 295 61 72 296 296 67 83 297 297 83 92 298 298 82 89 299 299 12 16 300 300 18 28 301 301 75 94 302 302 74 87 303 303 18 28 304 304 24 34 305 305 76 95 306 306 75 94 307 307 24 34 308 308 30 40 309 309 77 86 310 310 76 95 311 311 72 11 312 312 6 12 313 313 73 88 314 314 84 91 315 315 42 56 316 316 49 62 317 317 80 93 318 318 79 96 319 319 36 44 320 320 42 56 321 321 79 96 322 322 78 85 323 323 97 97 324 324 75 94 325 325 76 95 326 326 103 98 327 327 99 99 328 328 82 89 329 329 83 92 330 330 98 100 331 331 101 101 332 332 79 96 333 333 80 93 334 334 100 102 335 335 103 98 336 336 76 95 337 337 77 86 338 338 102 103 339 339 98 100 340 340 83 92 341 341 84 91 342 342 104 104 343 343 105 105 344 344 106 106 345 345 73 88 346 346 74 87 347 347 100 102 348 348 80 93 349 349 81 90 350 350 107 107 351 351 102 103 352 352 77 86 353 353 78 85 354 354 108 108 355 355 104 104 356 356 84 91 357 357 73 88 358 358 106 106 359 359 105 105 360 360 74 87 361 361 75 94 362 362 97 97 363 363 81 90 364 364 82 89 365 365 99 99 366 366 107 107 367 367 108 108 368 368 78 85 369 369 79 96 370 370 101 101 371 371 101 101 372 372 91 109 373 373 90 110 374 374 108 108 375 375 103 98 376 376 88 111 377 377 87 112 378 378 97 97 379 379 95 113 380 380 94 114 381 381 99 99 382 382 98 100 383 383 92 115 384 384 91 109 385 385 101 101 386 386 100 102 387 387 102 103 388 388 89 116 389 389 88 111 390 390 103 98 391 391 96 117 392 392 95 113 393 393 98 100 394 394 104 104 395 395 86 118 396 396 85 119 397 397 106 106 398 398 105 105 399 399 93 120 400 400 92 115 401 401 100 102 402 402 107 107 403 403 108 108 404 404 90 110 405 405 89 116 406 406 102 103 407 407 106 106 408 408 85 119 409 409 96 117 410 410 104 104 411 411 97 97 412 412 87 112 413 413 86 118 414 414 105 105 415 415 99 99 416 416 94 114 417 417 93 120 418 418 107 107 419 419 119 121 420 420 120 122 421 421 132 123 422 422 131 124 423 423 129 125 424 424 130 126 425 425 120 122 426 426 119 121 427 427 117 127 428 428 118 128 429 429 130 126 430 430 129 125 431 431 127 129 432 432 128 130 433 433 118 128 434 434 117 127 435 435 115 131 436 436 116 132 437 437 128 130 438 438 127 129 439 439 125 133 440 440 126 134 441 441 116 132 442 442 115 131 443 443 113 135 444 444 114 136 445 445 126 134 446 446 125 133 447 447 123 137 448 448 124 138 449 449 114 136 450 450 113 135 451 451 111 139 452 452 112 140 453 453 124 138 454 454 123 137 455 455 112 140 456 456 122 141 457 457 139 142 458 458 134 143 459 459 124 138 460 460 112 140 461 461 134 143 462 462 140 144 463 463 116 132 464 464 126 134 465 465 141 145 466 466 136 146 467 467 114 136 468 468 124 138 469 469 140 144 470 470 135 147 471 471 128 130 472 472 116 132 473 473 136 146 474 474 142 148 475 475 132 123 476 476 120 122 477 477 138 149 478 478 144 150 479 479 122 141 480 480 110 151 481 481 133 152 482 482 139 142 483 483 126 134 484 484 114 136 485 485 135 147 486 486 141 145 487 487 110 153 488 488 132 123 489 489 144 150 490 490 133 154 491 491 120 122 492 492 130 126 493 493 143 155 494 494 138 149 495 495 130 126 496 496 118 128 497 497 137 156 498 498 143 155 499 499 118 128 500 500 128 130 501 501 142 148 502 502 137 156 503 503 131 124 504 504 109 157 505 505 145 158 506 506 156 159 507 507 121 160 508 508 111 139 509 509 146 161 510 510 151 162 511 511 125 133 512 512 115 131 513 513 148 163 514 514 153 164 515 515 119 121 516 516 131 124 517 517 156 159 518 518 150 165 519 519 117 127 520 520 129 125 521 521 155 166 522 522 149 167 523 523 129 125 524 524 119 121 525 525 150 165 526 526 155 166 527 527 115 131 528 528 127 129 529 529 154 168 530 530 148 163 531 531 109 169 532 532 121 160 533 533 151 162 534 534 145 170 535 535 123 137 536 536 113 135 537 537 147 171 538 538 152 172 539 539 113 135 540 540 125 133 541 541 153 164 542 542 147 171 543 543 111 139 544 544 123 137 545 545 152 172 546 546 146 161 547 547 127 129 548 548 117 127 549 549 149 167 550 550 154 168 551 551 148 163 552 552 154 168 553 553 166 173 554 554 160 174 555 555 145 170 556 556 151 162 557 557 163 175 558 558 157 176 559 559 154 168 560 560 149 167 561 561 161 177 562 562 166 173 563 563 149 167 564 564 155 166 565 565 167 178 566 566 161 177 567 567 151 162 568 568 146 161 569 569 158 179 570 570 163 175 571 571 146 161 572 572 152 172 573 573 164 180 574 574 158 179 575 575 155 166 576 576 150 165 577 577 162 181 578 578 167 178 579 579 150 165 580 580 156 159 581 581 168 182 582 582 162 181 583 583 152 172 584 584 147 171 585 585 159 183 586 586 164 180 587 587 147 171 588 588 153 164 589 589 165 184 590 590 159 183 591 591 156 159 592 592 145 158 593 593 157 185 594 594 168 182 595 595 153 164 596 596 148 163 597 597 160 174 598 598 165 184 599 599 176 186 600 600 183 187 601 601 182 188 602 602 171 189 603 603 169 190 604 604 189 191 605 605 186 192 606 606 169 190 607 607 191 193 608 608 188 194 609 609 169 190 610 610 185 195 611 611 182 196 612 612 169 190 613 613 193 197 614 614 190 198 615 615 169 190 616 616 184 199 617 617 185 195 618 618 169 190 619 619 187 200 620 620 184 199 621 621 121 160 622 622 122 141 623 623 112 140 624 624 111 139 625 625 169 190 626 626 183 201 627 627 192 202 628 628 169 190 629 629 186 192 630 630 187 200 631 631 169 190 632 632 188 194 633 633 189 191 634 634 169 190 635 635 190 198 636 636 191 193 637 637 169 190 638 638 192 202 639 639 193 197 640 640 121 160 641 641 109 169 642 642 110 151 643 643 122 141 644 644 131 124 645 645 132 123 646 646 110 153 647 647 109 157 648 648 137 203 649 649 142 204 650 650 172 205 651 651 179 206 652 652 141 207 653 653 135 208 654 654 177 209 655 655 181 210 656 656 144 211 657 657 138 212 658 658 174 213 659 659 180 214 660 660 136 215 661 661 141 207 662 662 181 210 663 663 173 216 664 664 140 217 665 665 134 218 666 666 170 219 667 667 178 220 668 668 133 221 669 669 144 211 670 670 180 214 671 671 176 222 672 672 143 223 673 673 137 203 674 674 179 206 675 675 175 224 676 676 135 208 677 677 140 217 678 678 178 220 679 679 177 209 680 680 139 225 681 681 133 221 682 682 176 222 683 683 171 226 684 684 138 212 685 685 143 223 686 686 175 224 687 687 174 213 688 688 142 204 689 689 136 215 690 690 173 216 691 691 172 205 692 692 134 218 693 693 139 225 694 694 171 226 695 695 170 219 696 696 170 227 697 697 185 228 698 698 184 229 699 699 178 230 700 700 177 231 701 701 187 232 702 702 186 233 703 703 181 234 704 704 173 235 705 705 189 236 706 706 188 237 707 707 172 238 708 708 179 239 709 709 191 240 710 710 190 241 711 711 175 242 712 712 174 243 713 713 193 244 714 714 192 245 715 715 180 246 716 716 171 189 717 717 182 188 718 718 185 228 719 719 170 227 720 720 178 230 721 721 184 229 722 722 187 232 723 723 177 231 724 724 181 234 725 725 186 233 726 726 189 236 727 727 173 235 728 728 172 238 729 729 188 237 730 730 191 240 731 731 179 239 732 732 175 242 733 733 190 241 734 734 193 244 735 735 174 243 736 736 180 246 737 737 192 245 738 738 183 187 739 739 176 186 740 740 169 190 741 741 182 196 742 742 183 201 743 743 85 247 744 744 86 247 745 745 87 247 746 746 88 247 747 747 89 247 748 748 90 247 749 749 91 247 750 750 92 247 751 751 93 247 752 752 94 247 753 753 95 247 754 754 96 247 755 755 163 248 756 756 158 248 757 757 164 248 758 758 159 248 759 759 165 248 760 760 160 248 761 761 166 248 762 762 161 248 763 763 167 248 764 764 162 248 765 765 168 248 766 766 157 248 767 767</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="tow_beam_c_R" name="tow_beam_c_R" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_beam_c_R-mesh" name="tow_beam_c_R">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_mount_hz_b_R" name="tow_mount_hz_b_R" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_mount_hz_b_R-mesh" name="tow_mount_hz_b_R">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_mount_hz_R" name="tow_mount_hz_R" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_mount_hz_R-mesh" name="tow_mount_hz_R">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_mount_vt_R" name="tow_mount_vt_R" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_mount_vt_R-mesh" name="tow_mount_vt_R">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_beam_bend" name="tow_beam_bend" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_beam_bend-mesh" name="tow_beam_bend">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_shackle_mount" name="tow_shackle_mount" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_shackle_mount-mesh" name="tow_shackle_mount">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="Mesh_047-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_shackle" name="tow_shackle" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_shackle-mesh" name="tow_shackle">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="Mesh_068-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_reciever_b" name="tow_reciever_b" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_reciever_b-mesh" name="tow_reciever_b">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_reciever_a" name="tow_reciever_a" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_reciever_a-mesh" name="tow_reciever_a">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_mount_vt_L" name="tow_mount_vt_L" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_mount_vt_L-mesh" name="tow_mount_vt_L">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_mount_hz_L" name="tow_mount_hz_L" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_mount_hz_L-mesh" name="tow_mount_hz_L">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_mount_hz_b_L" name="tow_mount_hz_b_L" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_mount_hz_b_L-mesh" name="tow_mount_hz_b_L">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_hitch_b" name="tow_hitch_b" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_hitch_b-mesh" name="tow_hitch_b">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_hitch_a" name="tow_hitch_a" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_hitch_a-mesh" name="tow_hitch_a">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_beam_c_L" name="tow_beam_c_L" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_beam_c_L-mesh" name="tow_beam_c_L">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_beam_b" name="tow_beam_b" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_beam_b-mesh" name="tow_beam_b">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_beam_a" name="tow_beam_a" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_beam_a-mesh" name="tow_beam_a">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="tow_ball" name="tow_ball" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#tow_ball-mesh" name="tow_ball">
          <bind_material>
            <technique_common>
              <instance_material symbol="tow_hitch-material" target="#tow_hitch-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>