<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.91.0 commit date:2020-11-25, commit time:08:34, hash:0f45cab862b8</authoring_tool>
    </contributor>
    <created>2022-01-28T12:24:09</created>
    <modified>2022-01-28T12:24:09</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="unicycle_debug-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="unicycle_snowman-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images/>
  <library_materials>
    <material id="unicycle_debug-material" name="unicycle_debug">
      <instance_effect url="#unicycle_debug-effect"/>
    </material>
    <material id="unicycle_snowman-material" name="unicycle_snowman">
      <instance_effect url="#unicycle_snowman-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="debug_pointer-mesh" name="debug_pointer">
      <mesh>
        <source id="debug_pointer-mesh-positions">
          <float_array id="debug_pointer-mesh-positions-array" count="48">-0.2816923 0.09570842 1.076634 -0.2816923 0.09570842 0.9095211 -0.5148931 0.09570842 1.076634 -0.5148931 0.09570842 0.9095211 -0.5148931 0.2026518 1.076634 -0.5148931 0.2026518 0.9095211 -0.2816923 0 1.076634 -0.7480939 0 1.076634 -0.2816923 0 0.9095211 -0.7480939 0 0.9095211 -0.2816923 -0.09570837 1.076634 -0.2816923 -0.09570837 0.9095211 -0.5148931 -0.09570837 1.076634 -0.5148931 -0.09570837 0.9095211 -0.5148931 -0.2026517 1.076634 -0.5148931 -0.2026517 0.9095211</float_array>
          <technique_common>
            <accessor source="#debug_pointer-mesh-positions-array" count="16" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="debug_pointer-mesh-normals">
          <float_array id="debug_pointer-mesh-normals-array" count="48">1 0 0 -0.6559365 0.7548162 0 1.19499e-6 0 1 -1.63146e-7 0 -1 0 1 0 -0.6559365 -0.7548162 0 -3.33818e-7 0 1 0 0 -1 0 -1 0 3.33818e-7 0 1 0 0 1 0 0 -1 0 1 0 5.97498e-7 0 1 0 0 1 0 -1 0</float_array>
          <technique_common>
            <accessor source="#debug_pointer-mesh-normals-array" count="16" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="debug_pointer-mesh-map-0">
          <float_array id="debug_pointer-mesh-map-0-array" count="144">0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0</float_array>
          <technique_common>
            <accessor source="#debug_pointer-mesh-map-0-array" count="72" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="debug_pointer-mesh-vertices">
          <input semantic="POSITION" source="#debug_pointer-mesh-positions"/>
        </vertices>
        <triangles material="unicycle_debug-material" count="24">
          <input semantic="VERTEX" source="#debug_pointer-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#debug_pointer-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#debug_pointer-mesh-map-0" offset="2" set="0"/>
          <p>5 0 0 2 0 1 3 0 2 7 1 3 5 1 4 9 1 5 2 2 6 4 2 7 7 2 8 3 3 9 8 3 10 9 3 11 1 4 12 2 4 13 0 4 14 12 0 15 15 0 16 13 0 17 15 5 18 7 5 19 9 5 20 7 6 21 12 6 22 6 6 23 8 7 24 11 7 25 13 7 26 12 8 27 11 8 28 10 8 29 5 0 30 4 0 31 2 0 32 7 1 33 4 1 34 5 1 35 7 9 36 6 9 37 2 9 38 6 10 39 0 10 40 2 10 41 3 11 42 1 11 43 8 11 44 9 11 45 5 11 46 3 11 47 1 12 48 3 12 49 2 12 50 12 0 51 14 0 52 15 0 53 15 5 54 14 5 55 7 5 56 7 13 57 14 13 58 12 13 59 12 14 60 10 14 61 6 14 62 13 11 63 15 11 64 9 11 65 9 11 66 8 11 67 13 11 68 12 15 69 13 15 70 11 15 71</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="debug_ball-mesh" name="debug_ball">
      <mesh>
        <source id="debug_ball-mesh-positions">
          <float_array id="debug_ball-mesh-positions-array" count="126">0 0 -0.3006173 0.2175288 -0.1580421 -0.1344419 -0.08308696 -0.2557198 -0.134442 -0.26888 0 -0.1344407 -0.08308696 0.2557198 -0.134442 0.2175288 0.1580421 -0.1344419 0.08308696 -0.2557198 0.134442 -0.2175288 -0.1580421 0.1344419 -0.2175288 0.1580421 0.1344419 0.08308696 0.2557198 0.134442 0.26888 0 0.1344407 0 0 0.3006173 -0.04883682 -0.1503072 -0.2557213 0.1278594 -0.09289413 -0.2557213 0.07902288 -0.2432028 -0.1580458 0.2557194 0 -0.1580452 0.1278594 0.09289413 -0.2557213 -0.1580433 0 -0.2557206 -0.2068816 -0.1503075 -0.1580452 -0.04883682 0.1503072 -0.2557213 -0.2068816 0.1503075 -0.1580452 0.07902282 0.2432028 -0.1580458 0.2859044 -0.09289449 0 0.2859044 0.09289449 0 0 -0.3006173 0 0.1766984 -0.2432044 0 -0.2859044 -0.09289449 0 -0.1766984 -0.2432044 0 -0.1766984 0.2432044 0 -0.2859044 0.09289449 0 0.1766984 0.2432044 0 0 0.3006173 0 0.2068816 -0.1503075 0.1580452 -0.07902282 -0.2432028 0.1580458 -0.2557194 0 0.1580452 -0.07902288 0.2432028 0.1580458 0.2068816 0.1503075 0.1580452 0.04883682 -0.1503072 0.2557213 0.1580433 0 0.2557206 -0.1278594 -0.09289413 0.2557213 -0.1278594 0.09289413 0.2557213 0.04883682 0.1503072 0.2557213</float_array>
          <technique_common>
            <accessor source="#debug_ball-mesh-positions-array" count="42" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="debug_ball-mesh-normals">
          <float_array id="debug_ball-mesh-normals-array" count="240">0.1023809 -0.3150906 -0.9435232 0.7002245 -0.2680314 -0.6616986 -0.2680341 -0.1947373 -0.9435227 -0.2680341 0.1947373 -0.9435227 0.1023809 0.3150906 -0.9435232 0.9049891 -0.2680313 -0.3303849 0.02474659 -0.9435212 -0.330387 -0.889697 -0.3150955 -0.330385 -0.5746023 0.7487831 -0.330388 0.5345756 0.7778649 -0.3303865 0.8026089 -0.5831267 -0.1256272 -0.3065693 -0.9435214 -0.125629 -0.9920775 0 -0.1256282 -0.3065693 0.9435214 -0.125629 0.8026089 0.5831267 -0.1256272 0.4089469 -0.6284247 0.6616985 -0.4712994 -0.5831226 0.6616985 -0.7002245 0.2680314 0.6616986 0.03853029 0.7487789 0.661699 0.7240419 0.1947367 0.6616956 0.2680341 0.1947374 0.9435227 0.4911198 0.3568217 0.7946569 0.4089468 0.6284247 0.6616986 -0.1023809 0.3150906 0.9435232 -0.1875943 0.5773453 0.7946577 -0.4712995 0.5831225 0.6616986 -0.3313048 0 0.9435238 -0.6070611 0 0.7946553 -0.7002245 -0.2680313 0.6616986 -0.1023809 -0.3150906 0.9435232 -0.1875942 -0.5773454 0.7946577 0.03853029 -0.7487789 0.6616989 0.2680341 -0.1947374 0.9435227 0.4911199 -0.3568217 0.7946569 0.7240419 -0.1947367 0.6616956 0.889697 0.3150955 0.330385 0.7946555 0.5773481 0.1875953 0.5746023 0.7487831 0.330388 -0.02474665 0.9435212 0.3303869 -0.3035313 0.9341713 0.1875978 -0.5345757 0.7778647 0.3303866 -0.904989 0.2680314 0.3303849 -0.9822458 0 0.1875988 -0.904989 -0.2680314 0.3303849 -0.5345756 -0.7778649 0.3303866 -0.3035312 -0.9341713 0.1875978 -0.02474665 -0.9435212 0.3303869 0.5746023 -0.7487831 0.330388 0.7946555 -0.5773481 0.1875953 0.889697 -0.3150955 0.330385 0.3065693 0.9435214 0.1256291 0.3035312 0.9341713 -0.1875978 0.02474665 0.9435212 -0.330387 -0.8026089 0.5831267 0.1256271 -0.7946555 0.5773481 -0.1875953 -0.8896969 0.3150955 -0.330385 -0.8026089 -0.5831267 0.1256271 -0.7946555 -0.5773481 -0.1875953 -0.5746022 -0.7487831 -0.330388 0.3065693 -0.9435214 0.1256291 0.3035313 -0.9341713 -0.1875978 0.5345757 -0.7778648 -0.3303865 0.9920775 0 0.1256282 0.9822458 0 -0.1875988 0.9049891 0.2680313 -0.3303849 0.4712993 0.5831226 -0.6616986 0.1875942 0.5773454 -0.7946577 -0.03853034 0.7487789 -0.6616989 -0.4089468 0.6284246 -0.6616986 -0.4911199 0.3568217 -0.7946569 -0.7240419 0.1947367 -0.6616956 -0.7240419 -0.1947367 -0.6616956 -0.4911198 -0.3568217 -0.7946569 -0.4089468 -0.6284246 -0.6616986 0.7002245 0.2680313 -0.6616986 0.6070611 0 -0.7946553 0.3313048 0 -0.9435238 -0.03853034 -0.7487789 -0.6616989 0.1875943 -0.5773453 -0.7946577 0.4712995 -0.5831225 -0.6616985</float_array>
          <technique_common>
            <accessor source="#debug_ball-mesh-normals-array" count="80" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="debug_ball-mesh-map-0">
          <float_array id="debug_ball-mesh-map-0-array" count="480">0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645</float_array>
          <technique_common>
            <accessor source="#debug_ball-mesh-map-0-array" count="240" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="debug_ball-mesh-vertices">
          <input semantic="POSITION" source="#debug_ball-mesh-positions"/>
        </vertices>
        <triangles material="unicycle_debug-material" count="80">
          <input semantic="VERTEX" source="#debug_ball-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#debug_ball-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#debug_ball-mesh-map-0" offset="2" set="0"/>
          <p>0 0 0 13 0 1 12 0 2 1 1 3 13 1 4 15 1 5 0 2 6 12 2 7 17 2 8 0 3 9 17 3 10 19 3 11 0 4 12 19 4 13 16 4 14 1 5 15 15 5 16 22 5 17 2 6 18 14 6 19 24 6 20 3 7 21 18 7 22 26 7 23 4 8 24 20 8 25 28 8 26 5 9 27 21 9 28 30 9 29 1 10 30 22 10 31 25 10 32 2 11 33 24 11 34 27 11 35 3 12 36 26 12 37 29 12 38 4 13 39 28 13 40 31 13 41 5 14 42 30 14 43 23 14 44 6 15 45 32 15 46 37 15 47 7 16 48 33 16 49 39 16 50 8 17 51 34 17 52 40 17 53 9 18 54 35 18 55 41 18 56 10 19 57 36 19 58 38 19 59 38 20 60 41 20 61 11 20 62 38 21 63 36 21 64 41 21 65 36 22 66 9 22 67 41 22 68 41 23 69 40 23 70 11 23 71 41 24 72 35 24 73 40 24 74 35 25 75 8 25 76 40 25 77 40 26 78 39 26 79 11 26 80 40 27 81 34 27 82 39 27 83 34 28 84 7 28 85 39 28 86 39 29 87 37 29 88 11 29 89 39 30 90 33 30 91 37 30 92 33 31 93 6 31 94 37 31 95 37 32 96 38 32 97 11 32 98 37 33 99 32 33 100 38 33 101 32 34 102 10 34 103 38 34 104 23 35 105 36 35 106 10 35 107 23 36 108 30 36 109 36 36 110 30 37 111 9 37 112 36 37 113 31 38 114 35 38 115 9 38 116 31 39 117 28 39 118 35 39 119 28 40 120 8 40 121 35 40 122 29 41 123 34 41 124 8 41 125 29 42 126 26 42 127 34 42 128 26 43 129 7 43 130 34 43 131 27 44 132 33 44 133 7 44 134 27 45 135 24 45 136 33 45 137 24 46 138 6 46 139 33 46 140 25 47 141 32 47 142 6 47 143 25 48 144 22 48 145 32 48 146 22 49 147 10 49 148 32 49 149 30 50 150 31 50 151 9 50 152 30 51 153 21 51 154 31 51 155 21 52 156 4 52 157 31 52 158 28 53 159 29 53 160 8 53 161 28 54 162 20 54 163 29 54 164 20 55 165 3 55 166 29 55 167 26 56 168 27 56 169 7 56 170 26 57 171 18 57 172 27 57 173 18 58 174 2 58 175 27 58 176 24 59 177 25 59 178 6 59 179 24 60 180 14 60 181 25 60 182 14 61 183 1 61 184 25 61 185 22 62 186 23 62 187 10 62 188 22 63 189 15 63 190 23 63 191 15 64 192 5 64 193 23 64 194 16 65 195 21 65 196 5 65 197 16 66 198 19 66 199 21 66 200 19 67 201 4 67 202 21 67 203 19 68 204 20 68 205 4 68 206 19 69 207 17 69 208 20 69 209 17 70 210 3 70 211 20 70 212 17 71 213 18 71 214 3 71 215 17 72 216 12 72 217 18 72 218 12 73 219 2 73 220 18 73 221 15 74 222 16 74 223 5 74 224 15 75 225 13 75 226 16 75 227 13 76 228 0 76 229 16 76 230 12 77 231 14 77 232 2 77 233 12 78 234 13 78 235 14 78 236 13 79 237 1 79 238 14 79 239</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="debug_body-mesh" name="debug_body">
      <mesh>
        <source id="debug_body-mesh-positions">
          <float_array id="debug_body-mesh-positions-array" count="48">0 0.3568976 0.5 -0.2523647 0.2523647 0.5 -0.3568976 0 0.5 -0.2523647 -0.2523647 0.5 0 -0.3568976 0.5 0.2523648 -0.2523647 0.5 0.3568977 0 0.5 0.2523648 0.2523647 0.5 0 0.3568976 1.5 -0.2523647 0.2523647 1.5 -0.3568976 0 1.5 -0.2523647 -0.2523647 1.5 0 -0.3568976 1.5 0.2523648 -0.2523647 1.5 0.3568977 0 1.5 0.2523648 0.2523647 1.5</float_array>
          <technique_common>
            <accessor source="#debug_body-mesh-positions-array" count="16" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="debug_body-mesh-normals">
          <float_array id="debug_body-mesh-normals-array" count="30">0.9238795 0.3826835 0 0.3826833 -0.9238796 0 -0.9238795 -0.3826835 0 -0.3826835 0.9238795 0 0.3826833 0.9238796 0 0.9238795 -0.3826835 0 -0.3826835 -0.9238795 0 -0.9238795 0.3826835 0 0 0 1 0 0 -1</float_array>
          <technique_common>
            <accessor source="#debug_body-mesh-normals-array" count="10" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="debug_body-mesh-map-0">
          <float_array id="debug_body-mesh-map-0-array" count="168">0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645 0.2438576 0.427645</float_array>
          <technique_common>
            <accessor source="#debug_body-mesh-map-0-array" count="84" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="debug_body-mesh-vertices">
          <input semantic="POSITION" source="#debug_body-mesh-positions"/>
        </vertices>
        <triangles material="unicycle_debug-material" count="28">
          <input semantic="VERTEX" source="#debug_body-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#debug_body-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#debug_body-mesh-map-0" offset="2" set="0"/>
          <p>7 0 0 14 0 1 6 0 2 5 1 3 12 1 4 4 1 5 3 2 6 10 2 7 2 2 8 1 3 9 8 3 10 0 3 11 0 4 12 15 4 13 7 4 14 6 5 15 13 5 16 5 5 17 4 6 18 11 6 19 3 6 20 2 7 21 9 7 22 1 7 23 12 8 24 14 8 25 8 8 26 6 9 27 4 9 28 2 9 29 7 0 30 15 0 31 14 0 32 5 1 33 13 1 34 12 1 35 3 2 36 11 2 37 10 2 38 1 3 39 9 3 40 8 3 41 0 4 42 8 4 43 15 4 44 6 5 45 14 5 46 13 5 47 4 6 48 12 6 49 11 6 50 2 7 51 10 7 52 9 7 53 8 8 54 9 8 55 10 8 56 10 8 57 11 8 58 12 8 59 12 8 60 13 8 61 14 8 62 14 8 63 15 8 64 8 8 65 8 8 66 10 8 67 12 8 68 2 9 69 1 9 70 0 9 71 0 9 72 7 9 73 2 9 74 7 9 75 6 9 76 2 9 77 6 9 78 5 9 79 4 9 80 4 9 81 3 9 82 2 9 83</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="snowman_body-mesh" name="snowman_body">
      <mesh>
        <source id="snowman_body-mesh-positions">
          <float_array id="snowman_body-mesh-positions-array" count="1053">-0.005101919 0.1850219 0.4055748 -0.1131197 0.1455606 0.4093741 -0.1699286 0.06117528 0.4130293 -0.1655222 -0.07545185 0.4090081 -0.1119675 -0.1523947 0.4121775 -0.007411897 -0.1774345 0.4177305 0.1026687 -0.1505528 0.4159818 0.1690227 -0.05922287 0.4157811 0.1671988 0.05335342 0.4185758 0.1051704 0.1488348 0.4097748 0.006794035 0.2553286 0.5340514 -0.1664106 0.1971364 0.5452319 -0.2592713 0.07050442 0.5469592 -0.244527 -0.1053243 0.5533216 -0.1345399 -0.2459923 0.5640937 -0.02907598 -0.2686894 0.5667441 0.189431 -0.1718026 0.549069 0.2508457 -0.08581286 0.5573109 0.2493454 0.08002704 0.5549761 0.1590732 0.2011921 0.5354878 -0.01751124 -0.2032223 0.963656 -0.07604259 -0.1927101 0.9974664 -0.1187145 -0.2078385 0.898191 -0.02147549 -0.2146815 0.9195426 -0.01972442 -0.2788404 0.6688096 -0.1380296 -0.2491797 0.664001 -0.08898675 -0.176002 1.007021 -0.1276147 -0.1703208 0.8961031 -0.147704 -0.2270138 0.6579999 -0.004452884 -0.1927574 0.8938804 -0.00561136 -0.2595939 0.6642206 0.04381018 -0.1869443 0.9055379 0.06982642 -0.2525675 0.7175603 -0.02108824 -0.2445665 0.8029497 -0.1322467 -0.2288905 0.7811102 -0.1454191 -0.2112966 0.7860064 -0.009965121 -0.2399138 0.8077888 0.05324786 -0.2307932 0.8322262 0.05919528 -0.2032331 0.9269352 0.07753407 -0.2376487 0.8383162 0.08240514 -0.2572188 0.7329942 0.151416 -0.1660088 0.917666 0.1602872 -0.1986287 0.8300135 0.1138191 -0.1693776 0.9663875 0.1497942 -0.1443206 0.9521706 0.1562654 -0.1336883 0.8869146 0.1703814 -0.1754519 0.8206936 0.1431471 -0.1139244 0.9164754 0.1856444 -0.1133571 0.9441114 0.1584187 -0.08580887 0.9177274 0.2083693 0.04268062 0.9502192 0.19093 0.05136948 0.9144622 0.2140141 -0.03766757 0.9399287 0.1861773 -0.02407985 0.9106289 0.1746169 -0.2212448 0.7392625 0.1801428 -0.2037618 0.7354019 0.06822973 -0.2085531 0.956436 0.1225966 -0.1845502 0.9809742 -0.01591855 -0.1800381 1.033959 -0.03556925 -0.1574721 1.037824 -0.1254881 -0.1584028 1.031957 -0.09331852 -0.1403931 1.046654 -0.1693832 -0.09211689 1.060026 -0.1509948 -0.08773809 1.069828 -0.1554757 -0.1481715 0.9734156 -0.1984199 -0.07279241 0.9660187 -0.190586 -0.0855394 0.9045227 -0.1338983 -0.2283676 0.557259 0.04997318 -0.1765387 1.047199 0.04277002 -0.1661239 1.0539 0.001336634 -0.1830319 1.164341 0.0753147 -0.1655352 1.157934 0.00912255 -0.1624671 1.217765 -0.08810824 -0.1701583 1.165208 0.08158057 -0.1468608 1.211486 -0.0755279 -0.1494327 1.208266 0.02575594 -0.2005825 1.003177 0.06568437 -0.2047651 1.009783 0.1169982 -0.1604405 1.055985 0.1187848 -0.1845512 1.024817 0.1082887 -0.1354251 1.058867 0.1300022 -0.1319002 1.147338 0.130888 -0.1143277 1.200723 -0.253292 -0.1080167 0.7029406 -0.01811552 -0.2552207 0.5553558 -0.2336694 -0.08689486 0.8183389 0.1873694 -0.09949332 0.886506 0.2150893 -0.03613018 0.878655 0.2140395 0.07112371 0.8654925 0.2353808 -0.07845544 0.8058289 0.2065435 -0.126802 0.8361464 0.254274 0.07037293 0.7709633 0.253999 -0.09573924 0.6840164 0.2536329 0.07780122 0.6420308 0.1828582 -0.2063362 0.6379091 0.08100903 -0.2602247 0.650458 0.1787664 -0.2180761 0.662009 0.06928092 -0.2580504 0.635017 0.08453327 -0.2263399 0.5078502 0.1255221 -0.2279866 0.5611119 -0.1514059 -0.1019321 1.150578 -0.1478224 -0.08951389 1.1911 0.1659026 -0.05257505 1.045638 0.1586905 0.05539321 1.052024 0.09735304 0.1384479 1.056796 0.178667 -0.04559791 1.128601 0.1741531 0.0620501 1.112213 0.1188911 0.135654 1.104218 0.1745747 -0.02058535 1.170187 0.1711179 0.08707863 1.138405 0.1036072 0.1446385 1.121079 0.04710251 0.1627551 1.06402 -0.0255742 0.1692087 1.101035 -0.03558593 0.1677097 1.068707 -0.159807 0.09446895 1.13009 -0.1176097 0.133028 1.071365 -0.161695 0.07076698 1.076961 -0.1744394 0.06074261 0.9063407 -0.1983398 -0.002010405 0.9055097 -0.2302845 0.01454401 0.9643866 -0.1784306 0.00441277 1.161919 -0.1772029 -0.03762757 1.134689 -0.1754546 -4.47026e-4 1.076775 -0.1917689 0.06135863 1.05733 -0.2090802 0.08212828 0.9534209 -0.1930329 -0.006798923 1.068994 0.1681625 -0.1258093 0.9993443 -0.03547459 0.1832708 1.059698 0.1512573 -0.1516824 0.9673556 0.192241 -0.06478011 1.036399 0.1873583 0.0560767 1.044383 0.131687 0.1400568 0.96052 0.1232357 0.1277359 0.918585 -0.01114666 0.207436 0.9784941 0.1533821 0.1764622 0.8642526 0.0662266 0.2064524 0.870502 -0.03419435 0.2175424 0.8811378 -0.02271717 0.1620718 0.9311224 -0.1813308 0.1868081 0.7684664 -0.1627139 0.2135978 0.6414799 -0.229609 0.1289733 0.7590206 0.06390553 0.1550742 0.9249399 0.1982552 0.1834315 0.6307746 0.1951318 0.1774546 0.7523741 0.07270228 0.1747266 0.9682968 0.04926478 0.1753523 1.05207 0.1063094 0.1425881 1.044943 -0.1699044 0.1727825 0.8540806 -0.2601122 0.06936436 0.6676425 -0.1441926 0.1392461 1.028227 -0.1503992 0.1393688 0.9501284 -0.1392397 0.1091647 0.9178741 -0.2174521 0.1114442 0.8430384 -0.03129988 0.2573141 0.7672436 0.0893535 0.238943 0.7646164 -0.2409009 0.02769321 0.8299855 -0.01239997 0.2620376 0.7026215 -0.01862323 0.2728597 0.6222102 0.09509515 0.2552447 0.7024072 -0.07698166 0.1574787 1.070037 -0.1191992 0.1271082 1.120534 -0.102222 0.2231592 0.7702875 -0.1018471 0.200966 0.87687 -0.08784216 0.169672 1.044881 -0.09661006 0.1749725 0.9726232 -0.09931415 0.140959 0.9227761 -0.1079691 0.2404699 0.6976686 0.008048295 -0.1678397 1.231953 0.09109658 -0.1544619 1.223878 -0.08317565 -0.153793 1.221717 0.1389377 -0.1161649 1.21213 -0.1595103 -0.09286957 1.205048 0.1887561 -0.01809632 1.18207 0.1816807 0.09717822 1.146922 0.1097369 0.1589352 1.119161 -0.02592593 0.184062 1.103697 -0.1750757 0.09288007 1.143056 -0.188777 0.009441256 1.17176 -0.1323289 0.1474088 1.125079 0.01016479 -0.130397 1.323739 0.08050411 -0.1153852 1.317625 -0.07226455 -0.1177046 1.314489 0.1242251 -0.07063299 1.304265 -0.1426008 -0.05562496 1.296396 0.1660484 -0.00820738 1.283068 0.1710394 0.1031108 1.190682 0.1068795 0.1683863 1.165365 -0.02336078 0.1864057 1.147701 -0.166835 0.1056175 1.194543 -0.165664 0.02466493 1.275366 -0.1233067 0.1516662 1.169291 0.008574962 -0.09111678 1.389412 0.06768697 -0.07850104 1.384274 -0.06069731 -0.08045023 1.381639 0.1043009 -0.04050487 1.373086 -0.1227282 -0.02356773 1.365297 0.1371271 -1.76318e-5 1.359416 0.1420717 0.1019972 1.316319 0.0778172 0.1793352 1.293501 -0.01705211 0.1905385 1.294056 -0.1437529 0.1092215 1.299401 -0.1371914 0.03795129 1.347809 -0.09036237 0.1656284 1.291435 0.004410088 -0.03333634 1.432246 0.04426223 -0.02483111 1.428782 -0.04136514 -0.02380228 1.426712 0.071419 -0.006881475 1.422097 -0.08318507 0.01454669 1.415694 0.09107756 0.02808082 1.412024 0.09242564 0.0809983 1.393062 0.05567783 0.1342515 1.376346 -0.01396512 0.1475958 1.368296 -0.08390939 0.1056626 1.383353 -0.09356564 0.04845201 1.405367 -0.05121171 0.131314 1.37612 0.001552641 -0.01315754 1.502247 0.04192394 -0.007353842 1.502262 -0.04530918 0.001170456 1.493135 0.06938284 0.01079523 1.495503 -0.0878787 0.03009307 1.489325 0.08926022 0.04614681 1.485318 0.09062325 0.09965324 1.466145 0.05346649 0.153499 1.449244 -0.01695144 0.1669918 1.441104 -0.08919775 0.1247117 1.456925 -0.09837478 0.06437581 1.478883 -0.05461239 0.1505288 1.449014 -0.00170058 0.02607232 1.539609 0.02854377 0.02150082 1.530166 -0.02698993 0.03615576 1.535657 0.04111474 0.04297572 1.533124 -0.05535876 0.04927092 1.521828 0.05367571 0.06531512 1.526688 0.05704677 0.09899693 1.510581 0.03105694 0.1331534 1.50389 -0.001693367 0.133761 1.510467 -0.0515539 0.09850859 1.515427 -0.05089193 0.07541078 1.525199 -0.04122334 0.1351169 1.500069 0.01322388 0.05153626 1.541364 -0.003698348 0.07759505 1.536686 -0.01873093 0.1032554 1.528078 -0.2314676 -0.01828408 0.7806987 -0.2238955 -0.01828408 0.7577659 -0.2296746 -0.02665597 0.7752685 -0.2256885 -0.02665597 0.7631956 -0.2296746 -0.008204281 0.7752685 -0.2256885 -0.008204281 0.7631956 -0.7401302 -0.01809304 0.5211013 -0.7334364 -0.01809304 0.4939979 -0.741711 -0.02743035 0.513059 -0.7396914 -0.02743035 0.498018 -0.741711 -0.006850898 0.513059 -0.7371718 -0.006850898 0.4993112 -0.7529555 -0.02593553 0.496898 -0.7510359 -0.02593553 0.4901763 -0.7529555 -0.005356073 0.496898 -0.7510359 -0.005356073 0.4901763 -0.7572036 -0.01564586 0.4934863 -0.7559695 -0.01564586 0.4891641 -0.8204064 -0.008232176 0.5089454 -0.8224374 -0.003591537 0.5112031 -0.82063 0.00239557 0.5093703 -0.8209872 -0.008153975 0.5028274 -0.8212109 0.002473831 0.5032519 -0.8234039 -0.002839922 0.5011819 -0.7795065 -0.003095448 0.4299679 -0.7822062 -0.008446395 0.4324536 -0.7817456 0.002257943 0.431213 -0.7863104 -0.007530212 0.4354465 -0.7857637 0.002029061 0.43465 -0.7886051 -0.00333178 0.4372667 0.002524018 -0.1523237 1.121374 0.002481818 -0.1491898 1.085872 0.01613152 -0.1516546 1.112942 0.01610916 -0.1500049 1.094251 -0.0138815 -0.1514937 1.113 -0.01390379 -0.1498439 1.094309 9.38141e-4 -0.3489097 1.080341 9.26211e-4 -0.3479125 1.069044 0.00480169 -0.3486967 1.077658 0.004795372 -0.3481718 1.07171 -0.003719687 -0.3486457 1.077677 -0.003726065 -0.3481204 1.071729 8.00755e-4 -0.3568493 1.074967 7.9692e-4 -0.3565304 1.071356 0.00212723 -0.3577843 1.073996 0.002033472 -0.3566134 1.072208 -6.88121e-4 -0.3567648 1.074115 -7.39255e-4 -0.357591 1.072003 0.002256989 -0.2305158 1.113204 -0.01230412 -0.2296902 1.104874 0.01431483 -0.2282091 1.086222 0.01433467 -0.2298502 1.104816 0.002219498 -0.2273983 1.077886 -0.01232409 -0.228049 1.086281 -0.002510428 -0.1835262 1.106586 0.06465691 -0.1696427 1.102787 -0.08987927 -0.1694371 1.110249 0.1258452 -0.1349532 1.092263 -0.1547085 -0.1029218 1.10514 0.178667 -0.04559791 1.088748 0.1680833 0.05951452 1.075384 0.1157081 0.134122 1.076448 -0.1746549 -0.03881007 1.102602 0.04362398 0.1811344 1.105031 -0.08823156 0.1743914 1.112364 0.04515957 0.1893535 1.15372 -0.08102858 0.1562996 1.108715 -0.07807725 0.1770638 1.160187 0.04196166 0.1687361 1.104658 0.1261084 0.175728 0.4581308 0.1331968 -0.1825267 0.4671476 -0.1363761 0.176595 0.4592033 -0.2173415 -0.0751791 0.4684873 -0.1349477 -0.187388 0.4688593 0.2082497 -0.0716691 0.4634345 -0.2200406 0.06621193 0.4668248 -0.003735244 0.2180641 0.4577075 0.2064329 0.06606912 0.4602971 -0.006388962 -0.2269225 0.4678185 0.2300203 0.01861214 0.7476776 0.2391998 0.01861214 0.7697874 0.2321937 0.01024019 0.7529126 0.2370263 0.01024019 0.7645525 0.2321937 0.02869188 0.7529126 0.2370263 0.02869188 0.7645525 0.7381814 0.01880311 0.4867313 0.750564 0.01880311 0.5109227 0.7442815 0.009465873 0.4908418 0.7523065 0.009465873 0.5028081 0.7442815 0.03004539 0.4908418 0.7497846 0.03004539 0.5040958 0.7586586 0.009465873 0.4878277 0.7615531 0.009465873 0.49405 0.7586586 0.03004539 0.4878277 0.7615531 0.03004539 0.49405 0.7636796 0.0197556 0.486494 0.7655408 0.0197556 0.490495 0.7970054 0.01432788 0.4216188 0.7969762 0.01896858 0.4183366 0.7968919 0.0249558 0.4211377 0.8012233 0.01440608 0.4252804 0.80111 0.02503401 0.4247992 0.8038952 0.01972013 0.4243211 0.8185376 0.01946461 0.5105114 0.8188205 0.01411366 0.5064991 0.8192828 0.02481824 0.5077385 0.8197418 0.01502978 0.5009331 0.8198676 0.02458935 0.5019453 0.820165 0.01922827 0.4977217</float_array>
          <technique_common>
            <accessor source="#snowman_body-mesh-positions-array" count="351" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="snowman_body-mesh-normals">
          <float_array id="snowman_body-mesh-normals-array" count="1053">-0.7105405 0.2322791 -0.6642128 -0.8162003 -0.2578245 -0.517053 -0.8000242 0.2468374 -0.5468388 0.5115036 0.6419737 -0.5711688 -0.01269584 0.8711938 -0.4907752 0.5057622 0.6838105 -0.5259353 0.7127782 -0.2295661 -0.662757 0.516331 -0.6735682 -0.5288746 0.417809 -0.6223188 -0.6619328 -0.489292 0.630934 -0.6020928 -0.02017313 0.8447079 -0.5348474 0.7892206 0.2490653 -0.5613355 0.804668 -0.2497687 -0.5386326 -0.7244755 -0.282673 -0.6286743 -0.5013738 -0.714308 -0.4882504 -0.6153857 0.7512565 -0.2385672 -0.5110187 0.7026507 -0.495118 0.6899477 0.2311829 -0.6859496 0.1512826 -0.9836271 0.09793543 -0.3004309 -0.8309155 0.468317 -0.6531464 -0.6989561 0.291308 0.108526 -0.9640687 0.2424744 -0.5278013 -0.8299426 0.1806133 0.1967244 -0.8891967 -0.4130724 -0.6397456 -0.7664922 0.05670481 -0.6214942 -0.5993677 -0.5044834 -0.3528644 -0.7895044 0.502165 -0.8424955 -0.4906952 0.2223052 -0.7689421 -0.6093244 0.193525 0.4420366 -0.896647 0.02505606 0.1192091 -0.9371926 0.3278099 -0.2038994 -0.9746031 0.09259551 -0.1726757 -0.9230434 0.3437646 0.4051393 -0.9140735 0.01821976 -0.2040536 -0.9701854 -0.1307762 -0.8441839 -0.5360344 0.004547297 0.3435841 -0.937425 0.0564298 0.03820991 -0.9502128 0.3092502 0.106635 -0.9935306 -0.03906488 -0.01507645 -0.9933992 0.1137144 0.8397541 -0.4611079 0.2866932 0.524809 -0.7694512 -0.3640335 0.78746 -0.6047102 -0.1193001 0.9323762 -0.3614446 -0.005707144 0.7259007 -0.6206703 0.2963725 0.8507573 -0.2967396 0.4337714 0.7683541 -0.5265191 -0.3638817 0.9826659 -0.1766467 0.05624735 0.9491733 0.2934712 -0.113775 0.9385309 -0.1385273 -0.3161804 0.89958 -0.425802 -0.09720319 0.9082149 -0.3951905 0.1377322 0.8557806 -0.4231141 0.2976814 0.2906064 -0.9449437 -0.1504304 -0.02603298 -0.9728655 0.2299023 -0.2813846 -0.8893829 0.3603066 -0.3345246 -0.8924521 0.3026927 -0.3526813 -0.7814472 0.5147389 -0.7561696 -0.5429333 0.3652821 -0.6811876 -0.4326701 0.5905762 -0.7045701 -0.7048447 0.08231067 -0.9106287 -0.3729434 0.1779568 -0.9330018 -0.3576542 0.03988856 -0.2064278 -0.6892015 0.6945422 0.1082815 -0.9145942 0.3896059 0.0339986 -0.9800701 0.195721 -0.3786851 -0.9255017 -0.006653189 -0.4656348 -0.8468835 0.2568514 0.3844444 -0.9124488 0.140142 0.04712057 -0.9985665 -0.02539139 -0.1244879 -0.9426168 0.3098006 0.002014219 -0.6740147 0.7387152 0.07471066 -0.9423375 0.3262184 0.5037732 -0.7871093 0.3559097 0.354724 -0.4528126 0.8180047 0.5759823 -0.7649551 -0.2882505 0.538304 -0.6410023 0.5471242 0.3249962 -0.9221603 -0.2097567 0.723487 -0.6845141 0.08948212 0.708733 -0.6784572 -0.193374 -0.6239107 -0.6492419 -0.4349948 0.0904898 -0.9294355 -0.357717 -0.02502578 -0.7287394 -0.6843338 -0.4400277 -0.673348 -0.5941197 0.1517106 -0.9816483 -0.1155455 0.2722627 -0.885304 -0.3769745 0.4392642 -0.8700746 -0.2236453 -0.924048 -0.3755643 0.07132238 -0.899149 -0.3838672 -0.2101839 -0.887286 -0.3355591 0.3164235 0.7113987 -0.375903 0.5938088 0.8492592 -0.1316906 0.5112891 0.9246481 0.3614419 0.1199414 0.8384414 0.3050764 0.4516022 0.9273891 -0.2686306 0.2603599 0.8170574 -0.511256 0.2665233 0.9518015 0.2380801 0.1933695 0.9527527 -0.3003103 0.04556536 0.9694462 0.2450701 -0.01071226 0.930957 -0.2821193 -0.2317931 0.9303713 0.2707949 -0.2471427 0.7232751 -0.6840273 -0.09476214 0.7158241 -0.6824058 0.1480479 0.1305005 -0.9838204 -0.1227486 0.7106946 -0.688843 -0.1428591 0.7345287 -0.6171531 -0.2821166 -0.01922696 -0.8511748 -0.5245301 -0.8282423 -0.5354368 0.1652944 -0.7571197 -0.6130082 -0.2258114 0.6287604 0.7757418 -0.05371409 0.7875241 0.2128121 -0.5783743 0.9564178 0.2652443 -0.1221082 0.9094436 -0.2747954 -0.31209 0.9666104 -0.2419577 0.08438611 -0.7774029 0.3277837 0.536845 -0.8320152 0.04620605 0.5528253 -0.8528722 0.4117103 0.3210974 -0.7401263 -0.09818094 0.6652622 -0.9945802 -0.002319395 -0.1039474 -0.9123224 0.4063684 0.05032676 -0.997083 0.04278749 0.06320464 -0.9841713 -0.1627264 0.07019329 0.7435001 -0.6198987 -0.2508649 0.8101641 -0.5514523 0.1988329 0.7712534 -0.1994742 0.6044654 0.7892511 -0.2021881 0.5798299 0.7259349 0.286028 0.625465 0.8268358 0.3065079 0.4715882 0.6255309 0.773124 -0.104835 -0.9690113 0.2464721 0.01638871 -0.9412941 0.265789 -0.2081388 0.713357 0.6978837 -0.06387674 0.7326748 0.6598255 0.1667875 0.5597563 0.6622404 0.4981071 0.6073731 0.7621691 0.2240453 0.2277649 0.8232866 0.5199255 -0.06659221 0.9965333 0.04986786 0.2840075 0.9458981 0.1568968 0.3756278 0.9047661 -0.2007539 -9.15573e-5 0.9734371 -0.2289543 0.2826421 0.8895855 0.358819 -0.1020244 0.818362 0.5655747 -0.07040798 0.8567379 0.5109237 0.3258179 0.8290106 0.4545153 0.6016547 0.6880239 0.4057522 0.5929929 0.7779712 0.2076543 -0.6354371 0.7563841 0.1552504 -0.8266806 0.3534153 0.4378321 -0.6077919 0.6581487 0.4443305 -0.377488 0.9011002 -0.2133574 -0.6443634 0.7133685 0.2755017 -0.4055973 0.8597199 0.3104392 -0.4468597 0.8860286 0.1235715 -0.5673261 0.7455293 -0.349753 -0.6319029 0.7331355 0.2514182 -0.383503 0.871656 0.305191 -0.7376522 0.6751791 0.001586973 -0.843672 0.4710634 -0.2575207 -0.9339891 0.07703125 0.3488993 -0.8773388 0.4661215 0.1140506 -0.6259204 0.7782118 -0.05108928 -0.1207966 0.9695167 0.2131795 -0.07000994 0.842164 0.5346574 0.3043996 0.9145417 0.2663726 -0.3979105 0.8865244 0.2360972 -0.3531981 0.7854406 0.5082658 0.5673545 0.7772055 -0.2721409 -0.0585668 0.9945376 -0.08640056 -0.04648047 0.9919305 0.1179561 0.3414154 0.9394949 0.02801638 -0.3921074 0.9154158 0.09091591 0.04376453 0.9539081 -0.2968908 -0.8119194 0.2966722 -0.5027649 -0.6213432 0.5502942 -0.5577715 -0.5897623 0.541938 -0.5987352 0.01409965 -0.9993683 0.03262454 -0.4165208 -0.9083622 -0.03726345 0.7605652 -0.643372 -0.08725392 0.390186 -0.9144726 -0.1072134 -0.9173333 -0.1537535 -0.3672324 -0.8897202 0.2353014 -0.3911924 0.516421 0.8478937 0.1199417 0.03985768 0.7080388 0.705048 0.32503 0.6878124 0.6490529 0.9385468 -0.2441815 -0.2439373 -0.8060439 -0.5772411 -0.1307139 -0.4296206 0.627691 -0.6491766 -0.07834285 0.7470806 -0.6601008 -0.08630704 0.7486861 -0.6572825 0.4705522 0.4530339 -0.7571929 0.8666965 0.3398056 -0.3651979 -0.9652228 -0.1229303 -0.2307232 0.4745201 -0.760276 0.4436342 0.02252316 -0.894304 0.4468926 -0.3115067 0.9500848 0.01739579 -0.0794413 0.9879352 0.1329414 0.5032342 0.4815043 -0.7175717 0.8734058 0.4633159 0.1500031 -0.8749943 0.468505 0.122017 0.9411225 -0.1524437 0.3017439 0.7677475 -0.5043646 0.3951965 -0.4316334 -0.789868 0.4356619 -0.827531 -0.4197015 0.3728849 -0.9605736 -0.003570735 0.2780029 -0.6042701 0.7940048 0.06643915 -0.8235361 0.4598361 0.3321734 -0.5015865 0.8222234 0.268998 0.4512312 -0.6229341 0.6390178 0.01150578 -0.7424159 0.6698406 0.4107838 0.8672848 0.2812007 0.8227767 0.4211699 0.3816471 0.837319 -0.1442632 0.5273376 0.6917389 -0.4274462 0.5820544 -0.4121371 -0.6495188 0.638959 -0.7710025 -0.3041527 0.5595055 -0.8667357 0.09448635 0.4897361 0.1448731 0.9858818 -0.08395743 0.5429735 0.8384944 -0.04590141 0.884308 0.3499082 0.309134 0.9091628 -0.1364507 0.3934518 -0.804331 0.5481842 0.2292289 -0.4858291 0.8624314 0.1420651 0.4119458 -0.7381026 0.5343271 -0.01669383 -0.822699 0.5682321 -0.1000736 0.9601451 0.2609727 -0.100651 0.9782501 0.1813733 0.5043244 0.8355764 0.2178742 0.7149236 -0.5252743 0.4614881 -0.454954 -0.6885197 0.5647632 -0.8091995 -0.3958406 0.4341734 -0.956716 0.0290848 0.2895662 0.9026417 0.3529568 0.2462915 0.9145666 -0.1325445 0.3820994 -0.4930694 0.8678461 0.06103855 0.3627846 -0.7755905 0.516572 -0.09753996 -0.8480424 0.5208745 -0.1056863 0.993506 0.04214632 0.7192202 -0.521485 0.4591034 -0.8292856 0.5340473 0.164557 -0.4706399 -0.7463218 0.4706399 -0.7766872 -0.4525715 0.4381054 -0.9418168 0.03552412 0.3342443 0.2124179 0.6211088 -0.7543889 0.5680155 0.4041602 0.7169471 0.5535831 0.1449955 0.8200745 -0.3770392 0.6999061 0.6066077 0.2958862 -0.4748829 0.828817 -0.0815472 -0.4118866 0.907579 -0.5582066 0.3724938 0.7413864 0.4542787 -0.1702668 0.874437 -0.5812394 -0.2032278 0.7879463 -0.507196 0.1621171 0.8464457 -0.3651989 -0.287923 0.8852853 0.1155746 0.05111896 0.9919826 -4.57782e-4 0.2521461 0.9676892 -0.1225969 0.396418 0.9098477 0.7121333 -0.6931504 -0.1113948 0.6348388 -0.1010199 -0.7660122 0.8648056 -0.03296005 -0.5010239 0.7174884 0.6824517 -0.1395362 -0.04721266 0.9032973 0.4264096 0.4115793 0.687227 0.5985997 -0.3297878 -0.06674492 0.9416927 0.4295881 -0.7008121 0.5694881 0.2575526 -0.03323554 0.9656926 -0.18162 -0.9118538 0.3681536 0.3779825 0.8525894 -0.3608611 -0.1454233 0.9521946 -0.2686592 0.1929121 -0.9242751 -0.3293941 -0.3683055 -0.9084341 -0.1977338 -0.380176 -0.9083083 -0.1744775 -0.1284573 -0.6525411 -0.7467858 -0.6215756 -0.03589004 -0.7825316 -0.8961929 -0.3756289 -0.2360956 -0.5329562 -0.739754 0.4107574 -0.1303468 -0.6793538 -0.7221415 -0.7010329 -0.04992985 0.711379 -0.4614483 0.7897419 -0.4041945 -0.5711374 -0.7024619 -0.4246755 -0.7727137 0.03384566 -0.6338517 -0.4171403 0.7592638 0.4995124 0.6661412 -0.5178792 -0.5367096 0.66661 -0.7088187 0.2306674 0.2792221 -0.0365622 -0.9595303 -0.5718641 -0.8119872 0.1168265 -0.6625829 -0.6219916 0.4172655 0.03845435 -0.4038323 0.9140245 -0.8351475 -0.3127263 0.452472 -0.1016914 -0.6584916 0.7456861 0.85536 -0.3000032 0.4223239 -0.8562868 -0.1187821 0.5026568 0.04269599 -0.1837851 0.9820388 0.887684 0.00601226 -0.4604139 0.8780322 -0.1184748 0.463706 0.8652898 -0.1495152 -0.4784547 0.03976607 0.08429306 -0.9956473 -0.8480966 -0.1482009 -0.5086931 -0.8685147 0.01828098 -0.4953262 0.5122666 -0.8352525 0.1998404 -0.500241 -0.757793 -0.4189377 0.1532371 -0.4468933 -0.8813654 0.6917207 -0.4857763 -0.5343631 0.03732526 -0.08517974 -0.9956663 0.03994899 -0.1139569 0.9926822 0.8732123 -0.07016336 0.4822627 -0.8528225 -0.07095664 0.5173578 -0.858677 0.0351271 -0.511312 0.03460907 0.09628891 -0.9947516 0.8776453 0.03381544 -0.4781163 7.32464e-4 -0.9909632 -0.1341325 -0.4620624 -0.8750802 -0.14399 0.3456567 -0.9325041 -0.1046797 -0.8402338 -0.5296363 -0.1161574 0.6330564 0.7273907 -0.2648442 0.9015086 0.3234127 -0.2875525 0.9677325 -0.2148855 -0.1315987 0.6861589 -0.7099027 -0.1588209 -0.9652531 -0.2034392 -0.1640087 0.1791802 0.6566279 -0.7326216 -0.3786837 0.7021579 -0.6029702 -0.4067616 -0.6979465 0.5894198 0.2614904 -0.06729531 0.9628573 -0.2204681 -0.03308242 0.974833 -0.3881779 0.6851025 0.616403 -0.2729052 0.901973 -0.3346157 -0.7221563 0.6827859 -0.1108779 -0.6063795 -0.04733473 -0.7937654 -0.7151482 -0.6941818 -0.08169895 -0.8829467 -0.03296053 -0.4683148 -0.24983 -0.917773 -0.3086711 0.1037661 0.902857 0.4172314 0.3741345 0.9268672 -0.03067171 0.08484292 -0.9265887 0.3663811 0.3739228 -0.9270245 -0.02841347 0.2645135 -0.9277963 -0.2631096 0.270488 0.9278627 -0.256724 0.9922738 0.001312315 0.1240609 0.5778251 0.003814876 -0.8161517 -0.05118083 -0.7877027 -0.6139259 0.5371978 -0.08447694 0.8392152 -0.07489383 0.7200064 -0.6899145 0.6546568 0.7493869 -0.09921628 0.6693339 -0.7410221 -0.05365169 -0.06061077 -0.08111959 -0.9948598 0.9877974 0.009369194 -0.155462 0.615735 0.6429281 0.4555371 0.6919232 0.6607024 -0.2910582 0.6798729 -0.005188226 -0.7333118 0.5788899 -0.727641 0.3680016 0.6900166 -0.6501581 -0.3180748 0.5175485 -0.7553559 0.4019714</float_array>
          <technique_common>
            <accessor source="#snowman_body-mesh-normals-array" count="351" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="snowman_body-mesh-map-0">
          <float_array id="snowman_body-mesh-map-0-array" count="4044">0.7268944 0.9461439 0.6619715 0.9066622 0.7252817 0.9101398 0.3004664 0.9636306 0.2287343 0.9569483 0.2946858 0.9368837 0.4230973 0.9512196 0.4809621 0.9129567 0.4862269 0.9434596 0.7841948 0.9530001 0.8605996 0.9456296 0.8450219 0.9729975 0.4230973 0.9512196 0.3574759 0.92642 0.4203891 0.9203698 0.6515879 0.9414781 0.6008828 0.9052262 0.6619715 0.9066622 0.7252817 0.9101398 0.7925775 0.8834027 0.7909143 0.9230342 0.3608282 0.953599 0.2946858 0.9368837 0.3574759 0.92642 0.5399475 0.7503922 0.5650082 0.7167126 0.5832839 0.7564445 0.5419279 0.7929753 0.5832839 0.7564445 0.5881148 0.7983009 0.5515599 0.8675903 0.5879222 0.8372254 0.5864818 0.8648728 0.5832839 0.7564445 0.5738071 0.7091384 0.5991334 0.7512468 0.5832839 0.7564445 0.5966258 0.7952995 0.5881148 0.7983009 0.5419279 0.7929753 0.5377995 0.8396387 0.5371125 0.7914925 0.5371125 0.7914925 0.5094949 0.8243677 0.5111127 0.7862501 0.5290272 0.7606014 0.5111127 0.7862501 0.5141321 0.7596886 0.5419279 0.7929753 0.5290272 0.7606014 0.5399475 0.7503922 0.5881148 0.7983009 0.5975185 0.839123 0.5879222 0.8372254 0.5466961 0.8377726 0.5881148 0.7983009 0.5879222 0.8372254 0.5141321 0.7596886 0.5007824 0.7862811 0.5028623 0.7503656 0.5039301 0.8203099 0.5111127 0.7862501 0.5094949 0.8243677 0.5007824 0.7862811 0.4589771 0.759084 0.5028623 0.7503656 0.4727908 0.7399032 0.4589771 0.759084 0.4521602 0.7433568 0.4589771 0.759084 0.4380406 0.7563514 0.4521602 0.7433568 0.4659403 0.7920605 0.4439212 0.7682473 0.4589771 0.759084 0.4380406 0.7563514 0.4312847 0.7402563 0.4521602 0.7433568 0.3967977 0.7568753 0.367306 0.7360039 0.4012361 0.7404128 0.4259133 0.7562612 0.4012361 0.7404128 0.4312847 0.7402563 0.5039301 0.8203099 0.4659403 0.7920605 0.5007824 0.7862811 0.4659403 0.7920605 0.4631335 0.8227609 0.4560827 0.7944211 0.5290272 0.7606014 0.5028623 0.7503656 0.5399475 0.7503922 0.4727908 0.7399032 0.4982395 0.7388785 0.5028623 0.7503656 0.5028623 0.7503656 0.5375692 0.7326094 0.5399475 0.7503922 0.5650082 0.7167126 0.5462043 0.6882235 0.5738071 0.7091384 0.5738071 0.7091384 0.5812097 0.6826195 0.5938829 0.6981868 0.5938829 0.6981868 0.6239284 0.6701723 0.6292431 0.6800677 0.6292431 0.6800677 0.6103433 0.7202755 0.5938829 0.6981868 0.5738071 0.7091384 0.6103433 0.7202755 0.5991334 0.7512468 0.6489663 0.7436994 0.6103433 0.7202755 0.6514567 0.7166342 0.5344895 0.7001089 0.5019834 0.691319 0.5462043 0.6882235 0.7081227 0.4616826 0.6776376 0.4812427 0.6740837 0.4645502 0.7382892 0.4603712 0.7119697 0.4810864 0.7081227 0.4616826 0.4982395 0.7388785 0.5151194 0.7184686 0.5375692 0.7326094 0.4997704 0.6981855 0.5151194 0.7184686 0.496869 0.7194907 0.5375692 0.7326094 0.5344895 0.7001089 0.5650082 0.7167126 0.4997704 0.6981855 0.4740701 0.7162523 0.4686558 0.7026585 0.496869 0.7194907 0.4734783 0.731046 0.4740701 0.7162523 0.4997704 0.6981855 0.4642731 0.6907822 0.5019834 0.691319 0.741412 0.4796752 0.7643178 0.4609817 0.7653087 0.480566 0.5515599 0.8675903 0.591271 0.8711313 0.5471479 0.8738947 0.5515599 0.8675903 0.5377995 0.8396387 0.5466961 0.8377726 0.5864818 0.8648728 0.5975185 0.839123 0.591271 0.8711313 0.5459451 0.9385288 0.6008828 0.9052262 0.6015892 0.9352068 0.511176 0.8501723 0.5057838 0.8927308 0.4897609 0.8753 0.5966258 0.7952995 0.6557594 0.8224276 0.5975185 0.839123 0.5975185 0.839123 0.655372 0.8722486 0.591271 0.8711313 0.5991334 0.7512468 0.6590817 0.7809602 0.5966258 0.7952995 0.4260713 0.7721934 0.4380406 0.7563514 0.4439212 0.7682473 0.3967977 0.7568753 0.4260713 0.7721934 0.3987969 0.7745762 0.3585812 0.7515046 0.3987969 0.7745762 0.3510377 0.775877 0.4260713 0.7721934 0.4128621 0.8020641 0.3987969 0.7745762 0.4439212 0.7682473 0.4329681 0.7911397 0.4260713 0.7721934 0.3987969 0.7745762 0.3550215 0.8143847 0.3510377 0.775877 0.4128621 0.8020641 0.4560827 0.7944211 0.4631335 0.8227609 0.4631335 0.8227609 0.4180412 0.8416569 0.4128621 0.8020641 0.4180412 0.8416569 0.3550215 0.8143847 0.4128621 0.8020641 0.3530953 0.8575038 0.4172721 0.8813049 0.3535155 0.8870125 0.5039301 0.8203099 0.4699977 0.8442098 0.4697165 0.8216345 0.4699977 0.8442098 0.4631335 0.8227609 0.4697165 0.8216345 0.5066454 0.8452942 0.5094949 0.8243677 0.511176 0.8501723 0.5377995 0.8396387 0.511176 0.8501723 0.5094949 0.8243677 0.5066454 0.8452942 0.4664637 0.852426 0.4699977 0.8442098 0.4577429 0.8823245 0.4180412 0.8416569 0.4664637 0.852426 0.5057838 0.8927308 0.5438621 0.9072323 0.4809621 0.9129567 0.4897609 0.8753 0.4664637 0.852426 0.511176 0.8501723 0.4897609 0.8753 0.4809621 0.9129567 0.4577429 0.8823245 0.6381462 0.4703292 0.6776376 0.4812427 0.6397063 0.4850624 0.8886817 0.4864226 0.8551136 0.4895173 0.8493421 0.4766096 0.8493421 0.4766096 0.8088803 0.4820511 0.804805 0.4649826 0.804805 0.4649826 0.7653087 0.480566 0.7643178 0.4609817 0.703791 0.661821 0.6701397 0.6560865 0.7087913 0.6434049 0.6703539 0.6665333 0.6514567 0.7166342 0.6292431 0.6800677 0.6928438 0.7054956 0.7291133 0.7278029 0.6928929 0.7349513 0.6397063 0.4850624 0.6091783 0.4750753 0.6381462 0.4703292 0.6239284 0.6701723 0.6703539 0.6665333 0.6292431 0.6800677 0.703791 0.661821 0.6928438 0.7054956 0.6703539 0.6665333 0.4727908 0.7399032 0.4549773 0.7365203 0.4734783 0.731046 0.4521602 0.7433568 0.4421906 0.723272 0.4549773 0.7365203 0.4549773 0.7365203 0.4740701 0.7162523 0.4734783 0.731046 0.4642731 0.6907822 0.4150696 0.7060152 0.4140529 0.692337 0.4421906 0.723272 0.4686558 0.7026585 0.4740701 0.7162523 0.4150696 0.7060152 0.4312847 0.7402563 0.4012361 0.7404128 0.4140529 0.692337 0.3642977 0.6983326 0.3621151 0.6849643 0.367306 0.7360039 0.4150696 0.7060152 0.4012361 0.7404128 0.3585812 0.7515046 0.3078171 0.7190725 0.367306 0.7360039 0.7260137 0.8337884 0.7925775 0.8834027 0.7247678 0.8762714 0.3535155 0.8870125 0.3055439 0.8641141 0.3530953 0.8575038 0.3530953 0.8575038 0.3054962 0.8202008 0.3550215 0.8143847 0.3510377 0.775877 0.3054962 0.8202008 0.2947406 0.7759965 0.655372 0.8722486 0.7260137 0.8337884 0.7247678 0.8762714 0.3038519 0.7379367 0.3510377 0.775877 0.2947406 0.7759965 0.2509709 0.763798 0.2170149 0.7129927 0.2661948 0.7283352 0.2509709 0.763798 0.3038519 0.7379367 0.2947406 0.7759965 0.3078171 0.7190725 0.2661948 0.7283352 0.2742897 0.7044059 0.2742897 0.7044059 0.2170149 0.7129927 0.2293285 0.6793338 0.2827017 0.6560947 0.2401769 0.6362839 0.2460895 0.6287738 0.2293285 0.6793338 0.2778814 0.6634368 0.2742897 0.7044059 0.2742897 0.7044059 0.3095741 0.67815 0.3078171 0.7190725 0.3642977 0.6983326 0.3078171 0.7190725 0.3095741 0.67815 0.3097121 0.6706118 0.2778814 0.6634368 0.2827017 0.6560947 0.3621151 0.6849643 0.3095741 0.67815 0.3097121 0.6706118 0.7871951 0.7950263 0.747833 0.7672836 0.7835455 0.7596043 0.7940481 0.6701971 0.7532389 0.6560528 0.7834504 0.6349001 0.7934219 0.7045341 0.7623392 0.691142 0.7940481 0.6701971 0.7834504 0.6349001 0.7524225 0.6298934 0.7796587 0.6191234 0.7532389 0.6560528 0.7087913 0.6434049 0.7524225 0.6298934 0.762384 0.7152537 0.7231693 0.6997119 0.7623392 0.691142 0.7532389 0.6560528 0.7231693 0.6997119 0.703791 0.661821 0.6514567 0.7166342 0.6928929 0.7349513 0.6489663 0.7436994 0.7934219 0.7045341 0.7835455 0.7596043 0.762384 0.7152537 0.6557594 0.8224276 0.7085085 0.7731482 0.7260137 0.8337884 0.7534565 0.8001264 0.7085085 0.7731482 0.747833 0.7672836 0.7534565 0.8001264 0.8020046 0.845662 0.7260137 0.8337884 0.762384 0.7152537 0.747833 0.7672836 0.7291133 0.7278029 0.7291133 0.7278029 0.7085085 0.7731482 0.6928929 0.7349513 0.6590817 0.7809602 0.6928929 0.7349513 0.7085085 0.7731482 0.2509709 0.763798 0.1873897 0.8075383 0.1979269 0.7485545 0.2947406 0.7759965 0.2508066 0.8136221 0.2509709 0.763798 0.85667 0.7289475 0.8305692 0.7899054 0.8177358 0.7412206 0.2930082 0.9014983 0.1942658 0.879616 0.3055439 0.8641141 0.1958121 0.839264 0.2508066 0.8136221 0.2504337 0.839504 0.2504337 0.839504 0.3054962 0.8202008 0.3055439 0.8641141 0.1942658 0.879616 0.2504337 0.839504 0.3055439 0.8641141 0.7835455 0.7596043 0.8305692 0.7899054 0.7871951 0.7950263 0.8416814 0.6868596 0.8177358 0.7412206 0.7934219 0.7045341 0.8104072 0.6143471 0.7796587 0.6191234 0.8031063 0.6062882 0.8416814 0.6868596 0.7940481 0.6701971 0.8450152 0.6481516 0.8104072 0.6143471 0.7940481 0.6701971 0.7834504 0.6349001 0.8826798 0.8625734 0.8329647 0.8234259 0.8868406 0.8216595 0.8329647 0.8234259 0.7871951 0.7950263 0.8305692 0.7899054 0.8750601 0.7892293 0.8329647 0.8234259 0.8305692 0.7899054 0.7925775 0.8834027 0.8826798 0.8625734 0.8836882 0.9092286 0.5543316 0.4993487 0.5297292 0.5148118 0.527993 0.503876 0.6776376 0.4812427 0.7116764 0.4858503 0.6765171 0.4865766 0.8088803 0.4820511 0.7658189 0.4855588 0.7653087 0.480566 0.7653087 0.480566 0.7431293 0.4853609 0.741412 0.4796752 0.5967908 0.4913821 0.5609859 0.5050147 0.5543316 0.4993487 0.1396802 0.7378973 0.1732872 0.7563106 0.1557811 0.7588029 0.8551136 0.4895173 0.8092305 0.4889703 0.8088803 0.4820511 0.6397063 0.4850624 0.6765171 0.4865766 0.6401272 0.4917689 0.9979608 0.51365 0.9639065 0.5136308 0.9662626 0.5060328 0.8944035 0.4947218 0.8551911 0.4959903 0.8551136 0.4895173 0.741412 0.4796752 0.7116764 0.4858503 0.7119697 0.4810864 0.5967908 0.4913821 0.6401272 0.4917689 0.5964866 0.497782 0.7116764 0.4858503 0.741978 0.5188024 0.7137175 0.5198826 0.9639065 0.5136308 0.9852589 0.5475783 0.9570893 0.5330516 0.8944945 0.5019291 0.8525252 0.512175 0.8551911 0.4959903 0.7658189 0.4855588 0.741978 0.5188024 0.7431293 0.4853609 0.5964866 0.497782 0.5649716 0.5252228 0.5609859 0.5050147 0.7658189 0.4855588 0.7986058 0.5229825 0.767251 0.5213551 0.6401272 0.4917689 0.6807699 0.5199454 0.643523 0.5253897 0.6401272 0.4917689 0.6090652 0.5341989 0.5964866 0.497782 0.7116764 0.4858503 0.6807699 0.5199454 0.6765171 0.4865766 0.8092305 0.4889703 0.8525252 0.512175 0.7986058 0.5229825 0.5297292 0.5148118 0.5649716 0.5252228 0.5346451 0.5326449 0.5346451 0.5326449 0.5818749 0.5619173 0.5523074 0.5827227 0.7137175 0.5198826 0.7405338 0.547189 0.7145456 0.5483428 0.8525252 0.512175 0.8813012 0.5700325 0.8358098 0.5560289 0.767251 0.5213551 0.7405338 0.547189 0.741978 0.5188024 0.6090652 0.5341989 0.5818749 0.5619173 0.5649716 0.5252228 0.767251 0.5213551 0.7876043 0.5515131 0.7638055 0.5508071 0.643523 0.5253897 0.684008 0.5485497 0.6472994 0.5546409 0.6090652 0.5341989 0.6472994 0.5546409 0.619408 0.5630598 0.7137175 0.5198826 0.684008 0.5485497 0.6807699 0.5199454 0.8944945 0.5019291 0.9215656 0.5263497 0.8909725 0.5195028 0.7986058 0.5229825 0.8358098 0.5560289 0.7876043 0.5515131 0.7876043 0.5515131 0.8142431 0.5964899 0.7814917 0.587821 0.5523074 0.5827227 0.5994977 0.606967 0.5799201 0.6212012 0.7145456 0.5483428 0.7387405 0.5837526 0.7147506 0.5834184 0.5247884 0.6127725 0.5799201 0.6212012 0.5617107 0.6360383 0.8358098 0.5560289 0.853665 0.6108838 0.8142431 0.5964899 0.7638055 0.5508071 0.7387405 0.5837526 0.7405338 0.547189 0.619408 0.5630598 0.5994977 0.606967 0.5818749 0.5619173 0.7638055 0.5508071 0.7814917 0.587821 0.7577682 0.5830872 0.6472994 0.5546409 0.6871932 0.5866674 0.6532152 0.5902059 0.619408 0.5630598 0.6532152 0.5902059 0.6329522 0.5947315 0.7145456 0.5483428 0.6871932 0.5866674 0.684008 0.5485497 0.9215656 0.5263497 0.8813012 0.5700325 0.8909725 0.5195028 0.08362573 0.7663302 0.120467 0.7581407 0.1101434 0.7780118 0.1022531 0.9133934 0.1358373 0.9070524 0.1158869 0.9276711 0.07174408 0.8201543 0.1012102 0.8061625 0.09966009 0.8223494 0.1158869 0.9276711 0.149762 0.9199937 0.1287466 0.9420193 0.1173202 0.7167634 0.120467 0.7581407 0.0963397 0.7425011 0.07365959 0.8004646 0.1037465 0.7931552 0.1012102 0.8061625 0.0859332 0.8878949 0.1233112 0.8949099 0.1022531 0.9133934 0.08362573 0.7663302 0.1037465 0.7931552 0.07688188 0.7850657 0.07883661 0.8706542 0.09999328 0.842835 0.1055298 0.8622086 0.0859332 0.8878949 0.1055298 0.8622086 0.1118184 0.8746002 0.07174408 0.843208 0.09966009 0.8223494 0.09999328 0.842835 0.9288727 0.498989 0.8944945 0.5019291 0.8944035 0.4947218 0.9252064 0.6002672 0.853665 0.6108838 0.8813012 0.5700325 0.1101434 0.7780118 0.1387699 0.7725375 0.1311716 0.7889887 0.1233112 0.8949099 0.1491371 0.8889637 0.1358373 0.9070524 0.09966009 0.8223494 0.1186974 0.8102479 0.1203005 0.8248296 0.149762 0.9199937 0.1491371 0.8889637 0.1684552 0.8921127 0.120467 0.7581407 0.1557811 0.7588029 0.1387699 0.7725375 0.1037465 0.7931552 0.1186974 0.8102479 0.1012102 0.8061625 0.1118184 0.8746002 0.1402181 0.870485 0.1233112 0.8949099 0.1037465 0.7931552 0.1311716 0.7889887 0.1260749 0.8005653 0.09999328 0.842835 0.1236326 0.8523182 0.1055298 0.8622086 0.1118184 0.8746002 0.1236326 0.8523182 0.1346161 0.8596904 0.09966009 0.8223494 0.1222793 0.8365837 0.09999328 0.842835 0.1222793 0.8365837 0.1323873 0.8202516 0.1429558 0.8303617 0.1433252 0.8015676 0.1323873 0.8202516 0.1307615 0.8059208 0.1186974 0.8102479 0.1323873 0.8202516 0.1203005 0.8248296 0.1222793 0.8365837 0.137075 0.8533771 0.1236326 0.8523182 0.1697039 0.8563198 0.1537173 0.8402928 0.171921 0.8357099 0.161682 0.8029187 0.1429558 0.8303617 0.1433252 0.8015676 0.171921 0.8357099 0.1429558 0.8303617 0.1760225 0.8195084 0.137075 0.8533771 0.1537173 0.8402928 0.1491547 0.8569224 0.0272181 0.6443149 0.01752871 0.9469527 0.02192956 0.6443634 0.05669414 0.947237 0.05059236 0.6444936 0.05669534 0.6446155 0.05059236 0.6444936 0.04303699 0.9477811 0.04460299 0.6442112 0.03621757 0.9454541 0.0332126 0.6441214 0.03850102 0.6440532 0.04460299 0.6442112 0.03621757 0.9454541 0.03850102 0.6440532 0.03026843 0.9476806 0.0272181 0.6443149 0.0332126 0.6441214 0.2030872 0.6921655 0.1951894 0.6939143 0.1988061 0.6877163 0.1782119 0.693442 0.1853904 0.693897 0.1837024 0.6967729 0.1968402 0.696644 0.1904649 0.696272 0.1951894 0.6939143 0.1837024 0.6967729 0.1904649 0.696272 0.1904962 0.6982681 0.1861478 0.639564 0.189583 0.6833836 0.1800827 0.6414254 0.1856809 0.7287927 0.1719172 0.6943501 0.1782119 0.693442 0.1933735 0.6395639 0.1922906 0.6832109 0.1861478 0.639564 0.2043491 0.6481285 0.1985144 0.6829282 0.1986474 0.6455227 0.1868764 0.6842691 0.1719172 0.652624 0.1766753 0.6485808 0.189583 0.6833836 0.1766753 0.6485808 0.1800827 0.6414254 0.1892901 0.6866156 0.1922906 0.6832109 0.1930594 0.6876778 0.1930594 0.6876778 0.1956568 0.6828308 0.1962213 0.6859318 0.1968402 0.696644 0.1959837 0.7290629 0.2030872 0.6921655 0.1904962 0.6982681 0.1935033 0.7288079 0.1968402 0.696644 0.1837024 0.6967729 0.1856809 0.7287927 0.1782119 0.693442 0.1991252 0.7295395 0.2030872 0.6921655 0.2095298 0.694436 0.1904962 0.6982681 0.1880393 0.7282668 0.1837024 0.6967729 0.1880393 0.7282668 0.1854922 0.732344 0.1856809 0.7287927 0.1907805 0.7323293 0.1880393 0.7282668 0.1884703 0.7341842 0.1525282 0.2930282 0.1537079 0.2880605 0.1513414 0.2882345 0.1534895 0.2928987 0.1557638 0.2884148 0.1537079 0.2880605 0.1473726 0.2305662 0.1537079 0.2880605 0.1555426 0.2305666 0.1557638 0.2884148 0.1706797 0.2319654 0.1626074 0.2309786 0.1580548 0.2892864 0.1779567 0.2332336 0.1706797 0.2319654 0.1307586 0.2323297 0.1490319 0.289049 0.1391741 0.2311822 0.1391741 0.2311822 0.1513414 0.2882345 0.1473726 0.2305662 0.1537079 0.2880605 0.1626074 0.2309786 0.1555426 0.2305666 0.1541219 0.2937558 0.1525282 0.2930282 0.1522084 0.2941415 0.1541219 0.2937558 0.152963 0.2949934 0.1537868 0.2948299 0.1557638 0.2884148 0.1554334 0.2935386 0.1580548 0.2892864 0.1554334 0.2935386 0.1600895 0.2905388 0.1580548 0.2892864 0.1505956 0.294188 0.1490319 0.289049 0.1466456 0.2904154 0.1490319 0.289049 0.1525282 0.2930282 0.1513414 0.2882345 0.1567196 0.190142 0.1626074 0.2309786 0.1650435 0.1904394 0.1391741 0.2311822 0.146982 0.189882 0.1383678 0.1898933 0.128728 0.1902785 0.1391741 0.2311822 0.1383678 0.1898933 0.1736255 0.1909137 0.1779567 0.2332336 0.181805 0.1915907 0.1650435 0.1904394 0.1706797 0.2319654 0.1736255 0.1909137 0.1473726 0.2305662 0.1567196 0.190142 0.146982 0.189882 0.1986474 0.6455227 0.1956568 0.6828308 0.1933735 0.6395639 0.0332126 0.6441214 0.03288888 0.6340983 0.03576195 0.6394867 0.0243752 0.6398542 0.0332126 0.6441214 0.0272181 0.6443149 0.7272179 0.4190862 0.7055336 0.4413693 0.6863941 0.4131863 0.6863941 0.4131863 0.6697849 0.44716 0.6568499 0.4228258 0.7630777 0.425854 0.7352418 0.4400988 0.7272179 0.4190862 0.6568499 0.4228258 0.6327627 0.4549062 0.6235396 0.4427867 0.857438 0.4532184 0.8924716 0.4764438 0.8541666 0.4631785 0.857438 0.4532184 0.8093671 0.4512715 0.8105683 0.4332917 0.8105683 0.4332917 0.7662134 0.4410382 0.7630777 0.425854 0.6235396 0.4427867 0.6055663 0.4638135 0.5857678 0.4605998 0.6091783 0.4750753 0.6327627 0.4549062 0.6381462 0.4703292 0.6381462 0.4703292 0.6697849 0.44716 0.6740837 0.4645502 0.6740837 0.4645502 0.7055336 0.4413693 0.7081227 0.4616826 0.7081227 0.4616826 0.7352418 0.4400988 0.7382892 0.4603712 0.7382892 0.4603712 0.7662134 0.4410382 0.7643178 0.4609817 0.7662134 0.4410382 0.804805 0.4649826 0.7643178 0.4609817 0.8093671 0.4512715 0.8493421 0.4766096 0.804805 0.4649826 0.8541666 0.4631785 0.8886817 0.4864226 0.8493421 0.4766096 0.1461098 0.6912932 0.1396802 0.7378973 0.1173202 0.7167634 0.9298115 0.4800267 0.9662626 0.5060328 0.9288727 0.498989 0.9280588 0.5058062 0.9570893 0.5330516 0.9215656 0.5263497 0.5043969 0.5208212 0.5346451 0.5326449 0.5099318 0.5431228 0.9288727 0.498989 0.9639065 0.5136308 0.9280588 0.5058062 0.5037884 0.5101695 0.5297292 0.5148118 0.5043969 0.5208212 0.9979608 0.51365 0.9744321 0.4915809 0.9979087 0.4951478 0.5178116 0.4855716 0.5037884 0.5101695 0.4947662 0.4951798 0.5178116 0.4855716 0.5543316 0.4993487 0.527993 0.503876 0.9852589 0.5475783 0.9215656 0.5263497 0.9570893 0.5330516 0.5099318 0.5431228 0.5523074 0.5827227 0.5247884 0.6127725 0.7268944 0.9461439 0.7909143 0.9230342 0.7841948 0.9530001 0.4203891 0.9203698 0.3535155 0.8870125 0.4172721 0.8813049 0.3574759 0.92642 0.2930082 0.9014983 0.3535155 0.8870125 0.5438621 0.9072323 0.591271 0.8711313 0.6008828 0.9052262 0.5438621 0.9072323 0.4862269 0.9434596 0.4809621 0.9129567 0.4203891 0.9203698 0.4577429 0.8823245 0.4809621 0.9129567 0.6008828 0.9052262 0.655372 0.8722486 0.6619715 0.9066622 0.2946858 0.9368837 0.2168741 0.9176495 0.2930082 0.9014983 0.7909143 0.9230342 0.8836882 0.9092286 0.8605996 0.9456296 0.6619715 0.9066622 0.7247678 0.8762714 0.7252817 0.9101398 0.5967908 0.4913821 0.5548099 0.4742764 0.6091783 0.4750753 0.5857678 0.4605998 0.6091783 0.4750753 0.5548099 0.4742764 0.8944035 0.4947218 0.8924716 0.4764438 0.9288727 0.498989 0.8924716 0.4764438 0.9298115 0.4800267 0.9288727 0.498989 0.0272181 0.6443149 0.01752871 0.9469527 0.02192956 0.6443634 0.05669414 0.947237 0.05059236 0.6444936 0.05669534 0.6446155 0.05059236 0.6444936 0.04303699 0.9477811 0.04460299 0.6442112 0.03621757 0.9454541 0.0332126 0.6441214 0.03850102 0.6440532 0.04460299 0.6442112 0.03621757 0.9454541 0.03850102 0.6440532 0.03026843 0.9476806 0.0272181 0.6443149 0.0332126 0.6441214 0.2030872 0.6921655 0.1951894 0.6939143 0.1988061 0.6877163 0.1782119 0.693442 0.1853904 0.693897 0.1837024 0.6967729 0.1968402 0.696644 0.1904649 0.696272 0.1951894 0.6939143 0.1837024 0.6967729 0.1904649 0.696272 0.1904962 0.6982681 0.1861478 0.639564 0.189583 0.6833836 0.1800827 0.6414254 0.1782119 0.693442 0.1823452 0.7296367 0.1719172 0.6943501 0.1956568 0.6828308 0.1861478 0.639564 0.1933735 0.6395639 0.2043491 0.6481285 0.1985144 0.6829282 0.1986474 0.6455227 0.1868764 0.6842691 0.1719172 0.652624 0.1766753 0.6485808 0.189583 0.6833836 0.1766753 0.6485808 0.1800827 0.6414254 0.1892901 0.6866156 0.1922906 0.6832109 0.1930594 0.6876778 0.1930594 0.6876778 0.1956568 0.6828308 0.1962213 0.6859318 0.1968402 0.696644 0.1959837 0.7290629 0.1935033 0.7288079 0.1904962 0.6982681 0.1935033 0.7288079 0.1904243 0.7287371 0.1837024 0.6967729 0.1856809 0.7287927 0.1782119 0.693442 0.2095298 0.694436 0.1959837 0.7290629 0.2030872 0.6921655 0.1904962 0.6982681 0.1880393 0.7282668 0.1837024 0.6967729 0.1880393 0.7282668 0.1854922 0.732344 0.1856809 0.7287927 0.1880393 0.7282668 0.1907805 0.7323293 0.1884703 0.7341842 0.1986474 0.6455227 0.1956568 0.6828308 0.1933735 0.6395639 0.0332126 0.6441214 0.03288888 0.6340983 0.03576195 0.6394867 0.0243752 0.6398542 0.0332126 0.6441214 0.0272181 0.6443149 0.7268944 0.9461439 0.6515879 0.9414781 0.6619715 0.9066622 0.3004664 0.9636306 0.2422103 0.9837903 0.2287343 0.9569483 0.4230973 0.9512196 0.4203891 0.9203698 0.4809621 0.9129567 0.7841948 0.9530001 0.7909143 0.9230342 0.8605996 0.9456296 0.4230973 0.9512196 0.3608282 0.953599 0.3574759 0.92642 0.6515879 0.9414781 0.6015892 0.9352068 0.6008828 0.9052262 0.7252817 0.9101398 0.7247678 0.8762714 0.7925775 0.8834027 0.3608282 0.953599 0.3004664 0.9636306 0.2946858 0.9368837 0.5399475 0.7503922 0.5375692 0.7326094 0.5650082 0.7167126 0.5419279 0.7929753 0.5399475 0.7503922 0.5832839 0.7564445 0.5515599 0.8675903 0.5466961 0.8377726 0.5879222 0.8372254 0.5832839 0.7564445 0.5650082 0.7167126 0.5738071 0.7091384 0.5832839 0.7564445 0.5991334 0.7512468 0.5966258 0.7952995 0.5419279 0.7929753 0.5466961 0.8377726 0.5377995 0.8396387 0.5371125 0.7914925 0.5377995 0.8396387 0.5094949 0.8243677 0.5290272 0.7606014 0.5371125 0.7914925 0.5111127 0.7862501 0.5419279 0.7929753 0.5371125 0.7914925 0.5290272 0.7606014 0.5881148 0.7983009 0.5966258 0.7952995 0.5975185 0.839123 0.5466961 0.8377726 0.5419279 0.7929753 0.5881148 0.7983009 0.5141321 0.7596886 0.5111127 0.7862501 0.5007824 0.7862811 0.5039301 0.8203099 0.5007824 0.7862811 0.5111127 0.7862501 0.5007824 0.7862811 0.4659403 0.7920605 0.4589771 0.759084 0.4727908 0.7399032 0.5028623 0.7503656 0.4589771 0.759084 0.4589771 0.759084 0.4439212 0.7682473 0.4380406 0.7563514 0.4659403 0.7920605 0.4560827 0.7944211 0.4439212 0.7682473 0.4380406 0.7563514 0.4259133 0.7562612 0.4312847 0.7402563 0.3967977 0.7568753 0.3585812 0.7515046 0.367306 0.7360039 0.4259133 0.7562612 0.3967977 0.7568753 0.4012361 0.7404128 0.5039301 0.8203099 0.4697165 0.8216345 0.4659403 0.7920605 0.4659403 0.7920605 0.4697165 0.8216345 0.4631335 0.8227609 0.5290272 0.7606014 0.5141321 0.7596886 0.5028623 0.7503656 0.4727908 0.7399032 0.4734783 0.731046 0.4982395 0.7388785 0.5028623 0.7503656 0.4982395 0.7388785 0.5375692 0.7326094 0.5650082 0.7167126 0.5344895 0.7001089 0.5462043 0.6882235 0.5738071 0.7091384 0.5462043 0.6882235 0.5812097 0.6826195 0.5938829 0.6981868 0.5812097 0.6826195 0.6239284 0.6701723 0.6292431 0.6800677 0.6514567 0.7166342 0.6103433 0.7202755 0.5738071 0.7091384 0.5938829 0.6981868 0.6103433 0.7202755 0.6489663 0.7436994 0.5991334 0.7512468 0.6103433 0.7202755 0.5344895 0.7001089 0.4997704 0.6981855 0.5019834 0.691319 0.7081227 0.4616826 0.7119697 0.4810864 0.6776376 0.4812427 0.7382892 0.4603712 0.741412 0.4796752 0.7119697 0.4810864 0.4982395 0.7388785 0.496869 0.7194907 0.5151194 0.7184686 0.4997704 0.6981855 0.5344895 0.7001089 0.5151194 0.7184686 0.5375692 0.7326094 0.5151194 0.7184686 0.5344895 0.7001089 0.4997704 0.6981855 0.496869 0.7194907 0.4740701 0.7162523 0.496869 0.7194907 0.4982395 0.7388785 0.4734783 0.731046 0.4997704 0.6981855 0.4686558 0.7026585 0.4642731 0.6907822 0.741412 0.4796752 0.7382892 0.4603712 0.7643178 0.4609817 0.5515599 0.8675903 0.5864818 0.8648728 0.591271 0.8711313 0.5515599 0.8675903 0.5471479 0.8738947 0.5377995 0.8396387 0.5864818 0.8648728 0.5879222 0.8372254 0.5975185 0.839123 0.5459451 0.9385288 0.5438621 0.9072323 0.6008828 0.9052262 0.511176 0.8501723 0.5471479 0.8738947 0.5057838 0.8927308 0.5966258 0.7952995 0.6590817 0.7809602 0.6557594 0.8224276 0.5975185 0.839123 0.6557594 0.8224276 0.655372 0.8722486 0.5991334 0.7512468 0.6489663 0.7436994 0.6590817 0.7809602 0.4260713 0.7721934 0.4259133 0.7562612 0.4380406 0.7563514 0.3967977 0.7568753 0.4259133 0.7562612 0.4260713 0.7721934 0.3585812 0.7515046 0.3967977 0.7568753 0.3987969 0.7745762 0.4260713 0.7721934 0.4329681 0.7911397 0.4128621 0.8020641 0.4439212 0.7682473 0.4560827 0.7944211 0.4329681 0.7911397 0.3987969 0.7745762 0.4128621 0.8020641 0.3550215 0.8143847 0.4128621 0.8020641 0.4329681 0.7911397 0.4560827 0.7944211 0.4631335 0.8227609 0.4664637 0.852426 0.4180412 0.8416569 0.4180412 0.8416569 0.3530953 0.8575038 0.3550215 0.8143847 0.3530953 0.8575038 0.4180412 0.8416569 0.4172721 0.8813049 0.5039301 0.8203099 0.5066454 0.8452942 0.4699977 0.8442098 0.4699977 0.8442098 0.4664637 0.852426 0.4631335 0.8227609 0.5066454 0.8452942 0.5039301 0.8203099 0.5094949 0.8243677 0.5377995 0.8396387 0.5471479 0.8738947 0.511176 0.8501723 0.5066454 0.8452942 0.511176 0.8501723 0.4664637 0.852426 0.4577429 0.8823245 0.4172721 0.8813049 0.4180412 0.8416569 0.5057838 0.8927308 0.5471479 0.8738947 0.5438621 0.9072323 0.4897609 0.8753 0.4577429 0.8823245 0.4664637 0.852426 0.4897609 0.8753 0.5057838 0.8927308 0.4809621 0.9129567 0.6381462 0.4703292 0.6740837 0.4645502 0.6776376 0.4812427 0.8886817 0.4864226 0.8944035 0.4947218 0.8551136 0.4895173 0.8493421 0.4766096 0.8551136 0.4895173 0.8088803 0.4820511 0.804805 0.4649826 0.8088803 0.4820511 0.7653087 0.480566 0.703791 0.661821 0.6703539 0.6665333 0.6701397 0.6560865 0.6703539 0.6665333 0.6928438 0.7054956 0.6514567 0.7166342 0.6928438 0.7054956 0.7231693 0.6997119 0.7291133 0.7278029 0.6397063 0.4850624 0.5967908 0.4913821 0.6091783 0.4750753 0.6239284 0.6701723 0.6701397 0.6560865 0.6703539 0.6665333 0.703791 0.661821 0.7231693 0.6997119 0.6928438 0.7054956 0.4727908 0.7399032 0.4521602 0.7433568 0.4549773 0.7365203 0.4521602 0.7433568 0.4312847 0.7402563 0.4421906 0.723272 0.4549773 0.7365203 0.4421906 0.723272 0.4740701 0.7162523 0.4642731 0.6907822 0.4686558 0.7026585 0.4150696 0.7060152 0.4421906 0.723272 0.4150696 0.7060152 0.4686558 0.7026585 0.4150696 0.7060152 0.4421906 0.723272 0.4312847 0.7402563 0.4140529 0.692337 0.4150696 0.7060152 0.3642977 0.6983326 0.367306 0.7360039 0.3642977 0.6983326 0.4150696 0.7060152 0.3585812 0.7515046 0.3038519 0.7379367 0.3078171 0.7190725 0.7260137 0.8337884 0.8020046 0.845662 0.7925775 0.8834027 0.3535155 0.8870125 0.2930082 0.9014983 0.3055439 0.8641141 0.3530953 0.8575038 0.3055439 0.8641141 0.3054962 0.8202008 0.3510377 0.775877 0.3550215 0.8143847 0.3054962 0.8202008 0.655372 0.8722486 0.6557594 0.8224276 0.7260137 0.8337884 0.3038519 0.7379367 0.3585812 0.7515046 0.3510377 0.775877 0.2509709 0.763798 0.1979269 0.7485545 0.2170149 0.7129927 0.2509709 0.763798 0.2661948 0.7283352 0.3038519 0.7379367 0.3078171 0.7190725 0.3038519 0.7379367 0.2661948 0.7283352 0.2742897 0.7044059 0.2661948 0.7283352 0.2170149 0.7129927 0.2827017 0.6560947 0.2778814 0.6634368 0.2401769 0.6362839 0.2293285 0.6793338 0.2401769 0.6362839 0.2778814 0.6634368 0.2742897 0.7044059 0.2778814 0.6634368 0.3095741 0.67815 0.3642977 0.6983326 0.367306 0.7360039 0.3078171 0.7190725 0.3097121 0.6706118 0.3095741 0.67815 0.2778814 0.6634368 0.3621151 0.6849643 0.3642977 0.6983326 0.3095741 0.67815 0.7871951 0.7950263 0.7534565 0.8001264 0.747833 0.7672836 0.7940481 0.6701971 0.7623392 0.691142 0.7532389 0.6560528 0.7934219 0.7045341 0.762384 0.7152537 0.7623392 0.691142 0.7834504 0.6349001 0.7532389 0.6560528 0.7524225 0.6298934 0.7532389 0.6560528 0.703791 0.661821 0.7087913 0.6434049 0.762384 0.7152537 0.7291133 0.7278029 0.7231693 0.6997119 0.7532389 0.6560528 0.7623392 0.691142 0.7231693 0.6997119 0.6514567 0.7166342 0.6928438 0.7054956 0.6928929 0.7349513 0.7934219 0.7045341 0.8177358 0.7412206 0.7835455 0.7596043 0.6557594 0.8224276 0.6590817 0.7809602 0.7085085 0.7731482 0.7534565 0.8001264 0.7260137 0.8337884 0.7085085 0.7731482 0.7534565 0.8001264 0.7871951 0.7950263 0.8020046 0.845662 0.762384 0.7152537 0.7835455 0.7596043 0.747833 0.7672836 0.7291133 0.7278029 0.747833 0.7672836 0.7085085 0.7731482 0.6590817 0.7809602 0.6489663 0.7436994 0.6928929 0.7349513 0.2509709 0.763798 0.2508066 0.8136221 0.1873897 0.8075383 0.2947406 0.7759965 0.3054962 0.8202008 0.2508066 0.8136221 0.85667 0.7289475 0.8750601 0.7892293 0.8305692 0.7899054 0.2930082 0.9014983 0.2168741 0.9176495 0.1942658 0.879616 0.1958121 0.839264 0.1873897 0.8075383 0.2508066 0.8136221 0.2504337 0.839504 0.2508066 0.8136221 0.3054962 0.8202008 0.1942658 0.879616 0.1958121 0.839264 0.2504337 0.839504 0.7835455 0.7596043 0.8177358 0.7412206 0.8305692 0.7899054 0.8416814 0.6868596 0.85667 0.7289475 0.8177358 0.7412206 0.8104072 0.6143471 0.7834504 0.6349001 0.7796587 0.6191234 0.8416814 0.6868596 0.7934219 0.7045341 0.7940481 0.6701971 0.8104072 0.6143471 0.8450152 0.6481516 0.7940481 0.6701971 0.8826798 0.8625734 0.8020046 0.845662 0.8329647 0.8234259 0.8329647 0.8234259 0.8020046 0.845662 0.7871951 0.7950263 0.8750601 0.7892293 0.8868406 0.8216595 0.8329647 0.8234259 0.7925775 0.8834027 0.8020046 0.845662 0.8826798 0.8625734 0.5543316 0.4993487 0.5609859 0.5050147 0.5297292 0.5148118 0.6776376 0.4812427 0.7119697 0.4810864 0.7116764 0.4858503 0.8088803 0.4820511 0.8092305 0.4889703 0.7658189 0.4855588 0.7653087 0.480566 0.7658189 0.4855588 0.7431293 0.4853609 0.5967908 0.4913821 0.5964866 0.497782 0.5609859 0.5050147 0.1396802 0.7378973 0.1685438 0.7186296 0.1732872 0.7563106 0.8551136 0.4895173 0.8551911 0.4959903 0.8092305 0.4889703 0.6397063 0.4850624 0.6776376 0.4812427 0.6765171 0.4865766 0.9979608 0.51365 0.9970815 0.52523 0.9639065 0.5136308 0.8944035 0.4947218 0.8944945 0.5019291 0.8551911 0.4959903 0.741412 0.4796752 0.7431293 0.4853609 0.7116764 0.4858503 0.5967908 0.4913821 0.6397063 0.4850624 0.6401272 0.4917689 0.7116764 0.4858503 0.7431293 0.4853609 0.741978 0.5188024 0.9639065 0.5136308 0.9970815 0.52523 0.9852589 0.5475783 0.8944945 0.5019291 0.8909725 0.5195028 0.8525252 0.512175 0.7658189 0.4855588 0.767251 0.5213551 0.741978 0.5188024 0.5964866 0.497782 0.6090652 0.5341989 0.5649716 0.5252228 0.7658189 0.4855588 0.8092305 0.4889703 0.7986058 0.5229825 0.6401272 0.4917689 0.6765171 0.4865766 0.6807699 0.5199454 0.6401272 0.4917689 0.643523 0.5253897 0.6090652 0.5341989 0.7116764 0.4858503 0.7137175 0.5198826 0.6807699 0.5199454 0.8092305 0.4889703 0.8551911 0.4959903 0.8525252 0.512175 0.5297292 0.5148118 0.5609859 0.5050147 0.5649716 0.5252228 0.5346451 0.5326449 0.5649716 0.5252228 0.5818749 0.5619173 0.7137175 0.5198826 0.741978 0.5188024 0.7405338 0.547189 0.8525252 0.512175 0.8909725 0.5195028 0.8813012 0.5700325 0.767251 0.5213551 0.7638055 0.5508071 0.7405338 0.547189 0.6090652 0.5341989 0.619408 0.5630598 0.5818749 0.5619173 0.767251 0.5213551 0.7986058 0.5229825 0.7876043 0.5515131 0.643523 0.5253897 0.6807699 0.5199454 0.684008 0.5485497 0.6090652 0.5341989 0.643523 0.5253897 0.6472994 0.5546409 0.7137175 0.5198826 0.7145456 0.5483428 0.684008 0.5485497 0.8944945 0.5019291 0.9280588 0.5058062 0.9215656 0.5263497 0.7986058 0.5229825 0.8525252 0.512175 0.8358098 0.5560289 0.7876043 0.5515131 0.8358098 0.5560289 0.8142431 0.5964899 0.5523074 0.5827227 0.5818749 0.5619173 0.5994977 0.606967 0.7145456 0.5483428 0.7405338 0.547189 0.7387405 0.5837526 0.5247884 0.6127725 0.5523074 0.5827227 0.5799201 0.6212012 0.8358098 0.5560289 0.8813012 0.5700325 0.853665 0.6108838 0.7638055 0.5508071 0.7577682 0.5830872 0.7387405 0.5837526 0.619408 0.5630598 0.6329522 0.5947315 0.5994977 0.606967 0.7638055 0.5508071 0.7876043 0.5515131 0.7814917 0.587821 0.6472994 0.5546409 0.684008 0.5485497 0.6871932 0.5866674 0.619408 0.5630598 0.6472994 0.5546409 0.6532152 0.5902059 0.7145456 0.5483428 0.7147506 0.5834184 0.6871932 0.5866674 0.9215656 0.5263497 0.9252064 0.6002672 0.8813012 0.5700325 0.08362573 0.7663302 0.0963397 0.7425011 0.120467 0.7581407 0.1022531 0.9133934 0.1233112 0.8949099 0.1358373 0.9070524 0.07174408 0.8201543 0.07365959 0.8004646 0.1012102 0.8061625 0.1158869 0.9276711 0.1358373 0.9070524 0.149762 0.9199937 0.1173202 0.7167634 0.1396802 0.7378973 0.120467 0.7581407 0.07365959 0.8004646 0.07688188 0.7850657 0.1037465 0.7931552 0.0859332 0.8878949 0.1118184 0.8746002 0.1233112 0.8949099 0.08362573 0.7663302 0.1101434 0.7780118 0.1037465 0.7931552 0.07883661 0.8706542 0.07174408 0.843208 0.09999328 0.842835 0.0859332 0.8878949 0.07883661 0.8706542 0.1055298 0.8622086 0.07174408 0.843208 0.07174408 0.8201543 0.09966009 0.8223494 0.9288727 0.498989 0.9280588 0.5058062 0.8944945 0.5019291 0.9252064 0.6002672 0.8915607 0.6343036 0.853665 0.6108838 0.1101434 0.7780118 0.120467 0.7581407 0.1387699 0.7725375 0.1233112 0.8949099 0.1402181 0.870485 0.1491371 0.8889637 0.09966009 0.8223494 0.1012102 0.8061625 0.1186974 0.8102479 0.149762 0.9199937 0.1358373 0.9070524 0.1491371 0.8889637 0.120467 0.7581407 0.1396802 0.7378973 0.1557811 0.7588029 0.1037465 0.7931552 0.1260749 0.8005653 0.1186974 0.8102479 0.1118184 0.8746002 0.1346161 0.8596904 0.1402181 0.870485 0.1037465 0.7931552 0.1101434 0.7780118 0.1311716 0.7889887 0.09999328 0.842835 0.1222793 0.8365837 0.1236326 0.8523182 0.1118184 0.8746002 0.1055298 0.8622086 0.1236326 0.8523182 0.09966009 0.8223494 0.1203005 0.8248296 0.1222793 0.8365837 0.1222793 0.8365837 0.1203005 0.8248296 0.1323873 0.8202516 0.1433252 0.8015676 0.1429558 0.8303617 0.1323873 0.8202516 0.1186974 0.8102479 0.1307615 0.8059208 0.1323873 0.8202516 0.1222793 0.8365837 0.1429558 0.8303617 0.137075 0.8533771 0.1697039 0.8563198 0.1491547 0.8569224 0.1537173 0.8402928 0.161682 0.8029187 0.1760225 0.8195084 0.1429558 0.8303617 0.171921 0.8357099 0.1537173 0.8402928 0.1429558 0.8303617 0.137075 0.8533771 0.1429558 0.8303617 0.1537173 0.8402928 0.0272181 0.6443149 0.02342814 0.9493953 0.01752871 0.9469527 0.05669414 0.947237 0.04985725 0.9481026 0.05059236 0.6444936 0.05059236 0.6444936 0.04985725 0.9481026 0.04303699 0.9477811 0.03621757 0.9454541 0.03026843 0.9476806 0.0332126 0.6441214 0.04460299 0.6442112 0.04303699 0.9477811 0.03621757 0.9454541 0.03026843 0.9476806 0.02342814 0.9493953 0.0272181 0.6443149 0.2030872 0.6921655 0.1968402 0.696644 0.1951894 0.6939143 0.1782119 0.693442 0.1813836 0.6877161 0.1853904 0.693897 0.1968402 0.696644 0.1904962 0.6982681 0.1904649 0.696272 0.1837024 0.6967729 0.1853904 0.693897 0.1904649 0.696272 0.1861478 0.639564 0.1922906 0.6832109 0.189583 0.6833836 0.1856809 0.7287927 0.1823452 0.7296367 0.1719172 0.6943501 0.1933735 0.6395639 0.1956568 0.6828308 0.1922906 0.6832109 0.2043491 0.6481285 0.2015066 0.6842629 0.1985144 0.6829282 0.1868764 0.6842691 0.1844745 0.6864705 0.1719172 0.652624 0.189583 0.6833836 0.1868764 0.6842691 0.1766753 0.6485808 0.1892901 0.6866156 0.189583 0.6833836 0.1922906 0.6832109 0.1930594 0.6876778 0.1922906 0.6832109 0.1956568 0.6828308 0.1968402 0.696644 0.1935033 0.7288079 0.1959837 0.7290629 0.1904962 0.6982681 0.1904243 0.7287371 0.1935033 0.7288079 0.1837024 0.6967729 0.1880393 0.7282668 0.1856809 0.7287927 0.1991252 0.7295395 0.1959837 0.7290629 0.2030872 0.6921655 0.1904962 0.6982681 0.1904243 0.7287371 0.1880393 0.7282668 0.1880393 0.7282668 0.1884703 0.7341842 0.1854922 0.732344 0.1907805 0.7323293 0.1904243 0.7287371 0.1880393 0.7282668 0.1525282 0.2930282 0.1534895 0.2928987 0.1537079 0.2880605 0.1534895 0.2928987 0.1543009 0.2935815 0.1557638 0.2884148 0.1473726 0.2305662 0.1513414 0.2882345 0.1537079 0.2880605 0.1557638 0.2884148 0.1580548 0.2892864 0.1706797 0.2319654 0.1580548 0.2892864 0.1600895 0.2905388 0.1779567 0.2332336 0.1307586 0.2323297 0.1466456 0.2904154 0.1490319 0.289049 0.1391741 0.2311822 0.1490319 0.289049 0.1513414 0.2882345 0.1537079 0.2880605 0.1557638 0.2884148 0.1626074 0.2309786 0.1541219 0.2937558 0.1534895 0.2928987 0.1525282 0.2930282 0.1541219 0.2937558 0.1522084 0.2941415 0.152963 0.2949934 0.1557638 0.2884148 0.1543009 0.2935815 0.1554334 0.2935386 0.1554334 0.2935386 0.1561044 0.2940878 0.1600895 0.2905388 0.1505956 0.294188 0.1517615 0.293954 0.1490319 0.289049 0.1490319 0.289049 0.1517615 0.293954 0.1525282 0.2930282 0.1567196 0.190142 0.1555426 0.2305666 0.1626074 0.2309786 0.1391741 0.2311822 0.1473726 0.2305662 0.146982 0.189882 0.128728 0.1902785 0.1307586 0.2323297 0.1391741 0.2311822 0.1736255 0.1909137 0.1706797 0.2319654 0.1779567 0.2332336 0.1650435 0.1904394 0.1626074 0.2309786 0.1706797 0.2319654 0.1473726 0.2305662 0.1555426 0.2305666 0.1567196 0.190142 0.1986474 0.6455227 0.1985144 0.6829282 0.1956568 0.6828308 0.0332126 0.6441214 0.02689445 0.6342918 0.03288888 0.6340983 0.0243752 0.6398542 0.02689445 0.6342918 0.0332126 0.6441214 0.7272179 0.4190862 0.7352418 0.4400988 0.7055336 0.4413693 0.6863941 0.4131863 0.7055336 0.4413693 0.6697849 0.44716 0.7630777 0.425854 0.7662134 0.4410382 0.7352418 0.4400988 0.6568499 0.4228258 0.6697849 0.44716 0.6327627 0.4549062 0.857438 0.4532184 0.9027271 0.4700995 0.8924716 0.4764438 0.857438 0.4532184 0.8541666 0.4631785 0.8093671 0.4512715 0.8105683 0.4332917 0.8093671 0.4512715 0.7662134 0.4410382 0.6235396 0.4427867 0.6327627 0.4549062 0.6055663 0.4638135 0.6091783 0.4750753 0.6055663 0.4638135 0.6327627 0.4549062 0.6381462 0.4703292 0.6327627 0.4549062 0.6697849 0.44716 0.6740837 0.4645502 0.6697849 0.44716 0.7055336 0.4413693 0.7081227 0.4616826 0.7055336 0.4413693 0.7352418 0.4400988 0.7382892 0.4603712 0.7352418 0.4400988 0.7662134 0.4410382 0.7662134 0.4410382 0.8093671 0.4512715 0.804805 0.4649826 0.8093671 0.4512715 0.8541666 0.4631785 0.8493421 0.4766096 0.8541666 0.4631785 0.8924716 0.4764438 0.8886817 0.4864226 0.1461098 0.6912932 0.1685438 0.7186296 0.1396802 0.7378973 0.9298115 0.4800267 0.9744321 0.4915809 0.9662626 0.5060328 0.9280588 0.5058062 0.9639065 0.5136308 0.9570893 0.5330516 0.5043969 0.5208212 0.5297292 0.5148118 0.5346451 0.5326449 0.9288727 0.498989 0.9662626 0.5060328 0.9639065 0.5136308 0.5037884 0.5101695 0.527993 0.503876 0.5297292 0.5148118 0.9979608 0.51365 0.9662626 0.5060328 0.9744321 0.4915809 0.5178116 0.4855716 0.527993 0.503876 0.5037884 0.5101695 0.5178116 0.4855716 0.5548099 0.4742764 0.5543316 0.4993487 0.9852589 0.5475783 0.9252064 0.6002672 0.9215656 0.5263497 0.5099318 0.5431228 0.5346451 0.5326449 0.5523074 0.5827227 0.7268944 0.9461439 0.7252817 0.9101398 0.7909143 0.9230342 0.4203891 0.9203698 0.3574759 0.92642 0.3535155 0.8870125 0.3574759 0.92642 0.2946858 0.9368837 0.2930082 0.9014983 0.5438621 0.9072323 0.5471479 0.8738947 0.591271 0.8711313 0.5438621 0.9072323 0.5459451 0.9385288 0.4862269 0.9434596 0.4203891 0.9203698 0.4172721 0.8813049 0.4577429 0.8823245 0.6008828 0.9052262 0.591271 0.8711313 0.655372 0.8722486 0.2946858 0.9368837 0.2287343 0.9569483 0.2168741 0.9176495 0.7909143 0.9230342 0.7925775 0.8834027 0.8836882 0.9092286 0.6619715 0.9066622 0.655372 0.8722486 0.7247678 0.8762714 0.5967908 0.4913821 0.5543316 0.4993487 0.5548099 0.4742764 0.5857678 0.4605998 0.6055663 0.4638135 0.6091783 0.4750753 0.8944035 0.4947218 0.8886817 0.4864226 0.8924716 0.4764438 0.8924716 0.4764438 0.9027271 0.4700995 0.9298115 0.4800267 0.0272181 0.6443149 0.02342814 0.9493953 0.01752871 0.9469527 0.05669414 0.947237 0.04985725 0.9481026 0.05059236 0.6444936 0.05059236 0.6444936 0.04985725 0.9481026 0.04303699 0.9477811 0.03621757 0.9454541 0.03026843 0.9476806 0.0332126 0.6441214 0.04460299 0.6442112 0.04303699 0.9477811 0.03621757 0.9454541 0.03026843 0.9476806 0.02342814 0.9493953 0.0272181 0.6443149 0.2030872 0.6921655 0.1968402 0.696644 0.1951894 0.6939143 0.1782119 0.693442 0.1813836 0.6877161 0.1853904 0.693897 0.1968402 0.696644 0.1904962 0.6982681 0.1904649 0.696272 0.1837024 0.6967729 0.1853904 0.693897 0.1904649 0.696272 0.1861478 0.639564 0.1922906 0.6832109 0.189583 0.6833836 0.1782119 0.693442 0.1856809 0.7287927 0.1823452 0.7296367 0.1956568 0.6828308 0.1922906 0.6832109 0.1861478 0.639564 0.2043491 0.6481285 0.2015066 0.6842629 0.1985144 0.6829282 0.1868764 0.6842691 0.1844745 0.6864705 0.1719172 0.652624 0.189583 0.6833836 0.1868764 0.6842691 0.1766753 0.6485808 0.1892901 0.6866156 0.189583 0.6833836 0.1922906 0.6832109 0.1930594 0.6876778 0.1922906 0.6832109 0.1956568 0.6828308 0.1968402 0.696644 0.2030872 0.6921655 0.1959837 0.7290629 0.1904962 0.6982681 0.1968402 0.696644 0.1935033 0.7288079 0.1837024 0.6967729 0.1880393 0.7282668 0.1856809 0.7287927 0.2095298 0.694436 0.1991252 0.7295395 0.1959837 0.7290629 0.1904962 0.6982681 0.1904243 0.7287371 0.1880393 0.7282668 0.1880393 0.7282668 0.1884703 0.7341842 0.1854922 0.732344 0.1880393 0.7282668 0.1904243 0.7287371 0.1907805 0.7323293 0.1986474 0.6455227 0.1985144 0.6829282 0.1956568 0.6828308 0.0332126 0.6441214 0.02689445 0.6342918 0.03288888 0.6340983 0.0243752 0.6398542 0.02689445 0.6342918 0.0332126 0.6441214</float_array>
          <technique_common>
            <accessor source="#snowman_body-mesh-map-0-array" count="2022" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="snowman_body-mesh-vertices">
          <input semantic="POSITION" source="#snowman_body-mesh-positions"/>
        </vertices>
        <triangles material="unicycle_snowman-material" count="674">
          <input semantic="VERTEX" source="#snowman_body-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#snowman_body-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#snowman_body-mesh-map-0" offset="2" set="0"/>
          <p>2 0 0 314 1 1 317 2 2 9 3 3 318 4 4 311 5 5 7 6 6 312 7 7 6 8 8 1 9 9 318 4 10 0 10 11 7 6 12 319 11 13 316 12 14 3 13 15 315 14 16 314 1 17 317 2 18 11 15 19 313 16 20 8 17 21 311 5 22 319 11 23 23 18 24 21 19 25 22 20 26 33 21 27 22 20 28 34 22 29 15 23 30 25 24 31 14 25 32 22 20 33 26 26 34 27 27 35 22 20 36 35 28 37 34 22 38 33 21 39 30 29 40 36 30 41 36 30 42 32 31 43 37 32 44 29 33 45 37 32 46 31 34 47 33 21 48 29 33 49 23 18 50 34 22 51 28 35 52 25 24 53 24 36 54 34 22 55 25 24 56 31 34 57 39 37 58 38 38 59 40 39 60 37 32 61 32 31 62 39 37 63 41 40 64 38 38 65 43 41 66 41 40 67 44 42 68 41 40 69 47 43 70 44 42 71 42 44 72 45 45 73 41 40 74 47 43 75 48 46 76 44 42 77 53 47 78 50 48 79 52 49 80 49 50 81 52 49 82 48 46 83 40 39 84 42 44 85 39 37 86 42 44 87 55 51 88 46 52 89 29 33 90 38 38 91 23 18 92 43 41 93 56 53 94 38 38 95 38 38 96 20 54 97 23 18 98 21 19 99 59 55 100 26 26 101 26 26 102 61 56 103 60 57 104 60 57 105 63 58 106 62 59 107 62 59 108 64 60 109 60 57 110 26 26 111 64 60 112 27 27 113 66 61 114 64 60 115 65 62 116 58 63 117 69 64 118 59 55 119 70 65 120 75 66 121 73 67 122 71 68 123 72 69 124 70 65 125 56 53 126 76 70 127 20 54 128 68 71 129 76 70 130 77 72 131 20 54 132 58 63 133 21 19 134 68 71 135 79 73 136 78 74 137 77 72 138 57 75 139 79 73 140 68 71 141 80 76 142 69 64 143 74 77 144 81 78 145 82 79 146 15 23 147 67 80 148 84 81 149 15 23 150 30 29 151 24 36 152 14 25 153 28 35 154 67 80 155 5 82 156 315 14 157 4 83 158 97 84 159 98 85 160 99 86 161 35 28 162 83 87 163 28 35 164 28 35 165 13 88 166 67 80 167 27 27 168 85 89 169 35 28 170 86 90 171 47 43 172 45 45 173 53 47 174 86 90 175 87 91 176 51 92 177 87 91 178 88 93 179 86 90 180 89 94 181 87 91 182 45 45 183 90 95 184 86 90 185 87 91 186 91 96 187 88 93 188 89 94 189 46 52 190 55 51 191 55 51 192 92 97 193 89 94 194 92 97 195 91 96 196 89 94 197 93 98 198 17 99 199 18 100 200 40 39 201 96 101 202 54 102 203 96 101 204 55 51 205 54 102 206 95 103 207 32 31 208 97 84 209 30 29 210 97 84 211 32 31 212 95 103 213 94 104 214 96 101 215 16 105 216 92 97 217 94 104 218 98 85 219 320 106 220 312 7 221 99 86 222 94 104 223 97 84 224 99 86 225 312 7 226 16 105 227 100 107 228 75 66 229 101 108 230 107 109 231 109 110 232 106 111 233 106 111 234 108 112 235 105 113 236 105 113 237 82 79 238 81 78 239 123 114 240 122 115 241 116 116 242 125 117 243 65 62 244 62 59 245 119 118 246 117 119 247 118 120 248 101 108 249 121 121 250 100 107 251 63 58 252 125 117 253 62 59 254 123 114 255 119 118 256 125 117 257 43 41 258 128 122 259 57 75 260 44 42 261 126 123 262 128 122 263 128 122 264 79 73 265 57 75 266 80 76 267 129 124 268 102 125 269 126 123 270 78 74 271 79 73 272 129 124 273 48 46 274 52 49 275 102 125 276 130 126 277 103 127 278 50 48 279 129 124 280 52 49 281 51 92 282 131 128 283 50 48 284 148 129 285 11 15 286 12 130 287 18 100 288 142 131 289 93 98 290 93 98 291 143 132 292 91 96 293 88 93 294 143 132 295 134 133 296 13 88 297 148 129 298 12 130 299 132 134 300 88 93 301 134 133 302 135 135 303 137 136 304 141 137 305 135 135 306 132 134 307 134 133 308 131 128 309 141 137 310 144 138 311 144 138 312 137 136 313 133 139 314 111 140 315 127 141 316 113 142 317 133 139 318 145 143 319 144 138 320 144 138 321 146 144 322 131 128 323 130 126 324 131 128 325 146 144 326 104 145 327 145 143 328 111 140 329 103 127 330 146 144 331 104 145 332 138 146 333 152 147 334 147 148 335 164 149 336 149 150 337 163 151 338 165 152 339 150 153 340 164 149 341 163 151 342 115 154 343 159 155 344 149 150 345 116 116 346 115 154 347 151 156 348 124 157 349 150 153 350 149 150 351 124 157 352 123 114 353 65 62 354 118 120 355 66 61 356 165 152 357 147 148 358 151 156 359 83 87 360 155 158 361 148 129 362 140 159 363 155 158 364 152 147 365 140 159 366 139 160 367 148 129 368 151 156 369 152 147 370 117 119 371 117 119 372 155 158 373 118 120 374 85 89 375 118 120 376 155 158 377 135 135 378 153 161 379 136 162 380 134 133 381 154 163 382 135 135 383 136 162 384 161 164 385 162 165 386 19 166 387 157 167 388 142 131 389 156 168 390 154 163 391 158 169 392 158 169 393 143 132 394 142 131 395 157 167 396 158 169 397 142 131 398 147 148 399 161 164 400 138 146 401 137 136 402 162 165 403 165 152 404 127 141 405 159 155 406 113 142 407 137 136 408 164 149 409 133 139 410 127 141 411 164 149 412 163 151 413 157 167 414 166 170 415 156 168 416 166 170 417 138 146 418 161 164 419 153 161 420 166 170 421 161 164 422 11 15 423 157 167 424 10 171 425 114 172 426 178 173 427 160 174 428 75 66 429 167 175 430 169 176 431 108 112 432 170 177 433 82 79 434 82 79 435 168 178 436 74 77 437 120 179 438 176 180 439 114 172 440 222 181 441 235 182 442 234 183 443 109 110 444 172 184 445 108 112 446 101 108 447 169 176 448 171 185 449 308 186 450 175 187 451 112 188 452 110 189 453 173 190 454 109 110 455 74 77 456 167 175 457 72 69 458 120 179 459 171 185 460 177 191 461 167 175 462 180 192 463 179 193 464 175 187 465 309 194 466 187 195 467 174 196 468 185 197 469 173 190 470 170 177 471 180 192 472 168 178 473 177 191 474 188 198 475 176 180 476 170 177 477 184 199 478 182 200 479 171 185 480 181 201 481 183 202 482 171 185 483 189 203 484 177 191 485 167 175 486 181 201 487 169 176 488 172 184 489 185 197 490 184 199 491 178 173 492 188 198 493 190 204 494 190 204 495 200 205 496 202 206 497 179 193 498 192 207 499 191 208 500 185 197 501 198 209 502 197 210 503 182 200 504 192 207 505 180 192 506 189 203 507 200 205 508 188 198 509 182 200 510 196 211 511 194 212 512 183 202 513 193 213 514 195 214 515 189 203 516 195 214 517 201 215 518 179 193 519 193 213 520 181 201 521 174 196 522 307 216 523 186 217 524 184 199 525 197 210 526 196 211 527 196 211 528 209 218 529 208 219 530 202 206 531 212 220 532 214 221 533 191 208 534 204 222 535 203 223 536 199 224 537 214 221 538 211 225 539 197 210 540 210 226 541 209 218 542 194 212 543 204 222 544 192 207 545 201 215 546 212 220 547 200 205 548 194 212 549 208 219 550 206 227 551 195 214 552 205 228 553 207 229 554 201 215 555 207 229 556 213 230 557 191 208 558 205 228 559 193 213 560 307 216 561 198 209 562 186 217 563 208 219 564 221 231 565 220 232 566 212 220 567 226 233 568 214 221 569 203 223 570 216 234 571 215 235 572 214 221 573 223 236 574 211 225 575 210 226 576 221 231 577 209 218 578 204 222 579 218 237 580 216 234 581 213 230 582 224 238 583 212 220 584 208 219 585 218 237 586 206 227 587 207 229 588 217 239 589 219 240 590 213 230 591 219 240 592 225 241 593 205 228 594 215 235 595 217 239 596 310 242 597 174 196 598 110 189 599 199 224 600 210 226 601 198 209 602 220 232 603 233 243 604 232 244 605 224 238 606 238 245 607 226 233 608 215 235 609 228 246 610 227 247 611 223 236 612 238 245 613 235 182 614 221 231 615 234 183 616 233 243 617 218 237 618 228 246 619 216 234 620 225 241 621 236 248 622 224 238 623 218 237 624 232 244 625 230 249 626 217 239 627 231 250 628 219 240 629 225 241 630 231 250 631 237 251 632 215 235 633 229 252 634 217 239 635 229 252 636 239 253 637 240 254 638 232 244 639 239 253 640 230 249 641 228 246 642 239 253 643 227 247 644 229 252 645 237 251 646 231 250 647 238 245 648 241 255 649 235 182 650 233 243 651 240 254 652 232 244 653 235 182 654 240 254 655 234 183 656 237 251 657 241 255 658 236 248 659 245 256 660 249 257 661 243 258 662 249 257 663 247 259 664 243 258 665 247 259 666 252 260 667 246 261 668 248 262 669 244 263 670 242 264 671 246 261 672 248 262 673 242 264 674 250 265 675 245 256 676 244 263 677 253 266 678 256 267 679 252 260 680 251 268 681 254 269 682 255 270 683 257 271 684 258 272 685 256 267 686 255 270 687 258 272 688 259 273 689 248 262 690 260 274 691 250 265 692 267 275 693 249 257 694 251 268 695 252 260 696 261 276 697 248 262 698 258 272 699 264 277 700 256 267 701 263 278 702 258 272 703 254 269 704 260 274 705 254 269 706 250 265 707 263 278 708 261 276 709 265 279 710 265 279 711 262 280 712 264 277 713 257 271 714 268 281 715 253 266 716 259 273 717 270 282 718 257 271 719 255 270 720 267 275 721 251 268 722 266 283 723 253 266 724 249 257 725 259 273 726 269 284 727 255 270 728 269 284 729 266 283 730 267 275 731 270 282 732 269 284 733 268 281 734 288 285 735 278 286 736 282 287 737 284 288 738 280 289 739 278 286 740 291 290 741 278 286 742 290 291 743 280 289 744 292 292 745 293 293 746 281 294 747 294 295 748 292 292 749 294 295 750 283 296 751 295 297 752 295 297 753 282 287 754 291 290 755 278 286 756 293 293 757 290 291 758 286 298 759 288 285 760 289 299 761 286 298 762 285 300 763 287 301 764 280 289 765 287 301 766 281 294 767 287 301 768 279 302 769 281 294 770 285 300 771 283 296 772 279 302 773 283 296 774 288 285 775 282 287 776 272 303 777 293 293 778 274 304 779 295 297 780 276 305 781 277 306 782 273 307 783 295 297 784 277 306 785 275 308 786 294 295 787 273 307 788 274 304 789 292 292 790 275 308 791 291 290 792 272 303 793 276 305 794 256 267 795 262 280 796 252 260 797 244 263 798 246 261 799 242 264 800 243 258 801 244 263 802 245 256 803 69 64 804 296 309 805 59 55 806 59 55 807 298 310 808 61 56 809 80 76 810 297 311 811 69 64 812 61 56 813 300 312 814 63 58 815 103 127 816 303 313 817 302 314 818 103 127 819 301 315 820 102 125 821 102 125 822 299 316 823 80 76 824 63 58 825 304 317 826 122 115 827 121 121 828 300 312 829 100 107 830 100 107 831 298 310 832 73 67 833 73 67 834 296 309 835 70 65 836 70 65 837 297 311 838 71 68 839 71 68 840 299 316 841 81 78 842 299 316 843 105 113 844 81 78 845 301 315 846 106 111 847 105 113 848 302 314 849 107 109 850 106 111 851 211 225 852 222 181 853 210 226 854 111 140 855 112 188 856 310 242 857 305 318 858 187 195 859 307 216 860 306 319 861 190 204 862 309 194 863 310 242 864 175 187 865 305 318 866 308 186 867 178 173 868 306 319 869 308 186 870 113 142 871 159 155 872 115 154 873 308 186 874 159 155 875 115 154 876 114 172 877 160 174 878 309 194 879 307 216 880 187 195 881 309 194 882 202 206 883 199 224 884 2 0 885 313 16 886 1 9 887 316 12 888 18 100 889 17 99 890 319 11 891 19 166 892 18 100 893 320 106 894 67 80 895 315 14 896 320 106 897 6 8 898 312 7 899 316 12 900 16 105 901 312 7 902 315 14 903 13 88 904 314 1 905 311 5 906 10 171 907 19 166 908 313 16 909 10 171 910 318 4 911 314 1 912 12 130 913 317 2 914 120 179 915 116 116 916 121 121 917 122 115 918 121 121 919 116 116 920 110 189 921 303 313 922 310 242 923 303 313 924 111 140 925 310 242 926 324 320 927 328 321 928 322 322 929 328 321 930 326 323 931 322 322 932 326 323 933 331 324 934 325 325 935 327 326 936 323 327 937 321 328 938 325 325 939 327 326 940 321 328 941 329 329 942 324 320 943 323 327 944 332 330 945 335 331 946 331 324 947 330 332 948 333 333 949 334 334 950 336 335 951 337 336 952 335 331 953 334 334 954 337 336 955 338 337 956 327 326 957 339 338 958 329 329 959 330 332 960 345 339 961 328 321 962 341 340 963 327 326 964 331 324 965 337 336 966 343 341 967 335 331 968 342 342 969 337 336 970 333 333 971 339 338 972 333 333 973 329 329 974 342 342 975 340 343 976 344 344 977 344 344 978 341 340 979 343 341 980 336 335 981 347 345 982 349 346 983 338 337 984 349 346 985 350 347 986 334 334 987 346 348 988 330 332 989 328 321 990 347 345 991 332 330 992 338 337 993 348 349 994 334 334 995 348 349 996 345 339 997 346 348 998 348 349 999 349 346 1000 347 345 1001 335 331 1002 341 340 1003 331 324 1004 323 327 1005 325 325 1006 321 328 1007 322 322 1008 323 327 1009 324 320 1010 2 0 1011 3 13 1012 314 1 1013 9 3 1014 0 10 1015 318 4 1016 7 6 1017 316 12 1018 312 7 1019 1 9 1020 313 16 1021 318 4 1022 7 6 1023 8 17 1024 319 11 1025 3 13 1026 4 83 1027 315 14 1028 317 2 1029 12 130 1030 11 15 1031 8 17 1032 9 3 1033 311 5 1034 23 18 1035 20 54 1036 21 19 1037 33 21 1038 23 18 1039 22 20 1040 15 23 1041 24 36 1042 25 24 1043 22 20 1044 21 19 1045 26 26 1046 22 20 1047 27 27 1048 35 28 1049 33 21 1050 24 36 1051 30 29 1052 36 30 1053 30 29 1054 32 31 1055 29 33 1056 36 30 1057 37 32 1058 33 21 1059 36 30 1060 29 33 1061 34 22 1062 35 28 1063 28 35 1064 24 36 1065 33 21 1066 34 22 1067 31 34 1068 37 32 1069 39 37 1070 40 39 1071 39 37 1072 37 32 1073 39 37 1074 42 44 1075 41 40 1076 43 41 1077 38 38 1078 41 40 1079 41 40 1080 45 45 1081 47 43 1082 42 44 1083 46 52 1084 45 45 1085 47 43 1086 49 50 1087 48 46 1088 53 47 1089 51 92 1090 50 48 1091 49 50 1092 53 47 1093 52 49 1094 40 39 1095 54 102 1096 42 44 1097 42 44 1098 54 102 1099 55 51 1100 29 33 1101 31 34 1102 38 38 1103 43 41 1104 57 75 1105 56 53 1106 38 38 1107 56 53 1108 20 54 1109 21 19 1110 58 63 1111 59 55 1112 26 26 1113 59 55 1114 61 56 1115 60 57 1116 61 56 1117 63 58 1118 62 59 1119 65 62 1120 64 60 1121 26 26 1122 60 57 1123 64 60 1124 66 61 1125 27 27 1126 64 60 1127 58 63 1128 68 71 1129 69 64 1130 70 65 1131 72 69 1132 75 66 1133 71 68 1134 74 77 1135 72 69 1136 56 53 1137 77 72 1138 76 70 1139 68 71 1140 58 63 1141 76 70 1142 20 54 1143 76 70 1144 58 63 1145 68 71 1146 77 72 1147 79 73 1148 77 72 1149 56 53 1150 57 75 1151 68 71 1152 78 74 1153 80 76 1154 74 77 1155 71 68 1156 81 78 1157 15 23 1158 14 25 1159 67 80 1160 15 23 1161 84 81 1162 30 29 1163 14 25 1164 25 24 1165 28 35 1166 5 82 1167 320 106 1168 315 14 1169 97 84 1170 84 81 1171 98 85 1172 35 28 1173 85 89 1174 83 87 1175 28 35 1176 83 87 1177 13 88 1178 27 27 1179 66 61 1180 85 89 1181 86 90 1182 49 50 1183 47 43 1184 53 47 1185 49 50 1186 86 90 1187 51 92 1188 53 47 1189 87 91 1190 86 90 1191 90 95 1192 89 94 1193 45 45 1194 46 52 1195 90 95 1196 87 91 1197 89 94 1198 91 96 1199 89 94 1200 90 95 1201 46 52 1202 55 51 1203 94 104 1204 92 97 1205 92 97 1206 93 98 1207 91 96 1208 93 98 1209 92 97 1210 17 99 1211 40 39 1212 95 103 1213 96 101 1214 96 101 1215 94 104 1216 55 51 1217 95 103 1218 40 39 1219 32 31 1220 30 29 1221 84 81 1222 97 84 1223 95 103 1224 97 84 1225 94 104 1226 16 105 1227 17 99 1228 92 97 1229 98 85 1230 84 81 1231 320 106 1232 99 86 1233 16 105 1234 94 104 1235 99 86 1236 98 85 1237 312 7 1238 100 107 1239 73 67 1240 75 66 1241 107 109 1242 110 189 1243 109 110 1244 106 111 1245 109 110 1246 108 112 1247 105 113 1248 108 112 1249 82 79 1250 123 114 1251 125 117 1252 122 115 1253 125 117 1254 119 118 1255 65 62 1256 119 118 1257 124 157 1258 117 119 1259 101 108 1260 120 179 1261 121 121 1262 63 58 1263 122 115 1264 125 117 1265 123 114 1266 124 157 1267 119 118 1268 43 41 1269 44 42 1270 128 122 1271 44 42 1272 48 46 1273 126 123 1274 128 122 1275 126 123 1276 79 73 1277 80 76 1278 78 74 1279 129 124 1280 126 123 1281 129 124 1282 78 74 1283 129 124 1284 126 123 1285 48 46 1286 102 125 1287 129 124 1288 130 126 1289 50 48 1290 130 126 1291 129 124 1292 51 92 1293 132 134 1294 131 128 1295 148 129 1296 139 160 1297 11 15 1298 18 100 1299 19 166 1300 142 131 1301 93 98 1302 142 131 1303 143 132 1304 88 93 1305 91 96 1306 143 132 1307 13 88 1308 83 87 1309 148 129 1310 132 134 1311 51 92 1312 88 93 1313 135 135 1314 136 162 1315 137 136 1316 135 135 1317 141 137 1318 132 134 1319 131 128 1320 132 134 1321 141 137 1322 144 138 1323 141 137 1324 137 136 1325 111 140 1326 145 143 1327 127 141 1328 133 139 1329 127 141 1330 145 143 1331 144 138 1332 145 143 1333 146 144 1334 130 126 1335 50 48 1336 131 128 1337 104 145 1338 146 144 1339 145 143 1340 103 127 1341 130 126 1342 146 144 1343 138 146 1344 140 159 1345 152 147 1346 164 149 1347 150 153 1348 149 150 1349 165 152 1350 151 156 1351 150 153 1352 163 151 1353 149 150 1354 115 154 1355 149 150 1356 123 114 1357 116 116 1358 151 156 1359 117 119 1360 124 157 1361 149 150 1362 150 153 1363 124 157 1364 65 62 1365 119 118 1366 118 120 1367 165 152 1368 162 165 1369 147 148 1370 83 87 1371 85 89 1372 155 158 1373 140 159 1374 148 129 1375 155 158 1376 140 159 1377 138 146 1378 139 160 1379 151 156 1380 147 148 1381 152 147 1382 117 119 1383 152 147 1384 155 158 1385 85 89 1386 66 61 1387 118 120 1388 135 135 1389 154 163 1390 153 161 1391 134 133 1392 143 132 1393 154 163 1394 136 162 1395 153 161 1396 161 164 1397 19 166 1398 10 171 1399 157 167 1400 156 168 1401 153 161 1402 154 163 1403 158 169 1404 154 163 1405 143 132 1406 157 167 1407 156 168 1408 158 169 1409 147 148 1410 162 165 1411 161 164 1412 137 136 1413 136 162 1414 162 165 1415 127 141 1416 163 151 1417 159 155 1418 137 136 1419 165 152 1420 164 149 1421 127 141 1422 133 139 1423 164 149 1424 157 167 1425 139 160 1426 166 170 1427 166 170 1428 139 160 1429 138 146 1430 153 161 1431 156 168 1432 166 170 1433 11 15 1434 139 160 1435 157 167 1436 114 172 1437 176 180 1438 178 173 1439 75 66 1440 72 69 1441 167 175 1442 108 112 1443 172 184 1444 170 177 1445 82 79 1446 170 177 1447 168 178 1448 120 179 1449 177 191 1450 176 180 1451 222 181 1452 223 236 1453 235 182 1454 109 110 1455 173 190 1456 172 184 1457 101 108 1458 75 66 1459 169 176 1460 308 186 1461 306 319 1462 175 187 1463 110 189 1464 174 196 1465 173 190 1466 74 77 1467 168 178 1468 167 175 1469 120 179 1470 101 108 1471 171 185 1472 167 175 1473 168 178 1474 180 192 1475 175 187 1476 306 319 1477 309 194 1478 174 196 1479 186 217 1480 185 197 1481 170 177 1482 182 200 1483 180 192 1484 177 191 1485 189 203 1486 188 198 1487 170 177 1488 172 184 1489 184 199 1490 171 185 1491 169 176 1492 181 201 1493 171 185 1494 183 202 1495 189 203 1496 167 175 1497 179 193 1498 181 201 1499 172 184 1500 173 190 1501 185 197 1502 178 173 1503 176 180 1504 188 198 1505 190 204 1506 188 198 1507 200 205 1508 179 193 1509 180 192 1510 192 207 1511 185 197 1512 186 217 1513 198 209 1514 182 200 1515 194 212 1516 192 207 1517 189 203 1518 201 215 1519 200 205 1520 182 200 1521 184 199 1522 196 211 1523 183 202 1524 181 201 1525 193 213 1526 189 203 1527 183 202 1528 195 214 1529 179 193 1530 191 208 1531 193 213 1532 174 196 1533 305 318 1534 307 216 1535 184 199 1536 185 197 1537 197 210 1538 196 211 1539 197 210 1540 209 218 1541 202 206 1542 200 205 1543 212 220 1544 191 208 1545 192 207 1546 204 222 1547 199 224 1548 202 206 1549 214 221 1550 197 210 1551 198 209 1552 210 226 1553 194 212 1554 206 227 1555 204 222 1556 201 215 1557 213 230 1558 212 220 1559 194 212 1560 196 211 1561 208 219 1562 195 214 1563 193 213 1564 205 228 1565 201 215 1566 195 214 1567 207 229 1568 191 208 1569 203 223 1570 205 228 1571 307 216 1572 199 224 1573 198 209 1574 208 219 1575 209 218 1576 221 231 1577 212 220 1578 224 238 1579 226 233 1580 203 223 1581 204 222 1582 216 234 1583 214 221 1584 226 233 1585 223 236 1586 210 226 1587 222 181 1588 221 231 1589 204 222 1590 206 227 1591 218 237 1592 213 230 1593 225 241 1594 224 238 1595 208 219 1596 220 232 1597 218 237 1598 207 229 1599 205 228 1600 217 239 1601 213 230 1602 207 229 1603 219 240 1604 205 228 1605 203 223 1606 215 235 1607 310 242 1608 305 318 1609 174 196 1610 199 224 1611 211 225 1612 210 226 1613 220 232 1614 221 231 1615 233 243 1616 224 238 1617 236 248 1618 238 245 1619 215 235 1620 216 234 1621 228 246 1622 223 236 1623 226 233 1624 238 245 1625 221 231 1626 222 181 1627 234 183 1628 218 237 1629 230 249 1630 228 246 1631 225 241 1632 237 251 1633 236 248 1634 218 237 1635 220 232 1636 232 244 1637 217 239 1638 229 252 1639 231 250 1640 225 241 1641 219 240 1642 231 250 1643 215 235 1644 227 247 1645 229 252 1646 229 252 1647 227 247 1648 239 253 1649 232 244 1650 240 254 1651 239 253 1652 228 246 1653 230 249 1654 239 253 1655 229 252 1656 240 254 1657 237 251 1658 238 245 1659 236 248 1660 241 255 1661 233 243 1662 234 183 1663 240 254 1664 235 182 1665 241 255 1666 240 254 1667 237 251 1668 240 254 1669 241 255 1670 245 256 1671 251 268 1672 249 257 1673 249 257 1674 253 266 1675 247 259 1676 247 259 1677 253 266 1678 252 260 1679 248 262 1680 250 265 1681 244 263 1682 246 261 1683 252 260 1684 248 262 1685 250 265 1686 251 268 1687 245 256 1688 253 266 1689 257 271 1690 256 267 1691 251 268 1692 250 265 1693 254 269 1694 257 271 1695 259 273 1696 258 272 1697 255 270 1698 254 269 1699 258 272 1700 248 262 1701 261 276 1702 260 274 1703 267 275 1704 266 283 1705 249 257 1706 252 260 1707 262 280 1708 261 276 1709 258 272 1710 265 279 1711 264 277 1712 263 278 1713 265 279 1714 258 272 1715 260 274 1716 263 278 1717 254 269 1718 263 278 1719 260 274 1720 261 276 1721 265 279 1722 261 276 1723 262 280 1724 257 271 1725 270 282 1726 268 281 1727 259 273 1728 271 350 1729 270 282 1730 255 270 1731 269 284 1732 267 275 1733 266 283 1734 268 281 1735 253 266 1736 259 273 1737 271 350 1738 269 284 1739 269 284 1740 268 281 1741 266 283 1742 270 282 1743 271 350 1744 269 284 1745 288 285 1746 284 288 1747 278 286 1748 284 288 1749 286 298 1750 280 289 1751 291 290 1752 282 287 1753 278 286 1754 280 289 1755 281 294 1756 292 292 1757 281 294 1758 279 302 1759 294 295 1760 294 295 1761 279 302 1762 283 296 1763 295 297 1764 283 296 1765 282 287 1766 278 286 1767 280 289 1768 293 293 1769 286 298 1770 284 288 1771 288 285 1772 286 298 1773 289 299 1774 285 300 1775 280 289 1776 286 298 1777 287 301 1778 287 301 1779 285 300 1780 279 302 1781 285 300 1782 289 299 1783 283 296 1784 283 296 1785 289 299 1786 288 285 1787 272 303 1788 290 291 1789 293 293 1790 295 297 1791 291 290 1792 276 305 1793 273 307 1794 294 295 1795 295 297 1796 275 308 1797 292 292 1798 294 295 1799 274 304 1800 293 293 1801 292 292 1802 291 290 1803 290 291 1804 272 303 1805 256 267 1806 264 277 1807 262 280 1808 244 263 1809 247 259 1810 246 261 1811 243 258 1812 247 259 1813 244 263 1814 69 64 1815 297 311 1816 296 309 1817 59 55 1818 296 309 1819 298 310 1820 80 76 1821 299 316 1822 297 311 1823 61 56 1824 298 310 1825 300 312 1826 103 127 1827 104 145 1828 303 313 1829 103 127 1830 302 314 1831 301 315 1832 102 125 1833 301 315 1834 299 316 1835 63 58 1836 300 312 1837 304 317 1838 121 121 1839 304 317 1840 300 312 1841 100 107 1842 300 312 1843 298 310 1844 73 67 1845 298 310 1846 296 309 1847 70 65 1848 296 309 1849 297 311 1850 71 68 1851 297 311 1852 299 316 1853 299 316 1854 301 315 1855 105 113 1856 301 315 1857 302 314 1858 106 111 1859 302 314 1860 303 313 1861 107 109 1862 211 225 1863 223 236 1864 222 181 1865 111 140 1866 113 142 1867 112 188 1868 305 318 1869 175 187 1870 187 195 1871 306 319 1872 178 173 1873 190 204 1874 310 242 1875 112 188 1876 175 187 1877 308 186 1878 160 174 1879 178 173 1880 308 186 1881 112 188 1882 113 142 1883 115 154 1884 160 174 1885 308 186 1886 115 154 1887 116 116 1888 114 172 1889 309 194 1890 199 224 1891 307 216 1892 309 194 1893 190 204 1894 202 206 1895 2 0 1896 317 2 1897 313 16 1898 316 12 1899 319 11 1900 18 100 1901 319 11 1902 311 5 1903 19 166 1904 320 106 1905 84 81 1906 67 80 1907 320 106 1908 5 82 1909 6 8 1910 316 12 1911 17 99 1912 16 105 1913 315 14 1914 67 80 1915 13 88 1916 311 5 1917 318 4 1918 10 171 1919 313 16 1920 11 15 1921 10 171 1922 314 1 1923 13 88 1924 12 130 1925 120 179 1926 114 172 1927 116 116 1928 122 115 1929 304 317 1930 121 121 1931 110 189 1932 107 109 1933 303 313 1934 303 313 1935 104 145 1936 111 140 1937 324 320 1938 330 332 1939 328 321 1940 328 321 1941 332 330 1942 326 323 1943 326 323 1944 332 330 1945 331 324 1946 327 326 1947 329 329 1948 323 327 1949 325 325 1950 331 324 1951 327 326 1952 329 329 1953 330 332 1954 324 320 1955 332 330 1956 336 335 1957 335 331 1958 330 332 1959 329 329 1960 333 333 1961 336 335 1962 338 337 1963 337 336 1964 334 334 1965 333 333 1966 337 336 1967 327 326 1968 340 343 1969 339 338 1970 330 332 1971 346 348 1972 345 339 1973 341 340 1974 340 343 1975 327 326 1976 337 336 1977 344 344 1978 343 341 1979 342 342 1980 344 344 1981 337 336 1982 339 338 1983 342 342 1984 333 333 1985 342 342 1986 339 338 1987 340 343 1988 344 344 1989 340 343 1990 341 340 1991 336 335 1992 332 330 1993 347 345 1994 338 337 1995 336 335 1996 349 346 1997 334 334 1998 348 349 1999 346 348 2000 328 321 2001 345 339 2002 347 345 2003 338 337 2004 350 347 2005 348 349 2006 348 349 2007 347 345 2008 345 339 2009 348 349 2010 350 347 2011 349 346 2012 335 331 2013 343 341 2014 341 340 2015 323 327 2016 326 323 2017 325 325 2018 322 322 2019 326 323 2020 323 327 2021</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="snowman_ball-mesh" name="snowman_ball">
      <mesh>
        <source id="snowman_ball-mesh-positions">
          <float_array id="snowman_ball-mesh-positions-array" count="255">0.001388967 0.2028126 -0.2573836 -0.1175134 0.1641789 -0.2573836 -0.2044124 0.06736177 -0.2390273 -0.1645423 -0.05320256 -0.2632778 -0.1446222 -0.1677492 -0.2341087 -0.02712339 -0.1871936 -0.2557142 0.1380625 -0.1259157 -0.2562159 0.2173939 -0.05175125 -0.2434618 0.1444317 0.03582799 -0.2835157 0.13447 0.1746355 -0.2341006 -0.004933834 0.161943 0.2774574 -0.09781563 0.1280112 0.280242 -0.1466642 0.05545032 0.2829208 -0.1428751 -0.06203192 0.2799736 -0.09682482 -0.1281931 0.2822965 -0.006920099 -0.1497242 0.2863664 0.08773535 -0.1266093 0.2850847 0.1447917 -0.04807704 0.2849376 0.1432233 0.04872453 0.2869859 0.08988654 0.1308266 0.2805356 0.2302291 0.09756147 0.2088209 0.1521596 0.2053115 0.2059895 0.003321826 -0.2527645 0.2059895 0.1521597 -0.2044041 0.2059895 -0.2375029 0.07870233 0.2059895 -0.2375029 -0.07779502 0.2059895 0.003310859 0.2619146 0.2080571 0.2441466 -0.07779508 0.2059895 -0.145516 -0.2044042 0.2059895 -0.145516 0.2053114 0.2059895 0.301251 0.1237422 0.05368226 0.001862943 -0.3234398 0.05169558 -0.3060969 0.1004303 0.05169558 0.1921924 0.2623345 0.05169558 0.1921926 -0.261598 0.05169558 -0.3060969 -0.09969401 0.05169558 0.001862943 0.3241761 0.05169558 0.3098227 -0.09969401 0.05169558 -0.1884665 -0.261598 0.05169558 -0.1884666 0.2623344 0.05169558 0.2642076 0.08599597 -0.16834 9.35561e-4 -0.2763671 -0.16834 -0.2623365 0.08599585 -0.16834 0.1636465 0.2244063 -0.1683401 0.1636466 -0.2234989 -0.1683401 -0.2623365 -0.08508861 -0.16834 9.35561e-4 0.2772744 -0.16834 0.2642076 -0.08508867 -0.16834 -0.1617754 -0.2234989 -0.16834 -0.1617755 0.2244062 -0.16834 0.03701019 0.07216191 -0.2921639 -0.07556354 0.03030532 -0.2928566 -0.03825896 -0.08191424 -0.2910274 0.07724761 -0.04859596 -0.2870786 0.1871016 0.2461621 0.142484 0.1871018 -0.2473538 0.142484 -0.2857706 -0.09411484 0.1433216 0.007821619 0.3044138 0.142484 0.2979031 -0.09484905 0.142484 -0.1714584 -0.2473539 0.142484 -0.1714584 0.2461619 0.142484 0.2632213 0.1388808 0.1497672 0.007821619 -0.3056055 0.142484 -0.2857706 0.0943914 0.1433216 0.1240501 0.172061 0.2497401 0.1240501 -0.1711536 0.2497401 -0.2023662 -0.06509447 0.2497404 -6.29727e-4 0.2125719 0.2497404 0.2011067 -0.06509447 0.2497404 -0.1253097 -0.1711537 0.2497404 -0.1253097 0.172061 0.2497404 0.2011067 0.06600177 0.2497404 -6.29711e-4 -0.2116646 0.2497404 -0.2023662 0.06600177 0.2497404 0.1830524 0.2543985 -0.08104449 0.1830525 -0.2534911 -0.08104449 -0.2999789 -0.09654456 -0.08104443 -0.001448988 0.3143466 -0.08104443 0.2970806 -0.09654468 -0.08104443 -0.1859506 -0.2534911 -0.08104443 -0.1859508 0.2543984 -0.08104443 0.2970806 0.09745192 -0.08104443 -0.001448929 -0.3134393 -0.08104443 -0.2999789 0.09745186 -0.08104443 -0.00595206 0.002175033 0.3118338</float_array>
          <technique_common>
            <accessor source="#snowman_ball-mesh-positions-array" count="85" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="snowman_ball-mesh-normals">
          <float_array id="snowman_ball-mesh-normals-array" count="255">0.3672091 0.5013418 0.7834628 0.3513405 0.1221085 0.9282508 0.6255263 0.1807056 0.7589879 -0.006714284 -0.6445723 0.764514 0.2209259 -0.314039 0.9233479 -0.02050864 -0.3674471 0.9298183 -0.6195204 -0.1960894 0.7600944 -0.355428 0.1216805 0.9267497 -0.6308345 0.1985892 0.7500736 -0.01870846 0.5880815 0.8085854 0.1974292 0.32384 0.9252834 0.3707149 -0.5161687 0.7721014 0.3604327 -0.1222907 0.9247342 -0.3937555 -0.5297784 0.7511935 -0.3468526 -0.1399313 0.9274226 -0.3861352 0.5273194 0.756858 -0.01550346 0.3778825 0.9257238 0.5932005 -0.1941627 0.7812899 -0.2552935 -0.3120594 0.9151198 -0.2217202 0.3164206 0.9223439 -0.8611057 0.2831287 0.4222973 -0.4639219 0.6145645 0.6380338 -0.5278354 0.7418385 0.4136008 -0.5206926 -0.743973 0.4187881 -0.002288937 -0.7517574 0.6594358 -0.4510847 -0.6219964 0.6400337 0.8801352 -0.2517811 0.402453 0.7159793 0.262892 0.6467314 0.7016662 -0.2069199 0.6817982 0.002960264 0.7622033 0.6473311 0.01809775 0.9252135 0.3790152 -0.8609927 -0.2834879 0.4222868 -0.7315096 -0.2380786 0.638915 0.5436753 -0.7357043 0.4039263 0.4313256 -0.5955791 0.6776753 0.4371902 0.6030327 0.6672455 0.5425423 0.7381408 0.4009937 -0.7319138 0.2390271 0.6380975 0.01059013 -0.9113942 0.4113985 0.809342 0.3826513 0.4455822 0.563601 0.7920689 -0.2344802 0.9321829 0.3502095 0.09158855 0.9285508 0.2910324 -0.230421 0.5690046 -0.7895076 -0.2300251 -0.003418087 -0.9983028 0.05813843 -0.004608333 -0.973398 -0.2290743 -0.9266687 -0.29952 -0.2270969 -0.9474842 0.3080559 0.08587968 -0.9266648 0.2996714 -0.2269128 -0.003326535 0.9976424 0.06854611 0.573727 0.8176349 0.04806745 0.9250383 -0.3019879 -0.2304508 0.5855162 -0.8099564 0.03378504 -0.5753478 -0.7855029 -0.2279478 -0.9476516 -0.3077861 0.08499592 -0.004608392 0.9735404 -0.2284687 -0.5846613 0.8063547 0.08923888 0.954499 -0.2971078 0.02566689 -0.5864838 -0.8058239 0.0817604 -0.5753428 0.7856487 -0.2274575 -0.6020551 0.1754252 -0.7789453 -0.5021332 0.6932142 -0.5170266 -0.3447736 0.4532077 -0.8220304 -0.03817886 -0.541341 -0.839936 -0.491749 -0.7004669 -0.5172321 -0.4227482 -0.4839997 -0.7661778 0.6249174 -0.1740832 -0.7610344 0.8063399 0.2722896 -0.5250471 0.8257211 -0.2873971 -0.4853736 0.00540179 0.8590704 -0.5118288 0.02441513 0.5686594 -0.8222107 -0.7982848 -0.2547724 -0.5457403 -0.4672736 -0.1441099 -0.8722889 0.3457832 -0.4077068 -0.8451089 0.4848012 -0.6818041 -0.5478238 0.3991917 0.4962734 -0.7709466 0.5040587 0.6960868 -0.5112613 -0.8093755 0.2637795 -0.5247207 0.01434409 -0.8362916 -0.5480973 0.3638153 0.1197254 -0.9237447 0.06817966 0.1685876 -0.9833259 -0.1750589 0.06848537 -0.9821733 0.1409354 -0.1528071 -0.9781551 -0.07181024 -0.2000799 -0.9771445 -0.01739591 0.01428294 0.9997467</float_array>
          <technique_common>
            <accessor source="#snowman_ball-mesh-normals-array" count="85" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="snowman_ball-mesh-map-0">
          <float_array id="snowman_ball-mesh-map-0-array" count="996">0.7478754 0.1812677 0.8172046 0.1258785 0.8292943 0.1619637 0.2896807 0.5160591 0.3573951 0.5613988 0.2855111 0.5561627 0.1383287 0.5411437 0.09331977 0.6115427 0.06411218 0.5817208 0.664867 0.1848749 0.7421433 0.1482419 0.7478754 0.1812677 0.368974 0.5234175 0.4244493 0.5858125 0.3573951 0.5613988 0.2133309 0.5209206 0.1651643 0.5704666 0.1383287 0.5411437 0.5824998 0.1724743 0.6629052 0.1536462 0.664867 0.1848749 0.8292943 0.1619637 0.8875102 0.0936861 0.910067 0.1233915 0.2133309 0.5209206 0.2855111 0.5561627 0.2193918 0.5541422 0.5824998 0.1724743 0.5288106 0.1120408 0.5871484 0.141169 0.4852355 0.1760398 0.5821174 0.1915654 0.5807528 0.2150551 0.207892 0.4666682 0.2908053 0.4917921 0.2104593 0.4969428 0.9350308 0.1632087 0.8214126 0.1816571 0.920493 0.1394503 0.5807528 0.2150551 0.6672482 0.2042698 0.6691943 0.2269242 0.1220178 0.4884323 0.2104593 0.4969428 0.1302585 0.5185234 0.3764107 0.4652773 0.4644715 0.5255343 0.374019 0.4976335 0.6691943 0.2269242 0.7510772 0.2007841 0.7538832 0.2230451 0.1220178 0.4884323 0.04725879 0.5618784 0.03244197 0.5304931 0.2909139 0.4616089 0.374019 0.4976335 0.2908053 0.4917921 0.813376 0.2053362 0.7510772 0.2007841 0.8214126 0.1816571 0.7523996 0.2815026 0.8295593 0.2307265 0.8440757 0.2771902 0.3652493 0.3909661 0.2850992 0.4318116 0.2794001 0.3953087 0.10927 0.4141559 0.01428616 0.4908459 0.003034651 0.4289066 0.7523996 0.2815026 0.6663659 0.2497338 0.7526185 0.2458712 0.4735666 0.3808164 0.3713745 0.4322444 0.3652493 0.3909661 0.1964119 0.4002215 0.1172602 0.4554277 0.10927 0.4141559 0.6647393 0.2841731 0.5768166 0.2403504 0.6663659 0.2497338 0.8440757 0.2771902 0.9501098 0.2029547 0.9547625 0.2785682 0.2794001 0.3953087 0.2021842 0.4367191 0.1964119 0.4002215 0.5735722 0.2820302 0.4707399 0.2125093 0.5768166 0.2403504 0.4999915 0.4103977 0.5806299 0.3463115 0.5897566 0.4048516 0.2555715 0.2882436 0.1942968 0.3518877 0.1811389 0.3171877 0.9119157 0.394275 0.8426401 0.3414791 0.9389066 0.3578057 0.5897566 0.4048516 0.6682978 0.3411792 0.6702782 0.3951308 0.1811389 0.3171877 0.1072271 0.3578315 0.1085717 0.288474 0.360907 0.2796806 0.4448817 0.305065 0.3564143 0.3387991 0.7576928 0.379463 0.6682978 0.3411792 0.7544323 0.3394886 0.02684134 0.3059314 0.1072271 0.3578315 0.01185637 0.349909 0.2555715 0.2882436 0.3564143 0.3387991 0.2768834 0.3486084 0.7576928 0.379463 0.8426401 0.3414791 0.840549 0.4327974 0.2712711 0.05334126 0.3455429 0.1437492 0.2707571 0.1458674 0.2707571 0.1458674 0.1944761 0.09276682 0.2712711 0.05334126 0.3455429 0.1437492 0.4318525 0.09663909 0.4024503 0.1885732 0.2555715 0.2882436 0.3440082 0.2219726 0.360907 0.2796806 0.2707197 0.2185405 0.1830734 0.2555052 0.1971023 0.1776761 0.3440082 0.2219726 0.4231221 0.2649747 0.360907 0.2796806 0.3455429 0.1437492 0.3440082 0.2219726 0.2707197 0.2185405 0.2707197 0.2185405 0.2707571 0.1458674 0.3455429 0.1437492 0.7526185 0.2458712 0.813376 0.2053362 0.8295593 0.2307265 0.3713745 0.4322444 0.2909139 0.4616089 0.2850992 0.4318116 0.01428616 0.4908459 0.1220178 0.4884323 0.03244197 0.5304931 0.7526185 0.2458712 0.6691943 0.2269242 0.7538832 0.2230451 0.476703 0.4449812 0.3764107 0.4652773 0.3713745 0.4322444 0.2021842 0.4367191 0.1220178 0.4884323 0.1172602 0.4554277 0.6663659 0.2497338 0.5807528 0.2150551 0.6691943 0.2269242 0.8295593 0.2307265 0.9350308 0.1632087 0.9501098 0.2029547 0.2850992 0.4318116 0.207892 0.4666682 0.2021842 0.4367191 0.5768166 0.2403504 0.4852355 0.1760398 0.5807528 0.2150551 0.4970577 0.1525599 0.5824998 0.1724743 0.5821174 0.1915654 0.2104593 0.4969428 0.2896807 0.5160591 0.2133309 0.5209206 0.920493 0.1394503 0.8292943 0.1619637 0.910067 0.1233915 0.5821174 0.1915654 0.664867 0.1848749 0.6672482 0.2042698 0.1302585 0.5185234 0.2133309 0.5209206 0.1383287 0.5411437 0.374019 0.4976335 0.4518823 0.5516309 0.368974 0.5234175 0.6672482 0.2042698 0.7478754 0.1812677 0.7510772 0.2007841 0.1302585 0.5185234 0.06411218 0.5817208 0.04725879 0.5618784 0.2908053 0.4917921 0.368974 0.5234175 0.2896807 0.5160591 0.8214126 0.1816571 0.7478754 0.1812677 0.8292943 0.1619637 0.4793283 0.3650535 0.5735722 0.2820302 0.5806299 0.3463115 0.1942968 0.3518877 0.2794001 0.3953087 0.1964119 0.4002215 0.8426401 0.3414791 0.9547625 0.2785682 0.9389066 0.3578057 0.5806299 0.3463115 0.6647393 0.2841731 0.6682978 0.3411792 0.1072271 0.3578315 0.1964119 0.4002215 0.10927 0.4141559 0.3564143 0.3387991 0.4735666 0.3808164 0.3652493 0.3909661 0.6682978 0.3411792 0.7523996 0.2815026 0.7544323 0.3394886 0.1072271 0.3578315 0.003034651 0.4289066 0.01185637 0.349909 0.2768834 0.3486084 0.3652493 0.3909661 0.2794001 0.3953087 0.7544323 0.3394886 0.8440757 0.2771902 0.8426401 0.3414791 0.7478754 0.1812677 0.7421433 0.1482419 0.8172046 0.1258785 0.2896807 0.5160591 0.368974 0.5234175 0.3573951 0.5613988 0.1383287 0.5411437 0.1651643 0.5704666 0.09331977 0.6115427 0.664867 0.1848749 0.6629052 0.1536462 0.7421433 0.1482419 0.368974 0.5234175 0.4518823 0.5516309 0.4244493 0.5858125 0.2133309 0.5209206 0.2193918 0.5541422 0.1651643 0.5704666 0.5824998 0.1724743 0.5871484 0.141169 0.6629052 0.1536462 0.8292943 0.1619637 0.8172046 0.1258785 0.8875102 0.0936861 0.2133309 0.5209206 0.2896807 0.5160591 0.2855111 0.5561627 0.5824998 0.1724743 0.5047063 0.1365352 0.5288106 0.1120408 0.4852355 0.1760398 0.4970577 0.1525599 0.5821174 0.1915654 0.207892 0.4666682 0.2909139 0.4616089 0.2908053 0.4917921 0.9350308 0.1632087 0.813376 0.2053362 0.8214126 0.1816571 0.5807528 0.2150551 0.5821174 0.1915654 0.6672482 0.2042698 0.1220178 0.4884323 0.207892 0.4666682 0.2104593 0.4969428 0.3764107 0.4652773 0.4713721 0.4878647 0.4644715 0.5255343 0.6691943 0.2269242 0.6672482 0.2042698 0.7510772 0.2007841 0.1220178 0.4884323 0.1302585 0.5185234 0.04725879 0.5618784 0.2909139 0.4616089 0.3764107 0.4652773 0.374019 0.4976335 0.813376 0.2053362 0.7538832 0.2230451 0.7510772 0.2007841 0.7523996 0.2815026 0.7526185 0.2458712 0.8295593 0.2307265 0.3652493 0.3909661 0.3713745 0.4322444 0.2850992 0.4318116 0.10927 0.4141559 0.1172602 0.4554277 0.01428616 0.4908459 0.7523996 0.2815026 0.6647393 0.2841731 0.6663659 0.2497338 0.4735666 0.3808164 0.476703 0.4449812 0.3713745 0.4322444 0.1964119 0.4002215 0.2021842 0.4367191 0.1172602 0.4554277 0.6647393 0.2841731 0.5735722 0.2820302 0.5768166 0.2403504 0.8440757 0.2771902 0.8295593 0.2307265 0.9501098 0.2029547 0.2794001 0.3953087 0.2850992 0.4318116 0.2021842 0.4367191 0.5735722 0.2820302 0.4649596 0.2834405 0.4707399 0.2125093 0.4999915 0.4103977 0.4793283 0.3650535 0.5806299 0.3463115 0.2555715 0.2882436 0.2768834 0.3486084 0.1942968 0.3518877 0.9119157 0.394275 0.840549 0.4327974 0.8426401 0.3414791 0.5897566 0.4048516 0.5806299 0.3463115 0.6682978 0.3411792 0.1811389 0.3171877 0.1942968 0.3518877 0.1072271 0.3578315 0.360907 0.2796806 0.4231221 0.2649747 0.4448817 0.305065 0.7576928 0.379463 0.6702782 0.3951308 0.6682978 0.3411792 0.02684134 0.3059314 0.1085717 0.288474 0.1072271 0.3578315 0.2555715 0.2882436 0.360907 0.2796806 0.3564143 0.3387991 0.7576928 0.379463 0.7544323 0.3394886 0.8426401 0.3414791 0.2712711 0.05334126 0.3523979 0.05474191 0.3455429 0.1437492 0.2707571 0.1458674 0.1971023 0.1776761 0.1944761 0.09276682 0.3455429 0.1437492 0.3523979 0.05474191 0.4318525 0.09663909 0.2555715 0.2882436 0.2707197 0.2185405 0.3440082 0.2219726 0.2707197 0.2185405 0.2555715 0.2882436 0.1830734 0.2555052 0.3440082 0.2219726 0.4024503 0.1885732 0.4231221 0.2649747 0.3455429 0.1437492 0.4024503 0.1885732 0.3440082 0.2219726 0.2707197 0.2185405 0.1971023 0.1776761 0.2707571 0.1458674 0.7526185 0.2458712 0.7538832 0.2230451 0.813376 0.2053362 0.3713745 0.4322444 0.3764107 0.4652773 0.2909139 0.4616089 0.01428616 0.4908459 0.1172602 0.4554277 0.1220178 0.4884323 0.7526185 0.2458712 0.6663659 0.2497338 0.6691943 0.2269242 0.476703 0.4449812 0.4713721 0.4878647 0.3764107 0.4652773 0.2021842 0.4367191 0.207892 0.4666682 0.1220178 0.4884323 0.6663659 0.2497338 0.5768166 0.2403504 0.5807528 0.2150551 0.8295593 0.2307265 0.813376 0.2053362 0.9350308 0.1632087 0.2850992 0.4318116 0.2909139 0.4616089 0.207892 0.4666682 0.5768166 0.2403504 0.4707399 0.2125093 0.4852355 0.1760398 0.4970577 0.1525599 0.5047063 0.1365352 0.5824998 0.1724743 0.2104593 0.4969428 0.2908053 0.4917921 0.2896807 0.5160591 0.920493 0.1394503 0.8214126 0.1816571 0.8292943 0.1619637 0.5821174 0.1915654 0.5824998 0.1724743 0.664867 0.1848749 0.1302585 0.5185234 0.2104593 0.4969428 0.2133309 0.5209206 0.374019 0.4976335 0.4644715 0.5255343 0.4518823 0.5516309 0.6672482 0.2042698 0.664867 0.1848749 0.7478754 0.1812677 0.1302585 0.5185234 0.1383287 0.5411437 0.06411218 0.5817208 0.2908053 0.4917921 0.374019 0.4976335 0.368974 0.5234175 0.8214126 0.1816571 0.7510772 0.2007841 0.7478754 0.1812677 0.4793283 0.3650535 0.4649596 0.2834405 0.5735722 0.2820302 0.1942968 0.3518877 0.2768834 0.3486084 0.2794001 0.3953087 0.8426401 0.3414791 0.8440757 0.2771902 0.9547625 0.2785682 0.5806299 0.3463115 0.5735722 0.2820302 0.6647393 0.2841731 0.1072271 0.3578315 0.1942968 0.3518877 0.1964119 0.4002215 0.3564143 0.3387991 0.4448817 0.305065 0.4735666 0.3808164 0.6682978 0.3411792 0.6647393 0.2841731 0.7523996 0.2815026 0.1072271 0.3578315 0.10927 0.4141559 0.003034651 0.4289066 0.2768834 0.3486084 0.3564143 0.3387991 0.3652493 0.3909661 0.7544323 0.3394886 0.7523996 0.2815026 0.8440757 0.2771902 0.1960591 0.543289 0.2516627 0.4614272 0.2552762 0.5557702 0.3106966 0.5405627 0.2552762 0.5557702 0.2516627 0.4614272 0.3379778 0.4985627 0.3106966 0.5405627 0.2516627 0.4614272 0.3061966 0.3814492 0.2516627 0.4614272 0.2478538 0.3621985 0.1580349 0.435561 0.189598 0.3834372 0.2516627 0.4614272 0.2516627 0.4614272 0.3061966 0.3814492 0.3379778 0.4255374 0.3379778 0.4255374 0.3379778 0.4985627 0.2516627 0.4614272 0.189598 0.3834372 0.2478538 0.3621985 0.2516627 0.4614272 0.2516627 0.4614272 0.1960591 0.543289 0.1590625 0.4956915 0.1590625 0.4956915 0.1580349 0.435561 0.2516627 0.4614272</float_array>
          <technique_common>
            <accessor source="#snowman_ball-mesh-map-0-array" count="498" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="snowman_ball-mesh-vertices">
          <input semantic="POSITION" source="#snowman_ball-mesh-positions"/>
        </vertices>
        <triangles material="unicycle_snowman-material" count="166">
          <input semantic="VERTEX" source="#snowman_ball-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#snowman_ball-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#snowman_ball-mesh-map-0" offset="2" set="0"/>
          <p>64 0 0 18 1 1 71 2 2 72 3 3 16 4 4 15 5 5 66 6 6 12 7 7 73 8 8 67 9 9 19 10 10 64 0 11 65 11 12 17 12 13 16 4 14 69 13 15 13 14 16 66 6 17 70 15 18 10 16 19 67 9 20 71 2 21 17 12 22 68 17 23 69 13 24 15 5 25 14 18 26 70 15 27 12 7 28 11 19 29 63 20 30 29 21 31 60 22 32 59 23 33 22 24 34 28 25 35 58 26 36 20 27 37 27 28 38 60 22 39 26 29 40 57 30 41 56 31 42 28 25 43 25 32 44 55 33 45 27 28 46 23 34 47 57 30 48 21 35 49 54 36 50 56 31 51 24 37 52 63 20 53 62 38 54 23 34 55 22 24 56 61 39 57 21 35 58 20 27 59 74 40 60 30 41 61 81 42 62 75 43 63 31 44 64 82 45 65 76 46 66 32 47 67 83 48 68 74 40 69 36 49 70 33 50 71 78 51 72 34 52 73 75 43 74 79 53 75 35 54 76 76 46 77 77 55 78 39 56 79 36 49 80 81 42 81 37 57 82 78 51 83 82 45 84 38 58 85 79 53 86 80 59 87 32 47 88 39 56 89 2 60 90 49 61 91 1 62 92 5 63 93 48 64 94 4 65 95 7 66 96 40 67 97 47 68 98 1 62 99 46 69 100 0 70 101 4 65 102 45 71 103 3 72 104 6 73 105 47 68 106 44 74 107 9 75 108 46 69 109 43 76 110 2 60 111 45 71 112 42 77 113 5 63 114 44 74 115 41 78 116 9 75 117 40 67 118 8 79 119 1 62 120 50 80 121 51 81 122 51 81 123 2 60 124 1 62 125 50 80 126 9 75 127 8 79 128 5 63 129 53 82 130 6 73 131 52 83 132 4 65 133 3 72 134 53 82 135 7 66 136 6 73 137 50 80 138 53 82 139 52 83 140 52 83 141 51 81 142 50 80 143 33 50 144 61 39 145 30 41 146 34 52 147 62 38 148 31 44 149 32 47 150 56 31 151 63 20 152 33 50 153 57 30 154 54 36 155 37 57 156 55 33 157 34 52 158 38 58 159 56 31 160 35 54 161 36 49 162 60 22 163 57 30 164 30 41 165 58 26 166 37 57 167 31 44 168 59 23 169 38 58 170 39 56 171 63 20 172 60 22 173 24 37 174 70 15 175 29 21 176 28 25 177 72 3 178 69 13 179 27 28 180 71 2 181 68 17 182 29 21 183 67 9 184 26 29 185 25 32 186 69 13 187 66 6 188 23 34 189 68 17 190 65 11 191 26 29 192 64 0 193 21 35 194 25 32 195 73 8 196 24 37 197 22 24 198 65 11 199 72 3 200 20 27 201 64 0 202 71 2 203 42 77 204 80 59 205 49 61 206 48 64 207 82 45 208 79 53 209 40 67 210 78 51 211 47 68 212 49 61 213 77 55 214 46 69 215 45 71 216 79 53 217 76 46 218 44 74 219 78 51 220 75 43 221 46 69 222 74 40 223 43 76 224 45 71 225 83 48 226 42 77 227 41 78 228 75 43 229 82 45 230 43 76 231 81 42 232 40 67 233 64 0 234 19 10 235 18 1 236 72 3 237 65 11 238 16 4 239 66 6 240 13 14 241 12 7 242 67 9 243 10 16 244 19 10 245 65 11 246 68 17 247 17 12 248 69 13 249 14 18 250 13 14 251 70 15 252 11 19 253 10 16 254 71 2 255 18 1 256 17 12 257 69 13 258 72 3 259 15 5 260 70 15 261 73 8 262 12 7 263 63 20 264 24 37 265 29 21 266 59 23 267 62 38 268 22 24 269 58 26 270 61 39 271 20 27 272 60 22 273 29 21 274 26 29 275 56 31 276 59 23 277 28 25 278 55 33 279 58 26 280 27 28 281 57 30 282 26 29 283 21 35 284 56 31 285 25 32 286 24 37 287 62 38 288 55 33 289 23 34 290 61 39 291 54 36 292 21 35 293 74 40 294 33 50 295 30 41 296 75 43 297 34 52 298 31 44 299 76 46 300 35 54 301 32 47 302 74 40 303 77 55 304 36 49 305 78 51 306 37 57 307 34 52 308 79 53 309 38 58 310 35 54 311 77 55 312 80 59 313 39 56 314 81 42 315 30 41 316 37 57 317 82 45 318 31 44 319 38 58 320 80 59 321 83 48 322 32 47 323 2 60 324 42 77 325 49 61 326 5 63 327 41 78 328 48 64 329 7 66 330 8 79 331 40 67 332 1 62 333 49 61 334 46 69 335 4 65 336 48 64 337 45 71 338 6 73 339 7 66 340 47 68 341 9 75 342 0 70 343 46 69 344 2 60 345 3 72 346 45 71 347 5 63 348 6 73 349 44 74 350 9 75 351 43 76 352 40 67 353 1 62 354 0 70 355 50 80 356 51 81 357 3 72 358 2 60 359 50 80 360 0 70 361 9 75 362 5 63 363 52 83 364 53 82 365 52 83 366 5 63 367 4 65 368 53 82 369 8 79 370 7 66 371 50 80 372 8 79 373 53 82 374 52 83 375 3 72 376 51 81 377 33 50 378 54 36 379 61 39 380 34 52 381 55 33 382 62 38 383 32 47 384 35 54 385 56 31 386 33 50 387 36 49 388 57 30 389 37 57 390 58 26 391 55 33 392 38 58 393 59 23 394 56 31 395 36 49 396 39 56 397 60 22 398 30 41 399 61 39 400 58 26 401 31 44 402 62 38 403 59 23 404 39 56 405 32 47 406 63 20 407 24 37 408 73 8 409 70 15 410 28 25 411 22 24 412 72 3 413 27 28 414 20 27 415 71 2 416 29 21 417 70 15 418 67 9 419 25 32 420 28 25 421 69 13 422 23 34 423 27 28 424 68 17 425 26 29 426 67 9 427 64 0 428 25 32 429 66 6 430 73 8 431 22 24 432 23 34 433 65 11 434 20 27 435 21 35 436 64 0 437 42 77 438 83 48 439 80 59 440 48 64 441 41 78 442 82 45 443 40 67 444 81 42 445 78 51 446 49 61 447 80 59 448 77 55 449 45 71 450 48 64 451 79 53 452 44 74 453 47 68 454 78 51 455 46 69 456 77 55 457 74 40 458 45 71 459 76 46 460 83 48 461 41 78 462 44 74 463 75 43 464 43 76 465 74 40 466 81 42 467 16 4 468 84 84 469 15 5 470 14 18 471 15 5 472 84 84 473 13 14 474 14 18 475 84 84 476 11 19 477 84 84 478 10 16 479 18 1 480 19 10 481 84 84 482 84 84 483 11 19 484 12 7 485 12 7 486 13 14 487 84 84 488 19 10 489 10 16 490 84 84 491 84 84 492 16 4 493 17 12 494 17 12 495 18 1 496 84 84 497</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="debug_pointer" name="debug_pointer" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#debug_pointer-mesh" name="debug_pointer">
          <bind_material>
            <technique_common>
              <instance_material symbol="unicycle_debug-material" target="#unicycle_debug-material">
                <bind_vertex_input semantic="Circle-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="debug_ball" name="debug_ball" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#debug_ball-mesh" name="debug_ball">
          <bind_material>
            <technique_common>
              <instance_material symbol="unicycle_debug-material" target="#unicycle_debug-material">
                <bind_vertex_input semantic="Icosphere-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="debug_body" name="debug_body" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#debug_body-mesh" name="debug_body">
          <bind_material>
            <technique_common>
              <instance_material symbol="unicycle_debug-material" target="#unicycle_debug-material">
                <bind_vertex_input semantic="Circle-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="snowman_body" name="snowman_body" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#snowman_body-mesh" name="snowman_body">
          <bind_material>
            <technique_common>
              <instance_material symbol="unicycle_snowman-material" target="#unicycle_snowman-material">
                <bind_vertex_input semantic="Cube_001-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="snowman_ball" name="snowman_ball" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#snowman_ball-mesh" name="snowman_ball">
          <bind_material>
            <technique_common>
              <instance_material symbol="unicycle_snowman-material" target="#unicycle_snowman-material">
                <bind_vertex_input semantic="Cube_010-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>