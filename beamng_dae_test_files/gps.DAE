<?xml version='1.0' encoding='utf-8'?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>GabeNY</author>
      <authoring_tool>OpenCOLLADA for 3ds Max;  ;  </authoring_tool>
      </contributor>
    <created>2016-11-06T01:48:28</created>
    <modified>2016-11-06T01:48:28</modified>
    <unit meter="0.01" name="centimeter" />
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="suctionmount">
      <profile_COMMON>
        <newparam sid="suctionmount_d_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="suctionmount_d_tga-sampler">
          <sampler2D>
            <source>suctionmount_d_tga-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="suctionmount_s_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="suctionmount_s_tga-sampler">
          <sampler2D>
            <source>suctionmount_s_tga-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="suctionmount_n_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="suctionmount_n_tga-sampler">
          <sampler2D>
            <source>suctionmount_n_tga-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <blinn>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>0.5882353 0.5882353 0.5882353 1</color>
            </ambient>
            <diffuse>
              <texture texcoord="CHANNEL1" texture="suctionmount_d_tga-sampler" />
            </diffuse>
            <specular>
              <color>0 0 0 1</color>
            </specular>
            <shininess>
              <float>9.999999</float>
            </shininess>
            <reflective>
              <color>0 0 0 1</color>
            </reflective>
            <transparent opaque="A_ONE">
              <color>1 1 1 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </blinn>
          <extra>
            <technique profile="OpenCOLLADA3dsMax">
              <specularLevel>
                <texture texcoord="CHANNEL1" texture="suctionmount_s_tga-sampler" />
              </specularLevel>
              <bump bumptype="HEIGHTFIELD">
                <texture texcoord="CHANNEL1" texture="suctionmount_n_tga-sampler" />
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
      <extra>
        <technique profile="OpenCOLLADA3dsMax">
          <extended_shader>
            <apply_reflection_dimming type="bool">0</apply_reflection_dimming>
            <dim_level type="float">0</dim_level>
            <falloff type="float">0</falloff>
            <falloff_type type="integer">0</falloff_type>
            <index_of_refraction type="float">1.5</index_of_refraction>
            <opacity_type type="integer">0</opacity_type>
            <reflection_level type="float">3</reflection_level>
            <wire_size type="float">1</wire_size>
            <wire_units type="integer">0</wire_units>
          </extended_shader>
          <shader>
            <ambient_diffuse_lock type="bool">1</ambient_diffuse_lock>
            <ambient_diffuse_texture_lock type="bool">1</ambient_diffuse_texture_lock>
            <diffuse_specular_lock type="bool">0</diffuse_specular_lock>
            <self_illumination type="float">0</self_illumination>
            <soften type="float">0.1</soften>
            <specular_level type="float">0</specular_level>
            <use_self_illum_color type="bool">0</use_self_illum_color>
          </shader>
        </technique>
      </extra>
    </effect>
    <effect id="gps">
      <profile_COMMON>
        <newparam sid="gps_d_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="gps_d_tga-sampler">
          <sampler2D>
            <source>gps_d_tga-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="gps_s_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="gps_s_tga-sampler">
          <sampler2D>
            <source>gps_s_tga-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="gps_n_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="gps_n_tga-sampler">
          <sampler2D>
            <source>gps_n_tga-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <blinn>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>0.5882353 0.5882353 0.5882353 1</color>
            </ambient>
            <diffuse>
              <texture texcoord="CHANNEL1" texture="gps_d_tga-sampler" />
            </diffuse>
            <specular>
              <color>0 0 0 1</color>
            </specular>
            <shininess>
              <float>9.999999</float>
            </shininess>
            <reflective>
              <color>0 0 0 1</color>
            </reflective>
            <transparent opaque="A_ONE">
              <color>1 1 1 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </blinn>
          <extra>
            <technique profile="OpenCOLLADA3dsMax">
              <specularLevel>
                <texture texcoord="CHANNEL1" texture="gps_s_tga-sampler" />
              </specularLevel>
              <bump bumptype="HEIGHTFIELD">
                <texture texcoord="CHANNEL1" texture="gps_n_tga-sampler" />
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
      <extra>
        <technique profile="OpenCOLLADA3dsMax">
          <extended_shader>
            <apply_reflection_dimming type="bool">0</apply_reflection_dimming>
            <dim_level type="float">0</dim_level>
            <falloff type="float">0</falloff>
            <falloff_type type="integer">0</falloff_type>
            <index_of_refraction type="float">1.5</index_of_refraction>
            <opacity_type type="integer">0</opacity_type>
            <reflection_level type="float">3</reflection_level>
            <wire_size type="float">1</wire_size>
            <wire_units type="integer">0</wire_units>
          </extended_shader>
          <shader>
            <ambient_diffuse_lock type="bool">1</ambient_diffuse_lock>
            <ambient_diffuse_texture_lock type="bool">1</ambient_diffuse_texture_lock>
            <diffuse_specular_lock type="bool">0</diffuse_specular_lock>
            <self_illumination type="float">0</self_illumination>
            <soften type="float">0.1</soften>
            <specular_level type="float">0</specular_level>
            <use_self_illum_color type="bool">0</use_self_illum_color>
          </shader>
        </technique>
      </extra>
    </effect>
    <effect id="screen_gps">
      <profile_COMMON>
        <technique sid="common">
          <blinn>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>0 0 0 1</color>
            </ambient>
            <diffuse>
              <color>0 0 0 1</color>
            </diffuse>
            <specular>
              <color>0.585 0.585 0.585 1</color>
            </specular>
            <shininess>
              <float>35</float>
            </shininess>
            <reflective>
              <color>0 0 0 1</color>
            </reflective>
            <transparent opaque="A_ONE">
              <color>1 1 1 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </blinn>
        </technique>
      </profile_COMMON>
      <extra>
        <technique profile="OpenCOLLADA3dsMax">
          <extended_shader>
            <apply_reflection_dimming type="bool">0</apply_reflection_dimming>
            <dim_level type="float">0</dim_level>
            <falloff type="float">0</falloff>
            <falloff_type type="integer">0</falloff_type>
            <index_of_refraction type="float">1.5</index_of_refraction>
            <opacity_type type="integer">0</opacity_type>
            <reflection_level type="float">3</reflection_level>
            <wire_size type="float">1</wire_size>
            <wire_units type="integer">0</wire_units>
          </extended_shader>
          <shader>
            <ambient_diffuse_lock type="bool">1</ambient_diffuse_lock>
            <ambient_diffuse_texture_lock type="bool">1</ambient_diffuse_texture_lock>
            <diffuse_specular_lock type="bool">0</diffuse_specular_lock>
            <self_illumination type="float">0</self_illumination>
            <soften type="float">0.1</soften>
            <specular_level type="float">0.65</specular_level>
            <use_self_illum_color type="bool">0</use_self_illum_color>
          </shader>
        </technique>
      </extra>
    </effect>
  </library_effects>
  <library_materials>
    <material id="suctionmount-material" name="suctionmount">
      <instance_effect url="#suctionmount" />
    </material>
    <material id="gps-material" name="gps">
      <instance_effect url="#gps" />
    </material>
    <material id="screen_gps-material" name="screen_gps">
      <instance_effect url="#screen_gps" />
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="geom-suctionmount" name="suctionmount">
      <mesh>
        <source id="geom-suctionmount-positions">
          <float_array count="540" id="geom-suctionmount-positions-array">2.705014 -12.47049 4.715806 2.612843 -12.96554 4.220754 2.342611 -13.42685 3.75944 1.912734 -13.82299 3.3633 1.352508 -14.12696 3.059332 0.7001115 -14.31804 2.868249 3.44675e-6 -14.38322 2.803074 -0.7001045 -14.31804 2.868249 -1.352501 -14.12696 3.059332 -1.912727 -13.82299 3.3633 -2.342604 -13.42685 3.759439 -2.612836 -12.96554 4.220754 -2.705007 -12.47049 4.715805 -2.612836 -11.97543 5.210856 -2.342605 -11.51412 5.67217 -1.912729 -11.11798 6.06831 -1.352503 -10.81401 6.372279 -0.700106 -10.62293 6.563362 1.98494e-6 -10.55775 6.628537 0.70011 -10.62293 6.563362 1.352507 -10.81401 6.37228 1.912733 -11.11798 6.068312 2.34261 -11.51412 5.672172 2.612842 -11.97543 5.210858 2.705014 -12.13344 4.37876 2.612843 -12.62849 3.883708 2.342611 -13.0898 3.422394 1.912735 -13.48594 3.026255 1.352508 -13.78991 2.722286 0.7001115 -13.981 2.531203 3.44675e-6 -14.04617 2.466029 -0.7001045 -13.981 2.531203 -1.352501 -13.78991 2.722286 -1.912727 -13.48595 3.026254 -2.342604 -13.0898 3.422394 -2.612836 -12.62849 3.883708 -2.705007 -12.13344 4.378759 -2.612836 -11.63839 4.87381 -2.342605 -11.17707 5.335124 -1.912729 -10.78093 5.731264 -1.352503 -10.47697 6.035233 -0.700106 -10.28588 6.226316 1.98494e-6 -10.22071 6.291491 0.70011 -10.28588 6.226316 1.352507 -10.47697 6.035234 1.912733 -10.78093 5.731266 2.34261 -11.17707 5.335126 2.612842 -11.63839 4.873812 1.34503 -11.75385 3.999171 1.16483 -12.22939 3.523633 0.6725165 -12.57751 3.175514 3.35935e-6 -12.70493 3.048094 -0.67251 -12.57751 3.175514 -1.164824 -12.22939 3.523633 -1.345023 -11.75385 3.999171 -1.164825 -11.27831 4.47471 -0.6725106 -10.93019 4.822829 2.60296e-6 -10.80277 4.950249 0.672516 -10.93019 4.822829 1.16483 -11.27831 4.474711 1.03407 -11.48596 3.731275 0.8955317 -11.85155 3.365678 0.5170368 -12.11919 3.098041 3.30413e-6 -12.21715 3.00008 -0.5170302 -12.11919 3.098041 -0.8955252 -11.85155 3.365677 -1.034064 -11.48596 3.731275 -0.8955256 -11.12036 4.096873 -0.5170308 -10.85272 4.36451 2.71768e-6 -10.75476 4.462471 0.5170363 -10.85272 4.36451 0.8955314 -11.12036 4.096874 2.083798 -11.88583 4.131155 2.012794 -12.2672 3.749794 1.804622 -12.62257 3.394422 1.473468 -12.92773 3.089257 1.041901 -13.16189 2.855096 0.5393291 -13.30909 2.707896 3.41665e-6 -13.3593 2.65769 -0.5393221 -13.30909 2.707896 -1.041894 -13.16189 2.855096 -1.473462 -12.92773 3.089257 -1.804615 -12.62257 3.394422 -2.012788 -12.2672 3.749794 -2.083791 -11.88583 4.131154 -2.012788 -11.50447 4.512515 -1.804616 -11.1491 4.867887 -1.473463 -10.84394 5.173051 -1.041895 -10.60978 5.407213 -0.5393232 -10.46258 5.554413 2.27535e-6 -10.41237 5.60462 0.539328 -10.46258 5.554413 1.0419 -10.60978 5.407214 1.473468 -10.84394 5.173053 1.804622 -11.1491 4.867888 2.012794 -11.50447 4.512516 0.6000032 -11.50897 3.739998 0.5196185 -11.7211 3.527866 0.3000032 -11.87639 3.372575 3.17427e-6 -11.93323 3.315734 -0.2999969 -11.87639 3.372575 -0.519612 -11.7211 3.527866 -0.5999969 -11.50897 3.739998 -0.5196122 -11.29684 3.95213 -0.299997 -11.14155 4.107422 2.92155e-6 -11.0847 4.164262 0.3000029 -11.14154 4.107422 0.5196182 -11.29684 3.952131 0.6000032 -10.2568 2.641873 0.5196184 -10.43942 2.403867 0.3000032 -10.57312 2.229635 3.17926e-6 -10.62205 2.165861 -0.2999969 -10.57312 2.229635 -0.5196121 -10.43942 2.403867 -0.5999969 -10.2568 2.641873 -0.5196122 -10.07417 2.879879 -0.299997 -9.940475 3.054111 2.92609e-6 -9.89154 3.117885 0.3000029 -9.940475 3.054112 0.5196182 -10.07417 2.879879 0.6000032 -8.872003 1.716583 0.5196184 -9.022002 1.456776 0.3000032 -9.13181 1.266584 3.19413e-6 -9.172003 1.196968 -0.2999968 -9.13181 1.266584 -0.5196121 -9.022003 1.456776 -0.5999969 -8.872003 1.716583 -0.5196121 -8.722002 1.976391 -0.299997 -8.612195 2.166583 2.93962e-6 -8.572002 2.236198 0.3000029 -8.612195 2.166584 0.5196183 -8.722002 1.976392 0.6000032 -7.378282 0.9799621 0.5196185 -7.493087 0.7027979 0.3000032 -7.57713 0.4999001 3.21864e-6 -7.607892 0.4256339 -0.2999968 -7.57713 0.4999001 -0.519612 -7.493087 0.7027979 -0.5999968 -7.378282 0.9799614 -0.5196121 -7.263477 1.257126 -0.299997 -7.179434 1.460024 2.96192e-6 -7.148672 1.53429 0.3000029 -7.179433 1.460024 0.5196183 -7.263476 1.257126 0.6000033 -5.801191 0.4446118 0.5196184 -5.878836 0.1548338 0.3000033 -5.935678 -0.05729846 3.25235e-6 -5.956482 -0.1349442 -0.2999968 -5.935678 -0.05729822 -0.5196121 -5.878837 0.1548338 -0.5999967 -5.801191 0.4446118 -0.5196121 -5.723546 0.7343897 -0.2999969 -5.666704 0.9465215 2.9926e-6 -5.645899 1.024167 0.300003 -5.666704 0.9465215 0.5196183 -5.723545 0.73439 0.6000033 -4.167715 0.1196934 0.5196186 -4.206873 -0.17774 0.3000033 -4.235539 -0.3954762 3.2947e-6 -4.246031 -0.4751738 -0.2999967 -4.235539 -0.3954762 -0.5196119 -4.206873 -0.17774 -0.5999967 -4.167716 0.1196934 -0.5196121 -4.128558 0.4171268 -0.2999969 -4.099892 0.6348633 3.03114e-6 -4.089399 0.7145602 0.300003 -4.099892 0.6348633 0.5196183 -4.128557 0.4171273 0.6000034 -2.505805 0.01076608 0.5196186 -2.505805 -0.2892336 0.3000033 -2.505805 -0.5088491 3.34497e-6 -2.505804 -0.5892343 -0.2999967 -2.505805 -0.5088491 -0.5196119 -2.505805 -0.2892336 -0.5999967 -2.505805 0.01076608 -0.519612 -2.505805 0.310766 -0.2999969 -2.505805 0.5303817 3.07688e-6 -2.505805 0.6107655 0.3000031 -2.505805 0.5303817 0.5196184 -2.505805 0.3107663</float_array>
          <technique_common>
            <accessor count="180" source="#geom-suctionmount-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-suctionmount-normals">
          <float_array count="612" id="geom-suctionmount-normals-array">0.9659259 -0.1830127 -0.1830127 0.8436341 0.1845285 -0.504213 0.8733943 0.3443708 -0.3443706 1 5.38213e-8 -2.51166e-8 0.8660255 -0.3535535 -0.353553 0.7563818 0.03557922 -0.6531621 0.7071068 -0.5000001 -0.4999998 0.6175833 -0.09232674 -0.7810677 0.5000001 -0.6123725 -0.6123722 0.4366972 -0.1904719 -0.8792133 0.2588192 -0.6830127 -0.6830125 0.2260511 -0.2521688 -0.9409101 -1.43524e-8 -0.7071068 -0.7071068 -7.16272e-9 -0.2732126 -0.9619536 -0.258819 -0.6830127 -0.6830127 -0.2260509 -0.2521687 -0.9409102 -0.4999998 -0.612372 -0.6123731 -0.436697 -0.1904712 -0.8792135 -0.7071066 -0.4999999 -0.5000004 -0.617583 -0.09232689 -0.7810678 -0.8660254 -0.3535527 -0.353554 -0.7563812 0.03558019 -0.6531626 -0.9659257 -0.1830129 -0.1830132 -0.843634 0.1845281 -0.5042132 -1 -2.77479e-7 -2.77479e-7 -0.8733943 0.3443704 -0.3443711 -0.965926 0.1830124 0.1830124 -0.8436343 0.5042127 -0.1845286 -0.8660256 0.3535531 0.3535531 -0.756382 0.6531619 -0.03557944 -0.7071072 0.4999994 0.5 -0.6175826 0.7810683 0.092326 -0.4999993 0.6123734 0.6123721 -0.4366966 0.8792137 0.1904716 -0.2588199 0.683012 0.6830132 -0.226052 0.9409098 0.2521693 -4.44923e-7 0.7071065 0.707107 -5.0712e-7 0.9619536 0.2732126 0.2588185 0.683012 0.6830136 0.2260506 0.94091 0.2521694 0.4999996 0.6123725 0.6123728 0.4366969 0.8792134 0.1904722 0.7071062 0.5000002 0.5000007 0.6175822 0.7810685 0.09232689 0.866025 0.3535535 0.3535544 0.7563813 0.6531627 -0.03557868 0.9659257 0.1830131 0.183013 0.843634 0.5042133 -0.1845282 0.4175424 0.4490327 -0.7899544 0.2410677 0.3242474 -0.9147403 -1.58583e-7 0.2785715 -0.9604155 -0.2410683 0.324246 -0.9147407 -0.4175428 0.4490321 -0.7899546 -0.4821371 0.619493 -0.6194937 -0.4175431 0.7899542 -0.4490324 -0.2410682 0.9147407 -0.3242463 -6.83548e-8 0.9604155 -0.2785712 0.2410681 0.9147406 -0.3242464 0.4175426 0.7899546 -0.4490321 0.4821363 0.6194939 -0.6194932 0.3661219 0.5849748 -0.7237121 0.3807123 0.6538571 -0.6538572 0.3297068 0.5192549 -0.7884591 0.26802 0.4648248 -0.8438621 0.1903561 0.420719 -0.886995 0.09810209 0.395456 -0.9132308 -1.62387e-8 0.3846524 -0.9230615 -0.09810194 0.395456 -0.9132308 -0.1903565 0.4207192 -0.8869948 -0.2680201 0.4648247 -0.8438621 -0.3297066 0.5192551 -0.7884591 -0.3661219 0.5849743 -0.7237125 -0.3807124 0.6538564 -0.6538576 -0.3661218 0.7237121 -0.5849749 -0.3297061 0.7884595 -0.5192548 -0.2680189 0.8438624 -0.4648248 -0.1903569 0.8869947 -0.4207191 -0.09810258 0.9132308 -0.3954559 -5.26857e-7 0.9230615 -0.3846523 0.09810186 0.913231 -0.3954558 0.1903566 0.8869948 -0.4207188 0.2680194 0.8438622 -0.4648246 0.3297062 0.7884595 -0.5192546 0.3661218 0.7237124 -0.5849745 0.4240739 0.4434015 -0.789656 0.4896781 0.6165289 -0.6165286 0.244839 0.3166634 -0.9163941 -2.94599e-8 0.2702742 -0.9627835 -0.2448392 0.3166633 -0.9163941 -0.4240738 0.4434012 -0.7896562 -0.489678 0.6165285 -0.616529 -0.4240738 0.7896561 -0.4434012 -0.2448396 0.9163941 -0.3166628 -5.37643e-7 0.9627835 -0.2702737 0.2448389 0.9163941 -0.3166634 0.4240735 0.7896562 -0.4434012 3.11332e-7 -0.7071069 0.7071065 2.3092e-7 -0.7071071 0.7071064 1.97684e-7 -0.7071068 0.7071066 1.81849e-6 -0.7071045 0.707109 3.12558e-7 -0.7071062 0.7071073 2.41742e-7 -0.7071029 0.7071106 -3.31791e-7 -0.7071064 0.7071072 -5.88842e-6 -0.7071002 0.7071133 -7.56751e-7 -0.7071066 0.7071068 -3.4638e-6 -0.7071057 0.7071078 -8.49491e-7 -0.7071067 0.7071068 -1.7319e-6 -0.7071066 0.7071069 -2.1272e-7 -0.7071066 0.7071068 -1.7319e-6 -0.7071073 0.7071063 -3.61312e-8 -0.7071067 0.7071068 2.04941e-6 -0.7071045 0.7071089 9.31552e-8 -0.7071068 0.7071066 0 -0.7071041 0.7071094 1.25252e-7 -0.7071066 0.7071068 9.2368e-7 -0.7071078 0.7071057 -7.2754e-8 -0.7071065 0.7071069 1.7319e-6 -0.7071072 0.7071063 -3.17985e-8 -0.7071068 0.7071066 -5.54205e-6 -0.7071069 0.7071065 0.9999416 -0.007124974 -0.008124476 0.8608794 -0.3354811 -0.3825428 0.8613567 -0.3092512 -0.4030241 0.9999418 -0.006564206 -0.008554663 0.4945884 -0.5730555 -0.6534445 0.4953891 -0.5288136 -0.6891632 2.01014e-7 -0.6593458 -0.7518398 1.24349e-8 -0.6087615 -0.7933533 -0.494588 -0.5730556 -0.6534446 -0.4953888 -0.5288136 -0.6891632 -0.8608792 -0.3354814 -0.3825433 -0.8613566 -0.3092513 -0.4030242 -0.9999416 -0.007124975 -0.008124474 -0.9999418 -0.006564293 -0.008554718 -0.8702408 0.3248112 0.3703763 -0.8706929 0.2994054 0.3901927 -0.5039188 0.5695105 0.6494024 -0.5047246 0.5255317 0.6848863 -1.167e-6 0.6593457 0.7518398 -3.10327e-7 0.6087614 0.7933534 0.5039175 0.5695112 0.649403 0.5047237 0.5255322 0.6848868 0.8702407 0.3248114 0.3703765 0.8706926 0.2994057 0.3901932 0.8613564 -0.2540006 -0.4399419 0.9999418 -0.00539165 -0.009338505 0.4953896 -0.4343354 -0.7522911 -4.66308e-8 -0.5 -0.8660254 -0.4953891 -0.4343356 -0.7522913 -0.8613563 -0.2540008 -0.4399422 -0.9999418 -0.005391529 -0.009338438 -0.8706927 0.2459137 0.425935 -0.5047243 0.4316404 0.747623 -3.86388e-7 0.5000001 0.8660254 0.5047234 0.4316406 0.7476233 0.8706924 0.245914 0.4259355 0.8613564 -0.1944037 -0.4693318 0.9999419 -0.004126291 -0.009961826 0.4953891 -0.3324262 -0.8025474 9.01528e-8 -0.3826835 -0.9238796 -0.4953892 -0.3324261 -0.8025475 -0.8613563 -0.1944037 -0.4693322 -0.9999419 -0.004126587 -0.009962592 -0.8706929 0.1882141 0.4543889 -0.5047245 0.3303631 0.7975671 -3.05459e-7 0.3826835 0.9238795 0.5047238 0.3303632 0.7975675 0.8706924 0.1882144 0.4543898 0.8613566 -0.1314802 -0.4906911 0.9999418 -0.002790877 -0.01041549 0.4953894 -0.2248286 -0.8390718 -3.6745e-7 -0.258819 -0.9659259 -0.4953892 -0.2248286 -0.8390719 -0.8613565 -0.1314803 -0.4906913 -0.9999419 -0.002790865 -0.01041564 -0.8706929 0.1272942 0.4750685 -0.5047244 0.2234333 0.833865 -3.83346e-8 0.258819 0.9659258 0.5047241 0.2234334 0.8338653 0.8706924 0.1272944 0.4750694 0.8613564 -0.06630746 -0.5036552 0.9999418 -0.001407429 -0.01069053 0.4953895 -0.1133842 -0.8612393 2.98437e-8 -0.130526 -0.9914449 -0.4953896 -0.1133842 -0.8612393 -0.8613563 -0.06630732 -0.5036553 -0.9999419 -0.001407449 -0.01069096 -0.870693 0.06419633 0.4876193 -0.5047245 0.1126807 0.855895 -1.11353e-7 0.1305262 0.9914449 0.5047237 0.1126807 0.8558954 0.8706924 0.0641964 0.4876203 0.8608794 -0.03327765 -0.5077195 0.9999416 -7.06769e-4 -0.01078288 0.4945885 -0.05684364 -0.8672665 0 -0.06540315 -0.9978589 -0.4945885 -0.05684365 -0.8672664 -0.8608794 -0.03327769 -0.5077197 -0.9999416 -7.06738e-4 -0.01078326 -0.870241 0.03221928 0.4915713 -0.5039176 0.05649192 0.8619024 -2.13728e-7 0.06540314 0.9978589 0.5039174 0.05649204 0.8619025 0.8702406 0.03221924 0.4915721</float_array>
          <technique_common>
            <accessor count="204" source="#geom-suctionmount-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-suctionmount-map1">
          <float_array count="717" id="geom-suctionmount-map1-array">0.9825655 0.1945762 0 0.9782166 0.2276102 0 0.9654663 0.2583918 0 0.9451838 0.2848231 0 0.9187508 0.3051063 0 0.8879689 0.3178571 0 0.8549364 0.3222055 0 0.8219035 0.3178571 0 0.7911216 0.3051063 0 0.764689 0.2848231 0 0.7444062 0.2583918 0 0.7316561 0.2276102 0 0.7273072 0.1945762 0 0.7316561 0.161544 0 0.7444062 0.1307624 0 0.764689 0.1043292 0 0.7911218 0.08404603 0 0.8219035 0.07129704 0 0.8549364 0.06694679 0 0.6320372 0.01458142 2.705014 0.8879693 0.07129704 0 0.9187508 0.08404603 0 0.9451838 0.1043292 0 0.9654665 0.1307624 0 0.6790907 0.01458142 2.705013 0.01532893 0.1424348 0 0.009271975 0.1889496 0 0.6790907 0.0462851 2.705013 0.03333984 0.09912339 0 0.6320372 0.0462851 2.705014 0.06182738 0.06186988 0 0.7261931 0.01458142 2.705012 0.09911489 0.03341995 0 0.7261931 0.0462851 2.705012 0.1424183 0.01539716 0 0.7732956 0.01458142 2.705011 0.18892 0.009340756 0 0.7732956 0.0462851 2.705011 0.2354215 0.01539858 0 0.8203984 0.01458142 2.70501 0.2787248 0.0334227 0 0.8203984 0.0462851 2.70501 0.3160122 0.06187393 0 0.8675009 0.01458142 2.70501 0.3444985 0.09913152 0 0.8675009 0.04628507 2.705009 0.3625056 0.1424484 0 0.9146004 0.01458132 2.705009 0.3685569 0.1889659 0 0.9146004 0.0462851 2.705009 0.3625 0.235482 0 0.8674979 0.01458142 2.705008 0.3444845 0.278796 0 0.8674979 0.0462851 2.705007 0.3159776 0.3160523 0 0.8203955 0.01458142 2.705007 0.2787091 0.3445484 0 0.8203955 0.04628516 2.705007 0.2353915 0.3625576 0 0.7732929 0.01458142 2.705007 0.1888959 0.3685841 0 0.7732929 0.0462851 2.705007 0.1424017 0.3625454 0 0.7261901 0.01458142 2.705006 0.09909011 0.3445267 0 0.7261901 0.0462851 2.705006 0.06182992 0.316028 0 0.6790877 0.01458142 2.705007 0.033331 0.2787717 0 0.6790875 0.04628507 2.705006 0.01532231 0.2354616 0 0.1268457 0.1889604 0 0.1351636 0.1579276 0 0.157885 0.1352138 0 0.1889212 0.1269014 0 0.2199561 0.1352178 0 0.2426742 0.1579357 0 0.2509868 0.1889713 0 0.2426709 0.2200083 0 0.2199607 0.2427247 0 0.18891 0.2510127 0 0.1578616 0.2427166 0 0.1351558 0.2199974 0 0.05781712 0.1538256 0 0.05351502 0.1889523 0 0.07165623 0.1212549 0 0.09294412 0.09299227 0 0.1212226 0.07172008 0 0.1537961 0.05788061 0 0.18892 0.05358197 0 0.2240442 0.05788192 0 0.2566171 0.07172272 0 0.2848955 0.09299643 0 0.3061815 0.1212617 0 0.3200177 0.1538365 0 0.3243151 0.1889659 0 0.3200144 0.2240965 0 0.306173 0.2566727 0 0.2848872 0.2849502 0 0.256596 0.3062374 0 0.2239873 0.3200525 0 0.1888982 0.3243443 0 0.1538116 0.320043 0 0.1212087 0.3062198 0 0.09292422 0.2849272 0 0.07164538 0.256651 0 0.05781032 0.2240789 0 0.1001205 0.1889563 0 0.1120189 0.1445645 0 0.1445227 0.1120752 0 0.1889208 0.1001835 0 0.2333181 0.1120779 0 0.2658188 0.1445713 0 0.2777108 0.1889686 0 0.2658167 0.2333699 0 0.2333403 0.2658837 0 0.1889032 0.2776912 0 0.1444728 0.2658701 0 0.1120062 0.2333536 0 0.9782166 0.161544 0 0.08094395 0.9919508 0.6 0.05732717 0.9919508 0.6 0.05732717 0.8904028 0.6 0.08094395 0.8904028 0.6000001 0.03371055 0.9919508 0.6000001 0.03371055 0.8904028 0.6 0.01009375 0.9919508 0.6 0.01009375 0.8904028 0.5999999 0.2698779 0.9919508 0.6 0.2698779 0.8904028 0.5999999 0.2462612 0.9919508 0.6 0.2462612 0.8904028 0.6 0.2226444 0.9919508 0.6000001 0.2226444 0.8904028 0.6 0.1990276 0.9919508 0.6000001 0.1990276 0.8904028 0.6000001 0.175411 0.9919508 0.6000001 0.175411 0.8904028 0.6000001 0.1517942 0.9919508 0.6000001 0.1517942 0.8904028 0.6000001 0.1281774 0.9919508 0.6 0.1281774 0.8904028 0.6000001 0.1045608 0.9919508 0.6000001 0.1045608 0.8904028 0.6000001 0.05732717 0.7888533 0.5999999 0.08094395 0.7888533 0.6 0.03371055 0.7888533 0.5999999 0.01009375 0.7888533 0.5999998 0.2698779 0.7888533 0.5999998 0.2462612 0.7888533 0.5999999 0.2226444 0.7888533 0.6000001 0.1990276 0.7888533 0.6000002 0.175411 0.7888533 0.6000003 0.1517942 0.7888533 0.6000003 0.1281774 0.7888533 0.6000001 0.1045608 0.7888533 0.6000001 0.05732717 0.6873053 0.5999998 0.08094395 0.6873053 0.6000001 0.03371055 0.6873053 0.5999998 0.01009375 0.6873053 0.5999997 0.2698779 0.6873053 0.5999997 0.2462612 0.6873053 0.5999998 0.2226444 0.6873053 0.6 0.1990276 0.6873053 0.6000002 0.175411 0.6873053 0.6000003 0.1517942 0.6873053 0.6000003 0.1281774 0.6873053 0.6000003 0.1045608 0.6873053 0.6000002 0.05732717 0.5857558 0.5999998 0.08094395 0.5857558 0.6000001 0.03371055 0.5857558 0.5999997 0.01009375 0.5857558 0.5999996 0.2698779 0.5857558 0.5999997 0.2462612 0.5857558 0.5999998 0.2226444 0.5857558 0.6 0.1990276 0.5857558 0.6000003 0.175411 0.5857558 0.6000004 0.1517942 0.5857558 0.6000004 0.1281774 0.5857558 0.6000004 0.1045608 0.5857558 0.6000003 0.05732717 0.4842078 0.5999997 0.08094395 0.4842078 0.6 0.03371055 0.4842078 0.5999997 0.01009375 0.4842078 0.5999995 0.2698779 0.4842078 0.5999996 0.2462612 0.4842078 0.5999998 0.2226444 0.4842078 0.6000001 0.1990276 0.4842078 0.6000003 0.175411 0.4842078 0.6000005 0.1517942 0.4842078 0.6000005 0.1281774 0.4842078 0.6000004 0.1045608 0.4842078 0.6000003 0.05732717 0.3826583 0.5999997 0.08094395 0.3826583 0.6 0.03371055 0.3826583 0.5999995 0.01009375 0.3826583 0.5999994 0.2698777 0.3826583 0.5999995 0.2462612 0.3826583 0.5999997 0.2226444 0.3826583 0.6000001 0.1990276 0.3826583 0.6000004 0.175411 0.3826583 0.6000006 0.1517942 0.3826583 0.6000007 0.1281774 0.3826583 0.6000006 0.1045608 0.3826583 0.6000004 0.2934947 0.9919508 0.6 0.2934947 0.8904028 0.5999999 0.2934947 0.7888533 0.5999998 0.2934947 0.6873053 0.5999997 0.2934947 0.5857558 0.5999996 0.2934947 0.4842078 0.5999995 0.2934947 0.3826583 0.5999994 0.6319849 0.01458142 2.705007 0.6319849 0.0462851 2.705007 0.5848823 0.01458148 2.705008 0.5848823 0.0462851 2.705008 0.5377798 0.01458148 2.705009 0.5377798 0.0462851 2.705009 0.4906773 0.01458142 2.70501 0.4906773 0.04628516 2.70501 0.4435748 0.01458142 2.70501 0.4435748 0.0462851 2.70501 0.3964723 0.01458142 2.705011 0.3964723 0.0462851 2.705011 0.3493698 0.01458142 2.705013 0.3493698 0.04628507 2.705013 0.3965736 0.01458142 2.705013 0.3965736 0.0462851 2.705014 0.4436759 0.01458142 2.705014 0.4436759 0.0462851 2.705014 0.4907785 0.01458132 2.705014 0.4907785 0.0462851 2.705014 0.5378808 0.01458142 2.705014 0.5378808 0.0462851 2.705014 0.5849832 0.01458142 2.705014 0.5849832 0.0462851 2.705014 0.349471 0.01458142 2.705013 0.349471 0.04628507 2.705013 0.9146034 0.01458132 2.705009 0.9146034 0.0462851 2.705009</float_array>
          <technique_common>
            <accessor count="239" source="#geom-suctionmount-map1-array" stride="3">
              <param name="S" type="float" />
              <param name="T" type="float" />
              <param name="P" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="geom-suctionmount-vertices">
          <input semantic="POSITION" source="#geom-suctionmount-positions" />
        </vertices>
        <triangles count="332" material="suctionmount">
          <input offset="0" semantic="VERTEX" source="#geom-suctionmount-vertices" />
          <input offset="1" semantic="NORMAL" source="#geom-suctionmount-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#geom-suctionmount-map1" />
          <p>1 0 24 25 1 27 24 2 29 24 2 29 0 3 19 1 0 24 2 4 31 26 5 33 25 1 27 25 1 27 1 0 24 2 4 31 3 6 35 27 7 37 26 5 33 26 5 33 2 4 31 3 6 35 4 8 39 28 9 41 27 7 37 27 7 37 3 6 35 4 8 39 5 10 43 29 11 45 28 9 41 28 9 41 4 8 39 5 10 43 6 12 237 30 13 238 29 11 45 29 11 45 5 10 43 6 12 237 7 14 51 31 15 53 30 13 49 30 13 49 6 12 47 7 14 51 8 16 55 32 17 57 31 15 53 31 15 53 7 14 51 8 16 55 9 18 59 33 19 61 32 17 57 32 17 57 8 16 55 9 18 59 10 20 63 34 21 65 33 19 61 33 19 61 9 18 59 10 20 63 11 22 67 35 23 69 34 21 65 34 21 65 10 20 63 11 22 67 12 24 211 36 25 212 35 23 69 35 23 69 11 22 67 12 24 211 13 26 213 37 27 214 36 25 212 36 25 212 12 24 211 13 26 213 14 28 215 38 29 216 37 27 214 37 27 214 13 26 213 14 28 215 15 30 217 39 31 218 38 29 216 38 29 216 14 28 215 15 30 217 16 32 219 40 33 220 39 31 218 39 31 218 15 30 217 16 32 219 17 34 221 41 35 222 40 33 220 40 33 220 16 32 219 17 34 221 18 36 223 42 37 224 41 35 222 41 35 222 17 34 221 18 36 223 19 38 225 43 39 226 42 37 236 42 37 236 18 36 235 19 38 225 20 40 227 44 41 228 43 39 226 43 39 226 19 38 225 20 40 227 21 42 229 45 43 230 44 41 228 44 41 228 20 40 227 21 42 229 22 44 231 46 45 232 45 43 230 45 43 230 21 42 229 22 44 231 23 46 233 47 47 234 46 45 232 46 45 232 22 44 231 23 46 233 0 3 19 24 2 29 47 47 234 47 47 234 23 46 233 0 3 19 61 48 72 62 49 73 63 50 74 63 50 74 64 51 75 65 52 76 65 52 76 66 53 77 67 54 78 63 50 74 65 52 76 67 54 78 67 54 78 68 55 79 69 56 80 69 56 80 70 57 81 71 58 82 67 54 78 69 56 80 71 58 82 63 50 74 67 54 78 71 58 82 61 48 72 63 50 74 71 58 82 60 59 71 61 48 72 71 58 82 25 1 25 73 60 83 72 61 84 72 61 84 24 2 26 25 1 25 26 5 28 74 62 85 73 60 83 73 60 83 25 1 25 26 5 28 27 7 30 75 63 86 74 62 85 74 62 85 26 5 28 27 7 30 28 9 32 76 64 87 75 63 86 75 63 86 27 7 30 28 9 32 29 11 34 77 65 88 76 64 87 76 64 87 28 9 32 29 11 34 30 13 36 78 66 89 77 65 88 77 65 88 29 11 34 30 13 36 31 15 38 79 67 90 78 66 89 78 66 89 30 13 36 31 15 38 32 17 40 80 68 91 79 67 90 79 67 90 31 15 38 32 17 40 33 19 42 81 69 92 80 68 91 80 68 91 32 17 40 33 19 42 34 21 44 82 70 93 81 69 92 81 69 92 33 19 42 34 21 44 35 23 46 83 71 94 82 70 93 82 70 93 34 21 44 35 23 46 36 25 48 84 72 95 83 71 94 83 71 94 35 23 46 36 25 48 37 27 50 85 73 96 84 72 95 84 72 95 36 25 48 37 27 50 38 29 52 86 74 97 85 73 96 85 73 96 37 27 50 38 29 52 39 31 54 87 75 98 86 74 97 86 74 97 38 29 52 39 31 54 40 33 56 88 76 99 87 75 98 87 75 98 39 31 54 40 33 56 41 35 58 89 77 100 88 76 99 88 76 99 40 33 56 41 35 58 42 37 60 90 78 101 89 77 100 89 77 100 41 35 58 42 37 60 43 39 62 91 79 102 90 78 101 90 78 101 42 37 60 43 39 62 44 41 64 92 80 103 91 79 102 91 79 102 43 39 62 44 41 64 45 43 66 93 81 104 92 80 103 92 80 103 44 41 64 45 43 66 46 45 68 94 82 105 93 81 104 93 81 104 45 43 66 46 45 68 47 47 70 95 83 106 94 82 105 94 82 105 46 45 68 47 47 70 24 2 26 72 61 84 95 83 106 95 83 106 47 47 70 24 2 26 49 84 108 61 48 72 60 59 71 60 59 71 48 85 107 49 84 108 50 86 109 62 49 73 61 48 72 61 48 72 49 84 108 50 86 109 51 87 110 63 50 74 62 49 73 62 49 73 50 86 109 51 87 110 52 88 111 64 51 75 63 50 74 63 50 74 51 87 110 52 88 111 53 89 112 65 52 76 64 51 75 64 51 75 52 88 111 53 89 112 54 90 113 66 53 77 65 52 76 65 52 76 53 89 112 54 90 113 55 91 114 67 54 78 66 53 77 66 53 77 54 90 113 55 91 114 56 92 115 68 55 79 67 54 78 67 54 78 55 91 114 56 92 115 57 93 116 69 56 80 68 55 79 68 55 79 56 92 115 57 93 116 58 94 117 70 57 81 69 56 80 69 56 80 57 93 116 58 94 117 59 95 118 71 58 82 70 57 81 70 57 81 58 94 117 59 95 118 48 85 107 60 59 71 71 58 82 71 58 82 59 95 118 48 85 107 73 60 83 48 85 107 72 61 84 75 63 86 49 84 108 74 62 85 77 65 88 50 86 109 76 64 87 79 67 90 51 87 110 78 66 89 81 69 92 52 88 111 80 68 91 83 71 94 53 89 112 82 70 93 85 73 96 54 90 113 84 72 95 87 75 98 55 91 114 86 74 97 89 77 100 56 92 115 88 76 99 91 79 102 57 93 116 90 78 101 93 81 104 58 94 117 92 80 103 95 83 106 59 95 118 94 82 105 23 96 1 22 97 2 21 98 3 21 98 3 20 99 4 19 100 5 19 100 5 18 101 6 17 102 7 21 98 3 19 100 5 17 102 7 17 102 7 16 103 8 15 104 9 15 104 9 14 105 10 13 106 11 17 102 7 15 104 9 13 106 11 13 106 11 12 107 12 11 108 13 11 108 13 10 109 14 9 110 15 13 106 11 11 108 13 9 110 15 17 102 7 13 106 11 9 110 15 9 110 15 8 111 16 7 112 17 7 112 17 6 113 18 5 114 20 9 110 15 7 112 17 5 114 20 5 114 20 4 115 21 3 116 22 3 116 22 2 117 23 1 118 119 5 114 20 3 116 22 1 118 119 9 110 15 5 114 20 1 118 119 17 102 7 9 110 15 1 118 119 21 98 3 17 102 7 1 118 119 23 96 1 21 98 3 1 118 119 0 119 0 23 96 1 1 118 119 96 120 120 97 121 121 109 122 122 109 122 122 108 123 123 96 120 120 97 121 121 98 124 124 110 125 125 110 125 125 109 122 122 97 121 121 98 124 124 99 126 126 111 127 127 111 127 127 110 125 125 98 124 124 99 126 204 100 128 128 112 129 129 112 129 129 111 127 205 99 126 204 100 128 128 101 130 130 113 131 131 113 131 131 112 129 129 100 128 128 101 130 130 102 132 132 114 133 133 114 133 133 113 131 131 101 130 130 102 132 132 103 134 134 115 135 135 115 135 135 114 133 133 102 132 132 103 134 134 104 136 136 116 137 137 116 137 137 115 135 135 103 134 134 104 136 136 105 138 138 117 139 139 117 139 139 116 137 137 104 136 136 105 138 138 106 140 140 118 141 141 118 141 141 117 139 139 105 138 138 106 140 140 107 142 142 119 143 143 119 143 143 118 141 141 106 140 140 107 142 142 96 120 120 108 123 123 108 123 123 119 143 143 107 142 142 108 123 123 109 122 122 121 144 144 121 144 144 120 145 145 108 123 123 109 122 122 110 125 125 122 146 146 122 146 146 121 144 144 109 122 122 110 125 125 111 127 127 123 147 147 123 147 147 122 146 146 110 125 125 111 127 205 112 129 129 124 148 148 124 148 148 123 147 206 111 127 205 112 129 129 113 131 131 125 149 149 125 149 149 124 148 148 112 129 129 113 131 131 114 133 133 126 150 150 126 150 150 125 149 149 113 131 131 114 133 133 115 135 135 127 151 151 127 151 151 126 150 150 114 133 133 115 135 135 116 137 137 128 152 152 128 152 152 127 151 151 115 135 135 116 137 137 117 139 139 129 153 153 129 153 153 128 152 152 116 137 137 117 139 139 118 141 141 130 154 154 130 154 154 129 153 153 117 139 139 118 141 141 119 143 143 131 155 155 131 155 155 130 154 154 118 141 141 119 143 143 108 123 123 120 145 145 120 145 145 131 155 155 119 143 143 120 145 145 121 144 144 133 156 156 133 156 156 132 157 157 120 145 145 121 144 144 122 146 146 134 158 158 134 158 158 133 156 156 121 144 144 122 146 146 123 147 147 135 159 159 135 159 159 134 158 158 122 146 146 123 147 206 124 148 148 136 160 160 136 160 160 135 159 207 123 147 206 124 148 148 125 149 149 137 161 161 137 161 161 136 160 160 124 148 148 125 149 149 126 150 150 138 162 162 138 162 162 137 161 161 125 149 149 126 150 150 127 151 151 139 163 163 139 163 163 138 162 162 126 150 150 127 151 151 128 152 152 140 164 164 140 164 164 139 163 163 127 151 151 128 152 152 129 153 153 141 165 165 141 165 165 140 164 164 128 152 152 129 153 153 130 154 154 142 166 166 142 166 166 141 165 165 129 153 153 130 154 154 131 155 155 143 167 167 143 167 167 142 166 166 130 154 154 131 155 155 120 145 145 132 157 157 132 157 157 143 167 167 131 155 155 132 157 157 133 156 156 145 168 168 145 168 168 144 169 169 132 157 157 133 156 156 134 158 158 146 170 170 146 170 170 145 168 168 133 156 156 134 158 158 135 159 159 147 171 171 147 171 171 146 170 170 134 158 158 135 159 207 136 160 160 148 172 172 148 172 172 147 171 208 135 159 207 136 160 160 137 161 161 149 173 173 149 173 173 148 172 172 136 160 160 137 161 161 138 162 162 150 174 174 150 174 174 149 173 173 137 161 161 138 162 162 139 163 163 151 175 175 151 175 175 150 174 174 138 162 162 139 163 163 140 164 164 152 176 176 152 176 176 151 175 175 139 163 163 140 164 164 141 165 165 153 177 177 153 177 177 152 176 176 140 164 164 141 165 165 142 166 166 154 178 178 154 178 178 153 177 177 141 165 165 142 166 166 143 167 167 155 179 179 155 179 179 154 178 178 142 166 166 143 167 167 132 157 157 144 169 169 144 169 169 155 179 179 143 167 167 144 169 169 145 168 168 157 180 180 157 180 180 156 181 181 144 169 169 145 168 168 146 170 170 158 182 182 158 182 182 157 180 180 145 168 168 146 170 170 147 171 171 159 183 183 159 183 183 158 182 182 146 170 170 147 171 208 148 172 172 160 184 184 160 184 184 159 183 209 147 171 208 148 172 172 149 173 173 161 185 185 161 185 185 160 184 184 148 172 172 149 173 173 150 174 174 162 186 186 162 186 186 161 185 185 149 173 173 150 174 174 151 175 175 163 187 187 163 187 187 162 186 186 150 174 174 151 175 175 152 176 176 164 188 188 164 188 188 163 187 187 151 175 175 152 176 176 153 177 177 165 189 189 165 189 189 164 188 188 152 176 176 153 177 177 154 178 178 166 190 190 166 190 190 165 189 189 153 177 177 154 178 178 155 179 179 167 191 191 167 191 191 166 190 190 154 178 178 155 179 179 144 169 169 156 181 181 156 181 181 167 191 191 155 179 179 156 181 181 157 180 180 169 192 192 169 192 192 168 193 193 156 181 181 157 180 180 158 182 182 170 194 194 170 194 194 169 192 192 157 180 180 158 182 182 159 183 183 171 195 195 171 195 195 170 194 194 158 182 182 159 183 209 160 184 184 172 196 196 172 196 196 171 195 210 159 183 209 160 184 184 161 185 185 173 197 197 173 197 197 172 196 196 160 184 184 161 185 185 162 186 186 174 198 198 174 198 198 173 197 197 161 185 185 162 186 186 163 187 187 175 199 199 175 199 199 174 198 198 162 186 186 163 187 187 164 188 188 176 200 200 176 200 200 175 199 199 163 187 187 164 188 188 165 189 189 177 201 201 177 201 201 176 200 200 164 188 188 165 189 189 166 190 190 178 202 202 178 202 202 177 201 201 165 189 189 166 190 190 167 191 191 179 203 203 179 203 203 178 202 202 166 190 190 167 191 191 156 181 181 168 193 193 168 193 193 179 203 203 167 191 191 73 60 83 74 62 85 49 84 108 73 60 83 49 84 108 48 85 107 75 63 86 76 64 87 50 86 109 75 63 86 50 86 109 49 84 108 77 65 88 78 66 89 51 87 110 77 65 88 51 87 110 50 86 109 79 67 90 80 68 91 52 88 111 79 67 90 52 88 111 51 87 110 81 69 92 82 70 93 53 89 112 81 69 92 53 89 112 52 88 111 83 71 94 84 72 95 54 90 113 83 71 94 54 90 113 53 89 112 85 73 96 86 74 97 55 91 114 85 73 96 55 91 114 54 90 113 87 75 98 88 76 99 56 92 115 87 75 98 56 92 115 55 91 114 89 77 100 90 78 101 57 93 116 89 77 100 57 93 116 56 92 115 91 79 102 92 80 103 58 94 117 91 79 102 58 94 117 57 93 116 93 81 104 94 82 105 59 95 118 93 81 104 59 95 118 58 94 117 95 83 106 72 61 84 48 85 107 95 83 106 48 85 107 59 95 118</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="geom-gps" name="gps">
      <mesh>
        <source id="geom-gps-positions">
          <float_array count="492" id="geom-gps-positions-array">-5.234676 0.37643 -3.511534 -5.234676 0.2599254 -3.511534 5.234694 0.376429 3.511146 5.234694 0.2599244 3.511146 -5.234694 0.3764304 3.511146 -5.234694 0.2599257 3.511146 5.234676 0.3764287 -3.511534 5.234676 0.259924 -3.511534 -4.986298 0.3764305 3.768779 -4.986298 0.2599258 3.768779 4.986279 0.3764285 -3.76075 4.986279 0.2599238 -3.76075 4.986298 0.376429 3.768779 4.986298 0.2599243 3.768779 -4.986279 0.37643 -3.76075 -4.986279 0.2599254 -3.76075 -5.198568 0.3764305 3.636553 -5.198568 0.2599258 3.636553 5.198568 0.376429 3.636553 5.104618 0.2599243 3.732374 5.199545 0.3764285 -3.628868 5.199545 0.2599239 -3.628868 -5.199545 0.37643 -3.628868 -5.104599 0.2599254 -3.724339 -5.487586 0.3651474 -3.733176 5.487586 0.3651463 3.732896 -5.487586 0.3651477 3.732896 5.487586 0.3651459 -3.733176 -5.241875 0.3651478 3.990419 5.241875 0.3651457 -3.982286 5.241875 0.3651462 3.990419 -5.241875 0.3651474 -3.982286 -5.445095 0.3651478 3.857067 5.356819 0.3651462 3.947689 5.446027 0.3651458 -3.849257 -5.35682 0.3651474 -3.939553 -5.811636 0.2491486 -3.943039 5.811636 0.2491475 3.942476 -5.811636 0.2491491 3.942476 5.811636 0.2491471 -3.943039 -5.551416 0.2491492 4.214467 5.551416 0.2491469 -4.206144 5.551416 0.2491474 4.214467 -5.551416 0.2491486 -4.206144 -5.765353 0.2491491 4.077361 5.677001 0.2491474 4.167907 5.766286 0.2491469 -4.069366 -5.677001 0.2491486 -4.159585 -6.255535 -0.1999234 -4.184371 6.255535 -0.1999247 4.183484 -6.255535 -0.199923 4.183484 6.255535 -0.199925 -4.184371 -5.97544 -0.1999228 4.472111 5.97544 -0.1999252 -4.463571 5.97544 -0.1999246 4.472111 -5.97544 -0.1999234 -4.463571 -6.204273 -0.1999228 4.330767 6.115518 -0.1999246 4.420909 6.205214 -0.1999253 -4.322567 -6.115518 -0.1999234 -4.412372 6.255535 -0.7565968 4.183484 6.255535 -0.7565972 -4.184371 -5.97544 -0.756595 4.472111 5.97544 -0.7565974 -4.463571 5.97544 -0.7565967 4.472111 6.115518 -0.7565968 4.420909 6.205214 -0.7565974 -4.322567 -6.115518 -0.7565956 -4.412372 -4.79375 -5.28424e-5 -2.688947 -4.79375 -5.25413e-5 3.232751 4.70375 -5.39708e-5 3.322751 -5 0.2552309 3.435528 5 0.255229 -2.891724 4.91 0.2552294 3.525528 -4.91 0.2552305 -2.981724 4.70375 -5.42862e-5 -2.778947 -6.255535 -0.7565955 -4.184371 -6.255535 -0.7565951 4.183484 -5.97544 -0.7565956 -4.463571 -6.204273 -0.756595 4.330767 -6.236375 -1.017427 -4.171547 6.236375 -1.017428 4.170677 -6.236375 -1.017426 4.170677 6.236375 -1.017428 -4.171547 -5.957137 -1.017426 4.45842 5.957137 -1.017429 -4.449891 5.957137 -1.017428 4.45842 -5.957137 -1.017427 -4.449891 -6.18534 -1.017426 4.317307 6.096584 -1.017428 4.407449 6.186281 -1.017429 -4.309118 -6.096584 -1.017427 -4.398923 -6.136788 -1.270134 -4.104896 6.136788 -1.270135 4.104115 -6.136788 -1.270134 4.104115 6.136788 -1.270136 -4.104896 -5.86201 -1.270133 4.387263 5.86201 -1.270136 -4.378795 5.86201 -1.270135 4.387263 -5.86201 -1.270134 -4.378795 -6.086936 -1.270133 4.247348 5.99818 -1.270135 4.33749 6.087877 -1.270136 -4.239219 -5.99818 -1.270134 -4.329024 -5.104618 0.3764305 3.732374 -5.104618 0.2599258 3.732374 5.104618 0.376429 3.732374 5.198568 0.2599243 3.636553 5.104599 0.3764285 -3.724339 5.104599 0.2599238 -3.724339 -5.104599 0.37643 -3.724339 -5.199545 0.2599254 -3.628868 -5.356819 0.3651479 3.947689 5.445095 0.3651462 3.857067 5.35682 0.3651457 -3.939553 -5.446027 0.3651474 -3.849257 -5.677001 0.2491492 4.167907 5.765353 0.2491475 4.077361 5.677001 0.2491469 -4.159585 -5.766286 0.2491486 -4.069366 -6.115518 -0.1999227 4.420909 6.204273 -0.1999247 4.330767 6.115518 -0.1999253 -4.412372 -6.205214 -0.1999233 -4.322567 6.204273 -0.7565969 4.330767 6.115518 -0.7565975 -4.412372 -6.205214 -0.7565955 -4.322567 -6.115518 -0.7565949 4.420909 -6.096584 -1.017426 4.407449 6.18534 -1.017428 4.317307 6.096584 -1.017429 -4.398923 -6.186281 -1.017427 -4.309118 -5.99818 -1.270133 4.33749 6.086936 -1.270135 4.247348 5.99818 -1.270136 -4.329024 -6.087877 -1.270134 -4.239219 -6.08393 0.0456071 -4.09631 6.08393 0.04560585 4.09554 -6.08393 0.04560748 4.09554 6.08393 0.04560553 -4.096309 -5.811518 0.04560764 4.378097 5.811518 0.04560529 -4.369636 5.811518 0.04560588 4.378097 -5.811518 0.04560705 -4.369636 -5.946002 0.04560766 4.328621 6.034568 0.04560584 4.238289 5.946002 0.04560521 -4.320162 -6.035505 0.04560709 -4.230162 -6.034568 0.04560764 4.238288 5.946002 0.04560584 4.328621 6.035505 0.04560529 -4.230162 -5.946002 0.0456071 -4.320162 -4.70375 -5.28606e-5 -2.778947 -4.70375 -5.25503e-5 3.322751 4.79375 -5.3989e-5 3.232751 -4.91 0.2552309 3.525528 4.91 0.255229 -2.981724 5 0.2552294 3.435528 -5 0.2552305 -2.891724 4.79375 -5.42952e-5 -2.688947 -4.81 -5.28424e-5 -2.800004 -4.81 -5.25413e-5 3.340001 4.81 -5.3989e-5 3.340001 4.81 -5.42952e-5 -2.800004</float_array>
          <technique_common>
            <accessor count="164" source="#geom-gps-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-gps-normals">
          <float_array count="636" id="geom-gps-normals-array">0.9901825 0 -0.1397805 0.9901826 0 -0.1397804 0.9894391 0 0.1449489 0.9894391 0 0.1449489 -0.9894392 0 0.1449488 -0.9894391 0 0.1449489 -0.9901825 0 -0.1397804 -0.9901825 0 -0.1397804 -0.1486912 0 -0.9888837 -0.148691 0 -0.9888837 0.1486912 0 -0.9888837 0.148691 0 -0.9888837 0.1487118 0 0.9888806 0.1487114 0 0.9888807 -0.1487118 0 0.9888805 -0.1487114 0 0.9888806 0.8638108 0 -0.5038162 0.863811 0 -0.5038161 -0.863811 0 -0.5038161 -0.863811 0 -0.5038161 0.5200185 0 -0.854155 0.5200185 0 -0.8541549 -0.5200186 0 -0.854155 -0.5200185 0 -0.8541549 -0.8593612 0 0.5113691 -0.8593611 0 0.5113693 0.8593611 0 0.5113693 0.8593611 0 0.5113693 -0.5170028 0 0.8559837 -0.5170029 0 0.8559837 0.5170028 0 0.8559837 0.5170028 0 0.8559837 -0.04160263 0.9991285 0.003390802 -0.0415615 0.999129 -0.003710439 -0.1924442 0.9809771 -0.02548003 -0.1927524 0.9809456 0.02434098 0.04148874 0.9991328 -0.003519828 0.04167347 0.9991249 0.003576543 0.1925023 0.9809934 0.02439087 0.1926937 0.9809293 -0.02543472 0.003403033 0.9988926 0.04692516 -0.003595396 0.9988893 0.04698127 -0.02419323 0.9664643 0.2556588 0.02419431 0.9663738 0.2560008 -0.00340348 0.9988917 -0.04694606 0.003596383 0.9988884 -0.04700199 0.0242001 0.966419 -0.2558293 -0.024201 0.9663283 -0.2561718 -0.03054012 0.9993972 0.01651082 -0.1532124 0.9838188 0.09288058 0.03102945 0.9993836 0.0164224 0.1532198 0.9838257 0.0927936 -0.01701826 0.9993284 0.03245247 -0.09745249 0.9805553 0.170336 0.01703849 0.9993494 0.03178797 0.09758364 0.980527 0.1704231 0.03028465 0.9994 -0.01680572 0.1517621 0.9838831 -0.09456342 -0.03077668 0.9993865 -0.01672015 -0.1517682 0.9838909 -0.09447267 0.0169433 0.9993263 -0.03255462 0.0969701 0.9805502 -0.1706404 -0.01696317 0.9993475 -0.03188809 -0.09710148 0.9805218 -0.1707286 -0.4519278 0.8891144 -0.07236426 -0.4528269 0.8888846 0.06951242 0.4528038 0.8888959 0.0695185 0.4519503 0.8891035 -0.07235964 -0.08088501 0.8132876 0.5762125 0.08088623 0.8133128 0.5761767 0.08091901 0.8131157 -0.5764503 -0.08091944 0.8131418 -0.5764136 -0.3703279 0.9000446 0.2297324 0.3703074 0.9000563 0.2297195 -0.2456526 0.8798828 0.4067691 0.2456375 0.8798971 0.4067474 0.367586 0.9001185 -0.2338101 -0.3675653 0.9001305 -0.2337964 0.244642 0.8796647 -0.4078484 -0.2446256 0.8796797 -0.4078259 -0.6918694 0.7122053 -0.1186599 -0.6931164 0.7117565 0.1139835 0.6931164 0.7117566 0.1139836 0.6918693 0.7122057 -0.118659 -0.1359916 0.5787815 0.8040636 0.1359916 0.5787814 0.8040638 0.1360235 0.5785573 -0.8042195 -0.1360233 0.5785573 -0.8042196 -0.5826469 0.7269956 0.3633178 0.5826466 0.7269955 0.3633189 -0.3867536 0.6859868 0.6163148 0.3867531 0.6859866 0.6163152 0.5787092 0.7269405 -0.3696663 -0.5787089 0.7269406 -0.3696666 0.3850228 0.685452 -0.6179911 -0.3850229 0.6854523 -0.6179908 -0.9370489 0.3073133 0.1658246 -0.9357882 0.3074183 -0.1726112 -0.9836617 -0.04023174 -0.1754736 -0.9849023 -0.04010634 0.1684013 0.935788 0.3074187 -0.1726113 0.937049 0.3073132 0.1658247 0.9849022 -0.04010668 0.1684015 0.9836618 -0.04023171 -0.1754736 0.1917076 0.2329478 0.9534062 -0.1917074 0.2329477 0.9534063 -0.1774477 -0.03199187 0.9836102 0.1774478 -0.03199196 0.98361 -0.8072945 0.3136402 0.4999054 -0.8481976 -0.04404529 0.5278455 0.8072945 0.3136397 0.4999056 0.8481977 -0.04404566 0.5278453 -0.530902 0.2905018 0.7960852 -0.5425724 -0.04107082 0.8390045 0.530902 0.2905013 0.7960854 0.5425723 -0.04107093 0.8390045 0.8020511 0.3136775 -0.5082523 0.8425353 -0.04406326 -0.5368358 -0.8020512 0.3136777 -0.508252 -0.8425351 -0.04406346 -0.536836 0.1917105 0.2328348 -0.9534333 0.5280271 0.290198 -0.7981056 0.5395553 -0.04099801 -0.8409514 0.1774392 -0.03197026 -0.9836124 -0.5280269 0.2901982 -0.7981056 -0.1917103 0.2328345 -0.9534334 -0.1774393 -0.03197042 -0.9836123 -0.5395554 -0.04099836 -0.8409513 0.6735151 0.7093223 -0.2079409 0.673515 0.7093223 0.2079409 0.2642617 0.9565812 0.1229561 0.2646285 0.9561548 -0.1254581 -0.6735149 0.7093224 0.2079408 -0.673515 0.7093224 -0.207941 -0.2646289 0.9561548 -0.1254581 -0.2642508 0.956569 0.123074 -0.2064941 0.7054176 -0.6780459 0.2064943 0.7054179 -0.6780456 0.1265628 0.9559358 -0.2648933 -0.1265628 0.9559358 -0.2648935 0.2064943 0.7054176 0.6780458 -0.2064942 0.705418 0.6780455 -0.1252975 0.9578284 0.2585833 0.1250578 0.9578828 0.2584975 -0.955002 -0.2397195 -0.1746585 -0.9563759 -0.2391827 0.1677399 0.956376 -0.2391823 0.1677401 0.9550021 -0.2397194 -0.1746584 -0.1817773 -0.1970344 0.9633974 0.1817774 -0.1970342 0.9633974 -0.8199741 -0.257601 0.5111597 0.8199745 -0.2576007 0.5111592 -0.5306292 -0.2432901 0.8119376 0.5306292 -0.24329 0.8119377 0.8145329 -0.257757 -0.5197091 -0.8145328 -0.2577566 -0.5197094 0.5277425 -0.2429841 -0.8139082 0.1817762 -0.1969147 -0.9634221 -0.1817764 -0.1969146 -0.963422 -0.5277425 -0.2429835 -0.8139084 -1.46336e-7 -1 1.02514e-6 -1.46861e-7 -1 1.09582e-6 -1.42674e-7 -0.9999999 -7.27505e-8 -1.58994e-7 -1 1.15597e-7 -0.9078233 -0.3949414 -0.1409892 -0.9091761 -0.3938794 0.1351229 0.9091761 -0.3938793 0.1351229 0.9078234 -0.3949414 -0.1409891 -0.1330769 -0.3147229 0.9398085 0.1330768 -0.3147229 0.9398085 0.133074 -0.314518 -0.9398775 -0.1330742 -0.3145179 -0.9398775 -0.768499 -0.4303408 0.4735149 0.768499 -0.4303407 0.4735146 -0.4764528 -0.4019946 0.7819162 0.4764529 -0.4019946 0.7819161 0.7629429 -0.4304901 -0.4822824 -0.7629428 -0.4304906 -0.4822823 0.4739468 -0.4013459 -0.7837703 -0.4739466 -0.4013461 -0.7837704 -1.35977e-7 -1 3.54263e-9 -1.40734e-7 -0.9999999 4.09485e-7 -1.35978e-7 -1 2.84596e-9 -1.40696e-7 -1 4.32251e-7 -1.48963e-7 -1 1.32855e-6 -1.46883e-7 -1 1.05493e-6 -1.42755e-7 -1 -7.0895e-8 -1.58994e-7 -0.9999999 -1.92701e-7 -8.67219e-4 0.9999821 0.00594154 8.67496e-4 0.999982 0.005941513 0.01229214 0.9999201 0.002919924 0.019049 0.9998142 -0.002952415 -0.01904988 0.9998142 -0.002952038 -0.01074333 0.9999385 0.002708979 0.003002111 0.999827 -0.01835583 -0.003001862 0.9998271 -0.01835603 0.01440027 0.999855 -0.009081921 -0.01440083 0.999855 -0.009081543 -0.0062896 0.9999703 0.004440506 0.006316677 0.9999692 0.004656374 0.003066007 0.99998 0.005518482 -0.003066138 0.99998 0.005518408 -0.009116612 0.9998587 -0.01411688 0.009116507 0.9998587 -0.01411698 -1.58999e-7 -1 -4.3366e-7 -1.62687e-7 -1 4.4508e-7 -1.58969e-7 -1 1.95424e-6 -1.42351e-7 -1 -2.0057e-6 1.50683e-7 1 -4.93407e-8 1.50494e-7 1 -4.90439e-8 1.50828e-7 1 -4.95678e-8 1.51017e-7 1 -4.98646e-8</float_array>
          <technique_common>
            <accessor count="212" source="#geom-gps-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-gps-map1">
          <float_array count="702" id="geom-gps-map1-array">0.728707 0.9157627 0 0.7120892 0.9049294 0 0.7120895 0.5832168 0 0.7287279 0.5712396 0 0.1312159 0.5712398 0 0.1464602 0.5832179 0 0.1464605 0.9049299 0 0.1312368 0.9157626 0 0.1466798 0.9270167 0 0.1582617 0.915154 0 0.7002885 0.915154 0 0.7132661 0.9270173 0 0.7132744 0.5604025 0 0.7003012 0.5734146 0 0.1582489 0.5734149 0 0.1466715 0.5604031 0 0.7262863 0.920926 0 0.7098201 0.9096426 0 0.1487296 0.9096429 0 0.1336578 0.9209256 0 0.7054613 0.9133495 0 0.7201333 0.9252911 0 0.1398115 0.9252905 0 0.1530887 0.9133499 0 0.1335649 0.5664754 0 0.1486088 0.57888 0 0.7099412 0.5788789 0 0.7263791 0.5664752 0 0.1530402 0.5751952 0 0.139787 0.5621321 0 0.7201579 0.5621318 0 0.7055101 0.5751946 0 0.7422937 0.5601791 0 0.7422786 0.9268228 0 0.1176656 0.9268228 0 0.1176502 0.5601792 0 0.7273607 0.9379709 0 0.1325848 0.9379706 0 0.13258 0.5494456 0 0.7273655 0.5494452 0 0.739248 0.9321028 0 0.1206968 0.9321026 0 0.7334864 0.9362578 0 0.1264588 0.9362575 0 0.1206194 0.5552933 0 0.7393253 0.5552931 0 0.126444 0.5511564 0 0.7335011 0.5511562 0 0.7602186 0.5498847 0 0.7602096 0.9370993 0 0.09973513 0.9370993 0 0.09972592 0.5498847 0 0.7449648 0.9488066 0 0.1149819 0.9488064 0 0.114976 0.5386084 0 0.7449709 0.5386083 0 0.7570869 0.9427595 0 0.1028582 0.9427595 0 0.7515464 0.9467772 0 0.1083992 0.9467771 0 0.10278 0.5446404 0 0.7571647 0.5446404 0 0.1083813 0.5406356 0 0.7515643 0.5406356 0 0.7770067 0.5420417 0 0.7770066 0.9449272 0 0.08293842 0.9449272 0 0.08293834 0.5420416 0 0.7600892 0.9585698 0 0.0998589 0.9585695 0 0.0998509 0.5288382 0 0.760097 0.5288379 0 0.7734821 0.9514695 0 0.08646372 0.9514695 0 0.767664 0.9559112 0 0.09228291 0.9559112 0 0.08637838 0.5359271 0 0.7735669 0.5359274 0 0.09225783 0.5314934 0 0.7676886 0.5314936 0 0.7908733 0.9501335 0 0.7908552 0.5368205 0 0.8199144 0.5316901 0 0.8199537 0.9552372 0 0.06909057 0.5368202 0 0.06907246 0.9501334 0 0.03999272 0.9552374 0 0.04003183 0.5316893 0 0.08992178 0.9682743 0 0.7700284 0.9682741 0 0.775845 0.9934967 0 0.08410554 0.9934967 0 0.7862735 0.958584 0 0.8108894 0.9729974 0 0.07367305 0.9585842 0 0.0490572 0.972998 0 0.7792013 0.9645406 0 0.7956761 0.9859873 0 0.08074708 0.9645413 0 0.06427231 0.9859878 0 0.07358087 0.528796 0 0.04898687 0.5143375 0 0.9624535 0.6051965 7.523133 0.9624533 0.9677653 7.528377 0.9416844 0.9677653 7.505321 0.9416847 0.605198 7.500087 0.9001229 0.6020945 7.528377 0.9001231 0.9647623 7.523133 0.8793542 0.9647623 7.500087 0.879354 0.6020945 7.505321 0.9001231 0.981084 7.461063 0.9624535 0.5888799 7.461063 0.9416847 0.5888799 7.438206 0.8793542 0.981084 7.438206 0.9624535 0.5990587 7.563833 0.9416847 0.5990673 7.540603 0.9001231 0.9709064 7.563833 0.8793542 0.970898 7.540603 0.9624535 0.5939735 7.543622 0.9416847 0.5939651 7.520392 0.9001231 0.9759915 7.543622 0.8793542 0.975998 7.520392 0.9001229 0.5962852 7.564801 0.879354 0.5962951 7.541579 0.9624534 0.973479 7.564801 0.9416845 0.9734694 7.541579 0.9001229 0.5860981 7.461066 0.9001229 0.5911897 7.543626 0.879354 0.5911819 7.520403 0.879354 0.5860981 7.438216 0.9624534 0.9785748 7.543626 0.9001231 0.01471712 7.461066 0.8793542 0.01471712 7.438216 0.9416845 0.9785804 7.520403 0.9215624 0.9677668 7.385488 0.9215626 0.605198 7.380303 0.8592322 0.9647615 7.380303 0.8592319 0.6020927 7.385488 0.9215626 0.5888819 7.31941 0.8592322 0.9810835 7.31941 0.8592319 0.5860961 7.319456 0.8592319 0.01471926 7.319456 0.9215626 0.5991126 7.41987 0.8592322 0.9708527 7.41987 0.9215626 0.5939282 7.399658 0.8592322 0.9760357 7.399658 0.8592319 0.5963387 7.42088 0.9215624 0.9734257 7.42088 0.7863654 0.5287966 0 0.8109601 0.5143386 0 0.08991386 0.5191204 0 0.08071692 0.5228441 0 0.06426746 0.5013695 0 0.08410915 0.4938884 0 0.7792314 0.5228449 0 0.7700363 0.5191208 0 0.7758421 0.4938889 0 0.7956811 0.5013707 0 0.7935951 0.4645183 0 0.775762 0.4485939 0 0.775762 0.07733536 0 0.7935951 0.06179929 0 0.05918816 0.06179929 0 0.0775142 0.07733536 0 0.0775142 0.4485939 0 0.05918816 0.4645183 0 0.06579782 0.4702469 0 0.0840687 0.4542364 0 0.7692074 0.4542364 0 0.8592319 0.5911424 7.399704 0.7869855 0.4702469 0 0.9215624 0.9786201 7.399704 0.7869855 0.0560708 0 0.7692074 0.0716933 0 0.0840687 0.0716933 0 0.06579782 0.0560708 0 0.8108313 0.4693313 0 0.8108301 0.0223493 0 0.04195337 0.0223493 0 0.04195211 0.4693313 0 0.1488204 0.08221619 0 0.0601945 0.4857293 0 0.7023176 0.08221619 0 0.7925889 0.4857293 0 0.7064561 0.08602067 0 0.7925875 0.006486982 0 0.1446819 0.08602067 0 0.06019597 0.006486982 0 0.8081782 0.4773132 0 0.04460537 0.4773132 0 0.8012784 0.483412 0 0.05150511 0.483412 0 0.04453348 0.01488087 0 0.8082498 0.01488087 0 0.05150637 0.008804649 0 0.8012769 0.008804649 0 0.7181914 0.90598 0 0.7181821 0.5821462 0 0.1403677 0.5821471 0 0.1403583 0.9059807 0 0.157066 0.9204237 0 0.7014842 0.9204237 0 0.7014786 0.568139 0 0.1570712 0.568139 0 0.7149804 0.9126379 0 0.1435696 0.9126385 0 0.7088878 0.9178295 0 0.1496624 0.9178295 0 0.1434804 0.5758457 0 0.7150696 0.5758448 0 0.1496537 0.5706908 0 0.7088962 0.5706899 0 0.9624534 0.01749936 7.461063 0.9416845 0.01749936 7.438206 0.9215626 0.01749871 7.31941 0.9624534 0.9836667 7.461066 0.7087126 0.09171128 0 0.7087126 0.4346019 0 0.1424253 0.4346019 0 0.1424253 0.09171128 0 0.7023176 0.4444884 0 0.1488204 0.4444884 0 0.1447254 0.4406693 0 0.7064127 0.4406693 0 0.6960349 0.4465973 0 0.1551032 0.4465973 0 0.1551032 0.08010721 0 0.6960349 0.08010721 0 0.9416845 0.9836667 7.438216 0.9215624 0.9836685 7.319456 1 -1.66893e-6 0 1 0.999998 0 0 0.9999981 0 0 -1.66893e-6 0</float_array>
          <technique_common>
            <accessor count="234" source="#geom-gps-map1-array" stride="3">
              <param name="S" type="float" />
              <param name="T" type="float" />
              <param name="P" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="geom-gps-vertices">
          <input semantic="POSITION" source="#geom-gps-positions" />
        </vertices>
        <triangles count="310" material="gps">
          <input offset="0" semantic="VERTEX" source="#geom-gps-vertices" />
          <input offset="1" semantic="NORMAL" source="#geom-gps-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#geom-gps-map1" />
          <p>4 0 196 5 1 1 1 2 2 1 2 2 0 3 197 4 0 196 6 4 198 7 5 5 3 6 6 3 6 6 2 7 199 6 4 198 12 8 200 13 9 9 9 10 10 9 10 10 8 11 201 12 8 200 14 12 202 15 13 13 11 14 14 11 14 14 10 15 203 14 12 202 16 16 204 17 17 17 5 1 1 5 1 1 4 0 196 16 16 204 2 7 199 3 6 6 107 18 18 107 18 18 18 19 205 2 7 199 8 11 201 9 10 10 105 20 20 105 20 20 104 21 206 8 11 201 106 22 207 19 23 23 13 9 9 13 9 9 12 8 200 106 22 207 20 24 208 21 25 25 7 5 5 7 5 5 6 4 198 20 24 208 0 3 197 1 2 2 111 26 26 111 26 26 22 27 209 0 3 197 10 15 203 11 14 14 109 28 28 109 28 28 108 29 210 10 15 203 110 30 211 23 31 31 15 13 13 15 13 13 14 12 202 110 30 211 4 32 0 0 33 3 24 34 32 24 34 32 26 35 33 4 32 0 6 36 4 2 37 7 25 38 34 25 38 34 27 39 35 6 36 4 12 40 8 8 41 11 28 42 36 28 42 36 30 43 37 12 40 8 14 44 12 10 45 15 29 46 38 29 46 38 31 47 39 14 44 12 16 48 16 4 32 0 26 35 33 26 35 33 32 49 40 16 48 16 2 37 7 18 50 19 113 51 41 113 51 41 25 38 34 2 37 7 8 41 11 104 52 21 112 53 42 112 53 42 28 42 36 8 41 11 106 54 22 12 40 8 30 43 37 30 43 37 33 55 43 106 54 22 20 56 24 6 36 4 27 39 35 27 39 35 34 57 44 20 56 24 0 33 3 22 58 27 115 59 45 115 59 45 24 34 32 0 33 3 10 45 15 108 60 29 114 61 46 114 61 46 29 46 38 10 45 15 110 62 30 14 44 12 31 47 39 31 47 39 35 63 47 110 62 30 26 35 33 24 34 32 36 64 48 36 64 48 38 65 49 26 35 33 27 39 35 25 38 34 37 66 50 37 66 50 39 67 51 27 39 35 30 43 37 28 42 36 40 68 52 40 68 52 42 69 53 30 43 37 31 47 39 29 46 38 41 70 54 41 70 54 43 71 55 31 47 39 32 49 40 26 35 33 38 65 49 38 65 49 44 72 56 32 49 40 25 38 34 113 51 41 117 73 57 117 73 57 37 66 50 25 38 34 28 42 36 112 53 42 116 74 58 116 74 58 40 68 52 28 42 36 33 55 43 30 43 37 42 69 53 42 69 53 45 75 59 33 55 43 34 57 44 27 39 35 39 67 51 39 67 51 46 76 60 34 57 44 24 34 32 115 59 45 119 77 61 119 77 61 36 64 48 24 34 32 29 46 38 114 61 46 118 78 62 118 78 62 41 70 54 29 46 38 35 63 47 31 47 39 43 71 55 43 71 55 47 79 63 35 63 47 36 64 48 136 80 64 138 81 65 138 81 65 38 65 49 36 64 48 37 66 50 137 82 66 139 83 67 139 83 67 39 67 51 37 66 50 40 68 52 140 84 68 142 85 69 142 85 69 42 69 53 40 68 52 41 70 54 141 86 70 143 87 71 143 87 71 43 71 55 41 70 54 44 72 56 38 65 49 138 81 65 138 81 65 148 88 72 44 72 56 117 73 57 145 89 73 137 82 66 137 82 66 37 66 50 117 73 57 116 74 58 144 90 74 140 84 68 140 84 68 40 68 52 116 74 58 45 75 59 42 69 53 142 85 69 142 85 69 149 91 75 45 75 59 46 76 60 39 67 51 139 83 67 139 83 67 150 92 76 46 76 60 119 77 61 147 93 77 136 80 64 136 80 64 36 64 48 119 77 61 118 78 62 146 94 78 141 86 70 141 86 70 41 70 54 118 78 62 47 79 63 43 71 55 143 87 71 143 87 71 151 95 79 47 79 63 50 96 80 48 97 81 76 98 82 76 98 82 77 99 83 50 96 80 51 100 84 49 101 85 60 102 86 60 102 86 61 103 87 51 100 84 54 104 88 52 105 89 62 106 90 62 106 90 64 107 91 54 104 88 56 108 92 50 96 80 77 99 83 77 99 83 79 109 93 56 108 92 49 101 85 121 110 94 124 111 95 124 111 95 60 102 86 49 101 85 52 105 89 120 112 96 127 113 97 127 113 97 62 106 90 52 105 89 57 114 98 54 104 88 64 107 91 64 107 91 65 115 99 57 114 98 58 116 100 51 100 84 61 103 87 61 103 87 66 117 101 58 116 100 48 97 81 123 118 148 126 119 149 126 119 149 76 98 82 48 97 81 53 120 150 122 121 151 125 122 152 125 122 152 63 123 153 53 120 150 59 124 154 55 125 155 78 126 156 78 126 156 67 127 157 59 124 154 69 128 159 68 129 160 158 130 161 158 130 161 71 131 158 69 128 159 159 132 163 154 133 164 157 134 165 157 134 165 72 135 162 159 132 163 70 136 167 153 137 168 155 138 170 155 138 170 73 139 166 70 136 167 152 140 173 75 141 174 156 142 175 156 142 175 74 143 172 152 140 173 77 99 102 76 98 103 80 144 104 80 144 104 82 145 105 77 99 102 61 103 106 60 102 107 81 146 108 81 146 108 83 147 109 61 103 106 64 107 212 62 106 111 84 148 112 84 148 112 86 149 213 64 107 212 79 109 114 77 99 102 82 145 105 82 145 105 88 150 115 79 109 114 60 102 107 124 111 116 129 151 117 129 151 117 81 146 108 60 102 107 62 106 111 127 113 118 128 152 119 128 152 119 84 148 112 62 106 111 65 115 120 64 107 110 86 149 113 86 149 113 89 153 121 65 115 120 66 117 122 61 103 106 83 147 109 83 147 109 90 154 123 66 117 122 76 98 103 126 119 124 131 155 125 131 155 125 80 144 104 76 98 103 63 123 126 125 122 127 130 156 128 130 156 128 85 157 129 63 123 126 67 127 130 78 126 215 87 158 228 87 158 228 91 159 133 67 127 130 134 160 182 102 161 184 135 162 186 135 162 186 103 163 180 134 160 182 92 164 134 94 165 135 82 145 105 82 145 105 80 144 104 92 164 134 93 166 136 95 167 137 83 147 109 83 147 109 81 146 108 93 166 136 96 168 138 98 169 214 86 149 213 86 149 213 84 148 112 96 168 138 97 170 140 99 171 141 87 158 132 87 158 132 85 157 129 97 170 140 82 145 105 94 165 135 100 172 142 100 172 142 88 150 115 82 145 105 133 173 143 93 166 136 81 146 108 81 146 108 129 151 117 133 173 143 132 174 144 96 168 138 84 148 112 84 148 112 128 152 119 132 174 144 86 149 113 98 169 139 101 175 145 101 175 145 89 153 121 86 149 113 83 147 109 95 167 137 102 176 146 102 176 146 90 154 123 83 147 109 135 177 147 92 164 134 80 144 104 80 144 104 131 155 125 135 177 147 134 178 169 97 170 140 85 157 129 85 157 129 130 156 128 134 178 169 87 158 228 99 171 229 103 179 171 103 179 171 91 159 133 87 158 228 93 180 217 94 181 218 92 182 219 92 182 219 95 183 216 93 180 217 132 184 221 100 185 222 133 186 223 133 186 223 101 187 220 132 184 221 105 20 20 17 17 17 16 16 204 16 16 204 104 21 206 105 20 20 107 18 18 19 23 23 106 22 207 106 22 207 18 19 205 107 18 18 109 28 28 21 25 25 20 24 208 20 24 208 108 29 210 109 28 28 111 26 26 23 31 31 110 30 211 110 30 211 22 27 209 111 26 26 104 52 21 16 48 16 32 49 40 32 49 40 112 53 42 104 52 21 18 50 19 106 54 22 33 55 43 33 55 43 113 51 41 18 50 19 108 60 29 20 56 24 34 57 44 34 57 44 114 61 46 108 60 29 22 58 27 110 62 30 35 63 47 35 63 47 115 59 45 22 58 27 112 53 42 32 49 40 44 72 56 44 72 56 116 74 58 112 53 42 113 51 41 33 55 43 45 75 59 45 75 59 117 73 57 113 51 41 114 61 46 34 57 44 46 76 60 46 76 60 118 78 62 114 61 46 115 59 45 35 63 47 47 79 63 47 79 63 119 77 61 115 59 45 44 72 56 148 88 72 144 90 74 144 90 74 116 74 58 44 72 56 117 73 57 45 75 59 149 91 75 149 91 75 145 89 73 117 73 57 46 76 60 150 92 76 146 94 78 146 94 78 118 78 62 46 76 60 119 77 61 47 79 63 151 95 79 151 95 79 147 93 77 119 77 61 120 112 96 56 108 92 79 109 93 79 109 93 127 113 97 120 112 96 121 110 94 57 114 98 65 115 99 65 115 99 124 111 95 121 110 94 122 121 151 58 116 100 66 117 101 66 117 101 125 122 152 122 121 151 123 118 148 59 124 154 67 127 157 67 127 157 126 119 149 123 118 148 127 113 118 79 109 114 88 150 115 88 150 115 128 152 119 127 113 118 124 111 116 65 115 120 89 153 121 89 153 121 129 151 117 124 111 116 125 122 127 66 117 122 90 154 123 90 154 123 130 156 128 125 122 127 126 119 124 67 127 130 91 159 133 91 159 133 131 155 125 126 119 124 128 152 119 88 150 115 100 172 142 100 172 142 132 174 144 128 152 119 129 151 117 89 153 121 101 175 145 101 175 145 133 173 143 129 151 117 130 156 128 90 154 123 102 176 146 102 176 146 134 178 169 130 156 128 131 155 125 91 159 133 103 179 171 103 179 171 135 177 147 131 155 125 48 97 81 50 96 80 138 81 65 138 81 65 136 80 64 48 97 81 49 101 85 51 100 84 139 83 67 139 83 67 137 82 66 49 101 85 52 105 89 54 104 88 142 85 69 142 85 69 140 84 68 52 105 89 53 120 150 55 125 155 143 87 71 143 87 71 141 86 70 53 120 150 50 96 80 56 108 92 148 88 72 148 88 72 138 81 65 50 96 80 145 89 73 121 110 94 49 101 85 49 101 85 137 82 66 145 89 73 144 90 74 120 112 96 52 105 89 52 105 89 140 84 68 144 90 74 54 104 88 57 114 98 149 91 75 149 91 75 142 85 69 54 104 88 51 100 84 58 116 100 150 92 76 150 92 76 139 83 67 51 100 84 147 93 77 123 118 148 48 97 81 48 97 81 136 80 64 147 93 77 146 94 78 122 121 151 53 120 150 53 120 150 141 86 70 146 94 78 55 125 155 59 124 154 151 95 79 151 95 79 143 87 71 55 125 155 148 88 72 56 108 92 120 112 96 120 112 96 144 90 74 148 88 72 57 114 98 121 110 94 145 89 73 145 89 73 149 91 75 57 114 98 150 92 76 58 116 100 122 121 151 122 121 151 146 94 78 150 92 76 59 124 154 123 118 148 147 93 77 147 93 77 151 95 79 59 124 154 68 129 160 152 140 173 74 143 172 74 143 172 158 130 161 68 129 160 154 133 164 70 136 167 73 139 166 73 139 166 157 134 165 154 133 164 153 137 168 69 128 159 71 131 158 71 131 158 155 138 170 153 137 168 75 141 174 159 132 163 72 135 162 72 135 162 156 142 175 75 141 174 156 142 175 11 188 187 15 189 185 15 189 185 74 143 172 156 142 175 158 130 161 1 190 177 5 191 176 5 191 176 71 131 158 158 130 161 72 135 162 157 134 165 3 192 179 3 192 179 7 193 178 72 135 162 155 138 170 9 194 183 13 195 181 13 195 181 73 139 166 155 138 170 17 196 188 71 131 158 5 191 176 3 192 179 157 134 165 107 197 189 156 142 175 72 135 162 7 193 178 7 193 178 21 198 192 156 142 175 158 130 161 74 143 172 111 199 193 111 199 193 1 190 177 158 130 161 55 125 155 53 120 150 63 123 153 63 123 153 78 126 156 55 125 155 63 123 126 85 157 129 87 158 132 87 158 132 78 126 131 63 123 126 74 143 172 15 189 185 23 200 195 74 143 172 23 200 195 111 199 193 109 201 194 11 188 187 156 142 175 21 198 192 109 201 194 156 142 175 73 139 166 19 202 191 107 197 189 107 197 189 157 134 165 73 139 166 73 139 166 13 195 181 19 202 191 105 203 190 9 194 183 155 138 170 17 196 188 105 203 190 155 138 170 155 138 170 71 131 158 17 196 188 98 204 224 96 205 225 132 184 221 132 184 221 101 187 220 98 204 224 94 181 218 93 180 217 133 186 223 133 186 223 100 185 222 94 181 218 99 206 226 97 207 227 134 160 182 134 160 182 103 163 180 99 206 226 95 183 216 92 182 219 135 162 186 135 162 186 102 161 184 95 183 216</p>
        </triangles>
        <triangles count="2" material="screen_gps">
          <input offset="0" semantic="VERTEX" source="#geom-gps-vertices" />
          <input offset="1" semantic="NORMAL" source="#geom-gps-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#geom-gps-map1" />
          <p>160 208 230 161 209 231 162 210 232 162 210 232 163 211 233 160 208 230</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="geom-gpsholder" name="gpsholder">
      <mesh>
        <source id="geom-gpsholder-positions">
          <float_array count="156" id="geom-gpsholder-positions-array">-0.9134495 -1.40378 0.02999878 -0.7910705 -1.40378 -0.426726 -0.4567247 -1.40378 -0.7610717 1.02827e-8 -1.40378 -0.8834508 0.4567248 -1.40378 -0.7610717 0.7910705 -1.40378 -0.426726 0.9134496 -1.40378 0.02999864 0.7910706 -1.40378 0.4867234 0.4567251 -1.40378 0.8210691 3.91626e-7 -1.40378 0.9434484 -0.4567243 -1.40378 0.8210695 -0.7910702 -1.40378 0.4867241 -0.747183 -2.767838 0.02999886 -0.6470795 -2.767838 -0.3435925 -0.3735916 -2.767839 -0.6170804 -2.38608e-7 -2.767839 -0.7171839 0.3735911 -2.767839 -0.6170804 0.6470789 -2.767839 -0.3435925 0.7471824 -2.767839 0.02999875 0.647079 -2.767839 0.4035901 0.3735913 -2.767839 0.6770779 7.33228e-8 -2.767838 0.7771816 -0.3735912 -2.767838 0.6770782 -0.6470793 -2.767838 0.4035907 -0.6470792 -1.553958 -0.3435924 -0.7471827 -1.553958 0.02999878 -0.3735914 -1.553958 -0.6170806 1.02537e-8 -1.553958 -0.7171841 0.3735914 -1.553958 -0.6170806 0.6470792 -1.553958 -0.3435924 0.7471827 -1.553958 0.02999868 0.6470793 -1.553958 0.4035898 0.3735915 -1.553958 0.6770778 3.22184e-7 -1.553958 0.7771816 -0.373591 -1.553958 0.6770781 -0.647079 -1.553958 0.4035904 1.965173 -1.401741 -4.629996 -1.965175 -1.401741 4.629998 -1.965174 -1.268368 -4.474271 -1.965174 -1.268368 4.474271 1.965175 -1.268368 4.474271 1.965175 -1.268368 -4.474271 1.965173 -1.401741 4.629998 -1.965175 -1.401741 -4.629996 -1.965174 -0.1266023 4.629996 1.965175 -0.1266026 4.629996 1.965175 -0.1266026 4.474271 -1.965174 -0.1266023 4.474271 1.965175 -0.1266026 -4.629996 -1.965174 -0.1266023 -4.629996 -1.965174 -0.1266023 -4.474271 1.965175 -0.1266026 -4.474271</float_array>
          <technique_common>
            <accessor count="52" source="#geom-gpsholder-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-gpsholder-normals">
          <float_array count="288" id="geom-gpsholder-normals-array">-0.8660254 2.34909e-7 -0.5000001 -1 2.45513e-7 1.66957e-7 -0.9013166 -0.4331611 1.16469e-7 -0.7805631 -0.4331611 -0.450658 -0.5000001 5.00723e-8 -0.8660254 -0.4506584 -0.4331611 -0.7805629 0 -1.77606e-7 -1 -1.20226e-7 -0.4331612 -0.9013164 0.5 -2.46434e-7 -0.8660254 0.4506584 -0.4331611 -0.7805629 0.8660254 -2.26346e-7 -0.4999999 0.7805629 -0.4331613 -0.4506581 1 -2.31635e-7 -3.92841e-8 0.9013165 -0.4331613 -5.00941e-9 0.8660255 -1.19982e-7 0.4999999 0.7805629 -0.4331616 0.4506579 0.5000004 8.50817e-9 0.8660252 0.4506587 -0.4331613 0.7805627 5.69619e-7 1.10142e-8 1 2.60489e-7 -0.4331611 0.9013165 -0.4999994 1.24989e-7 0.8660257 -0.4506579 -0.4331613 0.780563 -0.8660251 2.57778e-7 0.5000005 -0.7805629 -0.4331611 0.4506585 -0.6702899 -0.7420993 4.94569e-8 -0.5804881 -0.7420995 -0.3351448 -0.3351451 -0.7420992 -0.5804882 -1.81342e-7 -0.7420993 -0.6702899 0.3351451 -0.7420993 -0.5804881 0.5804878 -0.7420996 -0.3351449 0.6702898 -0.7420995 1.64856e-8 0.5804877 -0.7420998 0.3351445 0.3351451 -0.7420995 0.5804878 4.39617e-8 -0.7420993 0.67029 -0.3351444 -0.7420997 0.5804879 -0.5804877 -0.7420996 0.3351451 -4.06267e-7 -1 -3.61351e-9 -3.1909e-7 -1 -1.19086e-6 -8.09178e-8 -1 -9.07905e-8 2.06263e-6 -1 -1.19086e-6 -1.68095e-7 -1 -3.61377e-9 -1.19086e-6 -1 -3.1909e-7 -4.06267e-7 -1 2.34559e-7 -3.1909e-7 -1 1.19086e-6 -2.87181e-7 -1 2.66468e-7 0 -0.9999999 0 -3.74358e-7 -1 1.15472e-7 -1.19086e-6 -1 -3.1909e-7 9.09914e-8 1 -1.33216e-8 9.09914e-8 1 -1.33216e-8 9.09914e-8 1 -1.33216e-8 9.09914e-8 1 -1.33216e-8 -1.21322e-7 -1 1.28736e-8 -1.21322e-7 -1 1.28736e-8 -1.21322e-7 -1 1.28736e-8 -1.21322e-7 -1 1.28736e-8 -1 5.04062e-6 -3.11777e-6 -1 4.61537e-6 3.70221e-6 -1 4.1023e-6 3.61136e-6 -1 4.54329e-6 -3.76391e-6 1 -5.04061e-6 -3.1178e-6 1 -4.61538e-6 3.70217e-6 1 -4.10227e-6 3.61135e-6 1 -4.54331e-6 -3.76392e-6 9.09914e-8 1 0 9.09914e-8 1 0 9.09914e-8 1 0 9.09914e-8 1 0 9.09914e-8 1 0 9.09914e-8 1 0 9.09914e-8 1 0 9.09914e-8 1 0 1.29309e-13 1.12185e-6 1 1.02079e-13 1.12185e-6 0.9999999 1.08874e-13 1.12185e-6 1 1.36105e-13 1.12185e-6 1 1 -8.62548e-7 5.92226e-7 1 -9.34873e-7 0 0 0 -0.9999999 0 0 -1 0 0 -0.9999999 0 0 -1 -1 8.06755e-8 -5.91507e-7 -1 0 0 0 0 -1 0 0 -1 0 0 -1 0 0 -1 -1 8.62548e-7 5.92229e-7 -1 9.34873e-7 0 0 0 0.9999999 0 0 1 0 0 0.9999999 0 0 1 1 -8.06754e-8 -5.91507e-7 1 0 0</float_array>
          <technique_common>
            <accessor count="96" source="#geom-gpsholder-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-gpsholder-map1">
          <float_array count="297" id="geom-gpsholder-map1-array">0.6617717 0.1386043 0.7471828 0.6617717 0.06717301 0.7471829 0.6336272 0.06717301 0.7471831 0.6336272 0.1386043 0.7471831 0.6899162 0.1386043 0.7471828 0.6899162 0.06717301 0.7471825 0.3804018 0.1386043 0.7471827 0.3804018 0.06717301 0.7471824 0.4085461 0.1386043 0.7471823 0.4085461 0.06717301 0.7471823 0.4366908 0.1386043 0.7471822 0.4366906 0.06717301 0.7471823 0.4179251 0.2006133 0 0.4331664 0.1853718 0 0.4539862 0.1797942 0 0.4748062 0.1853718 0 0.4900472 0.2006133 0 0.4956259 0.2214341 0 0.4900472 0.2422533 0 0.4748062 0.2574948 0 0.4539862 0.2630741 0 0.4331664 0.2574948 0 0.4648351 0.1386043 0.7471823 0.4179251 0.2422533 0 0.4648351 0.06717301 0.7471823 0.4123464 0.2214341 0 0.4929796 0.1386043 0.7471824 0.4929796 0.06717301 0.7471825 0.5211242 0.1386043 0.7471827 0.5211242 0.06717301 0.7471828 0.5492311 0.1386043 0.7471832 0.5492311 0.06717301 0.7471831 0.5773384 0.1386043 0.7471831 0.5773384 0.06717301 0.7471832 0.6054826 0.1386043 0.7471831 0.6054826 0.06717301 0.7471833 0.6336272 0.1516322 0.91345 0.6617717 0.1516322 0.9134498 0.6899162 0.1516322 0.9134495 0.3804018 0.1516322 0.9134493 0.4085461 0.1516322 0.9134492 0.4366906 0.1516322 0.913449 0.4648351 0.1516322 0.9134492 0.4929796 0.1516322 0.9134493 0.5211242 0.1516322 0.9134496 0.5492311 0.1516322 0.9134499 0.5773384 0.1516322 0.9134501 0.6054826 0.1516322 0.9134501 0.7180607 0.1386043 0.7471827 0.7180607 0.06717301 0.7471824 0.7180607 0.1516322 0.9134493 0.3948568 0.896791 0 0.3857718 0.886183 0 0.3857718 0.3977198 0 0.3948568 0.3871133 0 0.4045514 0.3873301 0 0.6939414 0.3873301 0 0.3948568 0.3871133 0 0.3857718 0.3977198 0 0.9948677 0.9132407 0 0.7054777 0.9132407 0 0.3857718 0.886183 0 0.3948568 0.896791 0 0.7054777 0.4334903 0 0.9948677 0.4334903 0 0.3079995 0.886183 0 0.3079995 0.896791 0 0.6939414 0.8969826 0 0.3079995 0.896791 0 0.4045514 0.8969826 0 0.3079995 0.886183 0 0.9948677 0.3372107 0 0.9948677 0.3601308 0 0.7054777 0.3601308 0 0.7054777 0.3372107 0 0.7054777 0.9938905 0 0.7054777 0.9831085 0 0.9948677 0.9831085 0 0.9948677 0.9938905 0 0.6939414 0.9936907 0 0.4045514 0.9936907 0 0.4045514 0.9068328 0 0.6939414 0.9068328 0 0.4045514 0.2894178 0 0.6939414 0.2894178 0 0.6939414 0.3762743 0 0.4045514 0.3762743 0 0.3079995 0.3977198 0 0.3079995 0.3871133 0 0.3079995 0.3871133 0 0.3079995 0.3977198 0 0.7054777 0.4166091 0 0.9948677 0.4166091 0 0.9948677 0.9012166 0 0.7054777 0.9012166 0 0.9948677 0.3478189 0 0.7054777 0.3478189 0 0.7054777 0.974179 0 0.9948677 0.974179 0</float_array>
          <technique_common>
            <accessor count="99" source="#geom-gpsholder-map1-array" stride="3">
              <param name="S" type="float" />
              <param name="T" type="float" />
              <param name="P" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="geom-gpsholder-vertices">
          <input semantic="POSITION" source="#geom-gpsholder-positions" />
        </vertices>
        <triangles count="86" material="suctionmount">
          <input offset="0" semantic="VERTEX" source="#geom-gpsholder-vertices" />
          <input offset="1" semantic="NORMAL" source="#geom-gpsholder-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#geom-gpsholder-map1" />
          <p>13 0 1 12 1 2 25 2 3 25 2 3 24 3 0 13 0 1 14 4 5 13 0 1 24 3 0 24 3 0 26 5 4 14 4 5 15 6 49 14 4 5 26 5 4 26 5 4 27 7 48 15 6 49 16 8 9 15 6 7 27 7 6 27 7 6 28 9 8 16 8 9 17 10 11 16 8 9 28 9 8 28 9 8 29 11 10 17 10 11 18 12 24 17 10 11 29 11 10 29 11 10 30 13 22 18 12 24 19 14 27 18 12 24 30 13 22 30 13 22 31 15 26 19 14 27 20 16 29 19 14 27 31 15 26 31 15 26 32 17 28 20 16 29 21 18 31 20 16 29 32 17 28 32 17 28 33 19 30 21 18 31 22 20 33 21 18 31 33 19 30 33 19 30 34 21 32 22 20 33 23 22 35 22 20 33 34 21 32 34 21 32 35 23 34 23 22 35 25 2 3 12 1 2 23 22 35 23 22 35 35 23 34 25 2 3 0 24 36 1 25 37 24 3 0 24 3 0 25 2 3 0 24 36 1 25 37 2 26 38 26 5 4 26 5 4 24 3 0 1 25 37 2 26 38 3 27 50 27 7 48 27 7 48 26 5 4 2 26 38 3 27 39 4 28 40 28 9 8 28 9 8 27 7 6 3 27 39 4 28 40 5 29 41 29 11 10 29 11 10 28 9 8 4 28 40 5 29 41 6 30 42 30 13 22 30 13 22 29 11 10 5 29 41 6 30 42 7 31 43 31 15 26 31 15 26 30 13 22 6 30 42 7 31 43 8 32 44 32 17 28 32 17 28 31 15 26 7 31 43 8 32 44 9 33 45 33 19 30 33 19 30 32 17 28 8 32 44 9 33 45 10 34 46 34 21 32 34 21 32 33 19 30 9 33 45 10 34 46 11 35 47 35 23 34 35 23 34 34 21 32 10 34 46 11 35 47 0 24 36 25 2 3 25 2 3 35 23 34 11 35 47 14 36 13 15 37 14 16 38 15 16 38 15 17 39 16 18 40 17 18 40 17 19 41 18 20 42 19 16 38 15 18 40 17 20 42 19 20 42 19 21 43 20 22 44 21 22 44 21 23 45 23 12 46 25 20 42 19 22 44 21 12 46 25 16 38 15 20 42 19 12 46 25 14 36 13 16 38 15 12 46 25 13 47 12 14 36 13 12 46 25 41 48 93 38 49 94 39 50 63 39 50 63 40 51 64 41 48 93 43 52 55 36 53 56 42 54 67 42 54 67 37 55 69 43 52 55 39 56 52 38 57 53 43 58 54 43 58 54 37 59 51 39 56 52 41 60 58 40 61 61 42 62 62 42 62 62 36 63 57 41 60 58 45 64 71 46 65 95 47 66 96 47 66 96 44 67 74 45 64 71 49 68 75 50 69 76 51 70 77 51 70 77 48 71 78 49 68 75 45 72 79 44 73 80 37 74 81 37 74 81 42 75 82 45 72 79 46 76 65 45 77 66 42 62 62 42 62 62 40 61 61 46 76 65 47 78 73 46 79 72 40 80 92 40 80 92 39 81 91 47 78 73 44 82 68 47 83 70 39 56 52 39 56 52 37 59 51 44 82 68 49 84 83 48 85 84 36 86 85 36 86 85 43 87 86 49 84 83 50 88 87 49 89 88 43 58 54 43 58 54 38 57 53 50 88 87 51 90 98 50 91 97 38 92 60 38 92 60 41 93 59 51 90 98 48 94 89 51 95 90 41 60 58 41 60 58 36 63 57 48 94 89</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="geom-suctionmount_alt" name="suctionmount_alt">
      <mesh>
        <source id="geom-suctionmount_alt-positions">
          <float_array count="540" id="geom-suctionmount_alt-positions-array">0.6000032 -12.2874 1.744962 0.5196185 -12.39811 1.447499 0.3000032 -12.47644 1.229686 3.19512e-6 -12.50453 1.149954 -0.2999968 -12.47644 1.229686 -0.519612 -12.39811 1.447499 -0.5999969 -12.2874 1.744962 -0.5196122 -12.17241 2.042289 -0.299997 -12.0855 2.259833 2.93583e-6 -12.05311 2.33943 0.3000029 -12.08549 2.259833 0.5196182 -12.1724 2.042291 0.6000032 -10.61558 1.208482 0.5196184 -10.70641 0.9146369 0.3000032 -10.77043 0.6995314 3.18941e-6 -10.79333 0.620803 -0.2999969 -10.77043 0.6995314 -0.5196121 -10.70641 0.9146364 -0.5999969 -10.61558 1.208482 -0.5196122 -10.52083 1.502296 -0.299997 -10.44898 1.71734 2.93243e-6 -10.42216 1.796039 0.3000029 -10.44898 1.71734 0.5196182 -10.52083 1.502297 0.6000031 -8.94583 0.7719272 0.5196184 -9.008139 0.4763267 0.3000032 -9.051611 0.2600238 3.19714e-6 -9.067067 0.1808734 -0.2999968 -9.051611 0.2600238 -0.5196121 -9.008141 0.4763259 -0.5999969 -8.94583 0.7719272 -0.5196122 -8.880133 1.067646 -0.299997 -8.829892 1.284184 2.94103e-6 -8.811049 1.363452 0.3000029 -8.829892 1.284184 0.5196182 -8.880134 1.067647 0.6000032 -7.290965 0.4345698 0.5196185 -7.341796 0.1390573 0.3000033 -7.377292 -0.07723725 3.21869e-6 -7.389922 -0.1563977 -0.2999968 -7.377292 -0.07723725 -0.519612 -7.341796 0.1390573 -0.5999968 -7.290965 0.4345691 -0.5196121 -7.237428 0.7301196 -0.299997 -7.196517 0.9464904 2.96196e-6 -7.181181 1.025689 0.3000029 -7.196517 0.9464905 0.5196183 -7.237427 0.7301198 0.6000032 -5.655957 0.1976767 0.5196184 -5.686036 -0.1002944 0.3000033 -5.706858 -0.318392 3.25407e-6 -5.714226 -0.3982136 -0.2999968 -5.706858 -0.3183918 -0.5196121 -5.686036 -0.1002945 -0.5999968 -5.655957 0.1976765 -0.5196121 -5.623988 0.49569 -0.2999969 -5.599385 0.7138724 2.9953e-6 -5.590127 0.7937367 0.300003 -5.599385 0.7138724 0.5196183 -5.623987 0.4956903 0.6000033 -4.039003 0.06406629 0.5196186 -4.050966 -0.2354963 0.3000033 -4.059107 -0.4547766 3.30302e-6 -4.061956 -0.535036 -0.2999967 -4.059107 -0.4547766 -0.5196119 -4.050966 -0.2354963 -0.5999967 -4.039004 0.06406629 -0.5196121 -4.02607 0.3636498 -0.2999969 -4.015984 0.5829722 3.0408e-6 -4.012162 0.6632517 0.300003 -4.015984 0.5829722 0.5196183 -4.026069 0.3636503 0.6000034 -2.433271 0.03610992 0.5196186 -2.428526 -0.2638521 0.3000034 -2.425053 -0.48344 3.36503e-6 -2.423781 -0.5638154 -0.2999966 -2.425053 -0.48344 -0.5196119 -2.428526 -0.2638521 -0.5999967 -2.433271 0.03611016 -0.519612 -2.438016 0.3360724 -0.2999968 -2.441489 0.5556608 3.09801e-6 -2.44276 0.6360345 0.3000031 -2.441489 0.5556608 0.5196184 -2.438016 0.3360727 2.705014 -13.55856 2.16736 2.612843 -13.76909 1.499655 2.342611 -13.96527 0.8774534 1.912734 -14.13373 0.3431561 1.352508 -14.263 -0.06682467 0.7001115 -14.34426 -0.3245494 3.44675e-6 -14.37197 -0.4124544 -0.7001045 -14.34426 -0.3245494 -1.352501 -14.263 -0.06682515 -1.912727 -14.13373 0.343155 -2.342604 -13.96527 0.8774527 -2.612836 -13.76909 1.499654 -2.705007 -13.55856 2.167359 -2.612836 -13.34803 2.835065 -2.342605 -13.15186 3.457267 -1.912729 -12.98339 3.991564 -1.352503 -12.85413 4.401544 -0.700106 -12.77287 4.659268 1.98494e-6 -12.74515 4.747174 0.70011 -12.77287 4.659269 1.352507 -12.85413 4.401545 1.912733 -12.98339 3.991565 2.34261 -13.15186 3.457269 2.612842 -13.34804 2.835068 2.705014 -13.10397 2.024028 2.612843 -13.3145 1.356322 2.342611 -13.51067 0.7341205 1.912735 -13.67914 0.199823 1.352508 -13.8084 -0.2101576 0.7001115 -13.88966 -0.4678819 3.44675e-6 -13.91738 -0.5557873 -0.7001045 -13.88966 -0.4678819 -1.352501 -13.8084 -0.2101576 -1.912727 -13.67914 0.1998222 -2.342604 -13.51067 0.73412 -2.612836 -13.3145 1.356321 -2.705007 -13.10397 2.024027 -2.612836 -12.89344 2.691732 -2.342605 -12.69726 3.313934 -1.912729 -12.5288 3.848231 -1.352503 -12.39953 4.258211 -0.700106 -12.31827 4.515936 1.98494e-6 -12.29056 4.603842 0.70011 -12.31827 4.515937 1.352507 -12.39953 4.258212 1.912733 -12.5288 3.848233 2.34261 -12.69726 3.313936 2.612842 -12.89344 2.691734 1.34503 -12.592 1.862602 1.16483 -12.79422 1.221216 0.6725165 -12.94227 0.7516869 3.35935e-6 -12.99645 0.5798278 -0.67251 -12.94227 0.7516869 -1.164824 -12.79422 1.221215 -1.345023 -12.592 1.862602 -1.164825 -12.38977 2.50399 -0.6725106 -12.24172 2.973518 2.60296e-6 -12.18754 3.145378 0.672516 -12.24172 2.973519 1.16483 -12.38977 2.503991 1.03407 -12.23067 1.748677 0.8955317 -12.38614 1.255573 0.5170368 -12.49996 0.894596 3.30413e-6 -12.54162 0.7624694 -0.5170302 -12.49996 0.8945957 -0.8955252 -12.38614 1.255573 -1.034064 -12.23067 1.748676 -0.8955256 -12.07519 2.24178 -0.5170308 -11.96138 2.602758 2.71768e-6 -11.91972 2.734884 0.5170363 -11.96138 2.602757 0.8955314 -12.07519 2.241781 2.083798 -12.77001 1.91873 2.012794 -12.93219 1.404366 1.804622 -13.08331 0.9250549 1.473468 -13.21309 0.5134615 1.041901 -13.31267 0.1976346 0.5393291 -13.37527 -9.02772e-4 3.41665e-6 -13.39662 -0.06861997 -0.5393221 -13.37527 -9.02772e-4 -1.041894 -13.31267 0.1976346 -1.473462 -13.21309 0.513461 -1.804615 -13.08331 0.9250542 -2.012788 -12.93219 1.404365 -2.083791 -12.77001 1.918729 -2.012788 -12.60783 2.433094 -1.804616 -12.45671 2.912405 -1.473463 -12.32693 3.323998 -1.041895 -12.22735 3.639824 -0.5393232 -12.16475 3.838363 2.27535e-6 -12.1434 3.906081 0.539328 -12.16475 3.838364 1.0419 -12.22735 3.639826 1.473468 -12.32693 3.324 1.804622 -12.45671 2.912407 2.012794 -12.60783 2.433095</float_array>
          <technique_common>
            <accessor count="180" source="#geom-suctionmount_alt-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-suctionmount_alt-normals">
          <float_array count="612" id="geom-suctionmount_alt-normals-array">0.9999586 -0.002176582 -0.008827164 0.8720552 -0.1458403 -0.4671727 0.8683681 -0.1364388 -0.4767821 0.9999864 -8.72082e-4 -0.005144455 0.5130207 -0.2541298 -0.8198949 0.5052378 -0.2354679 -0.8302346 4.18899e-4 -0.2960187 -0.955182 3.32946e-4 -0.2717781 -0.9623599 -0.5127061 -0.2559347 -0.8195302 -0.5049474 -0.2351381 -0.8305048 -0.8722984 -0.1474642 -0.4662078 -0.8685311 -0.1359855 -0.4766148 -0.9999658 -0.001953431 -0.008021098 -0.9999896 -6.86111e-4 -0.004483123 -0.8806859 0.1483863 0.44986 -0.8735799 0.1381356 0.4666656 -0.5248485 0.2684557 0.8077535 -0.5127999 0.2456039 0.8226269 -4.6833e-4 0.3154557 0.9489402 -3.69204e-4 0.2871388 0.957889 0.5244211 0.2666676 0.8086228 0.5125031 0.2460282 0.8226851 0.8808548 0.1465094 0.4501444 0.8737631 0.1384217 0.4662375 0.865104 -0.1125745 -0.4887966 0.9999873 -9.43239e-4 -0.004926823 0.499547 -0.1931321 -0.8444837 1.69499e-4 -0.2222388 -0.9749923 -0.4993949 -0.1928463 -0.8446389 -0.8651916 -0.112226 -0.4887215 -0.9999893 -8.40805e-4 -0.004561642 -0.8701144 0.1127551 0.4797782 -0.5063412 0.19854 0.8391665 -2.1093e-4 0.2309195 0.9729729 0.5061684 0.1988524 0.8391967 0.8702173 0.1129994 0.4795342 0.8637565 -0.08615103 -0.4964903 0.9999875 -7.41475e-4 -0.004943327 0.497379 -0.1478139 -0.8548481 5.70375e-5 -0.1700801 -0.9854303 -0.4973223 -0.1476455 -0.8549102 -0.8637845 -0.08595879 -0.4964751 -0.9999882 -7.14968e-4 -0.004796494 -0.8685114 0.08555875 0.488229 -0.5031586 0.1496349 0.8511409 -9.23117e-5 0.173455 0.9848418 0.5030875 0.149856 0.851144 0.8685587 0.08575061 0.4881113 0.8634201 -0.05699582 -0.5012558 0.9999865 -6.1532e-4 -0.005152772 0.4969928 -0.09783387 -0.8622219 2.45271e-6 -0.1126304 -0.993637 -0.4969857 -0.0977712 -0.8622332 -0.8634207 -0.05692954 -0.5012621 -0.9999866 -6.1102e-4 -0.005125871 -0.8681499 0.05613683 0.4931172 -0.5022289 0.09794498 0.85917 -2.39768e-5 0.1133469 0.9935554 0.5022123 0.09802061 0.859171 0.868162 0.05620597 0.4930877 0.8635462 -0.02525039 -0.5036373 0.9999855 -3.18323e-4 -0.005378079 0.4973802 -0.04337705 -0.8664476 -9.7515e-6 -0.04998054 -0.9987502 -0.4973855 -0.04338204 -0.8664442 -0.8635405 -0.02525321 -0.5036468 -0.9999855 -3.20697e-4 -0.005385288 -0.8682825 0.02474143 0.4954526 -0.5022596 0.04315193 0.8636395 -2.79151e-6 0.04991049 0.9987537 0.5022594 0.04316675 0.8636389 0.8682839 0.02475286 0.4954498 0.8635259 -0.008804823 -0.5042278 0.999985 -1.00459e-4 -0.005473083 0.4974159 -0.01519458 -0.8673792 -8.01846e-6 -0.01755351 -0.9998459 -0.4974205 -0.01522673 -0.8673759 -0.863521 -0.00883367 -0.5042355 -0.999985 -1.03515e-4 -0.005482331 -0.8682731 0.008618885 0.4960116 -0.5021691 0.01500443 0.8646393 1.1037e-6 0.01734139 0.9998496 0.5021716 0.01499646 0.8646379 0.8682727 0.008607403 0.4960127 0.9659259 -0.07782845 -0.2468401 0.8436343 0.3964979 -0.3620367 0.873394 0.4644737 -0.1464481 1 5.98015e-8 -9.56824e-8 0.8660254 -0.1503533 -0.4768582 0.7563817 0.3331562 -0.5629331 0.7071071 -0.2126319 -0.6743792 0.6175837 0.2787612 -0.7354472 0.5000002 -0.2604196 -0.8259428 0.4366975 0.2370242 -0.867822 0.2588188 -0.2904591 -0.9212201 0.226051 0.2107877 -0.951036 3.10967e-7 -0.3007064 -0.9537168 7.59248e-8 0.2018382 -0.9794189 -0.258819 -0.2904592 -0.92122 -0.2260511 0.2107881 -0.9510359 -0.4999996 -0.2604183 -0.8259435 -0.4366971 0.2370249 -0.8678219 -0.7071068 -0.2126312 -0.6743798 -0.6175833 0.2787609 -0.7354477 -0.8660256 -0.1503524 -0.4768584 -0.7563814 0.333157 -0.5629331 -0.9659257 -0.07782863 -0.2468406 -0.8436344 0.3964974 -0.3620372 -0.9999999 -1.84188e-7 -4.44923e-7 -0.8733941 0.4644735 -0.1464483 -0.965926 0.07782864 0.2468398 -0.8436347 0.5324473 0.06914049 -0.8660257 0.1503523 0.4768581 -0.7563812 0.5957914 0.270037 -0.7071064 0.2126315 0.6743801 -0.6175822 0.6501848 0.4425517 -0.4999999 0.260419 0.8259432 -0.4366973 0.6919214 0.5749263 -0.2588199 0.2904589 0.9212199 -0.2260516 0.7181588 0.6581403 -7.79811e-7 0.3007056 0.9537171 -7.17704e-7 0.7271073 0.6865239 0.2588193 0.2904594 0.9212198 0.2260512 0.7181584 0.6581409 0.4999998 0.2604185 0.8259435 0.4366969 0.6919211 0.574927 0.7071059 0.2126303 0.6743809 0.6175818 0.6501843 0.4425529 0.8660249 0.1503525 0.4768595 0.7563812 0.5957911 0.2700382 0.9659258 0.07782853 0.2468405 0.8436339 0.5324484 0.06914161 0.4175423 0.7630571 -0.4933582 0.2410684 0.7099907 -0.6616639 -2.46077e-7 0.6905667 -0.7232687 -0.241069 0.7099898 -0.6616648 -0.4175431 0.7630567 -0.4933582 -0.4821366 0.8355473 -0.2634482 -0.4175436 0.9080378 -0.03353652 -0.2410685 0.9611049 0.1347714 1.64051e-8 0.9805291 0.1963741 0.2410695 0.9611047 0.1347711 0.4175428 0.9080383 -0.03353499 0.4821358 0.8355483 -0.2634468 0.3661221 0.8530518 -0.3718293 0.3807113 0.8818958 -0.2780622 0.3297076 0.8246546 -0.459606 0.2680206 0.8019565 -0.5338826 0.1903561 0.7827511 -0.5925076 0.09810154 0.7724569 -0.6274443 -1.73213e-7 0.7674132 -0.6411528 -0.09810157 0.7724568 -0.6274446 -0.1903565 0.7827511 -0.5925075 -0.2680205 0.8019565 -0.5338827 -0.3297072 0.8246547 -0.4596061 -0.3661222 0.8530517 -0.3718299 -0.3807119 0.8818958 -0.2780615 -0.3661214 0.9120521 -0.1847057 -0.3297053 0.9391375 -0.09651451 -0.2680206 0.9631469 -0.02265286 -0.1903573 0.9810403 0.03638651 -0.09810218 0.9926468 0.0709098 -1.0465e-6 0.9963782 0.08503214 0.0981027 0.9926468 0.07091013 0.1903574 0.9810402 0.03638721 0.2680196 0.9631471 -0.02265177 0.329706 0.9391373 -0.09651395 0.3661213 0.9120523 -0.1847051 0.4240743 0.7579244 -0.4956929 0.4896783 0.8315489 -0.2621863 0.2448386 0.704028 -0.6666324 -1.59574e-8 0.6842999 -0.7292005 -0.2448389 0.7040279 -0.6666324 -0.4240739 0.7579246 -0.495693 -0.489678 0.831549 -0.2621866 -0.4240747 0.9051729 -0.02867968 -0.2448398 0.9590701 0.1422602 -8.21195e-7 0.9787981 0.2048278 0.2448391 0.9590706 0.1422591 0.4240739 0.9051734 -0.02867956 -1.23407e-6 -0.9537171 0.3007052 1.78963e-6 -0.9537166 0.3007068 -3.968e-7 -0.9537171 0.3007053 -4.76275e-6 -0.953719 0.3006992 2.08093e-7 -0.9537165 0.3007073 4.45601e-7 -0.9537179 0.3007028 -1.46803e-7 -0.9537169 0.3007059 -3.20403e-6 -0.9537152 0.3007112 1.03526e-6 -0.9537174 0.3007043 1.96282e-6 -0.9537177 0.3007035 -5.04754e-7 -0.9537169 0.3007059 -8.08218e-7 -0.9537166 0.3007068 7.80614e-7 -0.9537167 0.3007065 -9.98731e-6 -0.9537186 0.3007005 -3.82402e-7 -0.9537171 0.3007056 4.32974e-7 -0.9537171 0.3007053 1.96807e-8 -0.9537174 0.3007044 0 -0.953719 0.3006995 2.51868e-7 -0.9537171 0.3007051 2.48239e-6 -0.9537184 0.3007013 -3.15184e-7 -0.9537168 0.3007061 2.13601e-6 -0.9537172 0.3007049 -2.00646e-7 -0.9537171 0.3007056 -4.61837e-6 -0.9537171 0.3007051</float_array>
          <technique_common>
            <accessor count="204" source="#geom-suctionmount_alt-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-suctionmount_alt-map1">
          <float_array count="717" id="geom-suctionmount_alt-map1-array">0.08094395 0.9919508 0.6 0.05732717 0.9919508 0.6 0.05732717 0.8904028 0.6 0.08094395 0.8904028 0.6000001 0.03371055 0.9919508 0.6000001 0.03371055 0.8904028 0.6 0.01009375 0.9919508 0.6 0.01009375 0.8904028 0.5999999 0.2698779 0.9919508 0.6 0.2698779 0.8904028 0.5999999 0.2462612 0.9919508 0.6 0.2462612 0.8904028 0.6 0.2226444 0.9919508 0.6000001 0.2226444 0.8904028 0.6 0.1990276 0.9919508 0.6000001 0.1990276 0.8904028 0.6000001 0.175411 0.9919508 0.6000001 0.175411 0.8904028 0.6000001 0.1517942 0.9919508 0.6000001 0.1517942 0.8904028 0.6000001 0.1281774 0.9919508 0.6 0.1281774 0.8904028 0.6000001 0.1045608 0.9919508 0.6000001 0.1045608 0.8904028 0.6000001 0.05732717 0.7888533 0.5999999 0.08094395 0.7888533 0.6 0.03371055 0.7888533 0.5999999 0.01009375 0.7888533 0.5999998 0.2698779 0.7888533 0.5999998 0.2462612 0.7888533 0.5999999 0.2226444 0.7888533 0.6000001 0.1990276 0.7888533 0.6000002 0.175411 0.7888533 0.6000003 0.1517942 0.7888533 0.6000003 0.1281774 0.7888533 0.6000001 0.1045608 0.7888533 0.6000001 0.05732717 0.6873053 0.5999998 0.08094395 0.6873053 0.6000001 0.03371055 0.6873053 0.5999998 0.01009375 0.6873053 0.5999997 0.2698779 0.6873053 0.5999997 0.2462612 0.6873053 0.5999998 0.2226444 0.6873053 0.6 0.1990276 0.6873053 0.6000002 0.175411 0.6873053 0.6000003 0.1517942 0.6873053 0.6000003 0.1281774 0.6873053 0.6000003 0.1045608 0.6873053 0.6000002 0.05732717 0.5857558 0.5999998 0.08094395 0.5857558 0.6000001 0.03371055 0.5857558 0.5999997 0.01009375 0.5857558 0.5999996 0.2698779 0.5857558 0.5999997 0.2462612 0.5857558 0.5999998 0.2226444 0.5857558 0.6 0.1990276 0.5857558 0.6000003 0.175411 0.5857558 0.6000004 0.1517942 0.5857558 0.6000004 0.1281774 0.5857558 0.6000004 0.1045608 0.5857558 0.6000003 0.05732717 0.4842078 0.5999997 0.08094395 0.4842078 0.6 0.03371055 0.4842078 0.5999997 0.01009375 0.4842078 0.5999995 0.2698779 0.4842078 0.5999996 0.2462612 0.4842078 0.5999998 0.2226444 0.4842078 0.6000001 0.1990276 0.4842078 0.6000003 0.175411 0.4842078 0.6000005 0.1517942 0.4842078 0.6000005 0.1281774 0.4842078 0.6000004 0.1045608 0.4842078 0.6000003 0.05732717 0.3826583 0.5999997 0.08094395 0.3826583 0.6 0.03371055 0.3826583 0.5999995 0.01009375 0.3826583 0.5999994 0.2698777 0.3826583 0.5999995 0.2462612 0.3826583 0.5999997 0.2226444 0.3826583 0.6000001 0.1990276 0.3826583 0.6000004 0.175411 0.3826583 0.6000006 0.1517942 0.3826583 0.6000007 0.1281774 0.3826583 0.6000006 0.1045608 0.3826583 0.6000004 0.2934947 0.9919508 0.6 0.2934947 0.8904028 0.5999999 0.2934947 0.7888533 0.5999998 0.2934947 0.6873053 0.5999997 0.2934947 0.5857558 0.5999996 0.2934947 0.4842078 0.5999995 0.2934947 0.3826583 0.5999994 0.9825655 0.1945762 0 0.9782166 0.2276102 0 0.9654663 0.2583918 0 0.9451838 0.2848231 0 0.9187508 0.3051063 0 0.8879689 0.3178571 0 0.8549364 0.3222055 0 0.8219035 0.3178571 0 0.7911216 0.3051063 0 0.764689 0.2848231 0 0.7444062 0.2583918 0 0.7316561 0.2276102 0 0.7273072 0.1945762 0 0.7316561 0.161544 0 0.7444062 0.1307624 0 0.764689 0.1043292 0 0.7911218 0.08404603 0 0.8219035 0.07129704 0 0.8549364 0.06694679 0 0.6320372 0.01458142 2.705014 0.8879693 0.07129704 0 0.9187508 0.08404603 0 0.9451838 0.1043292 0 0.9654665 0.1307624 0 0.6790907 0.01458142 2.705013 0.01532893 0.1424348 0 0.009271975 0.1889496 0 0.6790907 0.0462851 2.705013 0.03333984 0.09912339 0 0.6320372 0.0462851 2.705014 0.06182738 0.06186988 0 0.7261931 0.01458142 2.705012 0.09911489 0.03341995 0 0.7261931 0.0462851 2.705012 0.1424183 0.01539716 0 0.7732956 0.01458142 2.705011 0.18892 0.009340756 0 0.7732956 0.0462851 2.705011 0.2354215 0.01539858 0 0.8203984 0.01458142 2.70501 0.2787248 0.0334227 0 0.8203984 0.0462851 2.70501 0.3160122 0.06187393 0 0.8675009 0.01458142 2.70501 0.3444985 0.09913152 0 0.8675009 0.04628507 2.705009 0.3625056 0.1424484 0 0.9146004 0.01458132 2.705009 0.3685569 0.1889659 0 0.9146004 0.0462851 2.705009 0.3625 0.235482 0 0.8674979 0.01458142 2.705008 0.3444845 0.278796 0 0.8674979 0.0462851 2.705007 0.3159776 0.3160523 0 0.8203955 0.01458142 2.705007 0.2787091 0.3445484 0 0.8203955 0.04628516 2.705007 0.2353915 0.3625576 0 0.7732929 0.01458142 2.705007 0.1888959 0.3685841 0 0.7732929 0.0462851 2.705007 0.1424017 0.3625454 0 0.7261901 0.01458142 2.705006 0.09909011 0.3445267 0 0.7261901 0.0462851 2.705006 0.06182992 0.316028 0 0.6790877 0.01458142 2.705007 0.033331 0.2787717 0 0.6790875 0.04628507 2.705006 0.01532231 0.2354616 0 0.1268457 0.1889604 0 0.1351636 0.1579276 0 0.157885 0.1352138 0 0.1889212 0.1269014 0 0.2199561 0.1352178 0 0.2426742 0.1579357 0 0.2509868 0.1889713 0 0.2426709 0.2200083 0 0.2199607 0.2427247 0 0.18891 0.2510127 0 0.1578616 0.2427166 0 0.1351558 0.2199974 0 0.05781712 0.1538256 0 0.05351502 0.1889523 0 0.07165623 0.1212549 0 0.09294412 0.09299227 0 0.1212226 0.07172008 0 0.1537961 0.05788061 0 0.18892 0.05358197 0 0.2240442 0.05788192 0 0.2566171 0.07172272 0 0.2848955 0.09299643 0 0.3061815 0.1212617 0 0.3200177 0.1538365 0 0.3243151 0.1889659 0 0.3200144 0.2240965 0 0.306173 0.2566727 0 0.2848872 0.2849502 0 0.256596 0.3062374 0 0.2239873 0.3200525 0 0.1888982 0.3243443 0 0.1538116 0.320043 0 0.1212087 0.3062198 0 0.09292422 0.2849272 0 0.07164538 0.256651 0 0.05781032 0.2240789 0 0.1001205 0.1889563 0 0.1120189 0.1445645 0 0.1445227 0.1120752 0 0.1889208 0.1001835 0 0.2333181 0.1120779 0 0.2658188 0.1445713 0 0.2777108 0.1889686 0 0.2658167 0.2333699 0 0.2333403 0.2658837 0 0.1889032 0.2776912 0 0.1444728 0.2658701 0 0.1120062 0.2333536 0 0.9782166 0.161544 0 0.6319849 0.01458142 2.705007 0.6319849 0.0462851 2.705007 0.5848823 0.01458148 2.705008 0.5848823 0.0462851 2.705008 0.5377798 0.01458148 2.705009 0.5377798 0.0462851 2.705009 0.4906773 0.01458142 2.70501 0.4906773 0.04628516 2.70501 0.4435748 0.01458142 2.70501 0.4435748 0.0462851 2.70501 0.3964723 0.01458142 2.705011 0.3964723 0.0462851 2.705011 0.3493698 0.01458142 2.705013 0.3493698 0.04628507 2.705013 0.3965736 0.01458142 2.705013 0.3965736 0.0462851 2.705014 0.4436759 0.01458142 2.705014 0.4436759 0.0462851 2.705014 0.4907785 0.01458132 2.705014 0.4907785 0.0462851 2.705014 0.5378808 0.01458142 2.705014 0.5378808 0.0462851 2.705014 0.5849832 0.01458142 2.705014 0.5849832 0.0462851 2.705014 0.349471 0.01458142 2.705013 0.349471 0.04628507 2.705013 0.9146034 0.01458132 2.705009 0.9146034 0.0462851 2.705009</float_array>
          <technique_common>
            <accessor count="239" source="#geom-suctionmount_alt-map1-array" stride="3">
              <param name="S" type="float" />
              <param name="T" type="float" />
              <param name="P" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="geom-suctionmount_alt-vertices">
          <input semantic="POSITION" source="#geom-suctionmount_alt-positions" />
        </vertices>
        <triangles count="332" material="suctionmount">
          <input offset="0" semantic="VERTEX" source="#geom-suctionmount_alt-vertices" />
          <input offset="1" semantic="NORMAL" source="#geom-suctionmount_alt-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#geom-suctionmount_alt-map1" />
          <p>0 0 0 1 1 1 13 2 2 13 2 2 12 3 3 0 0 0 1 1 1 2 4 4 14 5 5 14 5 5 13 2 2 1 1 1 2 4 4 3 6 6 15 7 7 15 7 7 14 5 5 2 4 4 3 6 84 4 8 8 16 9 9 16 9 9 15 7 85 3 6 84 4 8 8 5 10 10 17 11 11 17 11 11 16 9 9 4 8 8 5 10 10 6 12 12 18 13 13 18 13 13 17 11 11 5 10 10 6 12 12 7 14 14 19 15 15 19 15 15 18 13 13 6 12 12 7 14 14 8 16 16 20 17 17 20 17 17 19 15 15 7 14 14 8 16 16 9 18 18 21 19 19 21 19 19 20 17 17 8 16 16 9 18 18 10 20 20 22 21 21 22 21 21 21 19 19 9 18 18 10 20 20 11 22 22 23 23 23 23 23 23 22 21 21 10 20 20 11 22 22 0 0 0 12 3 3 12 3 3 23 23 23 11 22 22 12 3 3 13 2 2 25 24 24 25 24 24 24 25 25 12 3 3 13 2 2 14 5 5 26 26 26 26 26 26 25 24 24 13 2 2 14 5 5 15 7 7 27 27 27 27 27 27 26 26 26 14 5 5 15 7 85 16 9 9 28 28 28 28 28 28 27 27 86 15 7 85 16 9 9 17 11 11 29 29 29 29 29 29 28 28 28 16 9 9 17 11 11 18 13 13 30 30 30 30 30 30 29 29 29 17 11 11 18 13 13 19 15 15 31 31 31 31 31 31 30 30 30 18 13 13 19 15 15 20 17 17 32 32 32 32 32 32 31 31 31 19 15 15 20 17 17 21 19 19 33 33 33 33 33 33 32 32 32 20 17 17 21 19 19 22 21 21 34 34 34 34 34 34 33 33 33 21 19 19 22 21 21 23 23 23 35 35 35 35 35 35 34 34 34 22 21 21 23 23 23 12 3 3 24 25 25 24 25 25 35 35 35 23 23 23 24 25 25 25 24 24 37 36 36 37 36 36 36 37 37 24 25 25 25 24 24 26 26 26 38 38 38 38 38 38 37 36 36 25 24 24 26 26 26 27 27 27 39 39 39 39 39 39 38 38 38 26 26 26 27 27 86 28 28 28 40 40 40 40 40 40 39 39 87 27 27 86 28 28 28 29 29 29 41 41 41 41 41 41 40 40 40 28 28 28 29 29 29 30 30 30 42 42 42 42 42 42 41 41 41 29 29 29 30 30 30 31 31 31 43 43 43 43 43 43 42 42 42 30 30 30 31 31 31 32 32 32 44 44 44 44 44 44 43 43 43 31 31 31 32 32 32 33 33 33 45 45 45 45 45 45 44 44 44 32 32 32 33 33 33 34 34 34 46 46 46 46 46 46 45 45 45 33 33 33 34 34 34 35 35 35 47 47 47 47 47 47 46 46 46 34 34 34 35 35 35 24 25 25 36 37 37 36 37 37 47 47 47 35 35 35 36 37 37 37 36 36 49 48 48 49 48 48 48 49 49 36 37 37 37 36 36 38 38 38 50 50 50 50 50 50 49 48 48 37 36 36 38 38 38 39 39 39 51 51 51 51 51 51 50 50 50 38 38 38 39 39 87 40 40 40 52 52 52 52 52 52 51 51 88 39 39 87 40 40 40 41 41 41 53 53 53 53 53 53 52 52 52 40 40 40 41 41 41 42 42 42 54 54 54 54 54 54 53 53 53 41 41 41 42 42 42 43 43 43 55 55 55 55 55 55 54 54 54 42 42 42 43 43 43 44 44 44 56 56 56 56 56 56 55 55 55 43 43 43 44 44 44 45 45 45 57 57 57 57 57 57 56 56 56 44 44 44 45 45 45 46 46 46 58 58 58 58 58 58 57 57 57 45 45 45 46 46 46 47 47 47 59 59 59 59 59 59 58 58 58 46 46 46 47 47 47 36 37 37 48 49 49 48 49 49 59 59 59 47 47 47 48 49 49 49 48 48 61 60 60 61 60 60 60 61 61 48 49 49 49 48 48 50 50 50 62 62 62 62 62 62 61 60 60 49 48 48 50 50 50 51 51 51 63 63 63 63 63 63 62 62 62 50 50 50 51 51 88 52 52 52 64 64 64 64 64 64 63 63 89 51 51 88 52 52 52 53 53 53 65 65 65 65 65 65 64 64 64 52 52 52 53 53 53 54 54 54 66 66 66 66 66 66 65 65 65 53 53 53 54 54 54 55 55 55 67 67 67 67 67 67 66 66 66 54 54 54 55 55 55 56 56 56 68 68 68 68 68 68 67 67 67 55 55 55 56 56 56 57 57 57 69 69 69 69 69 69 68 68 68 56 56 56 57 57 57 58 58 58 70 70 70 70 70 70 69 69 69 57 57 57 58 58 58 59 59 59 71 71 71 71 71 71 70 70 70 58 58 58 59 59 59 48 49 49 60 61 61 60 61 61 71 71 71 59 59 59 60 61 61 61 60 60 73 72 72 73 72 72 72 73 73 60 61 61 61 60 60 62 62 62 74 74 74 74 74 74 73 72 72 61 60 60 62 62 62 63 63 63 75 75 75 75 75 75 74 74 74 62 62 62 63 63 89 64 64 64 76 76 76 76 76 76 75 75 90 63 63 89 64 64 64 65 65 65 77 77 77 77 77 77 76 76 76 64 64 64 65 65 65 66 66 66 78 78 78 78 78 78 77 77 77 65 65 65 66 66 66 67 67 67 79 79 79 79 79 79 78 78 78 66 66 66 67 67 67 68 68 68 80 80 80 80 80 80 79 79 79 67 67 67 68 68 68 69 69 69 81 81 81 81 81 81 80 80 80 68 68 68 69 69 69 70 70 70 82 82 82 82 82 82 81 81 81 69 69 69 70 70 70 71 71 71 83 83 83 83 83 83 82 82 82 70 70 70 71 71 71 60 61 61 72 73 73 72 73 73 83 83 83 71 71 71 85 84 115 109 85 118 108 86 120 108 86 120 84 87 110 85 84 115 86 88 122 110 89 124 109 85 118 109 85 118 85 84 115 86 88 122 87 90 126 111 91 128 110 89 124 110 89 124 86 88 122 87 90 126 88 92 130 112 93 132 111 91 128 111 91 128 87 90 126 88 92 130 89 94 134 113 95 136 112 93 132 112 93 132 88 92 130 89 94 134 90 96 237 114 97 238 113 95 136 113 95 136 89 94 134 90 96 237 91 98 142 115 99 144 114 97 140 114 97 140 90 96 138 91 98 142 92 100 146 116 101 148 115 99 144 115 99 144 91 98 142 92 100 146 93 102 150 117 103 152 116 101 148 116 101 148 92 100 146 93 102 150 94 104 154 118 105 156 117 103 152 117 103 152 93 102 150 94 104 154 95 106 158 119 107 160 118 105 156 118 105 156 94 104 154 95 106 158 96 108 211 120 109 212 119 107 160 119 107 160 95 106 158 96 108 211 97 110 213 121 111 214 120 109 212 120 109 212 96 108 211 97 110 213 98 112 215 122 113 216 121 111 214 121 111 214 97 110 213 98 112 215 99 114 217 123 115 218 122 113 216 122 113 216 98 112 215 99 114 217 100 116 219 124 117 220 123 115 218 123 115 218 99 114 217 100 116 219 101 118 221 125 119 222 124 117 220 124 117 220 100 116 219 101 118 221 102 120 223 126 121 224 125 119 222 125 119 222 101 118 221 102 120 223 103 122 225 127 123 226 126 121 236 126 121 236 102 120 235 103 122 225 104 124 227 128 125 228 127 123 226 127 123 226 103 122 225 104 124 227 105 126 229 129 127 230 128 125 228 128 125 228 104 124 227 105 126 229 106 128 231 130 129 232 129 127 230 129 127 230 105 126 229 106 128 231 107 130 233 131 131 234 130 129 232 130 129 232 106 128 231 107 130 233 84 87 110 108 86 120 131 131 234 131 131 234 107 130 233 84 87 110 145 132 163 146 133 164 147 134 165 147 134 165 148 135 166 149 136 167 149 136 167 150 137 168 151 138 169 147 134 165 149 136 167 151 138 169 151 138 169 152 139 170 153 140 171 153 140 171 154 141 172 155 142 173 151 138 169 153 140 171 155 142 173 147 134 165 151 138 169 155 142 173 145 132 163 147 134 165 155 142 173 144 143 162 145 132 163 155 142 173 109 85 116 157 144 174 156 145 175 156 145 175 108 86 117 109 85 116 110 89 119 158 146 176 157 144 174 157 144 174 109 85 116 110 89 119 111 91 121 159 147 177 158 146 176 158 146 176 110 89 119 111 91 121 112 93 123 160 148 178 159 147 177 159 147 177 111 91 121 112 93 123 113 95 125 161 149 179 160 148 178 160 148 178 112 93 123 113 95 125 114 97 127 162 150 180 161 149 179 161 149 179 113 95 125 114 97 127 115 99 129 163 151 181 162 150 180 162 150 180 114 97 127 115 99 129 116 101 131 164 152 182 163 151 181 163 151 181 115 99 129 116 101 131 117 103 133 165 153 183 164 152 182 164 152 182 116 101 131 117 103 133 118 105 135 166 154 184 165 153 183 165 153 183 117 103 133 118 105 135 119 107 137 167 155 185 166 154 184 166 154 184 118 105 135 119 107 137 120 109 139 168 156 186 167 155 185 167 155 185 119 107 137 120 109 139 121 111 141 169 157 187 168 156 186 168 156 186 120 109 139 121 111 141 122 113 143 170 158 188 169 157 187 169 157 187 121 111 141 122 113 143 123 115 145 171 159 189 170 158 188 170 158 188 122 113 143 123 115 145 124 117 147 172 160 190 171 159 189 171 159 189 123 115 145 124 117 147 125 119 149 173 161 191 172 160 190 172 160 190 124 117 147 125 119 149 126 121 151 174 162 192 173 161 191 173 161 191 125 119 149 126 121 151 127 123 153 175 163 193 174 162 192 174 162 192 126 121 151 127 123 153 128 125 155 176 164 194 175 163 193 175 163 193 127 123 153 128 125 155 129 127 157 177 165 195 176 164 194 176 164 194 128 125 155 129 127 157 130 129 159 178 166 196 177 165 195 177 165 195 129 127 157 130 129 159 131 131 161 179 167 197 178 166 196 178 166 196 130 129 159 131 131 161 108 86 117 156 145 175 179 167 197 179 167 197 131 131 161 108 86 117 133 168 199 145 132 163 144 143 162 144 143 162 132 169 198 133 168 199 134 170 200 146 133 164 145 132 163 145 132 163 133 168 199 134 170 200 135 171 201 147 134 165 146 133 164 146 133 164 134 170 200 135 171 201 136 172 202 148 135 166 147 134 165 147 134 165 135 171 201 136 172 202 137 173 203 149 136 167 148 135 166 148 135 166 136 172 202 137 173 203 138 174 204 150 137 168 149 136 167 149 136 167 137 173 203 138 174 204 139 175 205 151 138 169 150 137 168 150 137 168 138 174 204 139 175 205 140 176 206 152 139 170 151 138 169 151 138 169 139 175 205 140 176 206 141 177 207 153 140 171 152 139 170 152 139 170 140 176 206 141 177 207 142 178 208 154 141 172 153 140 171 153 140 171 141 177 207 142 178 208 143 179 209 155 142 173 154 141 172 154 141 172 142 178 208 143 179 209 132 169 198 144 143 162 155 142 173 155 142 173 143 179 209 132 169 198 157 144 174 132 169 198 156 145 175 159 147 177 133 168 199 158 146 176 161 149 179 134 170 200 160 148 178 163 151 181 135 171 201 162 150 180 165 153 183 136 172 202 164 152 182 167 155 185 137 173 203 166 154 184 169 157 187 138 174 204 168 156 186 171 159 189 139 175 205 170 158 188 173 161 191 140 176 206 172 160 190 175 163 193 141 177 207 174 162 192 177 165 195 142 178 208 176 164 194 179 167 197 143 179 209 178 166 196 107 180 92 106 181 93 105 182 94 105 182 94 104 183 95 103 184 96 103 184 96 102 185 97 101 186 98 105 182 94 103 184 96 101 186 98 101 186 98 100 187 99 99 188 100 99 188 100 98 189 101 97 190 102 101 186 98 99 188 100 97 190 102 97 190 102 96 191 103 95 192 104 95 192 104 94 193 105 93 194 106 97 190 102 95 192 104 93 194 106 101 186 98 97 190 102 93 194 106 93 194 106 92 195 107 91 196 108 91 196 108 90 197 109 89 198 111 93 194 106 91 196 108 89 198 111 89 198 111 88 199 112 87 200 113 87 200 113 86 201 114 85 202 210 89 198 111 87 200 113 85 202 210 93 194 106 89 198 111 85 202 210 101 186 98 93 194 106 85 202 210 105 182 94 101 186 98 85 202 210 107 180 92 105 182 94 85 202 210 84 203 91 107 180 92 85 202 210 157 144 174 158 146 176 133 168 199 157 144 174 133 168 199 132 169 198 159 147 177 160 148 178 134 170 200 159 147 177 134 170 200 133 168 199 161 149 179 162 150 180 135 171 201 161 149 179 135 171 201 134 170 200 163 151 181 164 152 182 136 172 202 163 151 181 136 172 202 135 171 201 165 153 183 166 154 184 137 173 203 165 153 183 137 173 203 136 172 202 167 155 185 168 156 186 138 174 204 167 155 185 138 174 204 137 173 203 169 157 187 170 158 188 139 175 205 169 157 187 139 175 205 138 174 204 171 159 189 172 160 190 140 176 206 171 159 189 140 176 206 139 175 205 173 161 191 174 162 192 141 177 207 173 161 191 141 177 207 140 176 206 175 163 193 176 164 194 142 178 208 175 163 193 142 178 208 141 177 207 177 165 195 178 166 196 143 179 209 177 165 195 143 179 209 142 178 208 179 167 197 156 145 175 132 169 198 179 167 197 132 169 198 143 179 209</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_lights>
    <light id="EnvironmentAmbientLight" name="EnvironmentAmbientLight">
      <technique_common>
        <ambient>
          <color>0 0 0</color>
        </ambient>
      </technique_common>
    </light>
  </library_lights>
  <library_visual_scenes>
    <visual_scene id="MaxScene">
      <node name="EnvironmentAmbientLight">
        <instance_light url="#EnvironmentAmbientLight" />
      </node>
      <node id="node-suctionmount" name="suctionmount">
        <instance_geometry url="#geom-suctionmount">
          <bind_material>
            <technique_common>
              <instance_material symbol="suctionmount" target="#suctionmount-material">
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADA">
            <cast_shadows type="bool">1</cast_shadows>
            <primary_visibility type="integer">1</primary_visibility>
            <receive_shadows type="bool">1</receive_shadows>
            <secondary_visibility type="integer">1</secondary_visibility>
          </technique>
        </extra>
      </node>
      <node id="node-gps" name="gps">
        <instance_geometry url="#geom-gps">
          <bind_material>
            <technique_common>
              <instance_material symbol="gps" target="#gps-material">
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
              </instance_material>
              <instance_material symbol="screen_gps" target="#screen_gps-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADA">
            <cast_shadows type="bool">0</cast_shadows>
            <primary_visibility type="integer">1</primary_visibility>
            <receive_shadows type="bool">0</receive_shadows>
            <secondary_visibility type="integer">1</secondary_visibility>
          </technique>
        </extra>
      </node>
      <node id="node-gpsholder" name="gpsholder">
        <instance_geometry url="#geom-gpsholder">
          <bind_material>
            <technique_common>
              <instance_material symbol="suctionmount" target="#suctionmount-material">
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADA">
            <cast_shadows type="bool">1</cast_shadows>
            <primary_visibility type="integer">1</primary_visibility>
            <receive_shadows type="bool">1</receive_shadows>
            <secondary_visibility type="integer">1</secondary_visibility>
          </technique>
        </extra>
      </node>
      <node id="node-suctionmount_alt" name="suctionmount_alt">
        <instance_geometry url="#geom-suctionmount_alt">
          <bind_material>
            <technique_common>
              <instance_material symbol="suctionmount" target="#suctionmount-material">
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
                <bind_vertex_input input_semantic="TEXCOORD" input_set="0" semantic="CHANNEL1" />
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADA">
            <cast_shadows type="bool">1</cast_shadows>
            <primary_visibility type="integer">1</primary_visibility>
            <receive_shadows type="bool">1</receive_shadows>
            <secondary_visibility type="integer">1</secondary_visibility>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#MaxScene" />
  </scene>
</COLLADA>