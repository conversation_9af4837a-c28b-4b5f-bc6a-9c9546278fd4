<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.91.0 commit date:2020-11-25, commit time:08:34, hash:0f45cab862b8</authoring_tool>
    </contributor>
    <created>2021-03-03T16:11:15</created>
    <modified>2021-03-03T16:11:15</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="wendover_flasher_1-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_2-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_3-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_4-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_5-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_6-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_7-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_8-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_9-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_10-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_11-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="wendover_flasher_12-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.8 0.8 0.8 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images/>
  <library_materials>
    <material id="wendover_flasher_1-material" name="wendover_flasher_1">
      <instance_effect url="#wendover_flasher_1-effect"/>
    </material>
    <material id="wendover_flasher_2-material" name="wendover_flasher_2">
      <instance_effect url="#wendover_flasher_2-effect"/>
    </material>
    <material id="wendover_flasher_3-material" name="wendover_flasher_3">
      <instance_effect url="#wendover_flasher_3-effect"/>
    </material>
    <material id="wendover_flasher_4-material" name="wendover_flasher_4">
      <instance_effect url="#wendover_flasher_4-effect"/>
    </material>
    <material id="wendover_flasher_5-material" name="wendover_flasher_5">
      <instance_effect url="#wendover_flasher_5-effect"/>
    </material>
    <material id="wendover_flasher_6-material" name="wendover_flasher_6">
      <instance_effect url="#wendover_flasher_6-effect"/>
    </material>
    <material id="wendover_flasher_7-material" name="wendover_flasher_7">
      <instance_effect url="#wendover_flasher_7-effect"/>
    </material>
    <material id="wendover_flasher_8-material" name="wendover_flasher_8">
      <instance_effect url="#wendover_flasher_8-effect"/>
    </material>
    <material id="wendover_flasher_9-material" name="wendover_flasher_9">
      <instance_effect url="#wendover_flasher_9-effect"/>
    </material>
    <material id="wendover_flasher_10-material" name="wendover_flasher_10">
      <instance_effect url="#wendover_flasher_10-effect"/>
    </material>
    <material id="wendover_flasher_11-material" name="wendover_flasher_11">
      <instance_effect url="#wendover_flasher_11-effect"/>
    </material>
    <material id="wendover_flasher_12-material" name="wendover_flasher_12">
      <instance_effect url="#wendover_flasher_12-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="wendover_grille_b_001-mesh" name="wendover_grille_b.001">
      <mesh>
        <source id="wendover_grille_b_001-mesh-positions">
          <float_array id="wendover_grille_b_001-mesh-positions-array" count="78">0.2566334 -2.353298 0.5299944 0.2564964 -2.34674 0.5557358 0.1289218 -2.357895 0.5300007 0.1288382 -2.351334 0.5557584 0.3925263 -2.342213 0.5299746 0.3925766 -2.335514 0.5557056 -0.2566334 -2.353298 0.5299944 -0.2564964 -2.34674 0.5557358 -0.1289218 -2.357895 0.5300007 -0.1288382 -2.351334 0.5557584 0 -2.353275 0.555771 0 -2.359838 0.5300034 -0.3925263 -2.342213 0.5299746 -0.3925766 -2.335514 0.5557056 -0.1927776 -2.355596 0.5299976 0.06441909 -2.352304 0.5557647 -0.3245798 -2.348406 0.5299845 0.1927776 -2.355596 0.5299976 0.3245798 -2.348406 0.5299845 -0.1926673 -2.349037 0.5557472 -0.3245365 -2.341777 0.5557207 0.1926673 -2.349037 0.5557472 0.3245365 -2.341777 0.5557207 -0.06446087 -2.358866 0.5300021 -0.06441909 -2.352304 0.5557647 0.06446087 -2.358866 0.5300021</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-positions-array" count="26" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-normals">
          <float_array id="wendover_grille_b_001-mesh-normals-array" count="57">0.0348832 -0.9684289 0.2468376 0.0523703 -0.9675086 0.2473553 0.05255448 -0.9674974 0.2473602 0.01458817 -0.9689545 0.2468088 0.02472054 -0.9687411 0.2468394 0.02475106 -0.9687404 0.2468392 0.07931911 -0.9653195 0.2487307 0.07910454 -0.9653379 0.2487276 -0.0348832 -0.9684289 0.2468376 -0.05255448 -0.9674974 0.2473602 -0.0523703 -0.9675086 0.2473553 -0.01458817 -0.9689545 0.2468088 -0.02475106 -0.9687404 0.2468392 -0.02472054 -0.9687411 0.2468394 -0.07931911 -0.9653195 0.2487307 -0.07910513 -0.9653452 0.248699 -0.08835351 -0.9642895 0.2496788 0 -0.9690577 0.2468346 0.08835351 -0.9642895 0.2496788</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-normals-array" count="19" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-map-0">
          <float_array id="wendover_grille_b_001-mesh-map-0-array" count="96">0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565 0.6680889 0.9558632 0.6681084 0.9177743 0.7664484 0.9177682 0.7665032 0.9558565</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-map-0-array" count="48" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-colors-colorSet0" name="colorSet0">
          <float_array id="wendover_grille_b_001-mesh-colors-colorSet0-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-colors-colorSet0-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-colors-Col" name="Col">
          <float_array id="wendover_grille_b_001-mesh-colors-Col-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-colors-Col-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-colors-geom-legran_body-map0" name="geom-legran_body-map0">
          <float_array id="wendover_grille_b_001-mesh-colors-geom-legran_body-map0-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" name="geom-legran_subframe_F-map0">
          <float_array id="wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" name="geom-pessima_swaybar_R-map0">
          <float_array id="wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" name="geom-legran_halfshaft_F-map0">
          <float_array id="wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" name="geom-legran_hub_F-map0">
          <float_array id="wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" name="geom-legran_strut_F-map0">
          <float_array id="wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0-array" count="192">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0-array" count="48" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="wendover_grille_b_001-mesh-vertices">
          <input semantic="POSITION" source="#wendover_grille_b_001-mesh-positions"/>
        </vertices>
        <polylist material="wendover_flasher_1-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>13 16 24 24 12 16 25 25 16 15 26 26 20 14 27 27</p>
        </polylist>
        <polylist material="wendover_flasher_2-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>20 14 20 20 16 15 21 21 6 10 22 22 7 9 23 23</p>
        </polylist>
        <polylist material="wendover_flasher_3-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>19 8 12 12 7 9 13 13 6 10 14 14 14 8 15 15</p>
        </polylist>
        <polylist material="wendover_flasher_4-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>9 12 32 32 19 8 33 33 14 8 34 34 8 13 35 35</p>
        </polylist>
        <polylist material="wendover_flasher_5-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>24 11 16 16 9 12 17 17 8 13 18 18 23 11 19 19</p>
        </polylist>
        <polylist material="wendover_flasher_6-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>10 17 28 28 24 11 29 29 23 11 30 30 11 17 31 31</p>
        </polylist>
        <polylist material="wendover_flasher_7-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>10 17 40 40 11 17 41 41 25 3 42 42 15 3 43 43</p>
        </polylist>
        <polylist material="wendover_flasher_8-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>15 3 4 4 25 3 5 5 2 4 6 6 3 5 7 7</p>
        </polylist>
        <polylist material="wendover_flasher_9-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>3 5 44 44 2 4 45 45 17 0 46 46 21 0 47 47</p>
        </polylist>
        <polylist material="wendover_flasher_10-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>21 0 0 0 17 0 1 1 0 1 2 2 1 2 3 3</p>
        </polylist>
        <polylist material="wendover_flasher_11-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>22 6 8 8 1 2 9 9 0 1 10 10 18 7 11 11</p>
        </polylist>
        <polylist material="wendover_flasher_12-material" count="1">
          <input semantic="VERTEX" source="#wendover_grille_b_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#wendover_grille_b_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#wendover_grille_b_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-colorSet0" offset="3" set="0"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-Col" offset="3" set="1"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_body-map0" offset="3" set="2"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_subframe_F-map0" offset="3" set="3"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-pessima_swaybar_R-map0" offset="3" set="4"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_halfshaft_F-map0" offset="3" set="5"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_hub_F-map0" offset="3" set="6"/>
          <input semantic="COLOR" source="#wendover_grille_b_001-mesh-colors-geom-legran_strut_F-map0" offset="3" set="7"/>
          <vcount>4 </vcount>
          <p>5 18 36 36 22 6 37 37 18 7 38 38 4 18 39 39</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="wendover_flasher_special" name="wendover_flasher_special" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 -2.99669e-8 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#wendover_grille_b_001-mesh" name="wendover_flasher_special">
          <bind_material>
            <technique_common>
              <instance_material symbol="wendover_flasher_1-material" target="#wendover_flasher_1-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_2-material" target="#wendover_flasher_2-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_3-material" target="#wendover_flasher_3-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_4-material" target="#wendover_flasher_4-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_5-material" target="#wendover_flasher_5-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_6-material" target="#wendover_flasher_6-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_7-material" target="#wendover_flasher_7-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_8-material" target="#wendover_flasher_8-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_9-material" target="#wendover_flasher_9-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_10-material" target="#wendover_flasher_10-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_11-material" target="#wendover_flasher_11-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="wendover_flasher_12-material" target="#wendover_flasher_12-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>