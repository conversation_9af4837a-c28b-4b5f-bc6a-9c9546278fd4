<?xml version='1.0' encoding='utf-8'?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>GabeNew</author>
      <authoring_tool>OpenCOLLADA for 3ds Max;  Version: 1.3.1;  Revision: 847M;  Platform: x64;  Configuration: Release_Max2010_static</authoring_tool>
      </contributor>
    <created>2013-07-01T00:08:21</created>
    <modified>2013-07-01T00:08:21</modified>
    <unit meter="0.01" name="centimeter" />
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="cone_01a">
      <profile_COMMON>
        <newparam sid="cone_01_d_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="cone_01_d_tga-sampler">
          <sampler2D>
            <source>cone_01_d_tga-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="cone_01_s_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="cone_01_s_tga-sampler">
          <sampler2D>
            <source>cone_01_s_tga-surface</source>
          </sampler2D>
        </newparam>
        <newparam sid="cone_01_n_tga-surface">
          <surface type="2D">
            </surface>
        </newparam>
        <newparam sid="cone_01_n_tga-sampler">
          <sampler2D>
            <source>cone_01_n_tga-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <blinn>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>0.588 0.588 0.588 1</color>
            </ambient>
            <diffuse>
              <texture texcoord="CHANNEL1" texture="cone_01_d_tga-sampler" />
            </diffuse>
            <specular>
              <texture texcoord="CHANNEL1" texture="cone_01_s_tga-sampler" />
            </specular>
            <shininess>
              <float>10</float>
            </shininess>
            <reflective>
              <color>0 0 0 1</color>
            </reflective>
            <transparent opaque="A_ONE">
              <color>1 1 1 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </blinn>
          <extra>
            <technique profile="OpenCOLLADA3dsMax">
              <specularLevel>
                <texture texcoord="CHANNEL1" texture="cone_01_s_tga-sampler" />
              </specularLevel>
              <bump bumptype="HEIGHTFIELD">
                <texture texcoord="CHANNEL1" texture="cone_01_n_tga-sampler" />
              </bump>
            </technique>
          </extra>
        </technique>
      </profile_COMMON>
      <extra>
        <technique profile="OpenCOLLADA3dsMax">
          <extended_shader>
            <apply_reflection_dimming>0</apply_reflection_dimming>
            <dim_level>0</dim_level>
            <falloff_type>0</falloff_type>
            <index_of_refraction>1.5</index_of_refraction>
            <opacity_type>0</opacity_type>
            <reflection_level>3</reflection_level>
            <wire_size>1</wire_size>
            <wire_units>0</wire_units>
          </extended_shader>
          <shader>
            <ambient_diffuse_lock>1</ambient_diffuse_lock>
            <ambient_diffuse_texture_lock>1</ambient_diffuse_texture_lock>
            <diffuse_specular_lock>0</diffuse_specular_lock>
            <soften>0.1</soften>
            <use_self_illum_color>0</use_self_illum_color>
          </shader>
        </technique>
      </extra>
    </effect>
  </library_effects>
  <library_materials>
    <material id="cone_01a-material" name="cone_01a">
      <instance_effect url="#cone_01a" />
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="geom-cone_01a" name="cone_01a">
      <mesh>
        <source id="geom-cone_01a-positions">
          <float_array count="138" id="geom-cone_01a-positions-array">12.22177 -3.13711e-7 2.179896 10.58436 6.110883 2.179896 6.110885 10.58436 2.179896 8.75451e-7 12.22177 2.179896 -6.110883 10.58436 2.179896 -10.58436 6.110884 2.179896 -12.22177 1.53172e-6 2.179896 -10.58436 -6.110882 2.179896 -6.110887 -10.58436 2.179896 -4.27237e-6 -12.22177 2.179896 6.110879 -10.58436 2.179896 10.58436 -6.110891 2.179896 2.070825 -3.37234e-7 47.1799 1.035412 1.793387 47.1799 -1.035413 1.793387 47.1799 -2.070826 -2.45479e-8 47.1799 -1.035414 -1.793387 47.1799 1.035411 -1.793388 47.1799 -14.68893 -12.78121 2.179896 12.39868 -15.07146 2.179896 -12.39868 15.07146 2.179896 14.68893 12.78121 2.179896 -12.39868 -15.07146 2.179896 14.68893 -12.78121 2.179896 -14.68893 12.78121 2.179896 12.39868 15.07146 2.179896 -12.39868 15.07146 0 -14.68893 12.78121 0 -14.68893 -12.78121 0 -12.39868 -15.07146 0 12.39868 -15.07146 0 14.68893 -12.78121 0 14.68893 12.78121 0 12.39868 15.07146 0 7.146297 -3.25472e-7 24.6799 3.573149 6.188875 24.6799 -3.573148 6.188874 24.6799 -7.146296 7.53588e-7 24.6799 -3.57315 -6.188874 24.6799 3.573146 -6.188876 24.6799 6.06874 3.503788 24.6799 3.27826e-7 7.007577 24.6799 -6.06874 3.503788 24.6799 -6.068741 -3.503788 24.6799 -2.65241e-6 -7.007578 24.6799 6.068738 -3.503793 24.6799</float_array>
          <technique_common>
            <accessor count="46" source="#geom-cone_01a-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-cone_01a-normals">
          <float_array count="204" id="geom-cone_01a-normals-array">0.9749058 0.001694477 0.2226117 0.8450238 0.4859186 0.2231995 0.8458111 0.4864306 0.2190637 0.9749978 0.001685269 0.2222082 -6.61326e-8 0.975355 0.2206414 -4.85684e-8 0.9752358 0.2211674 0.4872524 0.8439459 0.2243664 0.4877447 0.8447984 0.2200475 -0.8446822 0.4876774 0.2206414 -0.844579 0.4876179 0.2211675 -0.4872524 0.843946 0.2243664 -0.4877446 0.8447984 0.2200474 -0.8446823 -0.4876772 0.2206414 -0.8445792 -0.4876176 0.2211675 -0.9745048 1.38519e-7 0.2243664 -0.9754891 7.12191e-8 0.2200474 -3.77719e-7 -0.975355 0.2206414 -3.30266e-7 -0.9752358 0.2211675 -0.4872527 -0.8439458 0.2243664 -0.4877448 -0.8447982 0.2200474 0.8446819 -0.487678 0.2206414 0.8445789 -0.4876183 0.2211675 0.4872521 -0.8439462 0.2243664 0.4877442 -0.8447986 0.2200474 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 -0.9238794 0.3826836 0 -0.3826832 0.9238796 0 -0.3826832 0.9238796 0 -0.9238794 0.3826836 0 0.3826832 0.9238796 0 0.3826832 0.9238796 0 0.9238794 0.3826836 0 0.9238794 0.3826836 0 0.9238794 -0.3826836 0 0.9238794 -0.3826836 0 0.3826832 -0.9238796 0 0.3826832 -0.9238796 0 -0.3826832 -0.9238796 0 -0.3826832 -0.9238796 0 -0.9238794 -0.3826836 0 -0.9238794 -0.3826836 0 0 0 1 0 0 1 0 0 -1 0 0 -1 0 0 1 0 0 1 0 0 -1 0 0 -1 0.967053 -1.89424e-7 0.254575 0.4835265 0.8374925 0.254575 -0.4835264 0.8374925 0.254575 -0.967053 5.01785e-8 0.254575 -0.4835267 -0.8374923 0.254575 0.4835262 -0.8374927 0.254575</float_array>
          <technique_common>
            <accessor count="68" source="#geom-cone_01a-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-cone_01a-map-1">
          <float_array count="204" id="geom-cone_01a-map-1-array">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor count="68" source="#geom-cone_01a-map-1-array" stride="3">
              <param name="R" type="float" />
              <param name="G" type="float" />
              <param name="B" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-cone_01a-map0">
          <float_array count="204" id="geom-cone_01a-map0-array">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor count="68" source="#geom-cone_01a-map0-array" stride="3">
              <param name="R" type="float" />
              <param name="G" type="float" />
              <param name="B" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="geom-cone_01a-map1">
          <float_array count="213" id="geom-cone_01a-map1-array">0.8308912 0.4134514 12.22177 0.9131004 0.4134514 12.22177 0.8308912 0.992089 2.070823 0.008030912 0.992089 2.070824 0.008030912 0.4134514 12.22177 0.09031574 0.4134514 12.22177 0.1726023 0.992089 2.070826 0.1726023 0.4134514 12.22177 0.2548887 0.4134514 12.22177 0.3371753 0.992089 2.070827 0.3371753 0.4134514 12.22177 0.4194601 0.4134514 12.22177 0.5017466 0.992089 2.070826 0.5017466 0.4134514 12.22177 0.5840331 0.4134514 12.22177 0.6663181 0.992089 2.070824 0.6663198 0.4134514 12.22177 0.7486047 0.4134514 12.22177 0.9953869 0.992089 2.070824 0.9953094 0.4134514 12.22177 0.9364207 0.1960385 0 0.8831648 0.1960385 0 0.8964788 0.172978 0 0.9231066 0.172978 0 0.9231066 0.2190975 0 0.8964788 0.2190975 0 0.4856389 0.3711937 0 0.4598674 0.3454222 0 0.7904519 0.3454222 0 0.7646793 0.3711937 0 0.4583628 0.3953884 0 0.7909651 0.3953884 0 0.8216832 0.3657601 0 0.4276433 0.3657601 0 0.7904519 0.05777186 0 0.8216832 0.03506022 0 0.7646793 0.03200046 0 0.7909651 0.005430482 0 0.4856389 0.03200046 0 0.4583628 0.005430482 0 0.4598674 0.05777186 0 0.4276433 0.03506022 0 0.5090464 0.3422976 0 0.7418199 0.3422976 0 0.7633187 0.320799 0 0.4875479 0.320799 0 0.7633187 0.08084402 0 0.7418199 0.05934539 0 0.5090464 0.05934539 0 0.4875479 0.08084402 0 0.03765562 0.3921616 0 0.008206517 0.3627109 0 0.385964 0.3627109 0 0.3565148 0.3921616 0 0.385964 0.03401442 0 0.3565148 0.004565299 0 0.03765562 0.004565299 0 0.008206517 0.03401442 0 0.8308912 0.7027702 7.146295 0.9953482 0.7027702 7.146297 0.008030912 0.7027702 7.146297 0.1726023 0.7027702 7.146298 0.3371753 0.7027702 7.146298 0.5017466 0.7027702 7.146298 0.666319 0.7027702 7.146296 0.9131197 0.7027701 7.146295 0.09031616 0.7027701 7.146296 0.2548887 0.7027701 7.146298 0.4194605 0.7027701 7.146298 0.5840327 0.7027702 7.146297 0.7486047 0.7027701 7.146295</float_array>
          <technique_common>
            <accessor count="71" source="#geom-cone_01a-map1-array" stride="3">
              <param name="S" type="float" />
              <param name="T" type="float" />
              <param name="P" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="geom-cone_01a-vertices">
          <input semantic="POSITION" source="#geom-cone_01a-positions" />
        </vertices>
        <triangles count="74" material="cone_01a">
          <input offset="0" semantic="VERTEX" source="#geom-cone_01a-vertices" />
          <input offset="1" semantic="NORMAL" source="#geom-cone_01a-normals" />
          <input offset="2" semantic="COLOR" source="#geom-cone_01a-map-1" />
          <input offset="3" semantic="COLOR" set="0" source="#geom-cone_01a-map0" />
          <input offset="4" semantic="TEXCOORD" set="0" source="#geom-cone_01a-map1" />
          <p>0 0 0 0 0 1 1 1 1 1 40 2 62 62 65 40 2 62 62 65 34 3 56 56 58 0 0 0 0 0 3 4 3 3 5 41 5 63 63 66 35 6 57 57 60 35 6 57 57 60 2 7 2 2 4 3 4 3 3 5 5 8 5 5 8 42 9 64 64 67 36 10 58 58 61 36 10 58 58 61 4 11 4 4 7 5 8 5 5 8 7 12 7 7 11 43 13 65 65 68 37 14 59 59 62 37 14 59 59 62 6 15 6 6 10 7 12 7 7 11 9 16 9 9 14 44 17 66 66 69 38 18 60 60 63 38 18 60 60 63 8 19 8 8 13 9 16 9 9 14 11 20 11 11 17 45 21 67 67 70 39 22 61 61 64 39 22 61 61 64 10 23 10 10 16 11 20 11 11 17 15 24 21 21 21 16 25 22 22 22 17 26 23 23 23 17 26 23 23 23 12 27 18 18 20 15 24 21 21 21 1 1 1 1 1 2 7 2 2 19 35 6 57 57 59 35 6 57 57 59 40 2 62 62 65 1 1 1 1 1 3 4 3 3 5 4 11 4 4 7 36 10 58 58 61 36 10 58 58 61 41 5 63 63 66 3 4 3 3 5 5 8 5 5 8 6 15 6 6 10 37 14 59 59 62 37 14 59 59 62 42 9 64 64 67 5 8 5 5 8 7 12 7 7 11 8 19 8 8 13 38 18 60 60 63 38 18 60 60 63 43 13 65 65 68 7 12 7 7 11 9 16 9 9 14 10 23 10 10 16 39 22 61 61 64 39 22 61 61 64 44 17 66 66 69 9 16 9 9 14 11 20 11 11 17 0 0 0 0 0 34 3 56 56 58 34 3 56 56 58 45 21 67 67 70 11 20 11 11 17 13 28 19 19 24 14 29 20 20 25 15 24 21 21 21 15 24 21 21 21 12 27 18 18 20 13 28 19 19 24 24 30 30 30 51 21 31 27 27 52 25 32 31 31 53 25 32 31 31 53 20 33 26 26 50 24 30 30 30 51 33 34 39 39 43 32 35 38 38 44 27 36 33 33 45 27 36 33 33 45 26 37 32 32 42 33 34 39 39 43 24 38 46 46 27 20 39 42 42 26 26 40 48 48 30 26 40 48 48 30 27 41 49 49 33 24 38 46 46 27 20 39 42 42 26 25 42 47 47 29 33 43 55 55 31 33 43 55 55 31 26 40 48 48 30 20 39 42 42 26 25 42 47 47 29 21 44 43 43 28 32 45 54 54 32 32 45 54 54 32 33 43 55 55 31 25 42 47 47 29 21 44 43 43 28 23 46 45 45 34 31 47 53 53 35 31 47 53 53 35 32 45 54 54 32 21 44 43 43 28 23 46 45 45 34 19 48 41 41 36 30 49 52 52 37 30 49 52 52 37 31 47 53 53 35 23 46 45 45 34 19 48 41 41 36 22 50 44 44 38 29 51 51 51 39 29 51 51 51 39 30 49 52 52 37 19 48 41 41 36 22 50 44 44 38 18 52 40 40 40 28 53 50 50 41 28 53 50 50 41 29 51 51 51 39 22 50 44 44 38 18 52 40 40 40 24 38 46 46 27 27 41 49 49 33 27 41 49 49 33 28 53 50 50 41 18 52 40 40 40 18 54 24 24 57 23 55 29 29 54 21 31 27 27 52 21 31 27 27 52 24 30 30 30 51 18 54 24 24 57 31 56 37 37 46 28 57 34 34 49 27 36 33 33 45 27 36 33 33 45 32 35 38 38 44 31 56 37 37 46 22 58 28 28 56 19 59 25 25 55 23 55 29 29 54 23 55 29 29 54 18 54 24 24 57 22 58 28 28 56 30 60 36 36 47 29 61 35 35 48 28 57 34 34 49 28 57 34 34 49 31 56 37 37 46 30 60 36 36 47 40 2 62 62 65 12 62 12 12 2 34 3 56 56 58 40 2 62 62 65 13 63 13 13 18 12 62 12 12 2 41 5 63 63 66 13 63 13 13 3 35 6 57 57 60 41 5 63 63 66 14 64 14 14 6 13 63 13 13 3 42 9 64 64 67 14 64 14 14 6 36 10 58 58 61 42 9 64 64 67 15 65 15 15 9 14 64 14 14 6 43 13 65 65 68 15 65 15 15 9 37 14 59 59 62 43 13 65 65 68 16 66 16 16 12 15 65 15 15 9 44 17 66 66 69 16 66 16 16 12 38 18 60 60 63 44 17 66 66 69 17 67 17 17 15 16 66 16 16 12 45 21 67 67 70 17 67 17 17 15 39 22 61 61 64 45 21 67 67 70 12 62 12 12 2 17 67 17 17 15 35 6 57 57 59 13 63 13 13 18 40 2 62 62 65 36 10 58 58 61 14 64 14 14 6 41 5 63 63 66 37 14 59 59 62 15 65 15 15 9 42 9 64 64 67 38 18 60 60 63 16 66 16 16 12 43 13 65 65 68 39 22 61 61 64 17 67 17 17 15 44 17 66 66 69 34 3 56 56 58 12 62 12 12 2 45 21 67 67 70</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_lights>
    <light id="EnvironmentAmbientLight" name="EnvironmentAmbientLight">
      <technique_common>
        <ambient>
          <color>0 0 0</color>
        </ambient>
      </technique_common>
    </light>
  </library_lights>
  <library_visual_scenes>
    <visual_scene id="MaxScene">
      <node name="EnvironmentAmbientLight">
        <instance_light url="#EnvironmentAmbientLight" />
      </node>
      <node id="node-cone_01a" name="cone_01a">
        <instance_geometry url="#geom-cone_01a">
          <bind_material>
            <technique_common>
              <instance_material symbol="cone_01a" target="#cone_01a-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#MaxScene" />
  </scene>
</COLLADA>