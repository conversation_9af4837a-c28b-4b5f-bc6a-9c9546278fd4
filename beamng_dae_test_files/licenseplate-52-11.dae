<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 3.6.9 commit date:2024-02-19, commit time:16:48, hash:e958717a0c25</authoring_tool>
    </contributor>
    <created>2025-02-17T16:27:10</created>
    <modified>2025-02-17T16:27:10</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="licenseplate-52-11-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.7352942 0.7352942 0.7352942 1</color>
            </diffuse>
            <reflectivity>
              <float sid="specular">50</float>
            </reflectivity>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images/>
  <library_materials>
    <material id="licenseplate-52-11-material" name="licenseplate-52-11">
      <instance_effect url="#licenseplate-52-11-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="licenseplate-default_006-mesh" name="licenseplate-default.006">
      <mesh>
        <source id="licenseplate-default_006-mesh-positions">
          <float_array id="licenseplate-default_006-mesh-positions-array" count="204">-0.2515841 0.03386336 -0.04921054 -0.2515841 0.0338633 0.04921054 -0.2437807 0.03189331 -0.05499994 -0.2437807 0.03189325 0.05499982 0 0 -0.055 0 0 0.05499994 -0.08370035 0.00414443 -0.055 -0.1655922 0.01503306 -0.05499994 -0.08370035 0.00414443 0.05500006 -0.1655922 0.01503306 0.05499982 -0.2482454 0.03302043 0.05359327 -0.2482454 0.03302043 -0.05359321 0.2515842 0.0338633 -0.04921001 0.2515847 0.03386342 0.04920935 0.2437812 0.03189337 -0.05500817 0.2437809 0.03189325 0.05500239 0 0 -0.055 0 0 0.05499994 0.08370035 0.00414443 -0.05500954 0.1655923 0.01503306 -0.05500298 0.08370053 0.00414443 0.05499649 0.1655925 0.01503312 0.05499649 0.248246 0.03302055 0.05359768 0.2482455 0.03302043 -0.0535984 -0.2515841 0.03386336 -0.04921054 -0.2515841 0.0338633 0.04921054 -0.2437807 0.03189331 -0.05499994 -0.2437807 0.03189325 0.05499982 0 0 -0.055 0 0 0.05499994 -0.08370035 0.00414443 -0.055 -0.1655922 0.01503306 -0.05499994 -0.08370035 0.00414443 0.05500006 -0.1655922 0.01503306 0.05499982 -0.2482454 0.03302043 0.05359327 -0.2482454 0.03302043 -0.05359321 0.2515842 0.0338633 -0.04921001 0.2515847 0.03386342 0.04920935 0.2437812 0.03189337 -0.05500817 0.2437809 0.03189325 0.05500239 0.08370035 0.00414443 -0.05500954 0.1655923 0.01503306 -0.05500298 0.08370053 0.00414443 0.05499649 0.1655925 0.01503312 0.05499649 0.248246 0.03302055 0.05359768 0.2482455 0.03302043 -0.0535984 -0.2515883 0.03386437 -0.04921042 -0.2515883 0.03386437 0.04921066 -0.243785 0.03189432 -0.05499988 -0.243785 0.03189426 0.05499994 8.61192e-7 1.19209e-7 -0.05499988 8.68584e-7 0 0.05499994 -0.08370119 0.00414443 -0.055 -0.1655948 0.01503342 -0.05499994 -0.08370119 0.004144489 0.05499994 -0.1655948 0.01503348 0.05499994 -0.2482496 0.03302145 0.05359315 -0.2482496 0.0330215 -0.05359303 0.2515886 0.03386443 -0.04920876 0.2515879 0.03386425 0.04921585 0.2437853 0.03189438 -0.05500817 0.2437847 0.03189426 0.05500888 0.08370131 0.00414443 -0.05500817 0.1655947 0.01503342 -0.05500304 0.08370125 0.00414443 0.05500233 0.1655949 0.01503342 0.05499649 0.2482504 0.03302168 0.05360364 0.2482491 0.03302133 -0.05358666</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_006-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_006-mesh-normals">
          <float_array id="licenseplate-default_006-mesh-normals-array" count="144">-0.2260515 -0.9741154 -1.40658e-7 -0.2447814 -0.9695783 -5.13556e-7 -0.2260468 -0.9741165 -5.23903e-7 -0.1714348 -0.9851955 -1.58838e-7 -0.1714345 -0.9851955 0 -0.04945415 -0.9987764 -2.27908e-7 -0.09070634 -0.9958778 -1.64761e-7 -0.04945379 -0.9987765 -5.39877e-7 -0.09070652 -0.9958778 0 -0.2447793 -0.9695789 0 -0.2447956 -0.9695748 -5.58196e-7 0.2447789 -0.9695789 -3.07018e-7 0.2260539 -0.9741148 -2.90356e-7 0.2260436 -0.9741172 -2.45735e-7 0.1714358 -0.9851953 0 0.04945397 -0.9987764 -2.69062e-7 0.09070605 -0.9958778 -1.22911e-7 0.04945433 -0.9987764 -5.43033e-7 0.1714347 -0.9851955 0 0.2447802 -0.9695786 0 0.2447856 -0.9695773 0 -5.9007e-7 -1 -3.8594e-7 1.90665e-6 -1 -4.0496e-7 0.2447857 0.9695772 5.26515e-7 0.2260532 0.974115 5.29775e-7 0.2260538 0.9741148 1.64588e-7 0.1714371 0.9851952 -2.33554e-7 -1.87342e-6 1 0 0.09070652 0.9958778 -2.27587e-7 -6.03117e-7 1 5.42114e-7 0.1714375 0.9851951 -5.48389e-7 0.2447779 0.9695792 5.4451e-7 0.2447896 0.9695762 3.9848e-7 -0.2260553 0.9741145 -1.76526e-7 -0.2447807 0.9695785 0 -0.2260305 0.9741203 0 -0.1714386 0.9851949 0 -0.1714376 0.9851951 2.81119e-7 -0.09070616 0.9958778 0 -0.09070718 0.9958777 0 -0.2447744 0.9695801 -3.88637e-7 -0.2447862 0.9695771 0 -0.2447851 -0.9695774 0 0.09070652 -0.9958777 -1.40176e-7 0.2447825 -0.969578 0 0.09070611 0.9958778 -5.31229e-7 0.244794 0.9695752 0 -0.2447816 0.9695783 -4.26796e-7</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_006-mesh-normals-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_006-mesh-map-0">
          <float_array id="licenseplate-default_006-mesh-map-0-array" count="360">0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 1 0.9840519 0 0.9840519 1 0.5 0 0.6613504 1 0.5 1 0.6613504 1 0.822701 0 0.822701 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 1 0.9840519 0 0.9840519 1 0.5000001 0 0.6613504 1 0.5000001 1 0.6613504 1 0.822701 0 0.822701 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 6.82312e-6 1.12867e-5 1.59479e-5 9.99987e-4 1.59479e-5 0 1.77303e-4 9.99987e-4 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 0 3.38656e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 9.99987e-4 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 9.99987e-4 9.93196e-4 1.12867e-5 9.84069e-4 0 8.22704e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 6.6134e-4 9.99987e-4 5.00021e-4 0 5.00021e-4 9.99987e-4 8.22704e-4 9.99987e-4 6.6134e-4 0 6.6134e-4 9.99987e-4 9.93196e-4 1.12867e-5 0.000999987 9.47356e-4 0.000999987 5.26309e-5 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 1 0.822701 0 0.9840519 0 0.5 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 1 0.822701 0 0.9840519 0 0.5000001 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 1 0.05263155 1 0.9473694 0.9931765 0.988714 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 1.77303e-4 0 1.59479e-5 0 5.00021e-4 0 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 0 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 8.22704e-4 9.99987e-4 9.84069e-4 9.99987e-4 9.84069e-4 0 6.6134e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 8.22704e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 1.12867e-5 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_006-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_006-mesh-map-1">
          <float_array id="licenseplate-default_006-mesh-map-1-array" count="360">0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 1 0.9840519 0 0.9840519 1 0.5 0 0.6613504 1 0.5 1 0.6613504 1 0.822701 0 0.822701 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 1 0.9840519 0 0.9840519 1 0.5000001 0 0.6613504 1 0.5000001 1 0.6613504 1 0.822701 0 0.822701 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.006823301 0.01128667 0.01594793 0.9999871 0.01594793 0 0.177294 0.9999871 0.01594793 0 0.01594793 0.9999871 0.4999936 0 0.3386438 0.9999871 0.4999936 0.9999871 0.3386438 0.9999871 0.177294 0 0.177294 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0.9999871 0.9931922 0.01128667 0.9840727 0 0.8227229 0.9999871 0.9840727 0 0.8227229 0 0.6613731 0.9999871 0.4999936 0 0.4999936 0.9999871 0.8227229 0.9999871 0.6613731 0 0.6613731 0.9999871 0.9931922 0.01128667 0.9999871 0.9473562 0.9999871 0.0526309 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 1 0.822701 0 0.9840519 0 0.5 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 1 0.822701 0 0.9840519 0 0.5000001 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.006823301 0.01128667 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0.9999871 0.177294 0 0.01594793 0 0.4999936 0 0.3386438 0 0.3386438 0.9999871 0.3386438 0.9999871 0.3386438 0 0.177294 0 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0.9999871 0.9931922 0.9887219 0.9931922 0.01128667 0.8227229 0.9999871 0.9840727 0.9999871 0.9840727 0 0.6613731 0.9999871 0.6613731 0 0.4999936 0 0.8227229 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.01128667 0.9931922 0.9887219 0.9999871 0.9473562</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_006-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_006-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default_006-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_006-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_006-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default_006-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_006-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_006-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default_006-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_006-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default_006-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default_006-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default_006-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default_006-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_006-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_006-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_006-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default_006-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_006-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>3 0 0 0 11 1 1 1 2 2 2 2 3 0 3 3 7 3 4 4 9 4 5 5 5 5 6 6 6 6 7 7 4 7 8 8 9 4 9 9 6 6 10 10 8 8 11 11 11 1 12 12 1 9 13 13 0 10 14 14 23 11 15 15 15 12 16 16 14 13 17 17 21 14 18 18 14 13 19 19 15 12 20 20 16 15 21 21 20 16 22 22 17 17 23 23 20 16 24 24 19 18 25 25 21 14 26 26 12 19 27 27 22 20 28 28 23 11 29 29 27 0 30 30 35 1 31 31 26 2 32 32 27 0 33 33 31 3 34 34 33 4 35 35 29 21 36 36 30 6 37 37 28 22 38 38 33 4 39 39 30 6 40 40 32 8 41 41 35 1 42 42 25 9 43 43 24 10 44 44 45 11 45 45 39 12 46 46 38 13 47 47 43 14 48 48 38 13 49 49 39 12 50 50 28 22 51 51 42 16 52 52 29 21 53 53 42 16 54 54 41 18 55 55 43 14 56 56 36 19 57 57 44 20 58 58 45 11 59 59 57 23 60 60 49 24 61 61 48 25 62 62 55 26 63 63 48 25 64 64 49 24 65 65 50 27 66 66 54 28 67 67 51 29 68 68 54 28 69 69 53 30 70 70 55 26 71 71 46 31 72 72 56 32 73 73 57 23 74 74 61 33 75 75 67 34 76 76 60 35 77 77 65 36 78 78 60 35 79 79 63 37 80 80 64 38 81 81 50 27 82 82 51 29 83 83 65 36 84 84 62 39 85 85 64 38 86 86 67 34 87 87 59 40 88 88 58 41 89 89 3 0 90 90 10 42 91 91 11 1 92 92 3 0 93 93 2 2 94 94 7 3 95 95 5 5 96 96 8 8 97 97 6 6 98 98 9 4 99 99 7 3 100 100 6 6 101 101 11 1 102 102 10 42 103 103 1 9 104 104 23 11 105 105 22 20 106 106 15 12 107 107 21 14 108 108 19 18 109 109 14 13 110 110 16 15 111 111 18 43 112 112 20 16 113 113 20 16 114 114 18 43 115 115 19 18 116 116 12 19 117 117 13 44 118 118 22 20 119 119 27 0 120 120 34 42 121 121 35 1 122 122 27 0 123 123 26 2 124 124 31 3 125 125 29 21 126 126 32 8 127 127 30 6 128 128 33 4 129 129 31 3 130 130 30 6 131 131 35 1 132 132 34 42 133 133 25 9 134 134 45 11 135 135 44 20 136 136 39 12 137 137 43 14 138 138 41 18 139 139 38 13 140 140 28 22 141 141 40 43 142 142 42 16 143 143 42 16 144 144 40 43 145 145 41 18 146 146 36 19 147 147 37 44 148 148 44 20 149 149 57 23 150 150 56 32 151 151 49 24 152 152 55 26 153 153 53 30 154 154 48 25 155 155 50 27 156 156 52 45 157 157 54 28 158 158 54 28 159 159 52 45 160 160 53 30 161 161 46 31 162 162 47 46 163 163 56 32 164 164 61 33 165 165 66 47 166 166 67 34 167 167 65 36 168 168 61 33 169 169 60 35 170 170 64 38 171 171 62 39 172 172 50 27 173 173 65 36 174 174 63 37 175 175 62 39 176 176 67 34 177 177 66 47 178 178 59 40 179 179</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="licenseplate-default_005-mesh" name="licenseplate-default.005">
      <mesh>
        <source id="licenseplate-default_005-mesh-positions">
          <float_array id="licenseplate-default_005-mesh-positions-array" count="204">-0.2325797 0.05843305 -0.04921042 -0.2325797 0.05843305 0.0492106 -0.2258027 0.05520242 -0.05499988 -0.2258028 0.05520248 0.055 0 0 -0.05499988 0 0 0.055 -0.08279609 0.007516503 -0.05499994 -0.1583281 0.02669513 -0.05500054 -0.08279609 0.007516503 0.055 -0.1583278 0.02669501 0.05499643 -0.2296801 0.05705082 0.05359315 -0.2296801 0.05705082 -0.05359309 0.2325799 0.05843311 -0.04920744 0.23258 0.05843311 0.04920965 0.2258029 0.05520248 -0.05500262 0.2258029 0.05520248 0.05500262 0 0 -0.05499988 0 0 0.055 0.08279597 0.007516503 -0.05500054 0.1583279 0.02669507 -0.05499988 0.08279627 0.007516562 0.05500262 0.1583279 0.02669507 0.055 0.2296802 0.05705082 0.05359768 0.2296804 0.05705094 -0.05359494 -0.2325797 0.05843305 -0.04921042 -0.2325797 0.05843305 0.0492106 -0.2258027 0.05520242 -0.05499988 -0.2258028 0.05520248 0.055 0 0 -0.05499988 0 0 0.055 -0.08279609 0.007516503 -0.05499994 -0.1583281 0.02669513 -0.05500054 -0.08279609 0.007516503 0.055 -0.1583278 0.02669501 0.05499643 -0.2296801 0.05705082 0.05359315 -0.2296801 0.05705082 -0.05359309 0.2325799 0.05843311 -0.04920744 0.23258 0.05843311 0.04920965 0.2258029 0.05520248 -0.05500262 0.2258029 0.05520248 0.05500262 0.08279597 0.007516503 -0.05500054 0.1583279 0.02669507 -0.05499988 0.08279627 0.007516562 0.05500262 0.1583279 0.02669507 0.055 0.2296802 0.05705082 0.05359768 0.2296804 0.05705094 -0.05359494 -0.2325866 0.05843627 -0.04921042 -0.2325865 0.05843627 0.04921054 -0.2258096 0.0552057 -0.05499994 -0.2258096 0.0552057 0.055 8.68584e-7 0 -0.05499994 8.68584e-7 0 0.055 -0.08279871 0.00751692 -0.05499994 -0.1583337 0.02669715 -0.05500054 -0.08279871 0.00751692 0.055 -0.1583335 0.02669703 0.05499649 -0.229687 0.0570541 0.05359315 -0.229687 0.05705404 -0.05359309 0.2325865 0.05843621 -0.04920619 0.2325865 0.05843621 0.04921579 0.2258096 0.0552057 -0.05500054 0.2258097 0.05520576 0.05499649 0.08279883 0.00751692 -0.05499583 0.1583335 0.02669709 -0.05499994 0.08279865 0.00751686 0.05500262 0.1583335 0.02669709 0.05499994 0.2296873 0.05705416 0.05359154 0.2296873 0.05705416 -0.05359494</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_005-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_005-mesh-normals">
          <float_array id="licenseplate-default_005-mesh-normals-array" count="144">-0.4075439 -0.9131856 2.13378e-7 -0.4303114 -0.9026806 1.39001e-7 -0.430311 -0.9026808 2.53037e-7 -0.3185508 -0.9479058 0 -0.3185511 -0.9479058 0 -0.1687839 -0.9856532 0 -0.09041166 -0.9959046 0 -0.09041142 -0.9959045 2.67328e-7 -0.1687853 -0.9856529 0 -0.4303128 -0.9026799 2.50247e-7 0.4303084 -0.9026819 0 0.4075471 -0.9131842 0 0.4075424 -0.9131863 0 0.3185514 -0.9479057 0 0.3185508 -0.9479058 0 0.0904116 -0.9959045 3.03445e-7 0.1687848 -0.9856529 1.36086e-7 0.1687851 -0.985653 1.37645e-7 0.4303124 -0.90268 0 0.4303108 -0.9026809 0 1.61931e-7 -1 1.87634e-7 1.01563e-6 -1 2.8656e-7 0.4303088 0.9026818 0 0.4075496 0.9131832 0 0.4075466 0.9131844 0 0.3185601 0.9479027 1.26776e-7 5.66769e-7 1 0 0.1687892 0.9856522 0 -1.75027e-6 1 0 0.1687908 0.9856519 1.36489e-7 0.4303145 0.9026791 -2.48144e-7 0.4303103 0.9026811 -1.48595e-7 -0.4075484 0.9131836 0 -0.4303072 0.9026826 0 -0.407549 0.9131834 0 -0.3185617 0.9479022 -1.7954e-7 -0.3185584 0.9479032 -2.01916e-7 -0.1687917 0.9856518 0 -0.1687914 0.9856518 0 -0.4303051 0.9026836 0 -0.4303098 0.9026813 0 -0.4075447 -0.9131854 1.93499e-7 -0.4303108 -0.9026809 0 0.09041154 -0.9959045 2.67329e-7 0.4303025 -0.9026848 1.22349e-7 0.3185604 0.9479026 2.53557e-7 0.4303033 0.9026845 1.84122e-7 -0.4303086 0.9026818 0</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_005-mesh-normals-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_005-mesh-map-0">
          <float_array id="licenseplate-default_005-mesh-map-0-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.1772989 1 0.01594805 0 0.1772989 0 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 0 0.9840519 1 0.822701 1 0.5 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 0.9931765 0.01128661 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.1772989 1 0.01594805 0 0.1772989 0 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 0 0.9840519 1 0.822701 1 0.5000001 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 0.9931765 0.01128661 1 0.9473694 0.9931765 0.988714 6.82312e-6 1.12867e-5 1.59479e-5 9.99987e-4 1.59479e-5 0 1.77303e-4 9.99987e-4 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 0 3.38656e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 9.99987e-4 3.38656e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 9.99987e-4 9.93196e-4 1.12867e-5 9.84069e-4 0 9.84069e-4 9.99987e-4 8.22704e-4 0 8.22704e-4 9.99987e-4 5.00021e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 8.22704e-4 9.99987e-4 6.6134e-4 0 6.6134e-4 9.99987e-4 9.93196e-4 9.88722e-4 0.000999987 5.26309e-5 9.93196e-4 1.12867e-5 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.1772989 1 0.01594805 1 0.01594805 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 0 0.9840519 1 0.5 1 0.5 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 0.9931765 0.01128661 1 0.05263155 1 0.9473694 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.1772989 1 0.01594805 1 0.01594805 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 0 0.9840519 1 0.5000001 1 0.5000001 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 0.9931765 0.01128661 1 0.05263155 1 0.9473694 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 1.77303e-4 0 1.59479e-5 0 5.00021e-4 0 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 9.84069e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 5.00021e-4 9.99987e-4 6.6134e-4 9.99987e-4 6.6134e-4 0 8.22704e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4 0.000999987 5.26309e-5</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_005-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_005-mesh-map-1">
          <float_array id="licenseplate-default_005-mesh-map-1-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.1772989 1 0.01594805 0 0.1772989 0 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 0 0.9840519 1 0.822701 1 0.5 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 0.9931765 0.01128661 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.1772989 1 0.01594805 0 0.1772989 0 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 0 0.9840519 1 0.822701 1 0.5000001 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 0.9931765 0.01128661 1 0.9473694 0.9931765 0.988714 0.006823301 0.01128667 0.01594793 0.9999871 0.01594793 0 0.177294 0.9999871 0.01594793 0 0.01594793 0.9999871 0.4999936 0 0.3386438 0.9999871 0.4999936 0.9999871 0.3386438 0 0.177294 0.9999871 0.3386438 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0.9999871 0.9931922 0.01128667 0.9840727 0 0.9840727 0.9999871 0.8227229 0 0.8227229 0.9999871 0.4999936 0.9999871 0.6613731 0 0.4999936 0 0.8227229 0.9999871 0.6613731 0 0.6613731 0.9999871 0.9931922 0.9887219 0.9999871 0.0526309 0.9931922 0.01128667 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.1772989 1 0.01594805 1 0.01594805 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 0 0.9840519 1 0.5 1 0.5 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 0.9931765 0.01128661 1 0.05263155 1 0.9473694 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.1772989 1 0.01594805 1 0.01594805 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 0 0.9840519 1 0.5000001 1 0.5000001 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 0.9931765 0.01128661 1 0.05263155 1 0.9473694 0.006823301 0.01128667 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0.9999871 0.177294 0 0.01594793 0 0.4999936 0 0.3386438 0 0.3386438 0.9999871 0.3386438 0 0.177294 0 0.177294 0.9999871 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0.9999871 0.9931922 0.9887219 0.9931922 0.01128667 0.9840727 0.9999871 0.9840727 0 0.8227229 0 0.4999936 0.9999871 0.6613731 0.9999871 0.6613731 0 0.8227229 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.9887219 0.9999871 0.9473562 0.9999871 0.0526309</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_005-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_005-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default_005-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_005-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_005-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default_005-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_005-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_005-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default_005-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_005-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default_005-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default_005-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default_005-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default_005-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_005-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_005-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_005-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default_005-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_005-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>2 0 0 0 10 1 1 1 11 2 2 2 9 3 3 3 2 0 4 4 7 4 5 5 8 5 6 6 4 6 7 7 5 7 8 8 9 3 9 9 6 8 10 10 8 5 11 11 10 1 12 12 0 9 13 13 11 2 14 14 23 10 15 15 15 11 16 16 14 12 17 17 19 13 18 18 15 11 19 19 21 14 20 20 17 15 21 21 18 16 22 22 20 17 23 23 18 16 24 24 21 14 25 25 20 17 26 26 23 10 27 27 13 18 28 28 22 19 29 29 26 0 30 30 34 1 31 31 35 2 32 32 33 3 33 33 26 0 34 34 31 4 35 35 32 5 36 36 28 20 37 37 29 21 38 38 33 3 39 39 30 8 40 40 32 5 41 41 34 1 42 42 24 9 43 43 35 2 44 44 45 10 45 45 39 11 46 46 38 12 47 47 41 13 48 48 39 11 49 49 43 14 50 50 29 21 51 51 40 16 52 52 42 17 53 53 40 16 54 54 43 14 55 55 42 17 56 56 45 10 57 57 37 18 58 58 44 19 59 59 57 22 60 60 49 23 61 61 48 24 62 62 55 25 63 63 48 24 64 64 49 23 65 65 50 26 66 66 54 27 67 67 51 28 68 68 52 29 69 69 55 25 70 70 54 27 71 71 46 30 72 72 56 31 73 73 57 22 74 74 61 32 75 75 67 33 76 76 60 34 77 77 61 32 78 78 63 35 79 79 65 36 80 80 51 28 81 81 62 37 82 82 50 26 83 83 65 36 84 84 62 37 85 85 64 38 86 86 66 39 87 87 58 40 88 88 67 33 89 89 2 0 90 90 3 41 91 91 10 1 92 92 9 3 93 93 3 41 94 94 2 0 95 95 8 5 96 96 6 8 97 97 4 6 98 98 9 3 99 99 7 4 100 100 6 8 101 101 10 1 102 102 1 42 103 103 0 9 104 104 23 10 105 105 22 19 106 106 15 11 107 107 19 13 108 108 14 12 109 109 15 11 110 110 17 15 111 111 16 43 112 112 18 16 113 113 18 16 114 114 19 13 115 115 21 14 116 116 23 10 117 117 12 44 118 118 13 18 119 119 26 0 120 120 27 41 121 121 34 1 122 122 33 3 123 123 27 41 124 124 26 0 125 125 32 5 126 126 30 8 127 127 28 20 128 128 33 3 129 129 31 4 130 130 30 8 131 131 34 1 132 132 25 42 133 133 24 9 134 134 45 10 135 135 44 19 136 136 39 11 137 137 41 13 138 138 38 12 139 139 39 11 140 140 29 21 141 141 28 20 142 142 40 16 143 143 40 16 144 144 41 13 145 145 43 14 146 146 45 10 147 147 36 44 148 148 37 18 149 149 57 22 150 150 56 31 151 151 49 23 152 152 55 25 153 153 53 45 154 154 48 24 155 155 50 26 156 156 52 29 157 157 54 27 158 158 52 29 159 159 53 45 160 160 55 25 161 161 46 30 162 162 47 46 163 163 56 31 164 164 61 32 165 165 66 39 166 166 67 33 167 167 61 32 168 168 60 34 169 169 63 35 170 170 51 28 171 171 64 38 172 172 62 37 173 173 65 36 174 174 63 35 175 175 62 37 176 176 66 39 177 177 59 47 178 178 58 40 179 179</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="licenseplate-default_007-mesh" name="licenseplate-default.007">
      <mesh>
        <source id="licenseplate-default_007-mesh-positions">
          <float_array id="licenseplate-default_007-mesh-positions-array" count="204">-0.2565027 0.0237627 -0.04921054 -0.2565027 0.0237627 0.04921048 -0.2483884 0.02254962 -0.055 -0.2483884 0.02254962 0.05499994 0 1.19209e-7 -0.05499994 0 1.19209e-7 0.05499994 -0.08370035 0.00414443 -0.055 -0.1662924 0.0102756 -0.05500537 -0.08370035 0.00414443 0.05499994 -0.1662927 0.0102756 0.05500602 -0.253031 0.02324378 0.05359321 -0.253031 0.02324366 -0.05359303 0.2565029 0.0237627 -0.04919445 0.2565042 0.02376294 0.04921245 0.2483883 0.02254962 -0.05499714 0.2483897 0.02254974 0.05499643 0 1.19209e-7 -0.05499994 0 1.19209e-7 0.05499994 0.08370143 0.00414443 -0.05500537 0.1662924 0.01027572 -0.05499982 0.08370012 0.00414443 0.05501502 0.1662924 0.01027572 0.05499994 0.2530311 0.02324366 0.05358541 0.2530314 0.02324378 -0.05359554 -0.2565027 0.0237627 -0.04921054 -0.2565027 0.0237627 0.04921048 -0.2483884 0.02254962 -0.055 -0.2483884 0.02254962 0.05499994 0 1.19209e-7 -0.05499994 0 1.19209e-7 0.05499994 -0.08370035 0.00414443 -0.055 -0.1662924 0.0102756 -0.05500537 -0.08370035 0.00414443 0.05499994 -0.1662927 0.0102756 0.05500602 -0.253031 0.02324378 0.05359321 -0.253031 0.02324366 -0.05359303 0.2565029 0.0237627 -0.04919445 0.2565042 0.02376294 0.04921245 0.2483883 0.02254962 -0.05499714 0.2483897 0.02254974 0.05499643 0.08370143 0.00414443 -0.05500537 0.1662924 0.01027572 -0.05499982 0.08370012 0.00414443 0.05501502 0.1662924 0.01027572 0.05499994 0.2530311 0.02324366 0.05358541 0.2530314 0.02324378 -0.05359554 -0.2565053 0.02376317 -0.04921036 -0.2565053 0.02376317 0.04921048 -0.248391 0.02254998 -0.05499994 -0.248391 0.02254998 0.05499994 8.538e-7 1.19209e-7 -0.05499994 8.64888e-7 1.19209e-7 0.05499994 -0.08370119 0.00414443 -0.055 -0.1662948 0.01027584 -0.05500674 -0.08370119 0.00414443 0.05499976 -0.1662943 0.01027584 0.05499649 -0.2530335 0.02324414 0.05359303 -0.2530335 0.02324402 -0.05359303 0.2565055 0.02376317 -0.04919332 0.256506 0.02376317 0.04921245 0.2483901 0.02254986 -0.05499714 0.2483921 0.0225501 0.05498743 0.08370143 0.00414443 -0.05500537 0.166295 0.01027607 -0.055 0.08370119 0.00414443 0.05499708 0.166295 0.01027619 0.05499994 0.2530341 0.02324414 0.05359429 0.2530332 0.02324402 -0.05359554</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_007-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_007-mesh-normals">
          <float_array id="licenseplate-default_007-mesh-normals-array" count="144">-0.14787 -0.9890068 3.63926e-7 -0.1478523 -0.9890094 0 -0.1478579 -0.9890086 0 -0.1110244 -0.9938178 0 -0.1110245 -0.9938177 -2.61719e-7 -0.06174671 -0.9980919 0 -0.04945307 -0.9987766 0 -0.04945302 -0.9987766 0 -0.06174677 -0.9980919 0 -0.1478298 -0.9890129 1.10607e-6 -0.1478607 -0.9890082 0 0.1478627 -0.9890079 -5.44832e-7 0.1478633 -0.9890078 -5.24172e-7 0.1478616 -0.9890081 -8.32203e-7 0.1110216 -0.993818 0 0.04945284 -0.9987765 3.38992e-7 0.06174743 -0.9980919 2.95371e-7 0.06174707 -0.9980918 7.18618e-7 0.1478347 -0.9890121 -8.12132e-7 0.1478548 -0.9890092 -8.05561e-7 6.8859e-7 -1 0 2.75426e-6 -1 1.71294e-7 0.1478621 0.989008 -3.15581e-7 0.1478708 0.9890067 -4.89277e-7 0.147859 0.9890084 -1.65381e-7 0.1110255 0.9938176 -1.58585e-7 -1.48047e-6 1 0 0.06174683 0.9980919 0 0 1 0 0.06174719 0.9980918 0 0.1478898 0.9890039 -1.09812e-6 0.1478737 0.9890063 -8.42722e-7 -0.1478652 0.9890075 -1.36774e-7 -0.1478642 0.9890077 4.78573e-7 -0.1478629 0.9890079 5.88939e-7 -0.1110246 0.9938177 -3.20111e-7 -0.1110241 0.9938178 -1.0793e-6 -0.06174904 0.9980917 -3.76737e-7 -0.06174868 0.9980918 -1.39896e-7 -0.1478648 0.9890076 2.9222e-7 -0.1478691 0.989007 2.00188e-7 -0.1478644 -0.9890077 1.10503e-6 0.1110238 -0.9938178 0 0.04945242 -0.9987765 0 0.1478726 -0.9890064 4.21091e-7 0.1110257 0.9938176 -3.8556e-7 0.1478576 0.9890087 0 -0.1478524 0.9890094 6.88464e-7</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_007-mesh-normals-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_007-mesh-map-0">
          <float_array id="licenseplate-default_007-mesh-map-0-array" count="360">0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 1 0.9840519 0 0.9840519 1 0.5 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 1 0.9840519 0 0.9840519 1 0.5000001 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 6.82312e-6 1.12867e-5 1.59479e-5 9.99987e-4 1.59479e-5 0 1.77303e-4 9.99987e-4 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 0 3.38656e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 9.99987e-4 3.38656e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 9.99987e-4 9.93196e-4 1.12867e-5 9.84069e-4 0 9.84069e-4 9.99987e-4 8.22704e-4 0 8.22704e-4 9.99987e-4 6.6134e-4 9.99987e-4 5.00021e-4 0 5.00021e-4 9.99987e-4 6.6134e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 9.88722e-4 0.000999987 5.26309e-5 9.93196e-4 1.12867e-5 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 1 0.822701 0 0.9840519 0 0.5 1 0.5 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 1 0.822701 0 0.9840519 0 0.5000001 1 0.5000001 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 1.77303e-4 0 1.59479e-5 0 5.00021e-4 0 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 9.84069e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 6.6134e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 6.6134e-4 9.99987e-4 8.22704e-4 9.99987e-4 8.22704e-4 0 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4 0.000999987 5.26309e-5</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_007-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_007-mesh-map-1">
          <float_array id="licenseplate-default_007-mesh-map-1-array" count="360">0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 1 0.9840519 0 0.9840519 1 0.5 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 1 0.9840519 0 0.9840519 1 0.5000001 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.006823301 0.01128667 0.01594793 0.9999871 0.01594793 0 0.177294 0.9999871 0.01594793 0 0.01594793 0.9999871 0.4999936 0 0.3386438 0.9999871 0.4999936 0.9999871 0.3386438 0 0.177294 0.9999871 0.3386438 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0.9999871 0.9931922 0.01128667 0.9840727 0 0.9840727 0.9999871 0.8227229 0 0.8227229 0.9999871 0.6613731 0.9999871 0.4999936 0 0.4999936 0.9999871 0.6613731 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.9887219 0.9999871 0.0526309 0.9931922 0.01128667 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 1 0.822701 0 0.9840519 0 0.5 1 0.5 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 1 0.822701 0 0.9840519 0 0.5000001 1 0.5000001 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.006823301 0.01128667 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0.9999871 0.177294 0 0.01594793 0 0.4999936 0 0.3386438 0 0.3386438 0.9999871 0.3386438 0 0.177294 0 0.177294 0.9999871 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0.9999871 0.9931922 0.9887219 0.9931922 0.01128667 0.9840727 0.9999871 0.9840727 0 0.8227229 0 0.6613731 0.9999871 0.6613731 0 0.4999936 0 0.6613731 0.9999871 0.8227229 0.9999871 0.8227229 0 0.9931922 0.9887219 0.9999871 0.9473562 0.9999871 0.0526309</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_007-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_007-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default_007-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_007-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_007-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default_007-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_007-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_007-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default_007-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_007-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default_007-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default_007-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default_007-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default_007-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_007-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_007-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_007-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default_007-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_007-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>3 0 0 0 11 1 1 1 2 2 2 2 3 0 3 3 7 3 4 4 9 4 5 5 8 5 6 6 4 6 7 7 5 7 8 8 8 5 9 9 7 3 10 10 6 8 11 11 11 1 12 12 1 9 13 13 0 10 14 14 14 11 15 15 22 12 16 16 15 13 17 17 21 14 18 18 14 11 19 19 15 13 20 20 17 15 21 21 18 16 22 22 20 17 23 23 18 16 24 24 21 14 25 25 20 17 26 26 12 18 27 27 22 12 28 28 23 19 29 29 27 0 30 30 35 1 31 31 26 2 32 32 27 0 33 33 31 3 34 34 33 4 35 35 32 5 36 36 28 20 37 37 29 21 38 38 32 5 39 39 31 3 40 40 30 8 41 41 35 1 42 42 25 9 43 43 24 10 44 44 38 11 45 45 44 12 46 46 39 13 47 47 43 14 48 48 38 11 49 49 39 13 50 50 29 21 51 51 40 16 52 52 42 17 53 53 40 16 54 54 43 14 55 55 42 17 56 56 36 18 57 57 44 12 58 58 45 19 59 59 57 22 60 60 49 23 61 61 48 24 62 62 55 25 63 63 48 24 64 64 49 23 65 65 50 26 66 66 54 27 67 67 51 28 68 68 52 29 69 69 55 25 70 70 54 27 71 71 46 30 72 72 56 31 73 73 57 22 74 74 61 32 75 75 67 33 76 76 60 34 77 77 61 32 78 78 63 35 79 79 65 36 80 80 64 37 81 81 50 26 82 82 51 28 83 83 64 37 84 84 63 35 85 85 62 38 86 86 66 39 87 87 58 40 88 88 67 33 89 89 3 0 90 90 10 41 91 91 11 1 92 92 3 0 93 93 2 2 94 94 7 3 95 95 8 5 96 96 6 8 97 97 4 6 98 98 8 5 99 99 9 4 100 100 7 3 101 101 11 1 102 102 10 41 103 103 1 9 104 104 14 11 105 105 23 19 106 106 22 12 107 107 21 14 108 108 19 42 109 109 14 11 110 110 17 15 111 111 16 43 112 112 18 16 113 113 18 16 114 114 19 42 115 115 21 14 116 116 12 18 117 117 13 44 118 118 22 12 119 119 27 0 120 120 34 41 121 121 35 1 122 122 27 0 123 123 26 2 124 124 31 3 125 125 32 5 126 126 30 8 127 127 28 20 128 128 32 5 129 129 33 4 130 130 31 3 131 131 35 1 132 132 34 41 133 133 25 9 134 134 38 11 135 135 45 19 136 136 44 12 137 137 43 14 138 138 41 42 139 139 38 11 140 140 29 21 141 141 28 20 142 142 40 16 143 143 40 16 144 144 41 42 145 145 43 14 146 146 36 18 147 147 37 44 148 148 44 12 149 149 57 22 150 150 56 31 151 151 49 23 152 152 55 25 153 153 53 45 154 154 48 24 155 155 50 26 156 156 52 29 157 157 54 27 158 158 52 29 159 159 53 45 160 160 55 25 161 161 46 30 162 162 47 46 163 163 56 31 164 164 61 32 165 165 66 39 166 166 67 33 167 167 61 32 168 168 60 34 169 169 63 35 170 170 64 37 171 171 62 38 172 172 50 26 173 173 64 37 174 174 65 36 175 175 63 35 176 176 66 39 177 177 59 47 178 178 58 40 179 179</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="licenseplate-default_001-mesh" name="licenseplate-default.001">
      <mesh>
        <source id="licenseplate-default_001-mesh-positions">
          <float_array id="licenseplate-default_001-mesh-positions-array" count="204">-0.2592962 0.009618282 -0.04920971 -0.2592961 0.009618282 0.0492112 -0.2510483 0.009005069 -0.05499929 -0.251048 0.009005069 0.05499929 0 2.38419e-7 -0.05500054 0 2.38419e-7 0.05500072 -0.08385187 0.002074718 -0.05499929 -0.1677037 0.004149198 -0.05499994 -0.08385163 0.002074718 0.05499911 -0.1677037 0.004149198 0.05500006 -0.2557666 0.009355783 0.05359357 -0.2557672 0.009356021 -0.05359357 0.259296 0.009618282 -0.04921042 0.2592961 0.009618043 0.04921054 0.251048 0.009004831 -0.05499988 0.251048 0.009004831 0.05500006 0 2.38419e-7 -0.05500054 0 2.38419e-7 0.05500072 0.08385181 0.002074718 -0.05499988 0.1677035 0.004149436 -0.0550006 0.08385181 0.00207448 0.05499994 0.1677035 0.004149436 0.05500072 0.2557671 0.009355783 0.05359315 0.2557671 0.009355783 -0.05359309 -0.2592962 0.009618282 -0.04920971 -0.2592961 0.009618282 0.0492112 -0.2510483 0.009005069 -0.05499929 -0.251048 0.009005069 0.05499929 0 2.38419e-7 -0.05500054 0 2.38419e-7 0.05500072 -0.08385187 0.002074718 -0.05499929 -0.1677037 0.004149198 -0.05499994 -0.08385163 0.002074718 0.05499911 -0.1677037 0.004149198 0.05500006 -0.2557666 0.009355783 0.05359357 -0.2557672 0.009356021 -0.05359357 0.259296 0.009618282 -0.04921042 0.2592961 0.009618043 0.04921054 0.251048 0.009004831 -0.05499988 0.251048 0.009004831 0.05500006 0.08385181 0.002074718 -0.05499988 0.1677035 0.004149436 -0.0550006 0.08385181 0.00207448 0.05499994 0.1677035 0.004149436 0.05500072 0.2557671 0.009355783 0.05359315 0.2557671 0.009355783 -0.05359309 -0.2592973 0.009618282 -0.04920971 -0.2592973 0.009618282 0.04921114 -0.2510495 0.009005308 -0.05499935 -0.2510492 0.009005069 0.05499923 -3.05497e-7 2.38419e-7 -0.0550009 -3.05486e-7 2.38419e-7 0.05499929 -0.08385193 0.002074718 -0.05499899 -0.1677041 0.004149198 -0.05499988 -0.08385223 0.002074718 0.05499911 -0.1677041 0.004149198 0.05500006 -0.2557678 0.009356021 0.05359357 -0.2557685 0.009356021 -0.05359357 0.2592973 0.009618282 -0.04921042 0.2592973 0.009618282 0.04921054 0.2510493 0.009005069 -0.05499988 0.2510493 0.009005069 0.05500006 0.08385223 0.002074718 -0.05499994 0.1677043 0.004149436 -0.05500096 0.08385223 0.00207448 0.05500006 0.1677044 0.004149436 0.05499929 0.2557684 0.009356021 0.05359315 0.2557684 0.009355783 -0.05359309</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_001-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_001-mesh-normals">
          <float_array id="licenseplate-default_001-mesh-normals-array" count="144">-0.06535708 -0.9978619 -6.92534e-7 -0.07413417 -0.9972483 0 -0.0741536 -0.9972469 -1.79719e-6 -0.06533706 -0.9978632 1.22828e-7 -0.04145389 -0.9991405 0 -0.04145395 -0.9991405 0 -0.02473223 -0.9996941 0 -0.02473223 -0.9996941 0 -0.02473229 -0.9996942 0 -0.02473223 -0.9996941 0 -0.07411628 -0.9972496 -1.77675e-6 0.06535214 -0.9978623 0 0.07415008 -0.9972472 -6.20776e-7 0.06535238 -0.9978623 0 0.04145312 -0.9991406 -6.22022e-7 0.04145395 -0.9991405 0 0.02473104 -0.9996942 -1.26841e-6 0.02473366 -0.9996941 -8.93294e-7 0.02472937 -0.9996942 0 0.07417851 -0.997245 0 0.07416737 -0.9972459 0 -5.42672e-7 -1 -6.17779e-7 -1.37359e-6 -1 0 0.07411724 0.9972496 1.26918e-6 0.06535869 0.9978619 3.01366e-7 0.06533634 0.9978633 1.98673e-6 0.04145449 0.9991405 6.07571e-7 0.04145371 0.9991405 0 8.55812e-7 1 6.51582e-7 0.02473217 0.9996942 0 0.02473223 0.9996942 0 0.07411438 0.9972499 -4.44421e-7 0.07415258 0.997247 -3.31936e-7 -0.06533116 0.9978637 -9.63125e-7 -0.07415056 0.9972472 0 -0.07412916 0.9972487 -2.24116e-6 -0.04145455 0.9991405 6.44324e-7 -0.04145371 0.9991405 0 -0.02473354 0.9996942 9.01252e-7 0 1 0 -0.02473354 0.9996942 2.16861e-6 -0.07417631 0.9972451 -2.1941e-6 -0.07416892 -0.9972458 0 0.02473366 -0.9996941 -2.16557e-6 0.07410919 -0.9972501 -2.44425e-6 0.07410299 0.9972506 0 -0.06535404 0.9978622 0 -0.07411301 0.9972499 0</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_001-mesh-normals-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_001-mesh-map-0">
          <float_array id="licenseplate-default_001-mesh-map-0-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5 0 0.6613504 1 0.5 1 0.6613504 1 0.822701 0 0.822701 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5000001 0 0.6613504 1 0.5000001 1 0.6613504 1 0.822701 0 0.822701 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 6.82312e-6 1.12867e-5 1.59479e-5 9.99987e-4 1.59479e-5 0 1.77303e-4 0 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 9.99987e-4 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 0 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 8.22704e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 5.00021e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 8.22704e-4 9.99987e-4 6.6134e-4 0 6.6134e-4 9.99987e-4 9.93196e-4 9.88722e-4 0.000999987 5.26309e-5 9.93196e-4 1.12867e-5 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5000001 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 1 0.05263155 1 0.9473694 0.9931765 0.988714 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 0 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 9.99987e-4 5.00021e-4 0 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 0 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 0 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 8.22704e-4 9.99987e-4 9.84069e-4 9.99987e-4 9.84069e-4 0 5.00021e-4 9.99987e-4 6.6134e-4 9.99987e-4 6.6134e-4 0 8.22704e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4 0.000999987 5.26309e-5</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_001-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_001-mesh-map-1">
          <float_array id="licenseplate-default_001-mesh-map-1-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5 0 0.6613504 1 0.5 1 0.6613504 1 0.822701 0 0.822701 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5000001 0 0.6613504 1 0.5000001 1 0.6613504 1 0.822701 0 0.822701 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.006823301 0.01128667 0.01594793 0.9999871 0.01594793 0 0.177294 0 0.01594793 0.9999871 0.177294 0.9999871 0.4999936 0.9999871 0.3386438 0 0.3386438 0.9999871 0.3386438 0.9999871 0.177294 0 0.177294 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0 0.9931922 0.9887219 0.9931922 0.01128667 0.8227229 0.9999871 0.9840727 0 0.8227229 0 0.4999936 0.9999871 0.6613731 0 0.4999936 0 0.8227229 0.9999871 0.6613731 0 0.6613731 0.9999871 0.9931922 0.9887219 0.9999871 0.0526309 0.9931922 0.01128667 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5000001 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.006823301 0.01128667 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0 0.01594793 0 0.01594793 0.9999871 0.4999936 0.9999871 0.4999936 0 0.3386438 0 0.3386438 0.9999871 0.3386438 0 0.177294 0 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0 0.9840727 0.9999871 0.9931922 0.9887219 0.8227229 0.9999871 0.9840727 0.9999871 0.9840727 0 0.4999936 0.9999871 0.6613731 0.9999871 0.6613731 0 0.8227229 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.9887219 0.9999871 0.9473562 0.9999871 0.0526309</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_001-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_001-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default_001-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_001-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_001-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default_001-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_001-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_001-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default_001-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_001-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default_001-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default_001-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_001-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_001-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_001-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default_001-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_001-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>2 0 0 0 10 1 1 1 11 2 2 2 3 3 3 3 7 4 4 4 9 5 5 5 8 6 6 6 4 7 7 7 5 8 8 8 8 6 9 9 7 4 10 10 6 9 11 11 10 1 12 12 0 10 13 13 11 2 14 14 14 11 15 15 22 12 16 16 15 13 17 17 19 14 18 18 15 13 19 19 21 15 20 20 16 16 21 21 20 17 22 22 17 18 23 23 20 17 24 24 19 14 25 25 21 15 26 26 12 19 27 27 22 12 28 28 23 20 29 29 26 0 30 30 34 1 31 31 35 2 32 32 27 3 33 33 31 4 34 34 33 5 35 35 32 6 36 36 28 21 37 37 29 22 38 38 32 6 39 39 31 4 40 40 30 9 41 41 34 1 42 42 24 10 43 43 35 2 44 44 38 11 45 45 44 12 46 46 39 13 47 47 41 14 48 48 39 13 49 49 43 15 50 50 28 21 51 51 42 17 52 52 29 22 53 53 42 17 54 54 41 14 55 55 43 15 56 56 36 19 57 57 44 12 58 58 45 20 59 59 57 23 60 60 49 24 61 61 48 25 62 62 53 26 63 63 49 24 64 64 55 27 65 65 51 28 66 66 52 29 67 67 54 30 68 68 54 30 69 69 53 26 70 70 55 27 71 71 46 31 72 72 56 32 73 73 57 23 74 74 60 33 75 75 66 34 76 76 67 35 77 77 65 36 78 78 60 33 79 79 63 37 80 80 51 28 81 81 62 38 82 82 50 39 83 83 65 36 84 84 62 38 85 85 64 40 86 86 66 34 87 87 58 41 88 88 67 35 89 89 2 0 90 90 3 3 91 91 10 1 92 92 3 3 93 93 2 0 94 94 7 4 95 95 8 6 96 96 6 9 97 97 4 7 98 98 8 6 99 99 9 5 100 100 7 4 101 101 10 1 102 102 1 42 103 103 0 10 104 104 14 11 105 105 23 20 106 106 22 12 107 107 19 14 108 108 14 11 109 109 15 13 110 110 16 16 111 111 18 43 112 112 20 17 113 113 20 17 114 114 18 43 115 115 19 14 116 116 12 19 117 117 13 44 118 118 22 12 119 119 26 0 120 120 27 3 121 121 34 1 122 122 27 3 123 123 26 0 124 124 31 4 125 125 32 6 126 126 30 9 127 127 28 21 128 128 32 6 129 129 33 5 130 130 31 4 131 131 34 1 132 132 25 42 133 133 24 10 134 134 38 11 135 135 45 20 136 136 44 12 137 137 41 14 138 138 38 11 139 139 39 13 140 140 28 21 141 141 40 43 142 142 42 17 143 143 42 17 144 144 40 43 145 145 41 14 146 146 36 19 147 147 37 44 148 148 44 12 149 149 57 23 150 150 56 32 151 151 49 24 152 152 53 26 153 153 48 25 154 154 49 24 155 155 51 28 156 156 50 39 157 157 52 29 158 158 54 30 159 159 52 29 160 160 53 26 161 161 46 31 162 162 47 45 163 163 56 32 164 164 60 33 165 165 61 46 166 166 66 34 167 167 65 36 168 168 61 46 169 169 60 33 170 170 51 28 171 171 64 40 172 172 62 38 173 173 65 36 174 174 63 37 175 175 62 38 176 176 66 34 177 177 59 47 178 178 58 41 179 179</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="licenseplate-default_008-mesh" name="licenseplate-default.008">
      <mesh>
        <source id="licenseplate-default_008-mesh-positions">
          <float_array id="licenseplate-default_008-mesh-positions-array" count="204">-0.2572016 0.01900506 -0.04920256 -0.2572039 0.01900541 0.04919099 -0.2490867 0.01779186 -0.05499684 -0.2490875 0.01779198 0.05498409 0 1.19209e-7 -0.05499982 0 0 0.05500006 -0.08369958 0.00414431 -0.05499553 -0.1674007 0.00828886 -0.05500006 -0.08370286 0.004144549 0.05499672 -0.1674007 0.00828886 0.05499982 -0.2537331 0.01848649 0.05360984 -0.2537323 0.01848638 -0.05359768 0.2572029 0.01900541 -0.04921019 0.2572029 0.01900529 0.04921078 0.2490885 0.01779222 -0.05499994 0.2490885 0.01779222 0.0550003 0 1.19209e-7 -0.05499982 0 0 0.05500006 0.08370035 0.00414443 -0.05499982 0.167401 0.00828886 -0.05499684 0.08370035 0.00414443 0.05500006 0.1674013 0.00828886 0.05499619 0.2537311 0.01848638 0.05359292 0.2537311 0.01848626 -0.05359292 -0.2572016 0.01900506 -0.04920256 -0.2572039 0.01900541 0.04919099 -0.2490867 0.01779186 -0.05499684 -0.2490875 0.01779198 0.05498409 0 1.19209e-7 -0.05499982 0 0 0.05500006 -0.08369958 0.00414431 -0.05499553 -0.1674007 0.00828886 -0.05500006 -0.08370286 0.004144549 0.05499672 -0.1674007 0.00828886 0.05499982 -0.2537331 0.01848649 0.05360984 -0.2537323 0.01848638 -0.05359768 0.2572029 0.01900541 -0.04921019 0.2572029 0.01900529 0.04921078 0.2490885 0.01779222 -0.05499994 0.2490885 0.01779222 0.0550003 0.08370035 0.00414443 -0.05499982 0.167401 0.00828886 -0.05499684 0.08370035 0.00414443 0.05500006 0.1674013 0.00828886 0.05499619 0.2537311 0.01848638 0.05359292 0.2537311 0.01848626 -0.05359292 -0.2572055 0.01900565 -0.04920387 -0.2572047 0.01900553 0.04920327 -0.2490901 0.01779234 -0.05499553 -0.2490918 0.01779258 0.05498468 8.64888e-7 1.19209e-7 -0.05499994 8.57496e-7 1.19209e-7 0.05500006 -0.08370196 0.00414443 -0.05499553 -0.1674016 0.00828886 -0.05500006 -0.08370351 0.004144549 0.05500888 -0.1674016 0.00828886 0.05500006 -0.2537347 0.01848673 0.05359768 -0.2537338 0.01848661 -0.05359911 0.2572055 0.01900577 -0.04921054 0.2572055 0.01900577 0.0492103 0.2490911 0.01779258 -0.05499982 0.2490911 0.0177927 0.05499982 0.08370119 0.004144549 -0.05499994 0.1674022 0.00828886 -0.05499678 0.08370119 0.00414443 0.05500006 0.1674023 0.00828886 0.05498415 0.2537338 0.01848673 0.05359339 0.2537338 0.01848673 -0.05359315</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_008-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_008-mesh-normals">
          <float_array id="licenseplate-default_008-mesh-normals-array" count="144">-0.1301144 -0.991499 0 -0.1478586 -0.9890085 0 -0.1300837 -0.9915031 0 -0.08255136 -0.9965869 2.53071e-7 -0.08254939 -0.996587 0 -0.04945349 -0.9987765 0 -0.04945427 -0.9987765 1.66538e-7 -0.04945212 -0.9987766 -1.08608e-6 -0.04945468 -0.9987764 4.87593e-7 -0.1478638 -0.9890078 0 -0.147866 -0.9890074 0 0.1300719 -0.9915046 3.69212e-7 0.1478625 -0.989008 -2.34485e-7 0.1300832 -0.9915032 0 0.08255201 -0.9965868 0 0.04945361 -0.9987765 -4.4752e-7 0.04945403 -0.9987765 -3.16122e-7 0.04945445 -0.9987764 -1.07975e-6 0.04945367 -0.9987765 0 0.147889 -0.989004 1.08914e-6 0.1478598 -0.9890084 1.10502e-6 1.12196e-6 -1 -5.62909e-7 1.57432e-6 -1 -7.67734e-7 0.1478644 0.9890077 1.25832e-7 0.1301034 0.9915004 0 0.1300919 0.9915019 -1.66197e-7 0.08255225 0.9965868 0 0.08254921 0.9965871 0 1.08036e-6 1 0 0.04945325 0.9987765 -1.51091e-7 0.04945325 0.9987765 -3.7787e-7 0.14786 0.9890083 1.28607e-7 0.147866 0.9890075 0 -0.1300761 0.991504 -6.19383e-7 -0.1478713 0.9890066 -7.59459e-7 -0.1300852 0.9915028 -6.68225e-7 -0.08255314 0.9965867 0 -0.08255219 0.9965869 0 -0.04945385 0.9987765 7.58607e-7 -1.97562e-6 1 3.15584e-7 -0.04945385 0.9987765 7.81547e-7 -0.1478579 0.9890086 0 -0.1478636 0.9890078 0 -0.1478595 -0.9890084 0 0.08255177 -0.9965869 -2.5409e-7 0.1478259 -0.9890134 -1.22148e-6 0.1478633 0.9890079 0 -0.1478521 0.9890096 0</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_008-mesh-normals-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_008-mesh-map-0">
          <float_array id="licenseplate-default_008-mesh-map-0-array" count="360">0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 1 0.9840519 0 0.9840519 1 0.5 0 0.6613504 1 0.5 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 1 0.9840519 0 0.9840519 1 0.5000001 0 0.6613504 1 0.5000001 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 6.82312e-6 1.12867e-5 1.59479e-5 9.99987e-4 1.59479e-5 0 1.77303e-4 0 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 9.99987e-4 3.38656e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 9.99987e-4 9.93196e-4 1.12867e-5 9.84069e-4 0 8.22704e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 6.6134e-4 9.99987e-4 5.00021e-4 0 5.00021e-4 9.99987e-4 8.22704e-4 9.99987e-4 6.6134e-4 0 6.6134e-4 9.99987e-4 9.93196e-4 1.12867e-5 0.000999987 9.47356e-4 0.000999987 5.26309e-5 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 1 0.822701 0 0.9840519 0 0.5 0 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 1 0.822701 0 0.9840519 0 0.5000001 0 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 0 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 9.99987e-4 5.00021e-4 0 3.38656e-4 0 3.38656e-4 0 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 8.22704e-4 9.99987e-4 9.84069e-4 9.99987e-4 9.84069e-4 0 6.6134e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 8.22704e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 1.12867e-5 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_008-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_008-mesh-map-1">
          <float_array id="licenseplate-default_008-mesh-map-1-array" count="360">0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 1 0.9840519 0 0.9840519 1 0.5 0 0.6613504 1 0.5 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.3386496 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 1 0.9840519 0 0.9840519 1 0.5000001 0 0.6613504 1 0.5000001 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.006823301 0.01128667 0.01594793 0.9999871 0.01594793 0 0.177294 0 0.01594793 0.9999871 0.177294 0.9999871 0.4999936 0.9999871 0.3386438 0 0.3386438 0.9999871 0.3386438 0 0.177294 0.9999871 0.3386438 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0.9999871 0.9931922 0.01128667 0.9840727 0 0.8227229 0.9999871 0.9840727 0 0.8227229 0 0.6613731 0.9999871 0.4999936 0 0.4999936 0.9999871 0.8227229 0.9999871 0.6613731 0 0.6613731 0.9999871 0.9931922 0.01128667 0.9999871 0.9473562 0.9999871 0.0526309 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 1 0.822701 0 0.9840519 0 0.5 0 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.3386496 1 0.1772989 1 0.1772989 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 1 0.822701 0 0.9840519 0 0.5000001 0 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.006823301 0.01128667 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0 0.01594793 0 0.01594793 0.9999871 0.4999936 0.9999871 0.4999936 0 0.3386438 0 0.3386438 0 0.177294 0 0.177294 0.9999871 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0.9999871 0.9931922 0.9887219 0.9931922 0.01128667 0.8227229 0.9999871 0.9840727 0.9999871 0.9840727 0 0.6613731 0.9999871 0.6613731 0 0.4999936 0 0.8227229 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.01128667 0.9931922 0.9887219 0.9999871 0.9473562</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_008-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_008-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default_008-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_008-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_008-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default_008-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_008-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_008-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default_008-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_008-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default_008-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default_008-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default_008-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default_008-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_008-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_008-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_008-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default_008-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_008-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>3 0 0 0 11 1 1 1 2 2 2 2 3 0 3 3 7 3 4 4 9 4 5 5 5 5 6 6 6 6 7 7 4 7 8 8 8 8 9 9 7 3 10 10 6 6 11 11 11 1 12 12 1 9 13 13 0 10 14 14 14 11 15 15 22 12 16 16 15 13 17 17 21 14 18 18 14 11 19 19 15 13 20 20 16 15 21 21 20 16 22 22 17 17 23 23 18 18 24 24 21 14 25 25 20 16 26 26 12 19 27 27 22 12 28 28 23 20 29 29 27 0 30 30 35 1 31 31 26 2 32 32 27 0 33 33 31 3 34 34 33 4 35 35 29 21 36 36 30 6 37 37 28 22 38 38 32 8 39 39 31 3 40 40 30 6 41 41 35 1 42 42 25 9 43 43 24 10 44 44 38 11 45 45 44 12 46 46 39 13 47 47 43 14 48 48 38 11 49 49 39 13 50 50 28 22 51 51 42 16 52 52 29 21 53 53 40 18 54 54 43 14 55 55 42 16 56 56 36 19 57 57 44 12 58 58 45 20 59 59 57 23 60 60 49 24 61 61 48 25 62 62 53 26 63 63 49 24 64 64 55 27 65 65 51 28 66 66 52 29 67 67 54 30 68 68 52 29 69 69 55 27 70 70 54 30 71 71 46 31 72 72 56 32 73 73 57 23 74 74 61 33 75 75 67 34 76 76 60 35 77 77 65 36 78 78 60 35 79 79 63 37 80 80 64 38 81 81 50 39 82 82 51 28 83 83 65 36 84 84 62 40 85 85 64 38 86 86 67 34 87 87 59 41 88 88 58 42 89 89 3 0 90 90 10 43 91 91 11 1 92 92 3 0 93 93 2 2 94 94 7 3 95 95 5 5 96 96 8 8 97 97 6 6 98 98 8 8 99 99 9 4 100 100 7 3 101 101 11 1 102 102 10 43 103 103 1 9 104 104 14 11 105 105 23 20 106 106 22 12 107 107 21 14 108 108 19 44 109 109 14 11 110 110 16 15 111 111 18 18 112 112 20 16 113 113 18 18 114 114 19 44 115 115 21 14 116 116 12 19 117 117 13 45 118 118 22 12 119 119 27 0 120 120 34 43 121 121 35 1 122 122 27 0 123 123 26 2 124 124 31 3 125 125 29 21 126 126 32 8 127 127 30 6 128 128 32 8 129 129 33 4 130 130 31 3 131 131 35 1 132 132 34 43 133 133 25 9 134 134 38 11 135 135 45 20 136 136 44 12 137 137 43 14 138 138 41 44 139 139 38 11 140 140 28 22 141 141 40 18 142 142 42 16 143 143 40 18 144 144 41 44 145 145 43 14 146 146 36 19 147 147 37 45 148 148 44 12 149 149 57 23 150 150 56 32 151 151 49 24 152 152 53 26 153 153 48 25 154 154 49 24 155 155 51 28 156 156 50 39 157 157 52 29 158 158 52 29 159 159 53 26 160 160 55 27 161 161 46 31 162 162 47 46 163 163 56 32 164 164 61 33 165 165 66 47 166 166 67 34 167 167 65 36 168 168 61 33 169 169 60 35 170 170 64 38 171 171 62 40 172 172 50 39 173 173 65 36 174 174 63 37 175 175 62 40 176 176 67 34 177 177 66 47 178 178 59 41 179 179</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="licenseplate-default_004-mesh" name="licenseplate-default.004">
      <mesh>
        <source id="licenseplate-default_004-mesh-positions">
          <float_array id="licenseplate-default_004-mesh-positions-array" count="204">-0.1895415 0.08629006 -0.04921048 -0.1895415 0.08629006 0.04921048 -0.1849886 0.08170968 -0.05499988 -0.1849886 0.08170968 0.055 0 0 -0.05499994 0 0 0.05499994 -0.07959264 0.01336956 -0.05499935 -0.1430544 0.04583561 -0.05499994 -0.07959264 0.01336956 0.05500012 -0.1430544 0.04583561 0.055 -0.1875935 0.08433032 0.05359315 -0.1875935 0.08433032 -0.05359303 0.1895416 0.08629018 -0.0492084 0.1895416 0.08629018 0.04921084 0.1849887 0.08170968 -0.05500018 0.1849888 0.0817098 0.05500018 0 0 -0.05499994 0 0 0.05499994 0.0795927 0.01336961 -0.05499994 0.1430546 0.04583573 -0.05500018 0.0795927 0.01336961 0.05499994 0.1430544 0.04583561 0.05500018 0.1875938 0.08433049 0.05359274 0.1875937 0.08433043 -0.05359345 -0.1895415 0.08629006 -0.04921048 -0.1895415 0.08629006 0.04921048 -0.1849886 0.08170968 -0.05499988 -0.1849886 0.08170968 0.055 0 0 -0.05499994 0 0 0.05499994 -0.07959264 0.01336956 -0.05499935 -0.1430544 0.04583561 -0.05499994 -0.07959264 0.01336956 0.05500012 -0.1430544 0.04583561 0.055 -0.1875935 0.08433032 0.05359315 -0.1875935 0.08433032 -0.05359303 0.1895416 0.08629018 -0.0492084 0.1895416 0.08629018 0.04921084 0.1849887 0.08170968 -0.05500018 0.1849888 0.0817098 0.05500018 0.0795927 0.01336961 -0.05499994 0.1430546 0.04583573 -0.05500018 0.0795927 0.01336961 0.05499994 0.1430544 0.04583561 0.05500018 0.1875938 0.08433049 0.05359274 0.1875937 0.08433043 -0.05359345 -0.1895495 0.0862981 -0.04921042 -0.1895495 0.0862981 0.04921048 -0.1849967 0.08171772 -0.05499994 -0.1849967 0.08171772 0.055 7.87345e-7 0 -0.05499988 7.87345e-7 0 0.055 -0.07959789 0.01337164 -0.05500102 -0.1430615 0.04584032 -0.05499994 -0.07959789 0.01337164 0.05500018 -0.1430615 0.04584032 0.055 -0.1876015 0.08433836 0.05359315 -0.1876016 0.08433836 -0.05359303 0.1895496 0.08629816 -0.04921025 0.1895496 0.08629816 0.0492109 0.1849968 0.08171784 -0.05500322 0.1849967 0.08171778 0.05500018 0.07959783 0.01337164 -0.05499988 0.1430615 0.04584038 -0.05500239 0.07959783 0.01337164 0.05499994 0.1430616 0.04584038 0.05500018 0.1876016 0.08433848 0.0535928 0.1876017 0.08433848 -0.05359208</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_004-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_004-mesh-normals">
          <float_array id="licenseplate-default_004-mesh-normals-array" count="144">-0.6763141 -0.7366134 0 -0.7092417 -0.7049654 0 -0.7092292 -0.704978 0 -0.5565856 -0.8307903 0 -0.5565862 -0.8307899 -2.50407e-7 -0.1656541 -0.9861839 1.27959e-7 -0.3142313 -0.9493465 0 -0.1656543 -0.986184 2.6226e-7 -0.314231 -0.9493466 0 -0.7092421 -0.7049651 0 -0.7092293 -0.7049779 0 0.6763143 -0.7366132 1.36294e-7 0.7092356 -0.7049716 0 0.676315 -0.7366126 1.94058e-7 0.5565862 -0.83079 0 0.556586 -0.8307902 0 0.1656545 -0.9861838 0 0.3142315 -0.9493464 0 0.3142313 -0.9493466 -2.79241e-7 0.7092325 -0.7049747 0 0.7092291 -0.7049781 0 0 -1 0 7.11828e-7 -1 2.65934e-7 0.7092352 0.704972 0 0.6763343 0.7365948 0 0.6763318 0.7365972 0 0.5566087 0.8307749 0 0.556609 0.8307746 0 -9.47497e-7 1 0 0.3142471 0.9493413 0 -1.53909e-6 1 0 0.7092317 0.7049755 0 0.709239 0.7049682 0 -0.6763302 0.7365986 0 -0.7092342 0.704973 0 -0.6763184 0.7366095 0 -0.5566106 0.8307735 0 -0.5566094 0.8307744 0 -0.3142487 0.9493407 0 -0.3142501 0.9493404 0 -0.7092337 0.7049735 0 -0.70924 0.7049672 1.74516e-7 -0.6763195 -0.7366085 -1.59063e-7 0.165655 -0.9861838 2.6226e-7 0.7092412 -0.704966 0 0.3142471 0.9493413 0 0.7092279 0.7049794 0 -0.709235 0.7049722 0</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_004-mesh-normals-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_004-mesh-map-0">
          <float_array id="licenseplate-default_004-mesh-map-0-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.1772989 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.1772989 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5000001 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 6.82312e-6 1.12867e-5 1.59479e-5 9.99987e-4 1.59479e-5 0 1.77303e-4 0 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 5.00021e-4 0 3.38656e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 9.99987e-4 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 9.99987e-4 9.93196e-4 1.12867e-5 9.84069e-4 0 9.84069e-4 9.99987e-4 8.22704e-4 0 8.22704e-4 9.99987e-4 6.6134e-4 9.99987e-4 5.00021e-4 0 5.00021e-4 9.99987e-4 8.22704e-4 9.99987e-4 6.6134e-4 0 6.6134e-4 9.99987e-4 9.93196e-4 1.12867e-5 0.000999987 9.47356e-4 0.000999987 5.26309e-5 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.1772989 1 0.01594805 1 0.01594805 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5 1 0.5 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.1772989 1 0.01594805 1 0.01594805 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5000001 1 0.5000001 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 0 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 0 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 0 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 9.84069e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 6.6134e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 8.22704e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 1.12867e-5 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_004-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_004-mesh-map-1">
          <float_array id="licenseplate-default_004-mesh-map-1-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.1772989 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.1772989 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5000001 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.006823301 0.01128667 0.01594793 0.9999871 0.01594793 0 0.177294 0 0.01594793 0.9999871 0.177294 0.9999871 0.4999936 0 0.3386438 0.9999871 0.4999936 0.9999871 0.3386438 0.9999871 0.177294 0 0.177294 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0.9999871 0.9931922 0.01128667 0.9840727 0 0.9840727 0.9999871 0.8227229 0 0.8227229 0.9999871 0.6613731 0.9999871 0.4999936 0 0.4999936 0.9999871 0.8227229 0.9999871 0.6613731 0 0.6613731 0.9999871 0.9931922 0.01128667 0.9999871 0.9473562 0.9999871 0.0526309 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.1772989 1 0.01594805 1 0.01594805 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5 1 0.5 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.1772989 1 0.01594805 1 0.01594805 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5000001 1 0.5000001 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.006823301 0.01128667 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0 0.01594793 0 0.01594793 0.9999871 0.4999936 0 0.3386438 0 0.3386438 0.9999871 0.3386438 0.9999871 0.3386438 0 0.177294 0 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0.9999871 0.9931922 0.9887219 0.9931922 0.01128667 0.9840727 0.9999871 0.9840727 0 0.8227229 0 0.6613731 0.9999871 0.6613731 0 0.4999936 0 0.8227229 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.01128667 0.9931922 0.9887219 0.9999871 0.9473562</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_004-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_004-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default_004-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_004-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_004-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default_004-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_004-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_004-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default_004-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_004-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default_004-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default_004-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default_004-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default_004-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_004-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_004-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_004-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default_004-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_004-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>2 0 0 0 10 1 1 1 11 2 2 2 9 3 3 3 2 0 4 4 7 4 5 5 5 5 6 6 6 6 7 7 4 7 8 8 9 3 9 9 6 6 10 10 8 8 11 11 11 2 12 12 1 9 13 13 0 10 14 14 14 11 15 15 22 12 16 16 15 13 17 17 19 14 18 18 15 13 19 19 21 15 20 20 17 16 21 21 18 17 22 22 20 18 23 23 18 17 24 24 21 15 25 25 20 18 26 26 12 19 27 27 22 12 28 28 23 20 29 29 26 0 30 30 34 1 31 31 35 2 32 32 33 3 33 33 26 0 34 34 31 4 35 35 29 21 36 36 30 6 37 37 28 22 38 38 33 3 39 39 30 6 40 40 32 8 41 41 35 2 42 42 25 9 43 43 24 10 44 44 38 11 45 45 44 12 46 46 39 13 47 47 41 14 48 48 39 13 49 49 43 15 50 50 29 21 51 51 40 17 52 52 42 18 53 53 40 17 54 54 43 15 55 55 42 18 56 56 36 19 57 57 44 12 58 58 45 20 59 59 57 23 60 60 49 24 61 61 48 25 62 62 53 26 63 63 49 24 64 64 55 27 65 65 50 28 66 66 54 29 67 67 51 30 68 68 54 29 69 69 53 26 70 70 55 27 71 71 46 31 72 72 56 32 73 73 57 23 74 74 61 33 75 75 67 34 76 76 60 35 77 77 61 33 78 78 63 36 79 79 65 37 80 80 64 38 81 81 50 28 82 82 51 30 83 83 65 37 84 84 62 39 85 85 64 38 86 86 67 34 87 87 59 40 88 88 58 41 89 89 2 0 90 90 3 42 91 91 10 1 92 92 9 3 93 93 3 42 94 94 2 0 95 95 5 5 96 96 8 8 97 97 6 6 98 98 9 3 99 99 7 4 100 100 6 6 101 101 11 2 102 102 10 1 103 103 1 9 104 104 14 11 105 105 23 20 106 106 22 12 107 107 19 14 108 108 14 11 109 109 15 13 110 110 17 16 111 111 16 43 112 112 18 17 113 113 18 17 114 114 19 14 115 115 21 15 116 116 12 19 117 117 13 44 118 118 22 12 119 119 26 0 120 120 27 42 121 121 34 1 122 122 33 3 123 123 27 42 124 124 26 0 125 125 29 21 126 126 32 8 127 127 30 6 128 128 33 3 129 129 31 4 130 130 30 6 131 131 35 2 132 132 34 1 133 133 25 9 134 134 38 11 135 135 45 20 136 136 44 12 137 137 41 14 138 138 38 11 139 139 39 13 140 140 29 21 141 141 28 22 142 142 40 17 143 143 40 17 144 144 41 14 145 145 43 15 146 146 36 19 147 147 37 44 148 148 44 12 149 149 57 23 150 150 56 32 151 151 49 24 152 152 53 26 153 153 48 25 154 154 49 24 155 155 50 28 156 156 52 45 157 157 54 29 158 158 54 29 159 159 52 45 160 160 53 26 161 161 46 31 162 162 47 46 163 163 56 32 164 164 61 33 165 165 66 47 166 166 67 34 167 167 61 33 168 168 60 35 169 169 63 36 170 170 64 38 171 171 62 39 172 172 50 28 173 173 65 37 174 174 63 36 175 175 62 39 176 176 67 34 177 177 66 47 178 178 59 40 179 179</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="licenseplate-default_003-mesh" name="licenseplate-default.003">
      <mesh>
        <source id="licenseplate-default_003-mesh-positions">
          <float_array id="licenseplate-default_003-mesh-positions-array" count="204">-0.1291571 0.1057207 -0.04921042 -0.1291571 0.1057207 0.04921048 -0.1274108 0.1017417 -0.05499988 -0.1274108 0.1017417 0.055 0 0 -0.05499994 0 0 0.055 -0.07234776 0.02632665 -0.05499982 -0.1111875 0.07007825 -0.05499988 -0.07234781 0.02632665 0.05500048 -0.1111875 0.07007825 0.055 -0.1286648 0.104599 0.05359309 -0.1286648 0.104599 -0.05359303 0.129157 0.1057205 -0.04921042 0.129157 0.1057205 0.04921048 0.1274108 0.1017419 -0.05499958 0.1274108 0.101742 0.05500048 0 0 -0.05499994 0 0 0.055 0.07234776 0.02632665 -0.05499988 0.1111876 0.07007831 -0.05499982 0.07234776 0.02632665 0.05499994 0.1111875 0.07007825 0.05500048 0.1286649 0.1045991 0.05359429 0.1286649 0.1045991 -0.05359208 -0.1291571 0.1057207 -0.04921042 -0.1291571 0.1057207 0.04921048 -0.1274108 0.1017417 -0.05499988 -0.1274108 0.1017417 0.055 0 0 -0.05499994 0 0 0.055 -0.07234776 0.02632665 -0.05499982 -0.1111875 0.07007825 -0.05499988 -0.07234781 0.02632665 0.05500048 -0.1111875 0.07007825 0.055 -0.1286648 0.104599 0.05359309 -0.1286648 0.104599 -0.05359303 0.129157 0.1057205 -0.04921042 0.129157 0.1057205 0.04921048 0.1274108 0.1017419 -0.05499958 0.1274108 0.101742 0.05500048 0.07234776 0.02632665 -0.05499988 0.1111876 0.07007831 -0.05499982 0.07234776 0.02632665 0.05499994 0.1111875 0.07007825 0.05500048 0.1286649 0.1045991 0.05359429 0.1286649 0.1045991 -0.05359208 -0.1291571 0.1057207 -0.04921042 -0.1291571 0.1057207 0.04921048 -0.1274165 0.1017549 -0.05499988 -0.1274165 0.1017549 0.055 6.40496e-7 0 -0.05499988 6.39405e-7 0 0.055 -0.07235342 0.02633124 -0.05499982 -0.111194 0.07008802 -0.05499988 -0.07235348 0.0263313 0.05499958 -0.111194 0.07008802 0.05499994 -0.1286705 0.1046121 0.05359309 -0.1286705 0.1046122 -0.05359303 0.129157 0.1057205 -0.04921042 0.129157 0.1057205 0.04921048 0.1274166 0.101755 -0.05499982 0.1274166 0.101755 0.05500137 0.07235354 0.02633136 -0.05499994 0.1111941 0.0700882 -0.055 0.07235354 0.02633136 0.05499994 0.111194 0.07008808 0.05500048 0.1286706 0.1046124 0.05359274 0.1286706 0.1046124 -0.05359339</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_003-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_003-mesh-normals">
          <float_array id="licenseplate-default_003-mesh-normals-array" count="144">-0.9012269 -0.4333477 0 -0.9156894 -0.4018867 0 -0.9012277 -0.4333461 0 -0.8254806 -0.5644307 0 -0.8254802 -0.5644311 0 -0.3419539 -0.9397168 0 -0.5620793 -0.8270835 0 -0.3419538 -0.9397168 0 -0.5620793 -0.8270834 0 -0.9157012 -0.4018601 0 -0.9156826 -0.4019024 0 0.9156875 -0.4018911 -1.92157e-7 0.9012284 -0.4333446 0 0.9012258 -0.43335 0 0.825481 -0.5644299 0 0.3419538 -0.9397168 0 0.5620799 -0.8270831 0 0.3419538 -0.9397168 0 0.8254808 -0.5644304 0 0.9156973 -0.4018688 0 0.9156904 -0.4018843 0 -8.78257e-7 -1 0 1.21139e-7 -1 0 0.9012432 0.4333139 0 0.9156891 0.4018875 0 0.9012421 0.4333159 0 0.8255115 0.5643853 0 -3.45251e-6 1 0 0.5621111 0.8270618 0 -3.61403e-6 1 0 0.8255116 0.5643852 0 0.9156816 0.4019045 0 0.9156894 0.4018868 0 -0.901243 0.4333141 0 -0.9156891 0.4018875 0 -0.9156883 0.4018893 0 -0.8255123 0.5643842 0 -0.8255118 0.5643849 0 -0.5621141 0.8270598 0 -0.5621138 0.8270599 1.45529e-7 -0.9156886 0.4018886 0 -0.9156903 -0.4018847 -1.74811e-7 0.5620791 -0.8270835 0 0.9156841 -0.401899 0 0.5621106 0.8270621 0 0.9156922 0.4018803 0 -0.9012397 0.4333209 0 -0.9156935 0.4018774 0</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_003-mesh-normals-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_003-mesh-map-0">
          <float_array id="licenseplate-default_003-mesh-map-0-array" count="360">0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 1 0.9840519 0 0.9840519 1 0.5 0 0.6613504 1 0.5 1 0.6613504 1 0.822701 0 0.822701 1 0.9931765 0.01128661 1 0.9473694 0.9931765 0.988714 0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 1 0.9840519 0 0.9840519 1 0.5000001 0 0.6613504 1 0.5000001 1 0.6613504 1 0.822701 0 0.822701 1 0.9931765 0.01128661 1 0.9473694 0.9931765 0.988714 1.59479e-5 0 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 0 3.38656e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 9.99987e-4 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 0 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 8.22704e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 6.6134e-4 9.99987e-4 5.00021e-4 0 5.00021e-4 9.99987e-4 6.6134e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 9.88722e-4 0.000999987 5.26309e-5 9.93196e-4 1.12867e-5 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 1 0.822701 0 0.9840519 0 0.5 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 0.9931765 0.01128661 1 0.05263155 1 0.9473694 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 1 0.822701 0 0.9840519 0 0.5000001 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 0.9931765 0.01128661 1 0.05263155 1 0.9473694 1.59479e-5 0 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.77303e-4 9.99987e-4 1.77303e-4 0 1.59479e-5 0 5.00021e-4 0 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 0 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 0 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 8.22704e-4 9.99987e-4 9.84069e-4 9.99987e-4 9.84069e-4 0 6.6134e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 6.6134e-4 9.99987e-4 8.22704e-4 9.99987e-4 8.22704e-4 0 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4 0.000999987 5.26309e-5</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_003-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_003-mesh-map-1">
          <float_array id="licenseplate-default_003-mesh-map-1-array" count="360">0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 1 0.9840519 0 0.9840519 1 0.5 0 0.6613504 1 0.5 1 0.6613504 1 0.822701 0 0.822701 1 0.9931765 0.01128661 1 0.9473694 0.9931765 0.988714 0.01594805 1 0.00682342 0.01128661 0.01594805 0 0.01594805 1 0.1772989 0 0.1772989 1 0.5000001 1 0.3386496 0 0.5000001 0 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.01128661 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 1 0.9840519 0 0.9840519 1 0.5000001 0 0.6613504 1 0.5000001 1 0.6613504 1 0.822701 0 0.822701 1 0.9931765 0.01128661 1 0.9473694 0.9931765 0.988714 0.01594793 0 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0.9999871 0.01594793 0 0.01594793 0.9999871 0.4999936 0 0.3386438 0.9999871 0.4999936 0.9999871 0.3386438 0.9999871 0.177294 0 0.177294 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0 0.9931922 0.9887219 0.9931922 0.01128667 0.8227229 0.9999871 0.9840727 0 0.8227229 0 0.6613731 0.9999871 0.4999936 0 0.4999936 0.9999871 0.6613731 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.9887219 0.9999871 0.0526309 0.9931922 0.01128667 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 1 0.822701 0 0.9840519 0 0.5 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 0.9931765 0.01128661 1 0.05263155 1 0.9473694 0.01594805 1 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.01594805 0 0.1772989 0 0.5000001 1 0.3386496 1 0.3386496 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.01128661 0.00682342 0.988714 0 0.9473694 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 1 0.822701 0 0.9840519 0 0.5000001 0 0.6613504 0 0.6613504 1 0.6613504 1 0.6613504 0 0.822701 0 0.9931765 0.01128661 1 0.05263155 1 0.9473694 0.01594793 0 0.006823301 0.01128667 0.006823301 0.9887219 0.177294 0.9999871 0.177294 0 0.01594793 0 0.4999936 0 0.3386438 0 0.3386438 0.9999871 0.3386438 0.9999871 0.3386438 0 0.177294 0 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0 0.9840727 0.9999871 0.9931922 0.9887219 0.8227229 0.9999871 0.9840727 0.9999871 0.9840727 0 0.6613731 0.9999871 0.6613731 0 0.4999936 0 0.6613731 0.9999871 0.8227229 0.9999871 0.8227229 0 0.9931922 0.9887219 0.9999871 0.9473562 0.9999871 0.0526309</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_003-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_003-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default_003-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_003-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_003-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default_003-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_003-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_003-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default_003-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_003-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default_003-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default_003-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default_003-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default_003-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_003-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_003-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_003-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default_003-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_003-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>3 0 0 0 11 1 1 1 2 2 2 2 3 0 3 3 7 3 4 4 9 4 5 5 5 5 6 6 6 6 7 7 4 7 8 8 9 4 9 9 6 6 10 10 8 8 11 11 11 1 12 12 1 9 13 13 0 10 14 14 23 11 15 15 15 12 16 16 14 13 17 17 21 14 18 18 14 13 19 19 15 12 20 20 16 15 21 21 20 16 22 22 17 17 23 23 20 16 24 24 19 18 25 25 21 14 26 26 23 11 27 27 13 19 28 28 22 20 29 29 27 0 30 30 35 1 31 31 26 2 32 32 27 0 33 33 31 3 34 34 33 4 35 35 29 21 36 36 30 6 37 37 28 22 38 38 33 4 39 39 30 6 40 40 32 8 41 41 35 1 42 42 25 9 43 43 24 10 44 44 45 11 45 45 39 12 46 46 38 13 47 47 43 14 48 48 38 13 49 49 39 12 50 50 28 22 51 51 42 16 52 52 29 21 53 53 42 16 54 54 41 18 55 55 43 14 56 56 45 11 57 57 37 19 58 58 44 20 59 59 48 23 60 60 56 24 61 61 49 25 62 62 55 26 63 63 48 23 64 64 49 25 65 65 50 27 66 66 54 28 67 67 51 29 68 68 54 28 69 69 53 30 70 70 55 26 71 71 46 31 72 72 56 24 73 73 57 32 74 74 60 33 75 75 66 34 76 76 67 35 77 77 65 36 78 78 60 33 79 79 63 37 80 80 64 38 81 81 50 27 82 82 51 29 83 83 64 38 84 84 63 37 85 85 62 39 86 86 66 34 87 87 58 40 88 88 67 35 89 89 3 0 90 90 10 41 91 91 11 1 92 92 3 0 93 93 2 2 94 94 7 3 95 95 5 5 96 96 8 8 97 97 6 6 98 98 9 4 99 99 7 3 100 100 6 6 101 101 11 1 102 102 10 41 103 103 1 9 104 104 23 11 105 105 22 20 106 106 15 12 107 107 21 14 108 108 19 18 109 109 14 13 110 110 16 15 111 111 18 42 112 112 20 16 113 113 20 16 114 114 18 42 115 115 19 18 116 116 23 11 117 117 12 43 118 118 13 19 119 119 27 0 120 120 34 41 121 121 35 1 122 122 27 0 123 123 26 2 124 124 31 3 125 125 29 21 126 126 32 8 127 127 30 6 128 128 33 4 129 129 31 3 130 130 30 6 131 131 35 1 132 132 34 41 133 133 25 9 134 134 45 11 135 135 44 20 136 136 39 12 137 137 43 14 138 138 41 18 139 139 38 13 140 140 28 22 141 141 40 42 142 142 42 16 143 143 42 16 144 144 40 42 145 145 41 18 146 146 45 11 147 147 36 43 148 148 37 19 149 149 48 23 150 150 57 32 151 151 56 24 152 152 55 26 153 153 53 30 154 154 48 23 155 155 50 27 156 156 52 44 157 157 54 28 158 158 54 28 159 159 52 44 160 160 53 30 161 161 46 31 162 162 47 45 163 163 56 24 164 164 60 33 165 165 61 46 166 166 66 34 167 167 65 36 168 168 61 46 169 169 60 33 170 170 64 38 171 171 62 39 172 172 50 27 173 173 64 38 174 174 65 36 175 175 63 37 176 176 66 34 177 177 59 47 178 178 58 40 179 179</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="licenseplate-default_002-mesh" name="licenseplate-default.002">
      <mesh>
        <source id="licenseplate-default_002-mesh-positions">
          <float_array id="licenseplate-default_002-mesh-positions-array" count="204">-0.1317231 0.1405304 -0.04921042 -0.1317231 0.1405304 0.04921048 -0.1275766 0.1359879 -0.05499988 -0.1275766 0.1359879 0.055 0 3.66852e-4 -0.05499988 0 3.66852e-4 0.055 -0.04367423 0.04407083 -0.05499988 -0.08562541 0.09002935 -0.05499988 -0.04367423 0.04407083 0.055 -0.08562541 0.09002935 0.05499994 -0.1299489 0.1385868 0.05359309 -0.1299489 0.1385868 -0.05359309 0.1317231 0.1405304 -0.04921042 0.131723 0.1405304 0.04921054 0.1275766 0.1359879 -0.05499988 0.1275766 0.1359879 0.05499994 0 3.66852e-4 -0.05499988 0 3.66852e-4 0.055 0.04367423 0.04407083 -0.05499988 0.08562541 0.09002935 -0.05499988 0.04367423 0.04407083 0.055 0.08562541 0.09002935 0.05499994 0.129949 0.1385869 0.05359309 0.1299489 0.1385868 -0.05359303 -0.1317231 0.1405304 -0.04921042 -0.1317231 0.1405304 0.04921048 -0.1275766 0.1359879 -0.05499988 -0.1275766 0.1359879 0.055 0 3.66852e-4 -0.05499988 0 3.66852e-4 0.055 -0.04367423 0.04407083 -0.05499988 -0.08562541 0.09002935 -0.05499988 -0.04367423 0.04407083 0.055 -0.08562541 0.09002935 0.05499994 -0.1299489 0.1385868 0.05359309 -0.1299489 0.1385868 -0.05359309 0.1317231 0.1405304 -0.04921042 0.131723 0.1405304 0.04921054 0.1275766 0.1359879 -0.05499988 0.1275766 0.1359879 0.05499994 0.04367423 0.04407083 -0.05499988 0.08562541 0.09002935 -0.05499988 0.04367423 0.04407083 0.055 0.08562541 0.09002935 0.05499994 0.129949 0.1385869 0.05359309 0.1299489 0.1385868 -0.05359303 -0.1317312 0.1405393 -0.04921042 -0.1317312 0.1405393 0.04921048 -0.1275847 0.1359968 -0.05499994 -0.1275847 0.1359968 0.05499994 0 3.66852e-4 -0.05499988 0 3.66852e-4 0.055 -0.04368239 0.04407978 -0.05499988 -0.08563357 0.09003829 -0.05499988 -0.04368239 0.04407978 0.05499994 -0.08563357 0.09003829 0.05499994 -0.1299571 0.1385958 0.05359309 -0.1299571 0.1385958 -0.05359303 0.1317312 0.1405393 -0.04921042 0.1317312 0.1405393 0.04921048 0.1275847 0.1359968 -0.05499994 0.1275847 0.1359968 0.05499994 0.04368239 0.04407978 -0.05499988 0.08563357 0.09003829 -0.05499988 0.04368239 0.04407978 0.05499994 0.08563357 0.09003829 0.055 0.1299571 0.1385958 0.05359309 0.1299571 0.1385958 -0.05359303</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_002-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_002-mesh-normals">
          <float_array id="licenseplate-default_002-mesh-normals-array" count="144">-0.7385746 -0.6741718 0 -0.7385704 -0.6741764 0 -0.7385761 -0.6741701 0 -0.7385717 -0.6741751 0 -0.738573 -0.6741737 0 -0.738573 -0.6741737 0 -0.7231451 -0.6906962 0 -0.7073478 -0.7068657 0 -0.7073478 -0.7068657 0 -0.7231451 -0.6906961 0 -0.7385736 -0.6741729 0 0.7385737 -0.6741728 0 0.7385721 -0.6741746 0 0.7385733 -0.6741732 0 0.738573 -0.6741735 0 0.738573 -0.6741735 0 0.7073478 -0.7068657 0 0.7231451 -0.6906962 0 0.7231451 -0.6906961 0 0.7385739 -0.6741725 0 0.7385716 -0.6741752 0 0 -1 0 0 -1 0 0.738571 0.6741757 0 0.7385739 0.6741726 -1.29446e-7 0.738572 0.6741747 0 0.7385731 0.6741734 0 0.7385728 0.6741737 0 0 1 0 0.7231482 0.690693 0 0 1 0 0.7231482 0.6906929 0 0.7385731 0.6741735 0 0.7385743 0.6741721 -2.88297e-7 -0.7385731 0.6741734 0 -0.7385722 0.6741744 0 -0.7385721 0.6741746 0 -0.738573 0.6741735 0 -0.738573 0.6741735 0 -0.7231482 0.690693 0 -0.7231482 0.6906929 0 -0.7385727 0.6741739 0 -0.738578 0.674168 0 -0.7385725 -0.6741741 0 0.7073478 -0.7068657 0 0.7385746 -0.6741719 0 0.7385709 0.6741758 -2.24745e-7 -0.7385731 0.6741734 0</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_002-mesh-normals-array" count="48" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_002-mesh-map-0">
          <float_array id="licenseplate-default_002-mesh-map-0-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 0 0.9840519 1 0.822701 1 0.5 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 0 0.9840519 1 0.822701 1 0.5000001 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 6.82312e-6 1.12867e-5 1.59479e-5 9.99987e-4 1.59479e-5 0 1.77303e-4 0 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 5.00021e-4 0 3.38656e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 9.99987e-4 3.38656e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 9.99987e-4 9.93196e-4 1.12867e-5 9.84069e-4 0 9.84069e-4 9.99987e-4 8.22704e-4 0 8.22704e-4 9.99987e-4 6.6134e-4 9.99987e-4 5.00021e-4 0 5.00021e-4 9.99987e-4 6.6134e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 1.12867e-5 0.000999987 9.47356e-4 0.000999987 5.26309e-5 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 0 0.9840519 1 0.5 1 0.5 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 0 0.9840519 1 0.5000001 1 0.5000001 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 0 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 0 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 9.84069e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 6.6134e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 6.6134e-4 9.99987e-4 8.22704e-4 9.99987e-4 8.22704e-4 0 9.93196e-4 1.12867e-5 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_002-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_002-mesh-map-1">
          <float_array id="licenseplate-default_002-mesh-map-1-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 0 0.9840519 1 0.822701 1 0.5 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9931765 0.01128661 0.9840519 1 0.9840519 0 0.822701 0 0.9840519 1 0.822701 1 0.5000001 1 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.006823301 0.01128667 0.01594793 0.9999871 0.01594793 0 0.177294 0 0.01594793 0.9999871 0.177294 0.9999871 0.4999936 0 0.3386438 0.9999871 0.4999936 0.9999871 0.3386438 0 0.177294 0.9999871 0.3386438 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0.9999871 0.9931922 0.01128667 0.9840727 0 0.9840727 0.9999871 0.8227229 0 0.8227229 0.9999871 0.6613731 0.9999871 0.4999936 0 0.4999936 0.9999871 0.6613731 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.01128667 0.9999871 0.9473562 0.9999871 0.0526309 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 0 0.9840519 1 0.5 1 0.5 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9931765 0.01128661 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 0 0.9840519 1 0.5000001 1 0.5000001 0 0.6613504 0 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.006823301 0.01128667 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0 0.01594793 0 0.01594793 0.9999871 0.4999936 0 0.3386438 0 0.3386438 0.9999871 0.3386438 0 0.177294 0 0.177294 0.9999871 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0.9999871 0.9931922 0.9887219 0.9931922 0.01128667 0.9840727 0.9999871 0.9840727 0 0.8227229 0 0.6613731 0.9999871 0.6613731 0 0.4999936 0 0.6613731 0.9999871 0.8227229 0.9999871 0.8227229 0 0.9931922 0.01128667 0.9931922 0.9887219 0.9999871 0.9473562</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_002-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_002-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default_002-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_002-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_002-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default_002-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_002-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default_002-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default_002-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default_002-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default_002-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default_002-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default_002-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default_002-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_002-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default_002-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_002-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default_002-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default_002-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>2 0 0 0 10 1 1 1 11 2 2 2 3 3 3 3 7 4 4 4 9 5 5 5 8 6 6 6 4 7 7 7 5 8 8 8 9 5 9 9 6 9 10 10 8 6 11 11 10 1 12 12 0 10 13 13 11 2 14 14 23 11 15 15 15 12 16 16 14 13 17 17 19 14 18 18 15 12 19 19 21 15 20 20 17 16 21 21 18 17 22 22 20 18 23 23 18 17 24 24 21 15 25 25 20 18 26 26 12 19 27 27 22 20 28 28 23 11 29 29 26 0 30 30 34 1 31 31 35 2 32 32 27 3 33 33 31 4 34 34 33 5 35 35 32 6 36 36 28 21 37 37 29 22 38 38 33 5 39 39 30 9 40 40 32 6 41 41 34 1 42 42 24 10 43 43 35 2 44 44 45 11 45 45 39 12 46 46 38 13 47 47 41 14 48 48 39 12 49 49 43 15 50 50 29 22 51 51 40 17 52 52 42 18 53 53 40 17 54 54 43 15 55 55 42 18 56 56 36 19 57 57 44 20 58 58 45 11 59 59 57 23 60 60 49 24 61 61 48 25 62 62 53 26 63 63 49 24 64 64 55 27 65 65 50 28 66 66 54 29 67 67 51 30 68 68 52 31 69 69 55 27 70 70 54 29 71 71 46 32 72 72 56 33 73 73 57 23 74 74 61 34 75 75 67 35 76 76 60 36 77 77 61 34 78 78 63 37 79 79 65 38 80 80 64 39 81 81 50 28 82 82 51 30 83 83 64 39 84 84 63 37 85 85 62 40 86 86 67 35 87 87 59 41 88 88 58 42 89 89 2 0 90 90 3 3 91 91 10 1 92 92 3 3 93 93 2 0 94 94 7 4 95 95 8 6 96 96 6 9 97 97 4 7 98 98 9 5 99 99 7 4 100 100 6 9 101 101 10 1 102 102 1 43 103 103 0 10 104 104 23 11 105 105 22 20 106 106 15 12 107 107 19 14 108 108 14 13 109 109 15 12 110 110 17 16 111 111 16 44 112 112 18 17 113 113 18 17 114 114 19 14 115 115 21 15 116 116 12 19 117 117 13 45 118 118 22 20 119 119 26 0 120 120 27 3 121 121 34 1 122 122 27 3 123 123 26 0 124 124 31 4 125 125 32 6 126 126 30 9 127 127 28 21 128 128 33 5 129 129 31 4 130 130 30 9 131 131 34 1 132 132 25 43 133 133 24 10 134 134 45 11 135 135 44 20 136 136 39 12 137 137 41 14 138 138 38 13 139 139 39 12 140 140 29 22 141 141 28 21 142 142 40 17 143 143 40 17 144 144 41 14 145 145 43 15 146 146 36 19 147 147 37 45 148 148 44 20 149 149 57 23 150 150 56 33 151 151 49 24 152 152 53 26 153 153 48 25 154 154 49 24 155 155 50 28 156 156 52 31 157 157 54 29 158 158 52 31 159 159 53 26 160 160 55 27 161 161 46 32 162 162 47 46 163 163 56 33 164 164 61 34 165 165 66 47 166 166 67 35 167 167 61 34 168 168 60 36 169 169 63 37 170 170 64 39 171 171 62 40 172 172 50 28 173 173 64 39 174 174 65 38 175 175 63 37 176 176 67 35 177 177 66 47 178 178 59 41 179 179</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="licenseplate-default-mesh" name="licenseplate-default">
      <mesh>
        <source id="licenseplate-default-mesh-positions">
          <float_array id="licenseplate-default-mesh-positions-array" count="204">-0.26 0 -0.04921042 -0.26 0 0.04921048 -0.2517071 0 -0.05499988 -0.2517071 0 0.055 0 0 -0.05499988 0 0 0.055 -0.08390235 0 -0.05499988 -0.1678047 0 -0.05499988 -0.08390235 0 0.055 -0.1678047 0 0.055 -0.2564519 0 0.05359309 -0.2564519 0 -0.05359303 0.26 0 -0.04921042 0.26 0 0.04921048 0.2517071 0 -0.05499988 0.2517071 0 0.055 0 0 -0.05499988 0 0 0.055 0.08390235 0 -0.05499988 0.1678047 0 -0.05499988 0.08390235 0 0.055 0.1678047 0 0.055 0.2564519 0 0.05359309 0.2564519 0 -0.05359303 -0.26 0 -0.04921042 -0.26 0 0.04921048 -0.2517071 0 -0.05499988 -0.2517071 0 0.055 0 0 -0.05499988 0 0 0.055 -0.08390235 0 -0.05499988 -0.1678047 0 -0.05499988 -0.08390235 0 0.055 -0.1678047 0 0.055 -0.2564519 0 0.05359309 -0.2564519 0 -0.05359303 0.26 0 -0.04921042 0.26 0 0.04921048 0.2517071 0 -0.05499988 0.2517071 0 0.055 0.08390235 0 -0.05499988 0.1678047 0 -0.05499988 0.08390235 0 0.055 0.1678047 0 0.055 0.2564519 0 0.05359309 0.2564519 0 -0.05359303 -0.26 1.78732e-5 -0.04921042 -0.26 1.78732e-5 0.04921048 -0.2517071 1.78732e-5 -0.05499988 -0.2517071 1.78732e-5 0.055 0 1.78732e-5 -0.05499988 0 1.78732e-5 0.055 -0.08390235 1.78732e-5 -0.05499988 -0.1678047 1.78732e-5 -0.05499988 -0.08390235 1.78732e-5 0.055 -0.1678047 1.78732e-5 0.055 -0.2564519 1.78732e-5 0.05359309 -0.2564519 1.78732e-5 -0.05359303 0.26 1.78732e-5 -0.04921042 0.26 1.78732e-5 0.04921048 0.2517071 1.78732e-5 -0.05499988 0.2517071 1.78732e-5 0.055 0.08390235 1.78732e-5 -0.05499988 0.1678047 1.78732e-5 -0.05499988 0.08390235 1.78732e-5 0.055 0.1678047 1.78732e-5 0.055 0.2564519 1.78732e-5 0.05359309 0.2564519 1.78732e-5 -0.05359303</float_array>
          <technique_common>
            <accessor source="#licenseplate-default-mesh-positions-array" count="68" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default-mesh-normals">
          <float_array id="licenseplate-default-mesh-normals-array" count="6">0 -1 0 0 1 0</float_array>
          <technique_common>
            <accessor source="#licenseplate-default-mesh-normals-array" count="2" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default-mesh-map-0">
          <float_array id="licenseplate-default-mesh-map-0-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5 0 0.6613504 1 0.5 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5 0 0.6613504 1 0.5 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 1.59479e-5 0 6.82312e-6 9.88722e-4 1.59479e-5 9.99987e-4 1.77303e-4 0 1.59479e-5 9.99987e-4 1.77303e-4 9.99987e-4 5.00021e-4 0 3.38656e-4 9.99987e-4 5.00021e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 9.99987e-4 3.38656e-4 9.99987e-4 0 5.26309e-5 6.82312e-6 9.88722e-4 6.82312e-6 1.12867e-5 9.84069e-4 0 9.93196e-4 9.88722e-4 9.93196e-4 1.12867e-5 9.84069e-4 9.99987e-4 8.22704e-4 0 8.22704e-4 9.99987e-4 6.6134e-4 9.99987e-4 5.00021e-4 0 5.00021e-4 9.99987e-4 8.22704e-4 9.99987e-4 6.6134e-4 0 6.6134e-4 9.99987e-4 9.93196e-4 9.88722e-4 0.000999987 5.26309e-5 9.93196e-4 1.12867e-5 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5 0 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5 0 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 1.59479e-5 0 6.82312e-6 1.12867e-5 6.82312e-6 9.88722e-4 1.77303e-4 0 1.59479e-5 0 1.59479e-5 9.99987e-4 5.00021e-4 0 3.38656e-4 0 3.38656e-4 9.99987e-4 3.38656e-4 0 1.77303e-4 0 1.77303e-4 9.99987e-4 0 5.26309e-5 0 9.47356e-4 6.82312e-6 9.88722e-4 9.84069e-4 0 9.84069e-4 9.99987e-4 9.93196e-4 9.88722e-4 9.84069e-4 9.99987e-4 9.84069e-4 0 8.22704e-4 0 6.6134e-4 9.99987e-4 6.6134e-4 0 5.00021e-4 0 8.22704e-4 9.99987e-4 8.22704e-4 0 6.6134e-4 0 9.93196e-4 9.88722e-4 0.000999987 9.47356e-4 0.000999987 5.26309e-5</float_array>
          <technique_common>
            <accessor source="#licenseplate-default-mesh-map-0-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default-mesh-map-1">
          <float_array id="licenseplate-default-mesh-map-1-array" count="360">0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5 0 0.6613504 1 0.5 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594805 0 0.00682342 0.988714 0.00682342 0.01128661 0.01594805 1 0.1772989 0 0.1772989 1 0.3386496 1 0.5000001 0 0.5000001 1 0.1772989 1 0.3386496 0 0.3386496 1 0.00682342 0.988714 0 0.05263155 0.00682342 0.01128661 0.9840519 0 0.9931765 0.988714 0.9840519 1 0.822701 0 0.9840519 1 0.822701 1 0.5 0 0.6613504 1 0.5 1 0.6613504 0 0.822701 1 0.6613504 1 1 0.05263155 0.9931765 0.988714 0.9931765 0.01128661 0.01594793 0 0.006823301 0.9887219 0.01594793 0.9999871 0.177294 0 0.01594793 0.9999871 0.177294 0.9999871 0.4999936 0 0.3386438 0.9999871 0.4999936 0.9999871 0.3386438 0 0.177294 0.9999871 0.3386438 0.9999871 0 0.0526309 0.006823301 0.9887219 0.006823301 0.01128667 0.9840727 0 0.9931922 0.9887219 0.9931922 0.01128667 0.9840727 0.9999871 0.8227229 0 0.8227229 0.9999871 0.6613731 0.9999871 0.4999936 0 0.4999936 0.9999871 0.8227229 0.9999871 0.6613731 0 0.6613731 0.9999871 0.9931922 0.9887219 0.9999871 0.0526309 0.9931922 0.01128667 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5 0 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594805 0 0.01594805 1 0.00682342 0.988714 0.01594805 1 0.01594805 0 0.1772989 0 0.3386496 1 0.3386496 0 0.5000001 0 0.1772989 1 0.1772989 0 0.3386496 0 0.00682342 0.988714 0 0.9473694 0 0.05263155 0.9840519 0 0.9931765 0.01128661 0.9931765 0.988714 0.822701 0 0.9840519 0 0.9840519 1 0.5 0 0.6613504 0 0.6613504 1 0.6613504 0 0.822701 0 0.822701 1 1 0.05263155 1 0.9473694 0.9931765 0.988714 0.01594793 0 0.006823301 0.01128667 0.006823301 0.9887219 0.177294 0 0.01594793 0 0.01594793 0.9999871 0.4999936 0 0.3386438 0 0.3386438 0.9999871 0.3386438 0 0.177294 0 0.177294 0.9999871 0 0.0526309 0 0.9473562 0.006823301 0.9887219 0.9840727 0 0.9840727 0.9999871 0.9931922 0.9887219 0.9840727 0.9999871 0.9840727 0 0.8227229 0 0.6613731 0.9999871 0.6613731 0 0.4999936 0 0.8227229 0.9999871 0.8227229 0 0.6613731 0 0.9931922 0.9887219 0.9999871 0.9473562 0.9999871 0.0526309</float_array>
          <technique_common>
            <accessor source="#licenseplate-default-mesh-map-1-array" count="180" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default-mesh-colors-geom-licenseplate-default-map-2" name="geom-licenseplate-default-map-2">
          <float_array id="licenseplate-default-mesh-colors-geom-licenseplate-default-map-2-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default-mesh-colors-geom-licenseplate-default-map-2-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default-mesh-colors-geom-licenseplate-default-map-1" name="geom-licenseplate-default-map-1">
          <float_array id="licenseplate-default-mesh-colors-geom-licenseplate-default-map-1-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default-mesh-colors-geom-licenseplate-default-map-1-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="licenseplate-default-mesh-colors-geom-licenseplate-default-map0" name="geom-licenseplate-default-map0">
          <float_array id="licenseplate-default-mesh-colors-geom-licenseplate-default-map0-array" count="720">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#licenseplate-default-mesh-colors-geom-licenseplate-default-map0-array" count="180" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="licenseplate-default-mesh-vertices">
          <input semantic="POSITION" source="#licenseplate-default-mesh-positions"/>
        </vertices>
        <triangles material="licenseplate-52-11-material" count="60">
          <input semantic="VERTEX" source="#licenseplate-default-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#licenseplate-default-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#licenseplate-default-mesh-map-0" offset="2" set="0"/>
          <input semantic="TEXCOORD" source="#licenseplate-default-mesh-map-1" offset="2" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default-mesh-colors-geom-licenseplate-default-map-2" offset="3" set="0"/>
          <input semantic="COLOR" source="#licenseplate-default-mesh-colors-geom-licenseplate-default-map-1" offset="3" set="1"/>
          <input semantic="COLOR" source="#licenseplate-default-mesh-colors-geom-licenseplate-default-map0" offset="3" set="2"/>
          <p>2 0 0 0 10 0 1 1 11 0 2 2 3 0 3 3 7 0 4 4 9 0 5 5 8 0 6 6 4 0 7 7 5 0 8 8 9 0 9 9 6 0 10 10 8 0 11 11 10 0 12 12 0 0 13 13 11 0 14 14 14 0 15 15 22 0 16 16 15 0 17 17 19 0 18 18 15 0 19 19 21 0 20 20 16 0 21 21 20 0 22 22 17 0 23 23 18 0 24 24 21 0 25 25 20 0 26 26 12 0 27 27 22 0 28 28 23 0 29 29 26 0 30 30 34 0 31 31 35 0 32 32 27 0 33 33 31 0 34 34 33 0 35 35 32 0 36 36 28 0 37 37 29 0 38 38 33 0 39 39 30 0 40 40 32 0 41 41 34 0 42 42 24 0 43 43 35 0 44 44 38 0 45 45 44 0 46 46 39 0 47 47 41 0 48 48 39 0 49 49 43 0 50 50 28 0 51 51 42 0 52 52 29 0 53 53 40 0 54 54 43 0 55 55 42 0 56 56 36 0 57 57 44 0 58 58 45 0 59 59 48 1 60 60 56 1 61 61 49 1 62 62 53 1 63 63 49 1 64 64 55 1 65 65 50 1 66 66 54 1 67 67 51 1 68 68 52 1 69 69 55 1 70 70 54 1 71 71 46 1 72 72 56 1 73 73 57 1 74 74 60 1 75 75 66 1 76 76 67 1 77 77 61 1 78 78 63 1 79 79 65 1 80 80 64 1 81 81 50 1 82 82 51 1 83 83 65 1 84 84 62 1 85 85 64 1 86 86 66 1 87 87 58 1 88 88 67 1 89 89 2 0 90 90 3 0 91 91 10 0 92 92 3 0 93 93 2 0 94 94 7 0 95 95 8 0 96 96 6 0 97 97 4 0 98 98 9 0 99 99 7 0 100 100 6 0 101 101 10 0 102 102 1 0 103 103 0 0 104 104 14 0 105 105 23 0 106 106 22 0 107 107 19 0 108 108 14 0 109 109 15 0 110 110 16 0 111 111 18 0 112 112 20 0 113 113 18 0 114 114 19 0 115 115 21 0 116 116 12 0 117 117 13 0 118 118 22 0 119 119 26 0 120 120 27 0 121 121 34 0 122 122 27 0 123 123 26 0 124 124 31 0 125 125 32 0 126 126 30 0 127 127 28 0 128 128 33 0 129 129 31 0 130 130 30 0 131 131 34 0 132 132 25 0 133 133 24 0 134 134 38 0 135 135 45 0 136 136 44 0 137 137 41 0 138 138 38 0 139 139 39 0 140 140 28 0 141 141 40 0 142 142 42 0 143 143 40 0 144 144 41 0 145 145 43 0 146 146 36 0 147 147 37 0 148 148 44 0 149 149 48 1 150 150 57 1 151 151 56 1 152 152 53 1 153 153 48 1 154 154 49 1 155 155 50 1 156 156 52 1 157 157 54 1 158 158 52 1 159 159 53 1 160 160 55 1 161 161 46 1 162 162 47 1 163 163 56 1 164 164 60 1 165 165 61 1 166 166 66 1 167 167 61 1 168 168 60 1 169 169 63 1 170 170 64 1 171 171 62 1 172 172 50 1 173 173 65 1 174 174 63 1 175 175 62 1 176 176 66 1 177 177 59 1 178 178 58 1 179 179</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="licenseplate-52-11-r1" name="licenseplate-52-11-r1" type="NODE">
        <matrix sid="transform">1.007897 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default_006-mesh" name="licenseplate-52-11-r1">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="licenseplate-52-11-r0_5" name="licenseplate-52-11-r0_5" type="NODE">
        <matrix sid="transform">1.007897 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default_005-mesh" name="licenseplate-52-11-r0_5">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="licenseplate-52-11-r1_5" name="licenseplate-52-11-r1_5" type="NODE">
        <matrix sid="transform">1.007897 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default_007-mesh" name="licenseplate-52-11-r1_5">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="licenseplate-52-11-r4" name="licenseplate-52-11-r4" type="NODE">
        <matrix sid="transform">1.007897 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default_001-mesh" name="licenseplate-52-11-r4">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="licenseplate-52-11-r2" name="licenseplate-52-11-r2" type="NODE">
        <matrix sid="transform">1.007897 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default_008-mesh" name="licenseplate-52-11-r2">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="licenseplate-52-11-r0_3" name="licenseplate-52-11-r0_3" type="NODE">
        <matrix sid="transform">1.11 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default_004-mesh" name="licenseplate-52-11-r0_3">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="licenseplate-52-11-r0_2" name="licenseplate-52-11-r0_2" type="NODE">
        <matrix sid="transform">1.365657 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default_003-mesh" name="licenseplate-52-11-r0_2">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="licenseplate-52-11-b45" name="licenseplate-52-11-b45" type="NODE">
        <matrix sid="transform">1.095524 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default_002-mesh" name="licenseplate-52-11-b45">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="licenseplate-52-11" name="licenseplate-52-11" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#licenseplate-default-mesh" name="licenseplate-52-11">
          <bind_material>
            <technique_common>
              <instance_material symbol="licenseplate-52-11-material" target="#licenseplate-52-11-material">
                <bind_vertex_input semantic="licenseplate-default-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
                <bind_vertex_input semantic="licenseplate-default-mesh-map-1" input_semantic="TEXCOORD" input_set="1"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>