<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 3.3.1 commit date:2022-10-04, commit time:18:35, hash:b292cfe5a936</authoring_tool>
    </contributor>
    <created>2023-11-24T12:38:34</created>
    <modified>2023-11-24T12:38:34</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_cameras>
    <camera id="Camera-camera" name="Camera">
      <optics>
        <technique_common>
          <perspective>
            <xfov sid="xfov">39.59775</xfov>
            <aspect_ratio>1.777778</aspect_ratio>
            <znear sid="znear">0.1</znear>
            <zfar sid="zfar">100</zfar>
          </perspective>
        </technique_common>
      </optics>
      <extra>
        <technique profile="blender">
          <shiftx sid="shiftx" type="float">0</shiftx>
          <shifty sid="shifty" type="float">0</shifty>
          <dof_distance sid="dof_distance" type="float">10</dof_distance>
        </technique>
      </extra>
    </camera>
  </library_cameras>
  <library_lights>
    <light id="EnvironmentAmbientLight-light" name="EnvironmentAmbientLight">
      <technique_common>
        <directional>
          <color sid="color">0 0 0</color>
        </directional>
      </technique_common>
      <extra>
        <technique profile="blender">
          <type sid="type" type="int">1</type>
          <flag sid="flag" type="int">0</flag>
          <mode sid="mode" type="int">1</mode>
          <gamma sid="blender_gamma" type="float">1</gamma>
          <red sid="red" type="float">0</red>
          <green sid="green" type="float">0</green>
          <blue sid="blue" type="float">0</blue>
          <shadow_r sid="blender_shadow_r" type="float">0</shadow_r>
          <shadow_g sid="blender_shadow_g" type="float">0</shadow_g>
          <shadow_b sid="blender_shadow_b" type="float">0</shadow_b>
          <energy sid="blender_energy" type="float">1</energy>
          <dist sid="blender_dist" type="float">2500</dist>
          <spotsize sid="spotsize" type="float">45</spotsize>
          <spotblend sid="spotblend" type="float">0.15</spotblend>
          <att1 sid="att1" type="float">0</att1>
          <att2 sid="att2" type="float">1</att2>
          <falloff_type sid="falloff_type" type="int">2</falloff_type>
          <clipsta sid="clipsta" type="float">0.04999995</clipsta>
          <clipend sid="clipend" type="float">40</clipend>
          <bias sid="bias" type="float">1</bias>
          <soft sid="soft" type="float">3</soft>
          <bufsize sid="bufsize" type="int">512</bufsize>
          <samp sid="samp" type="int">3</samp>
          <buffers sid="buffers" type="int">1</buffers>
          <area_shape sid="area_shape" type="int">0</area_shape>
          <area_size sid="area_size" type="float">0.25</area_size>
          <area_sizey sid="area_sizey" type="float">0.25</area_sizey>
          <area_sizez sid="area_sizez" type="float">0.25</area_sizez>
        </technique>
      </extra>
    </light>
    <light id="Light-light" name="Light">
      <technique_common>
        <point>
          <color sid="color">1000 1000 1000</color>
          <constant_attenuation>1</constant_attenuation>
          <linear_attenuation>0</linear_attenuation>
          <quadratic_attenuation>0.00111109</quadratic_attenuation>
        </point>
      </technique_common>
      <extra>
        <technique profile="blender">
          <type sid="type" type="int">0</type>
          <flag sid="flag" type="int">0</flag>
          <mode sid="mode" type="int">1</mode>
          <gamma sid="blender_gamma" type="float">1</gamma>
          <red sid="red" type="float">1</red>
          <green sid="green" type="float">1</green>
          <blue sid="blue" type="float">1</blue>
          <shadow_r sid="blender_shadow_r" type="float">0</shadow_r>
          <shadow_g sid="blender_shadow_g" type="float">0</shadow_g>
          <shadow_b sid="blender_shadow_b" type="float">0</shadow_b>
          <energy sid="blender_energy" type="float">1000</energy>
          <dist sid="blender_dist" type="float">29.99998</dist>
          <spotsize sid="spotsize" type="float">75</spotsize>
          <spotblend sid="spotblend" type="float">0.15</spotblend>
          <att1 sid="att1" type="float">0</att1>
          <att2 sid="att2" type="float">1</att2>
          <falloff_type sid="falloff_type" type="int">2</falloff_type>
          <clipsta sid="clipsta" type="float">0.04999995</clipsta>
          <clipend sid="clipend" type="float">30.002</clipend>
          <bias sid="bias" type="float">1</bias>
          <soft sid="soft" type="float">3</soft>
          <bufsize sid="bufsize" type="int">2880</bufsize>
          <samp sid="samp" type="int">3</samp>
          <buffers sid="buffers" type="int">1</buffers>
          <area_shape sid="area_shape" type="int">1</area_shape>
          <area_size sid="area_size" type="float">0.1</area_size>
          <area_sizey sid="area_sizey" type="float">0.1</area_sizey>
          <area_sizez sid="area_sizez" type="float">1</area_sizez>
        </technique>
      </extra>
    </light>
  </library_lights>
  <library_effects>
    <effect id="woodplanks-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images/>
  <library_materials>
    <material id="woodplanks-material" name="woodplanks">
      <instance_effect url="#woodplanks-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="woodplanks_straps-mesh" name="woodplanks_straps">
      <mesh>
        <source id="woodplanks_straps-mesh-positions">
          <float_array id="woodplanks_straps-mesh-positions-array" count="192">49.01707 2.4244 100.3358 49.47768 2.4244 100.0153 49.47768 -2.424385 100.0153 49.01707 -2.424385 100.3358 50.69112 2.4244 0.09462738 50.94485 2.4244 0.2016792 50.59062 2.4244 -0.1525536 50.69112 -2.424385 0.09462738 50.59062 -2.424385 -0.1525536 50.94485 -2.424385 0.2016792 49.21414 -2.424385 100.0308 49.21414 2.4244 100.0308 50.94485 -2.424385 96.0536 -49.01714 2.4244 100.3358 -49.47775 2.4244 100.0153 50.94485 2.4244 96.0536 -49.47775 -2.424385 100.0153 -49.01714 -2.424385 100.3358 -50.69119 2.4244 0.09462738 -50.94492 2.4244 0.2016792 -50.59069 2.4244 -0.1525536 -50.69119 -2.424385 0.09462738 -50.59069 -2.424385 -0.1525536 -50.94492 -2.424385 0.2016792 -49.21422 -2.424385 100.0308 -49.21422 2.4244 100.0308 50.688 -2.424385 96.07228 50.688 2.4244 96.07228 -50.68807 2.4244 96.07228 -50.68807 -2.424385 96.07228 -50.94492 2.4244 96.0536 -50.94492 -2.424385 96.0536 49.01707 2.4244 100.3358 -49.01714 2.4244 100.3358 50.94485 2.4244 96.0536 50.94485 2.4244 0.2016792 50.69112 2.4244 0.09462738 -50.69119 2.4244 0.09462738 50.94485 -2.424385 0.2016792 50.59062 -2.424385 -0.1525536 49.01707 -2.424385 100.3358 -49.01714 -2.424385 100.3358 -50.59069 -2.424385 -0.1525536 50.94485 -2.424385 96.0536 50.688 2.4244 96.07228 49.47768 2.4244 100.0153 -50.94492 -2.424385 96.0536 -49.47775 -2.424385 100.0153 49.47768 -2.424385 100.0153 -50.59069 2.4244 -0.1525536 50.59062 2.4244 -0.1525536 -50.94492 2.4244 96.0536 -50.94492 2.4244 0.2016792 -50.94492 -2.424385 0.2016792 49.21414 -2.424385 100.0308 50.688 -2.424385 96.07228 49.21414 2.4244 100.0308 -50.68807 -2.424385 96.07228 -50.69119 -2.424385 0.09462738 -49.47775 2.4244 100.0153 -50.68807 2.4244 96.07228 -49.21422 -2.424385 100.0308 -49.21422 2.4244 100.0308 50.69112 -2.424385 0.09462738</float_array>
          <technique_common>
            <accessor source="#woodplanks_straps-mesh-positions-array" count="64" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="woodplanks_straps-mesh-normals">
          <float_array id="woodplanks_straps-mesh-normals-array" count="147">0.2993094 0 0.9541561 0.7907478 0 0.6121421 0.7907374 0 0.6121556 0.2992932 0 0.9541613 -0.299295 0 0.9541606 -0.2993113 0 0.9541556 0.9843167 0 0.1764105 0.9238806 0 -0.3826809 0.9238806 0 -0.3826808 0.9843167 0 0.1764106 -0.9841593 0 -0.1772868 -0.5705578 0 -0.8212576 -0.5705584 0 -0.8212571 -0.9841594 0 -0.1772863 0 1 0 0 1 -1.54169e-6 0 1 1.21779e-6 0 1 -2.44042e-6 0 -1 2.53166e-7 0 -1 4.68567e-7 0 -1 -5.73626e-7 0 -1 0 0 -1 -4.66878e-7 -0.3826857 0 -0.9238786 -0.3826859 0 -0.9238785 0.3826856 0 -0.9238786 0.382686 0 -0.9238785 0.9841596 0 -0.1772851 0.9841596 0 -0.1772847 0.5705596 0 -0.8212562 0.5705593 0 -0.8212565 -0.7071183 0 0.7070953 -0.7071178 0 0.7070958 -0.9843167 0 0.176411 -0.9843167 0 0.1764109 -0.7907488 0 0.612141 -0.7907382 0 0.6121544 0 -1 -1.5417e-6 0 -1 0 0 -1 2.41035e-7 -0.9238806 0 -0.3826809 -0.9238806 0 -0.3826808 0 1 0 0.707118 0 0.7070956 0.707118 0 0.7070955 0 -1 -5.73563e-7 0 1 7.70806e-7 0 1 0 0 1 -7.51525e-7</float_array>
          <technique_common>
            <accessor source="#woodplanks_straps-mesh-normals-array" count="49" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="woodplanks_straps-mesh-map-0">
          <float_array id="woodplanks_straps-mesh-map-0-array" count="384">0.9827691 0.05245959 0.982769 0.05045706 0.9733448 0.05046117 0.9733448 0.05046117 0.9733448 0.05246233 0.9827691 0.05245959 0.9733448 0.05246233 0.9733451 0.4477623 0.9827691 0.4477623 0.9827691 0.4477623 0.9827691 0.05245959 0.9733448 0.05246233 0.9500672 0.444316 0.950067 0.05453199 0.9406429 0.05453199 0.9406429 0.05453199 0.9406429 0.444316 0.9500672 0.444316 0.9827692 0.03344076 0.9827693 0.05045711 0.9733448 0.05045711 0.9733448 0.05045711 0.9733448 0.03344076 0.9827692 0.03344076 0.9205045 0.06860131 0.9205045 0.06573468 0.922895 0.06716841 0.8856008 0.04344737 0.8856008 0.03834468 0.8879913 0.04344791 0.9205045 0.06860131 0.922895 0.06716841 0.922895 0.4479362 0.922895 0.4479362 0.9205044 0.4451404 0.9205045 0.06860131 0.9682843 0.05245959 0.9682843 0.4477623 0.9659755 0.449809 0.9659755 0.449809 0.9659755 0.05146002 0.9682843 0.05245959 0.9351677 0.05453199 0.9328586 0.05357486 0.9351676 0.05254662 0.9540604 0.05254662 0.9563695 0.05357486 0.9540604 0.05453199 0.8663763 0.112247 0.8639857 0.112247 0.8639857 0.04344791 0.8639857 0.04344791 0.8663763 0.04344737 0.8663763 0.112247 0.9164238 0.04333305 0.9068641 0.04333263 0.9068642 0.4451404 0.9068642 0.4451404 0.9164239 0.4451404 0.9164238 0.04333305 0.8817301 0.05942744 0.8721704 0.05942744 0.8721704 0.04344743 0.8721704 0.04344743 0.8817301 0.04344743 0.8817301 0.05942744 0.9682843 0.05245959 0.9659755 0.05146002 0.9682844 0.05045711 0.9406429 0.4463022 0.9406429 0.05778294 0.9500672 0.05778294 0.9500672 0.05778294 0.9500669 0.4463022 0.9406429 0.4463022 0.8721698 0.06455844 0.8817294 0.06455844 0.8817294 0.04141277 0.8817294 0.04141277 0.8721698 0.04141277 0.8721698 0.06455844 0.8663763 0.03834468 0.8663763 0.04344737 0.8639857 0.04344791 0.9540604 0.4463022 0.9540604 0.05453199 0.9563695 0.05357486 0.9563695 0.05357486 0.9563694 0.4463022 0.9540604 0.4463022 0.8639857 0.112247 0.8663763 0.112247 0.8663763 0.4439374 0.8663763 0.4439374 0.8639857 0.4453705 0.8639857 0.112247 0.8988894 0.06716841 0.9012799 0.0686016 0.9012798 0.4451404 0.9012798 0.4451404 0.8988894 0.4479362 0.8988894 0.06716841 0.9682844 0.03347867 0.9682844 0.05045711 0.9659755 0.05146002 0.9659755 0.05146002 0.9659755 0.03347867 0.9682844 0.03347867 0.9068641 0.04333263 0.9164238 0.04333305 0.9164238 0.0405882 0.9164238 0.0405882 0.9068641 0.0405882 0.9068641 0.04333263 0.8817294 0.06455844 0.8721698 0.06455844 0.8721698 0.4439374 0.8721698 0.4439374 0.8817294 0.4439374 0.8817294 0.06455844 0.8856008 0.112247 0.8879913 0.112247 0.8879913 0.4453706 0.8879913 0.4453706 0.8856008 0.4439374 0.8856008 0.112247 0.9894861 0.03347867 0.9894863 0.05146026 0.9871771 0.05046123 0.9871771 0.05046123 0.987177 0.03347867 0.9894861 0.03347867 0.8721704 0.05942744 0.8817301 0.05942744 0.8817281 0.4439374 0.8817281 0.4439374 0.8721686 0.4439374 0.8721704 0.05942744 0.9012799 0.06573468 0.9012799 0.0686016 0.8988894 0.06716841 0.9871771 0.05046123 0.9894863 0.05146026 0.9871771 0.05246233 0.9827693 0.05045711 0.982769 0.4451074 0.9733451 0.4451074 0.9733451 0.4451074 0.9733448 0.05045711 0.9827693 0.05045711 0.9328586 0.4463022 0.9328586 0.05357486 0.9351677 0.05453199 0.9351677 0.05453199 0.9351676 0.4463022 0.9328586 0.4463022 0.8879913 0.112247 0.8856008 0.112247 0.8856008 0.04344737 0.8856008 0.04344737 0.8879913 0.04344791 0.8879913 0.112247 0.9068642 0.06860131 0.9164238 0.06860131 0.9164238 0.4451404 0.9164238 0.4451404 0.9068642 0.4451404 0.9068642 0.06860131 0.9894863 0.05146026 0.9894861 0.449809 0.9871768 0.4477623 0.9871768 0.4477623 0.9871771 0.05246233 0.9894863 0.05146026 0.9406429 0.05254662 0.9406429 0.05453199 0.950067 0.05453199 0.950067 0.05453199 0.9500672 0.05254662 0.9406429 0.05254662 0.9733449 0.03347867 0.9733448 0.05046117 0.982769 0.05045706 0.982769 0.05045706 0.982769 0.03347867 0.9733449 0.03347867 0.8817294 0.03834468 0.8721698 0.03834468 0.8721698 0.04141277 0.8721698 0.04141277 0.8817294 0.04141277 0.8817294 0.03834468</float_array>
          <technique_common>
            <accessor source="#woodplanks_straps-mesh-map-0-array" count="192" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="woodplanks_straps-mesh-colors-geom-woodplanks_straps-map0" name="geom-woodplanks_straps-map0">
          <float_array id="woodplanks_straps-mesh-colors-geom-woodplanks_straps-map0-array" count="768">1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1</float_array>
          <technique_common>
            <accessor source="#woodplanks_straps-mesh-colors-geom-woodplanks_straps-map0-array" count="192" stride="4">
              <param name="R" type="float"/>
              <param name="G" type="float"/>
              <param name="B" type="float"/>
              <param name="A" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="woodplanks_straps-mesh-vertices">
          <input semantic="POSITION" source="#woodplanks_straps-mesh-positions"/>
        </vertices>
        <triangles material="woodplanks-material" count="64">
          <input semantic="VERTEX" source="#woodplanks_straps-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#woodplanks_straps-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#woodplanks_straps-mesh-map-0" offset="2" set="0"/>
          <input semantic="COLOR" source="#woodplanks_straps-mesh-colors-geom-woodplanks_straps-map0" offset="3" set="0"/>
          <p>40 0 0 0 2 1 1 1 45 2 2 2 45 2 3 3 32 3 4 4 40 0 5 5 32 3 6 6 33 4 7 7 41 5 8 8 41 5 9 9 40 0 10 10 32 3 11 11 43 6 12 12 9 7 13 13 35 8 14 14 35 8 15 15 34 9 16 16 43 6 17 17 55 10 18 18 10 11 19 19 56 12 20 20 56 12 21 21 44 13 22 22 55 10 23 23 49 14 24 24 19 14 25 25 37 14 26 26 59 15 27 27 13 15 28 28 25 15 29 29 49 16 30 30 37 16 31 31 36 16 32 32 36 17 33 33 50 17 34 34 49 17 35 35 3 18 36 36 17 18 37 37 24 18 38 38 24 19 39 39 54 19 40 40 3 19 41 41 38 20 42 42 63 20 43 43 39 20 44 44 50 14 45 45 36 14 46 46 5 14 47 47 46 21 48 48 29 21 49 49 24 21 50 50 24 22 51 51 47 22 52 52 46 22 53 53 42 23 54 54 20 24 55 55 6 25 56 56 6 25 57 57 8 26 58 58 42 23 59 59 57 27 60 60 60 28 61 61 62 29 62 62 62 29 63 63 61 30 64 64 57 27 65 65 3 21 66 66 54 21 67 67 48 21 68 68 44 13 69 69 4 31 70 70 7 32 71 71 7 32 72 72 55 10 73 73 44 13 74 74 51 33 75 75 31 34 76 76 16 35 77 77 16 35 78 78 14 36 79 79 51 33 80 80 17 37 81 81 47 37 82 82 24 37 83 83 15 14 84 84 5 14 85 85 36 14 86 86 36 14 87 87 27 14 88 88 15 14 89 89 29 21 90 90 46 21 91 91 53 21 92 92 53 38 93 93 58 38 94 94 29 38 95 95 58 21 96 96 22 21 97 97 39 21 98 98 39 21 99 99 63 21 100 100 58 21 101 101 12 21 102 102 48 21 103 103 54 21 104 104 54 39 105 105 26 39 106 106 12 39 107 107 20 24 108 108 42 23 109 109 23 40 110 110 23 40 111 111 52 41 112 112 20 24 113 113 31 34 114 114 51 33 115 115 52 41 116 116 52 41 117 117 23 40 118 118 31 34 119 119 30 42 120 120 28 42 121 121 37 42 122 122 37 14 123 123 19 14 124 124 30 14 125 125 27 14 126 126 11 14 127 127 1 14 128 128 1 14 129 129 15 14 130 130 27 14 131 131 60 28 132 132 57 27 133 133 21 43 134 134 21 43 135 135 18 44 136 136 60 28 137 137 53 45 138 138 22 45 139 139 58 45 140 140 1 46 141 141 11 46 142 142 0 46 143 143 10 11 144 144 61 30 145 145 62 29 146 146 62 29 147 147 56 12 148 148 10 11 149 149 26 21 150 150 63 21 151 151 38 21 152 152 38 21 153 153 12 21 154 154 26 21 155 155 28 14 156 156 30 14 157 157 59 14 158 158 59 14 159 159 25 14 160 160 28 14 161 161 18 44 162 162 21 43 163 163 7 32 164 164 7 32 165 165 4 31 166 166 18 44 167 167 11 47 168 168 25 47 169 169 13 47 170 170 13 48 171 171 0 48 172 172 11 48 173 173 6 25 174 174 35 8 175 175 9 7 176 176 9 7 177 177 8 26 178 178 6 25 179 179 34 9 180 180 45 2 181 181 2 1 182 182 2 1 183 183 43 6 184 184 34 9 185 185 41 5 186 186 33 4 187 187 14 36 188 188 14 36 189 189 16 35 190 190 41 5 191 191</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="EnvironmentAmbientLight" name="EnvironmentAmbientLight" type="NODE">
        <matrix sid="transform">0.01 0 0 0 0 0.01 0 0 0 0 0.01 0 0 0 0 1</matrix>
        <instance_light url="#EnvironmentAmbientLight-light"/>
      </node>
      <node id="old_straps" name="old_straps" type="NODE">
        <matrix sid="transform">0.01 0 0 0 0 0.01 0 0 0 0 0.01 0 0 0 0 1</matrix>
        <instance_geometry url="#woodplanks_straps-mesh" name="old_straps">
          <bind_material>
            <technique_common>
              <instance_material symbol="woodplanks-material" target="#woodplanks-material">
                <bind_vertex_input semantic="woodplanks_straps-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="Camera" name="Camera" type="NODE">
        <matrix sid="transform">0.6859207 -0.3240135 0.6515582 7.358891 0.7276763 0.3054208 -0.6141704 -6.925791 0 0.8953956 0.4452714 4.958309 0 0 0 1</matrix>
        <instance_camera url="#Camera-camera"/>
      </node>
      <node id="Light" name="Light" type="NODE">
        <matrix sid="transform">-0.2908646 -0.7711008 0.5663932 4.076245 0.9551712 -0.1998834 0.2183912 1.005454 -0.05518906 0.6045247 0.7946723 5.903862 0 0 0 1</matrix>
        <instance_light url="#Light-light"/>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>