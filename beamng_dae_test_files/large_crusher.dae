<?xml version='1.0' encoding='utf-8'?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.70.0 commit date:2014-03-05, commit time:18:37, hash:19f7f9a</authoring_tool>
    </contributor>
    <created>2014-07-11T03:26:13</created>
    <modified>2014-07-11T03:26:13</modified>
    <unit meter="1" name="meter" />
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="boxes-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.512 0.512 0.512 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.25 0.25 0.25 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="slider-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.0366394 0 0.64 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="boxes-material" name="boxes">
      <instance_effect url="#boxes-effect" />
    </material>
    <material id="slider-material" name="slider">
      <instance_effect url="#slider-effect" />
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Cube_006-mesh" name="Cube.006">
      <mesh>
        <source id="Cube_006-mesh-positions">
          <float_array count="24" id="Cube_006-mesh-positions-array">4 2 0 5 2 0 5 -2.000001 0 4 -1.999999 0 4 2.000001 3 5 1.999998 3 5 -2.000001 3 4 -2 3</float_array>
          <technique_common>
            <accessor count="8" source="#Cube_006-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_006-mesh-normals">
          <float_array count="30" id="Cube_006-mesh-normals-array">1 0 0 0 0 1 2.98023e-6 1 6.75519e-7 -9.53674e-7 -1 0 -1 0 0 1 0 0 0 0 1 0 1 -3.17891e-7 -1.90735e-6 -1 -3.17891e-7 -1 0 0</float_array>
          <technique_common>
            <accessor count="10" source="#Cube_006-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_006-mesh-vertices">
          <input semantic="POSITION" source="#Cube_006-mesh-positions" />
        </vertices>
        <lines count="11">
          <input offset="0" semantic="VERTEX" source="#Cube_006-mesh-vertices" />
          <p>3 1 4 6 0 5 1 6 3 6 2 4 3 5 1 7 0 6 0 2 3 4</p>
        </lines>
        <polylist count="10" material="boxes-material">
          <input offset="0" semantic="VERTEX" source="#Cube_006-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_006-mesh-normals" />
          <vcount>3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>5 0 6 0 2 0 7 1 6 1 5 1 4 2 5 2 1 2 2 3 6 3 7 3 0 4 3 4 7 4 1 5 5 5 2 5 4 6 7 6 5 6 0 7 4 7 1 7 3 8 2 8 7 8 4 9 0 9 7 9</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cube_001-mesh" name="Cube.001">
      <mesh>
        <source id="Cube_001-mesh-positions">
          <float_array count="24" id="Cube_001-mesh-positions-array">2 4 0 2 5 0 -2 5 0 -1.999999 4 0 2.000001 4 3 1.999999 5 3 -2.000001 5 3 -2 4 3</float_array>
          <technique_common>
            <accessor count="8" source="#Cube_001-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-normals">
          <float_array count="30" id="Cube_001-mesh-normals-array">1 1.90735e-6 3.17891e-7 0 1 0 0 0 1 -1 -9.53674e-7 -3.17891e-7 0 -1 0 1 0 -3.17891e-7 0 1 0 0 0 1 -1 -9.53674e-7 -3.17891e-7 0 -1 0</float_array>
          <technique_common>
            <accessor count="10" source="#Cube_001-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_001-mesh-vertices">
          <input semantic="POSITION" source="#Cube_001-mesh-positions" />
        </vertices>
        <lines count="11">
          <input offset="0" semantic="VERTEX" source="#Cube_001-mesh-vertices" />
          <p>3 1 4 6 0 5 1 6 3 6 2 4 3 5 1 7 0 6 0 2 3 4</p>
        </lines>
        <polylist count="10" material="boxes-material">
          <input offset="0" semantic="VERTEX" source="#Cube_001-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_001-mesh-normals" />
          <vcount>3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>1 0 5 0 4 0 2 1 6 1 5 1 5 2 6 2 7 2 2 3 3 3 7 3 7 4 3 4 0 4 0 5 1 5 4 5 1 6 2 6 5 6 4 7 5 7 7 7 6 8 2 8 7 8 4 9 7 9 0 9</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cube_005-mesh" name="Cube.005">
      <mesh>
        <source id="Cube_005-mesh-positions">
          <float_array count="24" id="Cube_005-mesh-positions-array">2 -4 0 2 -5 0 -2 -5 0 -1.999999 -4 0 2.000001 -4 3 1.999999 -5 3 -2.000001 -5 3 -2 -4 3</float_array>
          <technique_common>
            <accessor count="8" source="#Cube_005-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_005-mesh-normals">
          <float_array count="30" id="Cube_005-mesh-normals-array">0 -1 0 0 0 1 1 -1.90735e-6 3.17891e-7 -1 9.53674e-7 -3.17891e-7 0 1 0 0 -1 0 0 0 1 1 0 -3.17891e-7 -1 9.53674e-7 -3.17891e-7 0 1 0</float_array>
          <technique_common>
            <accessor count="10" source="#Cube_005-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_005-mesh-vertices">
          <input semantic="POSITION" source="#Cube_005-mesh-positions" />
        </vertices>
        <lines count="11">
          <input offset="0" semantic="VERTEX" source="#Cube_005-mesh-vertices" />
          <p>3 1 4 6 0 5 1 6 3 6 2 4 3 5 1 7 0 6 0 2 3 4</p>
        </lines>
        <polylist count="10" material="boxes-material">
          <input offset="0" semantic="VERTEX" source="#Cube_005-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_005-mesh-normals" />
          <vcount>3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>5 0 6 0 2 0 7 1 6 1 5 1 4 2 5 2 1 2 2 3 6 3 7 3 0 4 3 4 7 4 1 5 5 5 2 5 4 6 7 6 5 6 0 7 4 7 1 7 3 8 2 8 7 8 4 9 0 9 7 9</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cube_007-mesh" name="Cube.007">
      <mesh>
        <source id="Cube_007-mesh-positions">
          <float_array count="24" id="Cube_007-mesh-positions-array">-4 2 0 -5 2 0 -5 -2 0 -4 -1.999999 0 -4 2.000001 3 -5 1.999999 3 -5 -2 3 -4 -1.999999 3</float_array>
          <technique_common>
            <accessor count="8" source="#Cube_007-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_007-mesh-normals">
          <float_array count="30" id="Cube_007-mesh-normals-array">-1 0 0 0 0 1 -1.90735e-6 1 3.17891e-7 9.53674e-7 -1 0 1 0 0 -1 0 0 0 0 1 0 1 -3.17891e-7 9.53674e-7 -1 0 1 0 0</float_array>
          <technique_common>
            <accessor count="10" source="#Cube_007-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_007-mesh-vertices">
          <input semantic="POSITION" source="#Cube_007-mesh-positions" />
        </vertices>
        <lines count="11">
          <input offset="0" semantic="VERTEX" source="#Cube_007-mesh-vertices" />
          <p>3 1 4 6 0 5 1 6 3 6 2 4 3 5 1 7 0 6 0 2 3 4</p>
        </lines>
        <polylist count="10" material="boxes-material">
          <input offset="0" semantic="VERTEX" source="#Cube_007-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_007-mesh-normals" />
          <vcount>3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>2 0 6 0 5 0 5 1 6 1 7 1 1 2 5 2 4 2 2 3 3 3 7 3 7 4 3 4 0 4 1 5 2 5 5 5 4 6 5 6 7 6 0 7 1 7 4 7 6 8 2 8 7 8 4 9 7 9 0 9</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cube_008-mesh" name="Cube.008">
      <mesh>
        <source id="Cube_008-mesh-positions">
          <float_array count="24" id="Cube_008-mesh-positions-array">-2 -2 5 -2 2 5 2 2 5 2 -2 5 -2 -2 6 -2 2 6 2 2 6 2 -2 6</float_array>
          <technique_common>
            <accessor count="8" source="#Cube_008-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_008-mesh-normals">
          <float_array count="36" id="Cube_008-mesh-normals-array">-1 0 0 0 1 0 1 0 0 0 -1 0 0 0 -1 0 0 1 -1 0 0 0 1 0 1 0 0 0 -1 0 0 0 -1 0 0 1</float_array>
          <technique_common>
            <accessor count="12" source="#Cube_008-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_008-mesh-vertices">
          <input semantic="POSITION" source="#Cube_008-mesh-positions" />
        </vertices>
        <lines count="10">
          <input offset="0" semantic="VERTEX" source="#Cube_008-mesh-vertices" />
          <p>3 4 0 5 1 6 2 7 4 6 1 3 2 4 1 7 3 5 0 6</p>
        </lines>
        <polylist count="12" material="boxes-material">
          <input offset="0" semantic="VERTEX" source="#Cube_008-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_008-mesh-normals" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>4 0 5 0 1 0 5 1 6 1 2 1 6 2 7 2 3 2 7 3 4 3 0 3 0 4 1 4 2 4 7 5 6 5 5 5 0 6 4 6 1 6 1 7 5 7 2 7 2 8 6 8 3 8 3 9 7 9 0 9 3 10 0 10 2 10 4 11 7 11 5 11</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Plane-mesh" name="Plane">
      <mesh>
        <source id="Plane-mesh-positions">
          <float_array count="36" id="Plane-mesh-positions-array">-0.5 -0.5 0.02517932 0.5 -0.5 0.02517932 -0.5 0.5 0.02517932 0.5 0.5 0.02517932 6 -0.5 0.02517932 6 0.5 0.02517932 -6 -0.5 0.02517932 -6 0.5 0.02517932 -0.5 6 0.02517932 0.5 6 0.02517932 -0.5 -6 0.02517932 0.5 -6 0.02517932</float_array>
          <technique_common>
            <accessor count="12" source="#Plane-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Plane-mesh-normals">
          <float_array count="30" id="Plane-mesh-normals-array">0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1</float_array>
          <technique_common>
            <accessor count="10" source="#Plane-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane-mesh-vertices">
          <input semantic="POSITION" source="#Plane-mesh-positions" />
        </vertices>
        <polylist count="10" material="slider-material">
          <input offset="0" semantic="VERTEX" source="#Plane-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Plane-mesh-normals" />
          <vcount>3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>0 0 1 0 3 0 3 1 1 1 4 1 0 2 2 2 7 2 2 3 3 3 9 3 1 4 0 4 10 4 2 5 0 5 3 5 5 6 3 6 4 6 6 7 0 7 7 7 8 8 2 8 9 8 11 9 1 9 10 9</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers />
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="cube_L" name="cube_L" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_006-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="boxes-material" target="#boxes-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="cube_B" name="cube_B" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_001-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="boxes-material" target="#boxes-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="cube_F" name="cube_F" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_005-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="boxes-material" target="#boxes-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="cube_R" name="cube_R" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_007-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="boxes-material" target="#boxes-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="cube_T" name="cube_T" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_008-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="boxes-material" target="#boxes-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="slidenodes_slider" name="slidenodes_slider" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="slider-material" target="#slider-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene" />
  </scene>
</COLLADA>