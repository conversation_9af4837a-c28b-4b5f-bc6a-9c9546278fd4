<?xml version='1.0' encoding='utf-8'?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2017-09-11, commit time:10:43, hash:5bd8ac9</authoring_tool>
    </contributor>
    <created>2018-03-06T20:33:03</created>
    <modified>2018-03-06T20:33:03</modified>
    <unit meter="1" name="meter" />
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="citybus_intdisplay-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.09905827 0.1307403 0.1343733 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="citybus_nextStop_DSP-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="citybus_intdisplay-material" name="citybus_intdisplay">
      <instance_effect url="#citybus_intdisplay-effect" />
    </material>
    <material id="citybus_nextStop_DSP-material" name="citybus_nextStop_DSP">
      <instance_effect url="#citybus_nextStop_DSP-effect" />
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="citybus_intdisplay-mesh" name="citybus_intdisplay">
      <mesh>
        <source id="citybus_intdisplay-mesh-positions">
          <float_array count="489" id="citybus_intdisplay-mesh-positions-array">-0.2387505 -0.007172226 -0.3364809 -0.2387505 0.03957253 -0.07136988 0.2387504 -0.007172286 -0.3364809 0.2387504 0.03957247 -0.07136988 -0.2387504 0.0329678 -0.07020545 -0.2387504 -0.01377815 -0.3353162 0.2387504 -0.01377815 -0.3353162 0.2387504 0.0329678 -0.07020545 0 -0.007172286 -0.3364809 0 0.03957247 -0.07136988 0 -0.01377815 -0.3353162 0 0.0329678 -0.07020545 0 -0.01516151 -0.3817918 0 0.04756176 -0.02605915 0 -0.03408724 -0.3784547 0 0.02863788 -0.02272224 -0.2839968 -0.01516151 -0.3817918 -0.2947603 -0.01329255 -0.3711917 -0.2913484 -0.01456904 -0.3784316 -0.2947603 -0.03221815 -0.3678546 -0.2839968 -0.03408724 -0.3784547 -0.2913484 -0.03349465 -0.3750942 -0.2839968 0.02863788 -0.02272224 -0.2947602 0.02676886 -0.03332209 -0.2913483 0.02804541 -0.02608227 -0.2947604 0.0456928 -0.03665924 -0.2839968 0.04756182 -0.02605915 -0.2913485 0.04696929 -0.02941966 0.2947603 -0.01329255 -0.3711917 0.2839968 -0.01516157 -0.3817918 0.2913484 -0.01456904 -0.3784316 0.2839968 0.04756176 -0.02605915 0.2947604 0.04569274 -0.03665924 0.2913485 0.04696923 -0.02941966 0.2839969 -0.03408724 -0.3784547 0.2947604 -0.03221815 -0.3678546 0.2913485 -0.03349465 -0.3750942 0.2947603 0.0267688 -0.03332209 0.2839968 0.02863788 -0.02272224 0.2913484 0.02804541 -0.02608227 -0.285862 -0.01629269 0.00309658 -0.285862 -0.01629269 -0.02896833 -0.285862 0.01629334 0.00309658 -0.285862 0.01629334 -0.02896833 0.285862 -0.01629269 0.00309658 0.285862 -0.01629269 -0.02896833 0.285862 0.01629334 0.00309658 0.285862 0.01629334 -0.02896833 0 0.01629334 0.00309658 0 0.01629334 -0.04701137 0 -0.01629269 0.00309658 0 -0.01629269 -0.04701137 0.08953142 0.01629334 -0.04701137 0.149219 0.01629334 -0.04701137 0.149219 -0.01629269 -0.04701137 0.08953142 -0.01629269 -0.04701137 -0.08953142 -0.01629269 -0.04701137 -0.149219 -0.01629269 -0.04701137 -0.149219 0.01629334 -0.04701137 -0.08953142 0.01629334 -0.04701137 0.08953142 0.01629334 -0.1120307 0.149219 0.01629334 -0.1120307 0.149219 -0.01629269 -0.2967041 0.08953142 -0.01629269 -0.2967041 -0.08953142 -0.01629269 -0.2967041 -0.149219 -0.01629269 -0.2967041 -0.149219 0.01629334 -0.1120307 -0.08953142 0.01629334 -0.1120307 -0.2387505 0.01620012 -0.2039253 0.2387504 0.01620006 -0.2039253 -0.2387504 0.009594798 -0.2027609 0.2387504 0.009594798 -0.2027609 -0.2947603 -0.002724647 -0.2005884 -0.2947603 0.01620006 -0.2039253 0.2947603 0.01620006 -0.2039253 0.2947604 -0.002724647 -0.2005884 0 -0.002724647 -0.2005884 -0.2387505 0.01620012 -0.2039253 -0.2387505 -0.007172226 -0.3364809 -0.2387505 -0.007172226 -0.3364809 0 -0.007172286 -0.3364809 0 0.03957247 -0.07136988 0.2387504 0.03957247 -0.07136988 0.2387504 0.03957247 -0.07136988 0.2387504 0.01620006 -0.2039253 0.2387504 -0.007172286 -0.3364809 0.2387504 -0.007172286 -0.3364809 -0.2387504 -0.01377815 -0.3353162 0.2387504 0.0329678 -0.07020545 0.2387504 -0.01377815 -0.3353162 -0.2387504 0.0329678 -0.07020545 -0.2387505 0.03957253 -0.07136988 -0.2387505 0.03957253 -0.07136988 -0.2913484 -0.01456904 -0.3784316 -0.2839968 -0.01516151 -0.3817918 -0.2913484 -0.03349465 -0.3750942 -0.2947603 -0.03221815 -0.3678546 -0.2913483 0.02804541 -0.02608227 -0.2839968 0.02863788 -0.02272224 -0.2913485 0.04696929 -0.02941966 -0.2947604 0.0456928 -0.03665924 0.2913485 -0.03349465 -0.3750942 0.2839969 -0.03408724 -0.3784547 0.2913484 -0.01456904 -0.3784316 0.2947603 -0.01329255 -0.3711917 0.2913485 0.04696923 -0.02941966 0.2839968 0.04756176 -0.02605915 0.2913484 0.02804541 -0.02608227 0.2947603 0.0267688 -0.03332209 0 -0.01516151 -0.3817918 -0.2947603 -0.002724647 -0.2005884 -0.2947602 0.02676886 -0.03332209 -0.2947603 0.01620006 -0.2039253 -0.2947603 -0.01329255 -0.3711917 0 0.02863788 -0.02272224 0.2947603 0.01620006 -0.2039253 0.2947604 0.04569274 -0.03665924 0.2839968 -0.01516157 -0.3817918 0.2947604 -0.002724647 -0.2005884 0.2947604 -0.03221815 -0.3678546 0.2839968 0.02863788 -0.02272224 -0.2839968 -0.03408724 -0.3784547 -0.2839968 0.04756182 -0.02605915 0 0.04756176 -0.02605915 0 -0.03408724 -0.3784547 -0.285862 0.01629334 0.00309658 -0.285862 -0.01629269 0.00309658 -0.285862 -0.01629269 -0.02896833 -0.285862 -0.01629269 -0.02896833 -0.285862 0.01629334 -0.02896833 -0.285862 0.01629334 -0.02896833 0.149219 0.01629334 -0.04701137 0.149219 0.01629334 -0.04701137 0.285862 0.01629334 -0.02896833 0.285862 0.01629334 -0.02896833 0.285862 0.01629334 0.00309658 0.285862 -0.01629269 0.00309658 0.285862 -0.01629269 -0.02896833 0.285862 -0.01629269 -0.02896833 -0.149219 -0.01629269 -0.04701137 -0.149219 -0.01629269 -0.04701137 -0.08953142 0.01629334 -0.04701137 -0.08953142 0.01629334 -0.04701137 0 0.01629334 -0.04701137 0.08953142 -0.01629269 -0.04701137 0.08953142 -0.01629269 -0.04701137 0 -0.01629269 -0.04701137 0.08953142 0.01629334 -0.04701137 0.08953142 0.01629334 -0.04701137 0.149219 -0.01629269 -0.04701137 0.149219 -0.01629269 -0.04701137 -0.08953142 -0.01629269 -0.04701137 -0.08953142 -0.01629269 -0.04701137 -0.149219 0.01629334 -0.04701137 -0.149219 0.01629334 -0.04701137 0.08953142 0.01629334 -0.1120307 0.149219 0.01629334 -0.1120307 0.149219 -0.01629269 -0.2967041 0.08953142 -0.01629269 -0.2967041 -0.08953142 -0.01629269 -0.2967041 -0.149219 -0.01629269 -0.2967041 -0.149219 0.01629334 -0.1120307 -0.08953142 0.01629334 -0.1120307</float_array>
          <technique_common>
            <accessor count="163" source="#citybus_intdisplay-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="citybus_intdisplay-mesh-normals">
          <float_array count="90" id="citybus_intdisplay-mesh-normals-array">0 -0.1736217 -0.9848124 0 -0.9848124 0.1736217 0.2155563 -0.1695641 -0.9616567 0 0.1736513 0.9848073 0.2155563 0.1695641 0.9616567 -0.2155272 0.1695652 0.961663 -0.2155563 -0.1695641 -0.9616567 1 0 0 -1 0 0 0 0.1736217 0.9848124 0.7071076 -0.1227802 -0.6963648 0.7071229 -0.1227775 -0.6963497 -0.7071076 -0.1227802 -0.6963648 -0.7071229 -0.1227775 -0.6963497 -0.9764963 -0.03741663 -0.2122619 -0.2155563 0.1695641 0.9616567 -0.7071076 0.1227802 0.6963648 -0.9764963 0.03741663 0.2122619 -0.7071229 0.1227775 0.6963497 0.9764963 -0.03741663 -0.2122619 0.9764899 -0.03741639 -0.212291 0.7071076 0.1227802 0.6963648 0.7071229 0.1227775 0.6963497 0.9764963 0.03741663 0.2122619 0 0.9848124 -0.1736217 0 1 0 0 -1 0 -0.1308942 0 -0.9913964 0 0 -1 0.1308942 0 -0.9913964</float_array>
          <technique_common>
            <accessor count="30" source="#citybus_intdisplay-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="citybus_intdisplay-mesh-map-0">
          <float_array count="792" id="citybus_intdisplay-mesh-map-0-array">0.01997393 0.9378944 0.4249404 0.9269775 0.4249404 0.9378951 0.1876037 0.1353449 0.1990112 0.1239373 0.4999999 0.1239373 0.3764873 0.733856 0.1166836 0.767795 0.1166844 0.7338496 0.9766894 0.7338832 0.7168931 0.7678192 0.7168931 0.733877 0.9766888 0.7338827 0.7168911 0.7678314 0.9766888 0.7678254 0.116684 0.7338812 0.3764852 0.7678274 0.3764851 0.7338817 0.2221233 0.9790414 0.4249404 0.9681235 0.2221232 0.9681229 0.01930505 0.9790409 0.2221226 0.9681222 0.2221227 0.9790409 0.4249404 0.9378932 0.01996743 0.9269734 0.4249404 0.9269737 0.01996797 0.9378947 0.4249404 0.9269756 0.01996797 0.9269756 0.3764873 0.7678021 0.3839026 0.7338564 0.3839018 0.7678025 0.3764852 0.7678274 0.3838996 0.7338812 0.3764851 0.7338817 0.8123963 0.1353449 0.4999999 0.1239373 0.8009886 0.123937 0.4249404 0.9378932 0.01996797 0.9269756 0.01996797 0.937893 0.3838996 0.7678274 0.3913139 0.7338817 0.3838996 0.7338812 0.7168911 0.733889 0.7094768 0.7678318 0.7168911 0.7678314 0.5466884 0.7678295 0.7020628 0.733889 0.5466883 0.7338853 0.7094766 0.733889 0.7020631 0.7678314 0.7094768 0.7678318 0.3839018 0.7678025 0.3913164 0.7338568 0.3913164 0.7678028 0.7168931 0.733877 0.7094792 0.7678191 0.7094792 0.7338769 0.7020659 0.7338765 0.5466908 0.7678108 0.5466912 0.7338666 0.7094792 0.7338769 0.7020651 0.7678189 0.7020659 0.7338765 0.02070772 0.3171403 0.111782 0.0982747 0.111782 0.3171404 0.4999999 0.3153537 0.8123962 0.4953626 0.4999999 0.50677 0.1990118 0.5067703 0.19122 0.5031541 0.1876038 0.4953626 0.1912197 0.1275539 0.1990112 0.1239373 0.1876037 0.1353449 0.4999999 0.50677 0.1990118 0.5067703 0.1876038 0.4953626 0.8009882 0.5067705 0.4999999 0.50677 0.8123962 0.4953626 0.9792923 0.3171403 0.8882178 0.09827482 0.9792923 0.04096293 0.8087802 0.1275537 0.8123963 0.1353449 0.8009886 0.123937 0.8123962 0.4953626 0.80878 0.5031543 0.8009882 0.5067705 0.4999998 0.6108197 0.4999998 0.536006 0.8882179 0.536006 0.8882178 0.09827482 0.5 0.09827476 0.5 0.02346074 0.9617905 0.6108197 0.4999998 0.6108197 0.8882179 0.536006 0.8882178 0.09827482 0.5 0.02346074 0.9617903 0.02346074 0.9617905 0.6108197 0.8882179 0.536006 0.9792923 0.5933176 0.9792923 0.5933176 0.9737444 0.605271 0.9617905 0.6108197 0.9737444 0.02900898 0.9792923 0.04096293 0.9617903 0.02346074 0.8882178 0.09827482 0.9617903 0.02346074 0.9792923 0.04096293 0.111782 0.5360061 0.4999998 0.536006 0.4999998 0.6108197 0.4999998 0.6108197 0.03820967 0.6108197 0.111782 0.5360061 0.02625548 0.6052711 0.02070772 0.5933178 0.03820967 0.6108197 0.02070772 0.04096281 0.02625548 0.02900886 0.03820967 0.02346062 0.03820967 0.02346062 0.5 0.02346074 0.111782 0.0982747 0.5 0.02346074 0.5 0.09827476 0.111782 0.0982747 0.111782 0.5360061 0.03820967 0.6108197 0.02070772 0.5933178 0.03820967 0.02346062 0.111782 0.0982747 0.02070772 0.04096281 1.025647 0.7716923 1.087915 0.7199749 1.087915 0.7716923 1.959966 0.6347939 1.959966 0.678853 1.737523 0.7082256 0.0879153 0.7716923 0.02564746 0.7199749 0.02564746 0.7716923 0.02924829 0.6347939 0.02924823 0.678853 0.2516905 0.7082256 1.444202 0.9802761 1.668576 0.9272283 1.444202 0.9272288 0.9775426 0.9802758 0.831793 0.9272285 0.9775426 0.9272285 0.4946055 0.6347939 0.4946055 0.708226 0.6403549 0.708226 1.494607 0.6347939 1.494607 0.7082256 1.348857 0.7082256 0.9599639 0.6347935 0.7375211 0.708226 0.959964 0.678853 0.4946055 0.6347939 0.7375211 0.708226 0.9599639 0.6347935 0.6685759 0.9802758 0.4442016 0.9272288 0.6685759 0.9272283 0.9804723 0.8942005 0.5739938 0.7970341 0.9804723 0.7970341 1.494607 0.6347939 1.640357 0.7082256 1.494607 0.7082256 1.494607 0.6347939 1.737523 0.7082256 1.640357 0.7082256 1.029248 0.6347939 1.251691 0.7082256 1.029248 0.678853 1.494607 0.6347939 1.251691 0.7082256 1.029248 0.6347939 0.831793 0.9802759 0.6860434 0.9272285 0.831793 0.9272285 1.424552 0.7970341 1.318706 0.8500816 1.018074 0.7970343 0.4946055 0.6347939 0.3488565 0.7082256 0.4946055 0.708226 0.4946055 0.6347939 0.2516905 0.7082256 0.3488565 0.7082256 0.5529123 0.7970341 0.4470661 0.8942005 0.5529123 0.8942005 1.552912 0.8942005 1.447066 0.7970342 1.552912 0.7970342 0.4245525 0.7970341 0.3187062 0.8500815 0.4245525 0.8500816 1.980472 0.8942004 1.573994 0.7970341 1.980472 0.7970341 1.018075 0.8942006 1.123921 0.8411531 1.018075 0.8411531 0.01807522 0.8942006 0.123921 0.8411531 0.4245532 0.8942004 0.1876037 0.1353449 0.4999999 0.3153537 0.1876037 0.3153538 0.1876038 0.4953626 0.4999999 0.3153537 0.4999999 0.50677 0.8882179 0.536006 0.9792923 0.3171403 0.9792923 0.5933176 0.8123963 0.1353449 0.4999999 0.3153537 0.4999999 0.1239373 0.02070772 0.3171403 0.111782 0.5360061 0.02070772 0.5933178 0.5466912 0.7338666 0.3913164 0.7678028 0.3913164 0.7338568 0.3913139 0.7678276 0.5466883 0.7338853 0.3913139 0.7338817 0.2221227 0.9790409 0.4249404 0.9681233 0.4249404 0.9790409 0.01930624 0.9790414 0.2221232 0.9681229 0.019306 0.9681223 0.01997393 0.9378944 0.01997393 0.9269767 0.4249404 0.9269775 0.3764873 0.733856 0.3764873 0.7678021 0.1166836 0.767795 0.9766894 0.7338832 0.9766894 0.7678254 0.7168931 0.7678192 0.9766888 0.7338827 0.7168911 0.733889 0.7168911 0.7678314 0.116684 0.7338812 0.116684 0.767827 0.3764852 0.7678274 0.2221233 0.9790414 0.4249404 0.9790414 0.4249404 0.9681235 0.01930505 0.9790409 0.01930481 0.9681211 0.2221226 0.9681222 0.4249404 0.9378932 0.01996737 0.9378935 0.01996743 0.9269734 0.01996797 0.9378947 0.4249404 0.9378951 0.4249404 0.9269756 0.3764873 0.7678021 0.3764873 0.733856 0.3839026 0.7338564 0.3764852 0.7678274 0.3838996 0.7678274 0.3838996 0.7338812 0.4249404 0.9378932 0.4249404 0.9269756 0.01996797 0.9269756 0.3838996 0.7678274 0.3913139 0.7678276 0.3913139 0.7338817 0.7168911 0.733889 0.7094766 0.733889 0.7094768 0.7678318 0.5466884 0.7678295 0.7020631 0.7678314 0.7020628 0.733889 0.7094766 0.733889 0.7020628 0.733889 0.7020631 0.7678314 0.3839018 0.7678025 0.3839026 0.7338564 0.3913164 0.7338568 0.7168931 0.733877 0.7168931 0.7678192 0.7094792 0.7678191 0.7020659 0.7338765 0.7020651 0.7678189 0.5466908 0.7678108 0.7094792 0.7338769 0.7094792 0.7678191 0.7020651 0.7678189 0.02070772 0.3171403 0.02070772 0.04096281 0.111782 0.0982747 0.4999999 0.3153537 0.8123962 0.3153538 0.8123962 0.4953626 0.9792923 0.3171403 0.8882179 0.3171404 0.8882178 0.09827482 1.025647 0.7716923 1.025647 0.7199749 1.087915 0.7199749 0.0879153 0.7716923 0.0879153 0.7199749 0.02564746 0.7199749 1.444202 0.9802761 1.668576 0.9802758 1.668576 0.9272283 0.9775426 0.9802758 0.831793 0.9802759 0.831793 0.9272285 0.4946055 0.6347939 0.6403549 0.708226 0.7375211 0.708226 0.6685759 0.9802758 0.4442017 0.9802761 0.4442016 0.9272288 0.9804723 0.8942005 0.5739938 0.8942005 0.5739938 0.7970341 1.494607 0.6347939 1.959966 0.6347939 1.737523 0.7082256 1.494607 0.6347939 1.348857 0.7082256 1.251691 0.7082256 0.831793 0.9802759 0.6860434 0.9802758 0.6860434 0.9272285 1.424552 0.7970341 1.424552 0.8500815 1.318706 0.8500816 0.4946055 0.6347939 0.02924829 0.6347939 0.2516905 0.7082256 0.5529123 0.7970341 0.4470661 0.7970341 0.4470661 0.8942005 1.552912 0.8942005 1.447066 0.8942005 1.447066 0.7970342 0.4245525 0.7970341 0.01807427 0.7970342 0.3187062 0.8500815 1.980472 0.8942004 1.573994 0.8942005 1.573994 0.7970341 1.018075 0.8942006 1.424553 0.8942005 1.123921 0.8411531 0.01807522 0.8942006 0.01807522 0.8411532 0.123921 0.8411531 0.1876037 0.1353449 0.4999999 0.1239373 0.4999999 0.3153537 0.1876038 0.4953626 0.1876037 0.3153538 0.4999999 0.3153537 0.8882179 0.536006 0.8882179 0.3171404 0.9792923 0.3171403 0.8123963 0.1353449 0.8123962 0.3153538 0.4999999 0.3153537 0.02070772 0.3171403 0.111782 0.3171404 0.111782 0.5360061 0.5466912 0.7338666 0.5466908 0.7678108 0.3913164 0.7678028 0.3913139 0.7678276 0.5466884 0.7678295 0.5466883 0.7338853 0.2221227 0.9790409 0.2221226 0.9681222 0.4249404 0.9681233 0.01930624 0.9790414 0.2221233 0.9790414 0.2221232 0.9681229</float_array>
          <technique_common>
            <accessor count="396" source="#citybus_intdisplay-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="citybus_intdisplay-mesh-vertices">
          <input semantic="POSITION" source="#citybus_intdisplay-mesh-positions" />
        </vertices>
        <triangles count="132" material="citybus_intdisplay-material">
          <input offset="0" semantic="VERTEX" source="#citybus_intdisplay-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#citybus_intdisplay-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#citybus_intdisplay-mesh-map-0" />
          <p>82 0 0 11 0 1 81 0 2 19 1 3 20 1 4 14 1 5 117 2 6 124 0 7 109 0 8 123 3 9 120 4 10 106 4 11 123 3 12 98 5 13 114 3 14 109 0 15 121 6 16 94 6 17 68 7 18 90 7 19 70 7 20 85 8 21 71 8 22 84 8 23 80 9 24 5 9 25 10 9 26 86 9 27 10 9 28 6 9 29 102 2 30 103 10 31 101 11 32 121 6 33 93 12 34 94 6 35 119 1 36 14 1 37 34 1 38 81 0 39 4 0 40 92 0 41 95 13 42 17 14 43 93 12 44 122 15 45 97 16 46 98 5 47 110 8 48 100 17 49 73 8 50 99 18 51 111 17 52 97 16 53 101 11 54 104 19 55 35 20 56 106 4 57 107 21 58 105 22 59 116 23 60 75 7 61 115 7 62 105 22 63 108 23 64 116 23 65 74 24 66 2 24 67 69 24 68 76 1 69 37 1 70 15 1 71 22 1 72 24 1 73 23 1 74 21 1 75 20 1 76 19 1 77 15 1 78 22 1 79 23 1 80 38 1 81 15 1 82 37 1 83 112 24 84 78 24 85 113 24 86 36 1 87 119 1 88 34 1 89 37 1 90 39 1 91 38 1 92 13 24 93 9 24 94 1 24 95 78 24 96 8 24 97 12 24 98 26 24 99 13 24 100 1 24 101 78 24 102 12 24 103 16 24 104 26 24 105 1 24 106 25 24 107 25 24 108 27 24 109 26 24 110 18 24 111 113 24 112 16 24 113 78 24 114 16 24 115 113 24 116 83 24 117 9 24 118 13 24 119 13 24 120 31 24 121 83 24 122 33 24 123 32 24 124 31 24 125 28 24 126 30 24 127 29 24 128 29 24 129 12 24 130 2 24 131 12 24 132 8 24 133 2 24 134 83 24 135 31 24 136 32 24 137 29 24 138 2 24 139 28 24 140 125 8 141 127 8 142 126 8 143 135 25 144 133 25 145 131 25 146 136 7 147 134 7 148 46 7 149 40 26 150 128 26 151 139 26 152 140 27 153 130 27 154 153 27 155 144 28 156 49 28 157 147 28 158 50 26 159 51 26 160 145 26 161 48 25 162 143 25 163 141 25 164 44 26 165 149 26 166 138 26 167 50 26 168 149 26 169 44 26 170 45 29 171 132 29 172 47 29 173 145 26 174 157 26 175 149 26 176 48 25 177 148 25 178 143 25 179 48 25 180 131 25 181 148 25 182 42 25 183 154 25 184 43 25 185 48 25 186 154 25 187 42 25 188 146 28 189 142 28 190 49 28 191 57 8 192 66 8 193 65 8 194 50 26 195 152 26 196 51 26 197 50 26 198 139 26 199 152 26 200 131 25 201 155 25 202 148 25 203 141 25 204 161 25 205 154 25 206 54 7 207 61 7 208 53 7 209 139 26 210 159 26 211 152 26 212 56 7 213 67 7 214 59 7 215 55 8 216 60 8 217 63 8 218 19 1 219 76 1 220 72 1 221 23 1 222 76 1 223 15 1 224 1 24 225 112 24 226 25 24 227 119 1 228 76 1 229 14 1 230 74 24 231 83 24 232 32 24 233 115 7 234 35 20 235 104 19 236 96 14 237 73 8 238 17 14 239 84 8 240 7 8 241 3 8 242 0 7 243 70 7 244 87 7 245 82 0 246 88 0 247 11 0 248 117 2 249 102 2 250 124 0 251 123 3 252 114 3 253 120 4 254 123 3 255 122 15 256 98 5 257 109 0 258 124 0 259 121 6 260 68 7 261 91 7 262 90 7 263 85 8 264 89 8 265 71 8 266 80 9 267 79 9 268 5 9 269 86 9 270 80 9 271 10 9 272 102 2 273 117 2 274 103 10 275 121 6 276 95 13 277 93 12 278 81 0 279 11 0 280 4 0 281 95 13 282 96 14 283 17 14 284 122 15 285 99 18 286 97 16 287 110 8 288 111 17 289 100 17 290 99 18 291 100 17 292 111 17 293 101 11 294 103 10 295 104 19 296 106 4 297 120 4 298 107 21 299 116 23 300 108 23 301 75 7 302 105 22 303 107 21 304 108 23 305 74 24 306 28 24 307 2 24 308 76 1 309 118 1 310 37 1 311 112 24 312 77 24 313 78 24 314 125 8 315 129 8 316 127 8 317 136 7 318 137 7 319 134 7 320 140 27 321 41 27 322 130 27 323 144 28 324 146 28 325 49 28 326 50 26 327 145 26 328 149 26 329 45 29 330 150 29 331 132 29 332 145 26 333 158 26 334 157 26 335 48 25 336 135 25 337 131 25 338 48 25 339 141 25 340 154 25 341 146 28 342 151 28 343 142 28 344 57 8 345 58 8 346 66 8 347 50 26 348 40 26 349 139 26 350 131 25 351 156 25 352 155 25 353 141 25 354 162 25 355 161 25 356 54 7 357 62 7 358 61 7 359 139 26 360 160 26 361 159 26 362 56 7 363 64 7 364 67 7 365 55 8 366 52 8 367 60 8 368 19 1 369 14 1 370 76 1 371 23 1 372 72 1 373 76 1 374 1 24 375 77 24 376 112 24 377 119 1 378 118 1 379 76 1 380 74 24 381 69 24 382 83 24 383 115 7 384 75 7 385 35 20 386 96 14 387 110 8 388 73 8 389 84 8 390 71 8 391 7 8 392 0 7 393 68 7 394 70 7 395</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="citybus_intdisplay_dsp-mesh" name="citybus_intdisplay_dsp">
      <mesh>
        <source id="citybus_intdisplay_dsp-mesh-positions">
          <float_array count="27" id="citybus_intdisplay_dsp-mesh-positions-array">-0.2387504 0.0329678 -0.07020545 -0.2387504 -0.01377815 -0.3353162 0.2387504 -0.01377815 -0.3353162 0.2387504 0.0329678 -0.07020545 0 -0.01377815 -0.3353162 0 0.0329678 -0.07020545 -0.2387504 0.009594798 -0.2027609 0.2387504 0.009594798 -0.2027609 0 0.009594798 -0.2027609</float_array>
          <technique_common>
            <accessor count="9" source="#citybus_intdisplay_dsp-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="citybus_intdisplay_dsp-mesh-normals">
          <float_array count="3" id="citybus_intdisplay_dsp-mesh-normals-array">0 0.9848124 -0.1736217</float_array>
          <technique_common>
            <accessor count="1" source="#citybus_intdisplay_dsp-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="citybus_intdisplay_dsp-mesh-map-0">
          <float_array count="48" id="citybus_intdisplay_dsp-mesh-map-0-array">0.5 1 0 0.5 0.5 0.5 1 1 0.5 0.5 1 0.5 1 0.5 0.5 0 1 0 0.5 0.5 0 0 0.5 0 0.5 1 0 1 0 0.5 1 1 0.5 1 0.5 0.5 1 0.5 0.5 0.5 0.5 0 0.5 0.5 0 0.5 0 0</float_array>
          <technique_common>
            <accessor count="24" source="#citybus_intdisplay_dsp-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="citybus_intdisplay_dsp-mesh-vertices">
          <input semantic="POSITION" source="#citybus_intdisplay_dsp-mesh-positions" />
        </vertices>
        <triangles count="8" material="citybus_nextStop_DSP-material">
          <input offset="0" semantic="VERTEX" source="#citybus_intdisplay_dsp-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#citybus_intdisplay_dsp-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#citybus_intdisplay_dsp-mesh-map-0" />
          <p>5 0 0 7 0 1 8 0 2 0 0 3 8 0 4 6 0 5 6 0 6 4 0 7 1 0 8 8 0 9 2 0 10 4 0 11 5 0 12 3 0 13 7 0 14 0 0 15 5 0 16 8 0 17 6 0 18 8 0 19 4 0 20 8 0 21 7 0 22 2 0 23</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="citybus_intdisplay_dsp_1-mesh" name="citybus_intdisplay_dsp_1">
      <mesh>
        <source id="citybus_intdisplay_dsp_1-mesh-positions">
          <float_array count="27" id="citybus_intdisplay_dsp_1-mesh-positions-array">-0.2387504 0.0329678 -0.07020545 -0.2387504 -0.01377815 -0.3353162 0.2387504 -0.01377815 -0.3353162 0.2387504 0.0329678 -0.07020545 0 -0.01377815 -0.3353162 0 0.0329678 -0.07020545 -0.2387504 0.009594798 -0.2027609 0.2387504 0.009594798 -0.2027609 0 0.009594798 -0.2027609</float_array>
          <technique_common>
            <accessor count="9" source="#citybus_intdisplay_dsp_1-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="citybus_intdisplay_dsp_1-mesh-normals">
          <float_array count="3" id="citybus_intdisplay_dsp_1-mesh-normals-array">0 0.9848124 -0.1736217</float_array>
          <technique_common>
            <accessor count="1" source="#citybus_intdisplay_dsp_1-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="citybus_intdisplay_dsp_1-mesh-map-0">
          <float_array count="48" id="citybus_intdisplay_dsp_1-mesh-map-0-array">0.5 1 0 0.5 0.5 0.5 1 1 0.5 0.5 1 0.5 1 0.5 0.5 0 1 0 0.5 0.5 0 0 0.5 0 0.5 1 0 1 0 0.5 1 1 0.5 1 0.5 0.5 1 0.5 0.5 0.5 0.5 0 0.5 0.5 0 0.5 0 0</float_array>
          <technique_common>
            <accessor count="24" source="#citybus_intdisplay_dsp_1-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="citybus_intdisplay_dsp_1-mesh-vertices">
          <input semantic="POSITION" source="#citybus_intdisplay_dsp_1-mesh-positions" />
        </vertices>
        <triangles count="8" material="citybus_nextStop_DSP-material">
          <input offset="0" semantic="VERTEX" source="#citybus_intdisplay_dsp_1-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#citybus_intdisplay_dsp_1-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#citybus_intdisplay_dsp_1-mesh-map-0" />
          <p>5 0 0 7 0 1 8 0 2 0 0 3 8 0 4 6 0 5 6 0 6 4 0 7 1 0 8 8 0 9 2 0 10 4 0 11 5 0 12 3 0 13 7 0 14 0 0 15 5 0 16 8 0 17 6 0 18 8 0 19 4 0 20 8 0 21 7 0 22 2 0 23</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="citybus_intdisplay_dsp_2-mesh" name="citybus_intdisplay_dsp_2">
      <mesh>
        <source id="citybus_intdisplay_dsp_2-mesh-positions">
          <float_array count="27" id="citybus_intdisplay_dsp_2-mesh-positions-array">-0.2387504 0.0329678 -0.07020545 -0.2387504 -0.01377815 -0.3353162 0.2387504 -0.01377815 -0.3353162 0.2387504 0.0329678 -0.07020545 0 -0.01377815 -0.3353162 0 0.0329678 -0.07020545 -0.2387504 0.009594798 -0.2027609 0.2387504 0.009594798 -0.2027609 0 0.009594798 -0.2027609</float_array>
          <technique_common>
            <accessor count="9" source="#citybus_intdisplay_dsp_2-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="citybus_intdisplay_dsp_2-mesh-normals">
          <float_array count="3" id="citybus_intdisplay_dsp_2-mesh-normals-array">0 0.9848124 -0.1736217</float_array>
          <technique_common>
            <accessor count="1" source="#citybus_intdisplay_dsp_2-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="citybus_intdisplay_dsp_2-mesh-map-0">
          <float_array count="48" id="citybus_intdisplay_dsp_2-mesh-map-0-array">0.5 1 0 0.5 0.5 0.5 1 1 0.5 0.5 1 0.5 1 0.5 0.5 0 1 0 0.5 0.5 0 0 0.5 0 0.5 1 0 1 0 0.5 1 1 0.5 1 0.5 0.5 1 0.5 0.5 0.5 0.5 0 0.5 0.5 0 0.5 0 0</float_array>
          <technique_common>
            <accessor count="24" source="#citybus_intdisplay_dsp_2-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="citybus_intdisplay_dsp_2-mesh-vertices">
          <input semantic="POSITION" source="#citybus_intdisplay_dsp_2-mesh-positions" />
        </vertices>
        <triangles count="8" material="citybus_nextStop_DSP-material">
          <input offset="0" semantic="VERTEX" source="#citybus_intdisplay_dsp_2-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#citybus_intdisplay_dsp_2-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#citybus_intdisplay_dsp_2-mesh-map-0" />
          <p>5 0 0 7 0 1 8 0 2 0 0 3 8 0 4 6 0 5 6 0 6 4 0 7 1 0 8 8 0 9 2 0 10 4 0 11 5 0 12 3 0 13 7 0 14 0 0 15 5 0 16 8 0 17 6 0 18 8 0 19 4 0 20 8 0 21 7 0 22 2 0 23</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers />
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="citybus_intdisplay" name="citybus_intdisplay" type="NODE">
        <matrix sid="transform">1 0 0 2.84674e-9 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry name="citybus_intdisplay" url="#citybus_intdisplay-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="citybus_intdisplay-material" target="#citybus_intdisplay-material" />
              <instance_material symbol="citybus_nextStop_DSP-material" target="#citybus_nextStop_DSP-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="citybus_intdisplay_dsp_0" name="citybus_intdisplay_dsp_0" type="NODE">
        <matrix sid="transform">1 0 0 2.84674e-9 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry name="citybus_intdisplay_dsp_0" url="#citybus_intdisplay_dsp-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="citybus_intdisplay-material" target="#citybus_intdisplay-material" />
              <instance_material symbol="citybus_nextStop_DSP-material" target="#citybus_nextStop_DSP-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="citybus_intdisplay_dsp_1" name="citybus_intdisplay_dsp_1" type="NODE">
        <matrix sid="transform">1 0 0 2.84674e-9 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry name="citybus_intdisplay_dsp_1" url="#citybus_intdisplay_dsp_1-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="citybus_intdisplay-material" target="#citybus_intdisplay-material" />
              <instance_material symbol="citybus_nextStop_DSP-material" target="#citybus_nextStop_DSP-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="citybus_intdisplay_dsp_2" name="citybus_intdisplay_dsp_2" type="NODE">
        <matrix sid="transform">1 0 0 2.84674e-9 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry name="citybus_intdisplay_dsp_2" url="#citybus_intdisplay_dsp_2-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="citybus_intdisplay-material" target="#citybus_intdisplay-material" />
              <instance_material symbol="citybus_nextStop_DSP-material" target="#citybus_nextStop_DSP-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene" />
  </scene>
</COLLADA>