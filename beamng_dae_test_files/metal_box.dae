<?xml version='1.0' encoding='utf-8'?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.72.0 commit date:2014-10-03, commit time:13:58, hash:95182d1</authoring_tool>
    </contributor>
    <created>2014-11-24T02:03:09</created>
    <modified>2014-11-24T02:03:09</modified>
    <unit meter="1" name="meter" />
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="metal_box_decals-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.25 0.25 0.25 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="metal_box-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.25 0.25 0.25 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="metal_box_decals-material" name="metal_box_decals">
      <instance_effect url="#metal_box_decals-effect" />
    </material>
    <material id="metal_box-material" name="metal_box">
      <instance_effect url="#metal_box-effect" />
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Cube_003-mesh" name="Cube.003">
      <mesh>
        <source id="Cube_003-mesh-positions">
          <float_array count="12" id="Cube_003-mesh-positions-array">0.454384 -0.4897249 0.04561597 -0.4543839 -0.4897249 0.04561597 0.4543838 -0.4897251 0.954384 -0.4543842 -0.4897246 0.954384</float_array>
          <technique_common>
            <accessor count="4" source="#Cube_003-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_003-mesh-normals">
          <float_array count="6" id="Cube_003-mesh-normals-array">-5.57501e-7 -1 3.60736e-7 0 -1 -2.29559e-7</float_array>
          <technique_common>
            <accessor count="2" source="#Cube_003-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_003-mesh-map-0">
          <float_array count="12" id="Cube_003-mesh-map-0-array">0.991683 0.4971216 0.5019846 0.4971216 0.5019848 0.007423162 0.991683 0.007423162 0.991683 0.4971216 0.5019848 0.007423162</float_array>
          <technique_common>
            <accessor count="6" source="#Cube_003-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_003-mesh-vertices">
          <input semantic="POSITION" source="#Cube_003-mesh-positions" />
        </vertices>
        <polylist count="2" material="metal_box_decals-material">
          <input offset="0" semantic="VERTEX" source="#Cube_003-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_003-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Cube_003-mesh-map-0" />
          <vcount>3 3 </vcount>
          <p>2 0 0 3 0 1 1 0 2 0 1 3 2 1 4 1 1 5</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cube_004-mesh" name="Cube.004">
      <mesh>
        <source id="Cube_004-mesh-positions">
          <float_array count="168" id="Cube_004-mesh-positions-array">0.5048711 -0.5048711 -0.004871129 0.5048713 0.5048708 1.004871 0.5048708 -0.5048714 1.004871 0.5048711 0.4543839 0.04561597 0.5048711 -0.454384 0.04561597 0.5048713 0.4543837 0.954384 0.5048708 -0.4543842 0.954384 0.4846763 0.4543839 0.04561597 0.4846763 -0.454384 0.04561597 0.4846765 0.4543837 0.954384 0.484676 -0.4543842 0.954384 0.454384 -0.5048711 0.04561597 -0.454384 -0.5048713 0.04561597 0.4543838 -0.5048713 0.954384 -0.4543842 -0.5048708 0.954384 0.454384 -0.4846763 0.04561597 -0.454384 -0.4846763 0.04561597 0.4543838 -0.4846765 0.954384 -0.4543842 -0.484676 0.954384 0.5048713 0.5048711 -0.004871129 -0.4543839 0.5048713 0.04561597 0.4543841 0.5048711 0.04561597 -0.4543837 0.5048714 0.954384 0.4543843 0.5048708 0.954384 -0.4543839 0.4846763 0.04561597 0.4543841 0.4846763 0.04561597 -0.4543837 0.4846766 0.954384 0.4543843 0.484676 0.954384 -0.5048713 0.504871 -0.004871129 -0.5048711 -0.4543841 0.04561597 -0.5048713 0.4543839 0.04561597 -0.5048713 -0.4543838 0.954384 -0.504871 0.4543842 0.954384 -0.4846762 -0.454384 0.04561597 -0.4846763 0.4543839 0.04561597 -0.4846764 -0.4543838 0.954384 -0.484676 0.4543842 0.954384 -0.5048711 0.5048708 1.004871 -0.5048711 -0.5048714 1.004871 0.454384 0.4543839 1.004871 0.454384 -0.454384 1.004871 -0.4543839 0.4543837 1.004871 -0.4543839 -0.4543842 1.004871 0.454384 0.4543839 0.9846763 0.454384 -0.454384 0.9846763 -0.4543839 0.4543837 0.9846766 -0.4543839 -0.4543842 0.9846761 -0.5048711 -0.5048708 -0.004871487 0.4543841 -0.4543839 -0.004871129 0.4543841 0.454384 -0.004871129 -0.4543839 -0.4543836 -0.004871487 -0.4543839 0.4543844 -0.00487101 0.4543841 -0.4543839 0.01532369 0.4543841 0.454384 0.01532369 -0.4543839 -0.4543836 0.01532328 -0.4543839 0.4543844 0.01532381</float_array>
          <technique_common>
            <accessor count="56" source="#Cube_004-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_004-mesh-normals">
          <float_array count="324" id="Cube_004-mesh-normals-array">1 -5.31266e-7 -5.31267e-7 0 0 -1 1 -3.27942e-7 3.27942e-7 1 -5.57501e-7 3.27942e-7 0 1 2.29559e-7 0 -1 -1.96765e-7 0 0 1 0 0 -1 -5.57501e-7 -1 3.27942e-7 1 0 2.29559e-7 -1 0 -1.96765e-7 0 0 1 0 0 -1 6.88678e-7 1 3.27942e-7 -1 0 2.29559e-7 1 0 -1.96765e-7 0 0 1 0 0 -1 -1 4.59119e-7 3.27942e-7 0 -1 3.27942e-7 0 1 -3.27942e-7 0 0 1 2.36118e-7 -2.36118e-7 1 1 0 0 -1.31177e-7 -5.24707e-7 1 -2.29559e-7 1 0 1.96765e-7 -1 0 -1 0 0 1 0 0 -1.31177e-7 5.90296e-7 -1 -4.59119e-7 -1 0 3.27942e-7 1 0 -1 0 0 1 0 0 1 -1.77089e-7 3.36468e-6 -5.24707e-7 -1 -1.23307e-5 -7.67384e-6 -1 -5.90296e-7 -2.06604e-6 -1 -2.95148e-7 -2.95148e-7 -1 -2.95147e-7 6.55884e-7 1 -6.55882e-7 2.95148e-7 1 2.95148e-7 -1.16288e-5 1 1.7709e-7 0 1 -4.6043e-6 -1 3.9353e-7 -3.9353e-6 -1 3.27942e-7 3.27942e-7 -1 -3.7385e-6 -1.96764e-7 -1 -1.31177e-7 1.31175e-7 -2.62354e-7 -2.6235e-7 1 0 0 1 0 0 1 2.88587e-6 5.24707e-7 -1 -1.31177e-7 1.31177e-7 -1 3.54177e-7 -3.54177e-7 -1 0 0 -1 1 -5.90296e-7 5.90295e-7 0 0 -1 1 2.95147e-7 2.95148e-7 1 0 -2.29559e-7 0 1 2.29559e-7 0 -1 -1.96765e-7 0 0 1 0 0 -1 0 -1 -2.29559e-7 1 0 2.29559e-7 -1 0 -1.96765e-7 0 0 1 0 0 -1 0 1 -3.60736e-7 -1 0 2.29559e-7 1 0 -1.96765e-7 0 0 1 0 0 -1 -1 0 -2.29559e-7 0 -1 3.27942e-7 -5.90293e-6 1 -1.96767e-7 0 0 1 -2.62353e-7 -2.62354e-7 1 1 0 0 3.9353e-7 0 1 -2.29559e-7 1 0 1.96765e-7 -1 0 -1 0 0 1 0 0 4.59119e-7 0 -1 -4.59119e-7 -1 0 3.27942e-7 1 0 -1 0 0 1 -3.80412e-6 -2.62354e-7 1 0 0 0 -1 -2.36118e-6 1.22651e-5 -1 4.59122e-7 -1.96765e-7 -1 -1.96765e-7 1.31177e-7 -1 -8.39525e-6 0 1 1.18059e-5 -3.27943e-7 1 3.27942e-7 -4.52558e-6 1 -1.96764e-7 1.31177e-7 1 1.31177e-7 -1 0 3.54179e-6 -1 -3.65987e-6 0 -1 0 0 -1 0 0 -2.36118e-7 2.36118e-7 1 0 0 1 0 0 1 -3.54177e-7 3.54178e-7 -1 0 -2.36121e-6 -1 3.93531e-7 3.93528e-7 -1 0 0 -1</float_array>
          <technique_common>
            <accessor count="108" source="#Cube_004-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_004-mesh-map-0">
          <float_array count="648" id="Cube_004-mesh-map-0-array">1 1 0 0.9999994 0.05000007 0.9499994 0.95 0.95 0.05000007 0.9499994 0.07202523 0.9289469 0 0 0.04999995 0.05000001 0.05000007 0.9499994 0.928922 0.9289475 0.07202523 0.9289469 0.07202512 0.07205057 0.05000007 0.9499994 0.04999995 0.05000001 0.07202512 0.07205057 0.95 0.05000001 0.95 0.95 0.928922 0.9289475 0.04999995 0.05000001 0.95 0.05000001 0.928922 0.07205057 0.95 0.95 0.05000007 0.9499994 0.07202523 0.9289469 0.928922 0.9289475 0.07202523 0.9289469 0.07202512 0.07205057 0.05000007 0.9499994 0.04999995 0.05000001 0.07202512 0.07205057 0.95 0.05000001 0.95 0.95 0.928922 0.9289475 0.04999995 0.05000001 0.95 0.05000001 0.928922 0.07205057 0.05000007 0.9499994 0.07202523 0.9289469 0.928922 0.9289475 0.928922 0.9289475 0.07202523 0.9289469 0.07202512 0.07205057 0.05000007 0.9499994 0.04999995 0.05000001 0.07202512 0.07205057 0.95 0.05000001 0.95 0.95 0.928922 0.9289475 0.04999995 0.05000001 0.95 0.05000001 0.928922 0.07205057 0.95 0.95 0.05000007 0.9499994 0.07202523 0.9289469 0.928922 0.9289475 0.07202523 0.9289469 0.07202512 0.07205057 0.05000007 0.9499994 0.04999995 0.05000001 0.07202512 0.07205057 0.95 0.05000001 0.95 0.95 0.928922 0.9289475 0.04999995 0.05000001 0.95 0.05000001 0.928922 0.07205057 1 1 0 0.9999994 0.05000007 0.9499994 0.95 0.95 0.05000007 0.9499994 0.07202523 0.9289469 0.928922 0.9289475 0.07202523 0.9289469 0.07202512 0.07205057 0.05000007 0.9499994 0.04999995 0.05000001 0.07202512 0.07205057 0.95 0.05000001 0.95 0.95 0.928922 0.9289475 0.04999995 0.05000001 0.95 0.05000001 0.928922 0.07205057 0.95 0.95 0.05000007 0.9499994 0.07202523 0.9289469 0.928922 0.9289475 0.07202523 0.9289469 0.07202512 0.07205057 0.05000007 0.9499994 0.04999995 0.05000001 0.07202512 0.07205057 0.95 0.05000001 0.95 0.95 0.928922 0.9289475 0.04999995 0.05000001 0.95 0.05000001 0.928922 0.07205057 1 0 1 1 0.95 0.95 0 0 1 0 0.95 0.05000001 0 0.9999994 0.05000007 0.9499994 0.95 0.95 0 0.9999994 0 0 0.04999995 0.05000001 1 0 1 1 0.95 0.95 0 0 1 0 0.95 0.05000001 0 0.9999994 0.05000007 0.9499994 0.95 0.95 0 0.9999994 0 0 0.04999995 0.05000001 1 0 1 1 0.95 0.95 0 0 1 0 0.95 0.05000001 0 0.9999994 0.05000007 0.9499994 0.95 0.95 0 0 0.04999995 0.05000001 0.05000007 0.9499994 1 1 0.95 0.95 0.95 0.05000001 1 0 0.95 0.05000001 0.04999995 0.05000001 0 0 0.04999995 0.05000001 0.05000007 0.9499994 1 1 0.95 0.95 0.95 0.05000001 1 0 0.95 0.05000001 0.04999995 0.05000001 0 0.9999994 0.05000007 0.9499994 0.95 0.95 0 0 0.04999995 0.05000001 0.05000007 0.9499994 1 0 1 1 0.95 0.95 0 0 1 0 0.95 0.05000001 0.95 0.95 1 1 0.05000007 0.9499994 0.928922 0.9289475 0.95 0.95 0.07202523 0.9289469 0 0.9999994 0 0 0.05000007 0.9499994 0.928922 0.07205057 0.928922 0.9289475 0.07202512 0.07205057 0.07202523 0.9289469 0.05000007 0.9499994 0.07202512 0.07205057 0.928922 0.07205057 0.95 0.05000001 0.928922 0.9289475 0.07202512 0.07205057 0.04999995 0.05000001 0.928922 0.07205057 0.928922 0.9289475 0.95 0.95 0.07202523 0.9289469 0.928922 0.07205057 0.928922 0.9289475 0.07202512 0.07205057 0.07202523 0.9289469 0.05000007 0.9499994 0.07202512 0.07205057 0.928922 0.07205057 0.95 0.05000001 0.928922 0.9289475 0.07202512 0.07205057 0.04999995 0.05000001 0.928922 0.07205057 0.95 0.95 0.05000007 0.9499994 0.928922 0.9289475 0.928922 0.07205057 0.928922 0.9289475 0.07202512 0.07205057 0.07202523 0.9289469 0.05000007 0.9499994 0.07202512 0.07205057 0.928922 0.07205057 0.95 0.05000001 0.928922 0.9289475 0.07202512 0.07205057 0.04999995 0.05000001 0.928922 0.07205057 0.928922 0.9289475 0.95 0.95 0.07202523 0.9289469 0.928922 0.07205057 0.928922 0.9289475 0.07202512 0.07205057 0.07202523 0.9289469 0.05000007 0.9499994 0.07202512 0.07205057 0.928922 0.07205057 0.95 0.05000001 0.928922 0.9289475 0.07202512 0.07205057 0.04999995 0.05000001 0.928922 0.07205057 0.95 0.95 1 1 0.05000007 0.9499994 0.928922 0.9289475 0.95 0.95 0.07202523 0.9289469 0.928922 0.07205057 0.928922 0.9289475 0.07202512 0.07205057 0.07202523 0.9289469 0.05000007 0.9499994 0.07202512 0.07205057 0.928922 0.07205057 0.95 0.05000001 0.928922 0.9289475 0.07202512 0.07205057 0.04999995 0.05000001 0.928922 0.07205057 0.928922 0.9289475 0.95 0.95 0.07202523 0.9289469 0.928922 0.07205057 0.928922 0.9289475 0.07202512 0.07205057 0.07202523 0.9289469 0.05000007 0.9499994 0.07202512 0.07205057 0.928922 0.07205057 0.95 0.05000001 0.928922 0.9289475 0.07202512 0.07205057 0.04999995 0.05000001 0.928922 0.07205057 0.95 0.05000001 1 0 0.95 0.95 0.04999995 0.05000001 0 0 0.95 0.05000001 1 1 0 0.9999994 0.95 0.95 0.05000007 0.9499994 0 0.9999994 0.04999995 0.05000001 0.95 0.05000001 1 0 0.95 0.95 0.04999995 0.05000001 0 0 0.95 0.05000001 1 1 0 0.9999994 0.95 0.95 0.05000007 0.9499994 0 0.9999994 0.04999995 0.05000001 0.95 0.05000001 1 0 0.95 0.95 0.04999995 0.05000001 0 0 0.95 0.05000001 1 1 0 0.9999994 0.95 0.95 0 0.9999994 0 0 0.05000007 0.9499994 1 0 1 1 0.95 0.05000001 0 0 1 0 0.04999995 0.05000001 0 0.9999994 0 0 0.05000007 0.9499994 1 0 1 1 0.95 0.05000001 0 0 1 0 0.04999995 0.05000001 1 1 0 0.9999994 0.95 0.95 0 0.9999994 0 0 0.05000007 0.9499994 0.95 0.05000001 1 0 0.95 0.95 0.04999995 0.05000001 0 0 0.95 0.05000001</float_array>
          <technique_common>
            <accessor count="324" source="#Cube_004-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_004-mesh-vertices">
          <input semantic="POSITION" source="#Cube_004-mesh-positions" />
        </vertices>
        <polylist count="108" material="metal_box-material">
          <input offset="0" semantic="VERTEX" source="#Cube_004-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_004-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Cube_004-mesh-map-0" />
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>1 0 0 2 0 1 6 0 2 5 1 3 6 1 4 10 1 5 0 2 6 4 2 7 6 2 8 9 3 9 10 3 10 8 3 11 6 4 12 4 4 13 8 4 14 3 5 15 5 5 16 9 5 17 4 6 18 3 6 19 7 6 20 13 7 21 14 7 22 18 7 23 17 8 24 18 8 25 16 8 26 14 9 27 12 9 28 16 9 29 11 10 30 13 10 31 17 10 32 12 11 33 11 11 34 15 11 35 23 12 36 27 12 37 26 12 38 26 13 39 27 13 40 25 13 41 23 14 42 21 14 43 25 14 44 20 15 45 22 15 46 26 15 47 21 16 48 20 16 49 24 16 50 31 17 51 32 17 52 36 17 53 35 18 54 36 18 55 34 18 56 32 19 57 30 19 58 34 19 59 29 20 60 31 20 61 35 20 62 30 21 63 29 21 64 33 21 65 37 22 66 38 22 67 42 22 68 41 23 69 42 23 70 46 23 71 45 24 72 46 24 73 44 24 74 42 25 75 40 25 76 44 25 77 39 26 78 41 26 79 45 26 80 40 27 81 39 27 82 43 27 83 50 28 84 51 28 85 55 28 86 54 29 87 55 29 88 53 29 89 51 30 90 49 30 91 53 30 92 48 31 93 50 31 94 54 31 95 49 32 96 48 32 97 52 32 98 19 33 99 1 33 100 5 33 101 0 34 102 19 34 103 3 34 104 38 35 105 14 35 106 13 35 107 38 36 108 47 36 109 12 36 110 0 37 111 2 37 112 13 37 113 47 38 114 0 38 115 11 38 116 1 39 117 23 39 118 22 39 119 1 40 120 19 40 121 21 40 122 28 41 123 37 41 124 22 41 125 19 42 126 28 42 127 20 42 128 37 43 129 32 43 130 31 43 131 28 44 132 30 44 133 32 44 134 38 45 135 31 45 136 29 45 137 47 46 138 29 46 139 30 46 140 2 47 141 40 47 142 42 47 143 37 48 144 41 48 145 39 48 146 1 49 147 39 49 148 40 49 149 28 50 150 51 50 151 50 50 152 19 51 153 49 51 154 51 51 155 0 52 156 47 52 157 50 52 158 19 53 159 0 53 160 48 53 161 5 54 162 1 54 163 6 54 164 9 55 165 5 55 166 10 55 167 2 56 168 0 56 169 6 56 170 7 57 171 9 57 172 8 57 173 10 58 174 6 58 175 8 58 176 7 59 177 3 59 178 9 59 179 8 60 180 4 60 181 7 60 182 17 61 183 13 61 184 18 61 185 15 62 186 17 62 187 16 62 188 18 63 189 14 63 190 16 63 191 15 64 192 11 64 193 17 64 194 16 65 195 12 65 196 15 65 197 22 66 198 23 66 199 26 66 200 24 67 201 26 67 202 25 67 203 27 68 204 23 68 205 25 68 206 24 69 207 20 69 208 26 69 209 25 70 210 21 70 211 24 70 212 35 71 213 31 71 214 36 71 215 33 72 216 35 72 217 34 72 218 36 73 219 32 73 220 34 73 221 33 74 222 29 74 223 35 74 224 34 75 225 30 75 226 33 75 227 41 76 228 37 76 229 42 76 230 45 77 231 41 77 232 46 77 233 43 78 234 45 78 235 44 78 236 46 79 237 42 79 238 44 79 239 43 80 240 39 80 241 45 80 242 44 81 243 40 81 244 43 81 245 54 82 246 50 82 247 55 82 248 52 83 249 54 83 250 53 83 251 55 84 252 51 84 253 53 84 254 52 85 255 48 85 256 54 85 257 53 86 258 49 86 259 52 86 260 3 87 261 19 87 262 5 87 263 4 88 264 0 88 265 3 88 266 2 89 267 38 89 268 13 89 269 14 90 270 38 90 271 12 90 272 11 91 273 0 91 274 13 91 275 12 92 276 47 92 277 11 92 278 37 93 279 1 93 280 22 93 281 23 94 282 1 94 283 21 94 284 20 95 285 28 95 286 22 95 287 21 96 288 19 96 289 20 96 290 38 97 291 37 97 292 31 97 293 37 98 294 28 98 295 32 98 296 47 99 297 38 99 298 29 99 299 28 100 300 47 100 301 30 100 302 38 101 303 2 101 304 42 101 305 1 102 306 37 102 307 39 102 308 2 103 309 1 103 310 40 103 311 47 104 312 28 104 313 50 104 314 28 105 315 19 105 316 51 105 317 48 106 318 0 106 319 50 106 320 49 107 321 19 107 322 48 107 323</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cube_002-mesh" name="Cube.002">
      <mesh>
        <source id="Cube_002-mesh-positions">
          <float_array count="12" id="Cube_002-mesh-positions-array">-0.4897249 -0.454384 0.04561597 -0.489725 0.4543839 0.04561597 -0.4897251 -0.4543838 0.954384 -0.4897246 0.4543842 0.954384</float_array>
          <technique_common>
            <accessor count="4" source="#Cube_002-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_002-mesh-normals">
          <float_array count="6" id="Cube_002-mesh-normals-array">-1 4.91913e-7 3.60736e-7 -1 0 -2.29559e-7</float_array>
          <technique_common>
            <accessor count="2" source="#Cube_002-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_002-mesh-map-0">
          <float_array count="12" id="Cube_002-mesh-map-0-array">0.9943875 0.9986782 0.5043925 0.9986782 0.5043927 0.5086834 0.9943875 0.5086834 0.9943875 0.9986782 0.5043927 0.5086834</float_array>
          <technique_common>
            <accessor count="6" source="#Cube_002-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_002-mesh-vertices">
          <input semantic="POSITION" source="#Cube_002-mesh-positions" />
        </vertices>
        <polylist count="2" material="metal_box_decals-material">
          <input offset="0" semantic="VERTEX" source="#Cube_002-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_002-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Cube_002-mesh-map-0" />
          <vcount>3 3 </vcount>
          <p>2 0 0 3 0 1 1 0 2 0 1 3 2 1 4 1 1 5</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cube_005-mesh" name="Cube.005">
      <mesh>
        <source id="Cube_005-mesh-positions">
          <float_array count="12" id="Cube_005-mesh-positions-array">0.489725 0.4543839 0.04561597 0.489725 -0.454384 0.04561597 0.4897252 0.4543837 0.954384 0.4897246 -0.4543842 0.954384</float_array>
          <technique_common>
            <accessor count="4" source="#Cube_005-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_005-mesh-normals">
          <float_array count="6" id="Cube_005-mesh-normals-array">1 -5.90296e-7 3.60736e-7 1 0 -2.29559e-7</float_array>
          <technique_common>
            <accessor count="2" source="#Cube_005-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_005-mesh-map-0">
          <float_array count="12" id="Cube_005-mesh-map-0-array">0.4913228 0.995742 0.002782642 0.995742 0.002782762 0.5072021 0.4913229 0.5072021 0.4913228 0.995742 0.002782762 0.5072021</float_array>
          <technique_common>
            <accessor count="6" source="#Cube_005-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_005-mesh-vertices">
          <input semantic="POSITION" source="#Cube_005-mesh-positions" />
        </vertices>
        <polylist count="2" material="metal_box_decals-material">
          <input offset="0" semantic="VERTEX" source="#Cube_005-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_005-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Cube_005-mesh-map-0" />
          <vcount>3 3 </vcount>
          <p>2 0 0 3 0 1 1 0 2 0 1 3 2 1 4 1 1 5</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cube_006-mesh" name="Cube.006">
      <mesh>
        <source id="Cube_006-mesh-positions">
          <float_array count="12" id="Cube_006-mesh-positions-array">-0.4543839 0.489725 0.04561597 0.454384 0.4897249 0.04561597 -0.4543837 0.4897252 0.954384 0.4543842 0.4897246 0.954384</float_array>
          <technique_common>
            <accessor count="4" source="#Cube_006-mesh-positions-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_006-mesh-normals">
          <float_array count="6" id="Cube_006-mesh-normals-array">6.55884e-7 1 3.60736e-7 0 1 -2.29559e-7</float_array>
          <technique_common>
            <accessor count="2" source="#Cube_006-mesh-normals-array" stride="3">
              <param name="X" type="float" />
              <param name="Y" type="float" />
              <param name="Z" type="float" />
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_006-mesh-map-0">
          <float_array count="12" id="Cube_006-mesh-map-0-array">0.4909911 0.4936166 0.006269037 0.4936166 0.006269156 0.008894443 0.4909914 0.008894443 0.4909911 0.4936166 0.006269156 0.008894443</float_array>
          <technique_common>
            <accessor count="6" source="#Cube_006-mesh-map-0-array" stride="2">
              <param name="S" type="float" />
              <param name="T" type="float" />
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_006-mesh-vertices">
          <input semantic="POSITION" source="#Cube_006-mesh-positions" />
        </vertices>
        <polylist count="2" material="metal_box_decals-material">
          <input offset="0" semantic="VERTEX" source="#Cube_006-mesh-vertices" />
          <input offset="1" semantic="NORMAL" source="#Cube_006-mesh-normals" />
          <input offset="2" semantic="TEXCOORD" set="0" source="#Cube_006-mesh-map-0" />
          <vcount>3 3 </vcount>
          <p>2 0 0 3 0 1 1 0 2 0 1 3 2 1 4 1 1 5</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers />
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="metal_box_decal_F" name="metal_box_decal_F" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_003-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="metal_box_decals-material" target="#metal_box_decals-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="metal_box" name="metal_box" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_004-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="metal_box-material" target="#metal_box-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="metal_box_decal_R" name="metal_box_decal_R" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_002-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="metal_box_decals-material" target="#metal_box_decals-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="metal_box_decal_L" name="metal_box_decal_L" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_005-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="metal_box_decals-material" target="#metal_box_decals-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="metal_box_decal_B" name="metal_box_decal_B" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_006-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="metal_box_decals-material" target="#metal_box_decals-material" />
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene" />
  </scene>
</COLLADA>