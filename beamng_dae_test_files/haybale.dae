<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 3.6.2 commit date:2023-08-16, commit time:16:43, hash:e53e55951e7a</authoring_tool>
    </contributor>
    <created>2023-09-06T17:35:33</created>
    <modified>2023-09-06T17:35:33</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="haybale3-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <transparent opaque="A_ONE">
              <color sid="alpha">0 0 0 0</color>
            </transparent>
            <index_of_refraction>
              <float sid="ior">1</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="haybale2-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <transparent opaque="A_ONE">
              <color sid="alpha">0 0 0 0</color>
            </transparent>
            <index_of_refraction>
              <float sid="ior">1</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="haybale-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images/>
  <library_materials>
    <material id="haybale3-material" name="haybale3">
      <instance_effect url="#haybale3-effect"/>
    </material>
    <material id="haybale2-material" name="haybale2">
      <instance_effect url="#haybale2-effect"/>
    </material>
    <material id="haybale-material" name="haybale">
      <instance_effect url="#haybale-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Cube_006-mesh" name="Cube.006">
      <mesh>
        <source id="Cube_006-mesh-positions">
          <float_array id="Cube_006-mesh-positions-array" count="492">0.5660006 -0.7169939 1.196813 0.501617 -0.7327666 0.8168179 0.4781515 -0.3321881 1.215573 0.5581436 -0.2986889 0.8046905 0.5918369 -0.343427 1.180372 0.6195253 -0.4913154 0.8176872 0.6044142 0.02606642 1.041969 0.5109063 -0.1124399 0.6759562 0.4382221 0.5052664 1.173878 0.5394837 0.5048487 0.7858495 0.3621599 0.8905778 1.178752 0.5289098 0.9227507 0.7987297 0.7046223 -1.12892 0.1823413 0.4490614 -0.8364422 0.1481831 0.6776857 -1.127238 0.5749475 0.5653824 -0.725432 0.5425417 0.6862148 -1.150307 0.5971539 0.3415011 -0.9190304 0.5818099 0.7046592 -1.088326 0.9928375 0.5085942 -0.7274795 0.9612811 0.9009805 -0.7724117 -0.007075309 0.3280665 -1.160001 -0.0123707 0.8346677 -0.6404666 0.9406374 0.2063559 -0.9561473 0.9791511 0.702596 1.151128 0.1787258 0.4983872 0.7995718 0.1448678 0.6777254 1.12199 0.5889133 0.5942478 0.6776189 0.5463467 0.848744 0.9335289 0.6151747 0.4895631 0.7668271 0.5719288 0.7905649 0.9822849 0.9995459 0.4217802 0.8187136 0.9803439 0.6627421 0.04662084 -0.08029127 0.3613786 0.01515877 0.1744802 0.6941544 0.4364069 -0.01076906 0.3414096 0.4535828 0.2184549 0.7532917 0.475928 0.003469347 0.4932597 0.448108 0.3025111 0.7156838 0.871505 -0.01294475 0.4613857 0.8640579 0.2977167 0.6562458 -0.8193286 -0.157141 0.4903866 -0.8347746 0.2323151 0.632941 -0.42414 -0.1479435 0.3632375 -0.4341239 0.1465163 0.7412716 -0.4029071 -0.06455051 0.4954207 -0.4233042 0.2712697 0.7945477 0.002571523 0.04937696 0.3900634 -0.00938791 0.2082584 0.6785785 0.2919123 1.197304 0.3763948 0.6057415 0.7958946 0.553968 -0.1889454 0.905342 0.3507672 0.1096447 0.4434633 0.7655223 -0.4340379 0.4618614 0.5232568 -0.1477497 0.5344195 0.6780888 -0.4301022 0.06333005 0.4984525 -0.02539515 0.09981161 -0.03955924 1.031838 1.115283 -0.002258896 0.9141834 0.5411123 0.5393546 1.044403 1.143139 0.596462 0.9783488 0.5701219 -0.1492427 0.938643 0.4714797 0.002039492 1.100977 -0.08852702 0.4308834 0.8616037 0.565724 0.5763363 0.9033733 -0.002290189 0.2340709 1.150741 0.3201845 0.5454503 1.001975 -0.1362595 0.4088671 0.6978493 0.6136676 0.6231504 0.4733877 0.1354438 0.191515 0.9431144 1.15072 0.2731398 1.129993 0.6077711 0.5962659 0.5400826 1.085543 0.8128276 0.8048901 0.616696 -0.7800293 0.6989182 1.011352 -0.6414006 1.015296 0.5410228 -0.2836824 0.8363946 1.265574 -0.1203559 1.180115 0.8104314 -0.5104666 -1.056464 0.4982065 -0.8906263 -0.8883402 0.9241145 -0.07301115 -1.041622 0.8708591 -0.4260275 -0.8318623 1.295995 0.5799099 -1.062069 0.4969533 0.2149498 -1.197674 0.9242067 0.7757216 -0.6097548 0.7893546 0.5410076 -0.8267884 1.278718 -0.4287263 -0.7213373 1.205088 -0.3889761 -0.7241826 0.8146896 -0.4460827 -0.2964153 1.213081 -0.5983703 -0.3177766 0.8117514 -0.4892156 -0.4447338 1.155094 -0.4904038 -0.4425016 0.7538926 -0.5460905 -0.0433771 1.172317 -0.5418327 -0.02093714 0.7573176 -0.6444494 -1.142737 0.1818417 -0.3342097 -0.8882039 0.1568118 -0.5588067 -1.180364 0.5856803 -0.3416141 -0.839208 0.5738392 -0.6616428 -1.117116 0.5951731 -0.374445 -0.8581784 0.560182 -0.6891552 -1.054702 0.9925395 -0.3127457 -0.8471686 0.9923244 -0.7365512 0.0484066 -0.0256263 -0.4164594 0.01098865 0.2062107 -0.679376 0.4453199 -0.07007074 -0.3902626 0.427303 0.2173995 -0.7308602 0.4539492 -0.008318662 -0.3643268 0.430978 0.1559095 -0.6602471 0.87371 -0.08028542 -0.3510289 0.8522322 0.1746157 -0.6664487 -0.7971368 -0.1204136 -0.4301113 -0.8636995 0.2244095 -0.7692345 -0.4263166 0.06210768 -0.4896134 -0.4517665 0.3288305 -0.6804574 -0.3849748 -0.07233512 -0.3934957 -0.4303839 0.2319768 -0.6529921 0.009896457 -0.07400524 -0.3541692 -0.01338148 0.241492 -0.3251152 1.139202 0.536412 -0.3046824 1.102397 -0.03791028 -0.5452476 0.6011903 0.5620831 -0.7596415 0.6954642 0.01784873 0.005212545 0.1425857 1.007102 0.3728246 0.3533349 0.9770677 0.2615327 -0.172174 1.156606 0.5424364 -0.02322572 0.9066036 -0.02251791 0.3248018 1.029123 -0.05266255 0.7303411 0.9374259 0.3795642 0.3847566 1.05546 0.3583868 0.7580506 0.9649653 0.4095759 -0.7083269 1.219037 0.1748187 -0.9404295 1.001606 0.1887416 -0.3866572 1.044438 -0.1232224 -0.7382007 0.9831792 -0.04238778 -0.5577593 1.035695 0.02510881 -0.1690685 0.943474 0.351471 -0.5973552 1.125051 0.4469298 -0.2558606 0.9361279 0.04705417 0.6081401 1.224663 0.09783983 0.3277633 0.9351296 -0.3349028 0.6918806 1.020659 -0.3222818 0.2385845 0.9893398 0.06796598 0.04549127 -0.1626611 0.293134 0.3081424 0.04189598 0.3118636 -0.2640608 -0.04520529 0.571154 -0.004989206 0.1321616 -0.03075599 0.3418585 -0.08016318 -0.05944514 0.7819333 -0.05397814 0.3767379 0.4575726 -0.2099014 0.3849119 0.8079762 0.07261121 0.3725997 -0.7455375 -0.2502329 0.19245 -1.002913 -0.007593572 0.1123307 -0.4552513 -0.1401515 -0.08959466 -0.7063417 0.1046496 -0.0322625 -0.54799 -0.06233829 0.0520671 -0.1505292 0.00600934 0.3476604 -0.6324432 -0.07410639 0.4417455 -0.2864211 0.1230247 0.05480796 0.6740984 -0.06263726 0.09659868 0.3070718 0.08238291 -0.3507783 0.6277324 -0.05773532 -0.294594 0.2076775 -0.06455826 -0.6095063 1.048722 1.139135 -0.5126239 1.132581 0.6614481 -0.2787981 0.673228 1.150492 -0.1700035 0.7281974 0.6642418</float_array>
          <technique_common>
            <accessor source="#Cube_006-mesh-positions-array" count="164" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_006-mesh-normals">
          <float_array id="Cube_006-mesh-normals-array" count="246">0.9585638 0.2272127 -0.1718432 0.9969401 -0.004707515 0.07802873 0.9510145 0.1845978 0.2479819 0.7493248 0.6604182 0.04858112 0.5552224 0.8173366 -0.1539122 0.4479877 -0.8632848 0.2324791 -0.863889 0.503408 -0.01661902 -0.397178 0.9010171 -0.1744075 0.5418903 -0.05940622 0.8383471 0.7713216 0.06640177 0.6329723 0.6837601 0.3536794 0.6382656 0.350189 0.2274606 0.9086415 0.9377475 0.1676477 -0.3041775 -0.7345086 -0.6607499 0.1546181 -0.09981125 0.9872804 -0.123755 0.3020852 0.9419376 0.1466222 0.918369 0.2791647 0.2804739 0.4114263 0.6990444 0.5848637 -0.4643543 0.7788788 0.4215723 -0.1528403 -0.9307096 0.3322944 0.7280893 -0.683945 0.04588323 -0.8368655 -0.4287208 0.3403742 -0.9925182 -0.1209464 -0.01672285 0.6115949 -0.7652129 -0.2010002 0.6716713 -0.7233394 0.1601182 -0.5646266 0.1717298 0.8072829 -0.3875404 0.2187286 0.895528 -0.7789821 -0.4371774 0.449514 -0.7225414 0.05317193 0.6892799 -0.9249708 0.3757413 -0.05698668 0.6183874 0.1336204 0.7744305 -0.0805096 0.2305171 0.9697319 -0.111382 -0.0738517 0.9910298 0.1095127 0.4575586 0.8824098 0.1411277 -0.06432694 0.9878994 0.569846 0.1418622 -0.8094139 -0.3124737 0.0361104 -0.9492399 -0.6141863 -0.2693888 -0.7417581 0.006825149 0.1680628 -0.9857527 0.03039789 -0.3643108 -0.9307812 -0.7209239 -0.6427745 -0.2590554 0.97605 -0.1220729 0.1800686 0.9408532 0.1575161 -0.2999736 0.9153349 0.01075303 0.4025501 0.9143989 0.2286285 -0.3340715 0.7664776 0.3705896 -0.5245717 0.5527957 -0.8192006 0.1527327 -0.9498687 0.1522821 0.273056 -0.3910297 0.902689 -0.1795784 0.6457017 -0.1842075 0.7410379 0.7464132 0.09827327 0.6581867 0.9184636 0.04501801 0.3929352 0.7864674 -0.2606594 0.5599336 0.8339852 0.1119403 -0.540313 -0.9143429 -0.4003901 -0.06053715 -0.01156508 0.9794309 -0.2014485 0.07813501 0.9516815 0.2969803 0.7718884 0.527692 0.3545837 0.692965 0.6432679 0.3255857 -0.4329928 0.801854 0.4117614 -0.1646718 -0.9587981 0.2314941 0.7032682 -0.5748523 0.4182807 -0.9941393 -0.03870797 -0.1009407 -0.9900932 -0.1403952 0.00215131 0.8422299 -0.5334999 0.07763379 0.4822829 -0.8747768 -0.04657149 -0.7040734 0.02523112 0.7096788 -0.6366014 -0.014144 0.7710633 -0.6771619 -0.2712084 0.6840305 -0.7230959 0.05247741 0.6887515 -0.5972464 0.7169892 0.3594765 -0.1210024 0.3437758 0.9312232 -0.09542363 0.2127843 0.9724285 -0.6563876 -0.04175919 0.7532673 -0.1890726 0.257652 0.9475585 -0.4564428 -0.6778501 0.5763499 0.4528394 0.1486422 -0.8791143 0.1826243 0.6145163 -0.7674751 -0.5878645 -0.2705304 -0.7623836 0.3842938 0.3758968 -0.8432199 0.3375077 0.06040167 -0.939383 -0.7404639 -0.6290059 -0.2367803</float_array>
          <technique_common>
            <accessor source="#Cube_006-mesh-normals-array" count="82" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_006-mesh-map-0">
          <float_array id="Cube_006-mesh-map-0-array" count="492">0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.80731 1.10331 0.9392474 0.2630026 0.09894013 0.1310653 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.9840079 0.05287802 0.9840079 1.034196 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.9840079 0.05287802 0.9840079 1.034196 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 -0.03299725 0.9713724 0.80731 1.10331 0.09894013 0.1310653 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.002689719 1.034196 0.002689719 0.05287802 0.9840079 1.034196 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.002689719 1.034196 0.002689719 0.05287802 0.9840079 1.034196</float_array>
          <technique_common>
            <accessor source="#Cube_006-mesh-map-0-array" count="246" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_006-mesh-vertices">
          <input semantic="POSITION" source="#Cube_006-mesh-positions"/>
        </vertices>
        <triangles material="haybale3-material" count="82">
          <input semantic="VERTEX" source="#Cube_006-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_006-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_006-mesh-map-0" offset="2" set="0"/>
          <p>2 0 0 0 0 1 1 0 2 6 1 3 4 1 4 5 1 5 10 2 6 8 2 7 9 2 8 14 3 9 12 3 10 13 3 11 18 4 12 16 4 13 17 4 14 22 5 15 23 5 16 21 5 17 26 6 18 24 6 19 25 6 20 30 7 21 28 7 22 29 7 23 34 8 24 35 8 25 33 8 26 38 9 27 39 9 28 37 9 29 42 10 30 43 10 31 41 10 32 46 11 33 47 11 34 45 11 35 50 12 36 51 12 37 49 12 38 54 13 39 52 13 40 53 13 41 58 14 42 59 14 43 57 14 44 62 15 45 63 15 46 61 15 47 66 16 48 67 16 49 65 16 50 70 17 51 71 17 52 69 17 53 74 18 54 75 18 55 73 18 56 78 19 57 79 19 58 77 19 59 82 20 60 83 20 61 81 20 62 86 21 63 87 21 64 85 21 65 90 22 66 91 22 67 89 22 68 94 23 69 92 23 70 93 23 71 98 24 72 96 24 73 97 24 74 102 25 75 100 25 76 101 25 77 106 26 78 104 26 79 105 26 80 110 27 81 108 27 82 109 27 83 114 28 84 112 28 85 113 28 86 118 29 87 116 29 88 117 29 89 122 30 90 123 30 91 121 30 92 126 31 93 127 31 94 125 31 95 130 32 96 131 32 97 129 32 98 134 33 99 135 33 100 133 33 101 138 34 102 139 34 103 137 34 104 142 35 105 140 35 106 141 35 107 146 36 108 144 36 109 145 36 110 150 37 111 148 37 112 149 37 113 154 38 114 152 38 115 153 38 116 158 39 117 156 39 118 157 39 119 162 40 120 160 40 121 161 40 122 3 41 123 2 41 124 1 41 125 7 42 126 6 42 127 5 42 128 11 43 129 10 43 130 9 43 131 15 44 132 14 44 133 13 44 134 19 45 135 18 45 136 17 45 137 20 46 138 22 46 139 21 46 140 27 47 141 26 47 142 25 47 143 31 48 144 30 48 145 29 48 146 32 49 147 34 49 148 33 49 149 36 50 150 38 50 151 37 50 152 40 51 153 42 51 154 41 51 155 44 52 156 46 52 157 45 52 158 48 53 159 50 53 160 49 53 161 55 54 162 54 54 163 53 54 164 56 55 165 58 55 166 57 55 167 60 56 168 62 56 169 61 56 170 64 57 171 66 57 172 65 57 173 68 58 174 70 58 175 69 58 176 72 59 177 74 59 178 73 59 179 76 60 180 78 60 181 77 60 182 80 61 183 82 61 184 81 61 185 84 62 186 86 62 187 85 62 188 88 63 189 90 63 190 89 63 191 95 64 192 94 64 193 93 64 194 99 65 195 98 65 196 97 65 197 103 66 198 102 66 199 101 66 200 107 67 201 106 67 202 105 67 203 111 68 204 110 68 205 109 68 206 115 69 207 114 69 208 113 69 209 119 70 210 118 70 211 117 70 212 120 71 213 122 71 214 121 71 215 124 72 216 126 72 217 125 72 218 128 73 219 130 73 220 129 73 221 132 74 222 134 74 223 133 74 224 136 75 225 138 75 226 137 75 227 143 76 228 142 76 229 141 76 230 147 77 231 146 77 232 145 77 233 151 78 234 150 78 235 149 78 236 155 79 237 154 79 238 153 79 239 159 80 240 158 80 241 157 80 242 163 81 243 162 81 244 161 81 245</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Cube_005-mesh" name="Cube.005">
      <mesh>
        <source id="Cube_005-mesh-positions">
          <float_array id="Cube_005-mesh-positions-array" count="492">0.5517912 0.7235369 -0.2053822 0.6260941 0.7227326 0.1903643 0.606516 0.3267145 -0.2111661 0.4365817 0.3173917 0.1785289 0.4873593 0.3450446 -0.177644 0.4964894 0.4945856 0.1828541 0.4862746 -0.02257108 -0.04210019 0.5291513 0.1073849 0.3332344 0.5873685 -0.5072538 -0.1821389 0.5186207 -0.5051003 0.2113121 0.6254902 -0.9051027 -0.186835 0.5192708 -0.9197025 0.207796 0.6809664 1.149145 0.8178111 0.4517933 0.8300485 0.8510408 0.6640604 1.13604 0.4234191 0.4848381 0.7634043 0.4383579 0.7195096 1.104447 0.4058889 0.4524874 0.8134807 0.43399 0.639691 1.140679 0.004600822 0.4540162 0.7743386 0.0196132 0.8785524 0.7397451 1.01284 0.2083829 0.9693348 1.039183 0.8184776 0.6137504 0.06522667 0.2470809 1.013501 0.01675128 0.7366923 -1.122795 0.8268183 0.4775313 -0.8156622 0.8510145 0.7612169 -1.054245 0.4253621 0.4097747 -0.8506947 0.4383872 0.7791137 -1.048411 0.4021208 0.5149152 -0.751762 0.4294096 0.7551587 -1.042439 0.007414579 0.4226896 -0.8173654 0.01958036 0.7606909 -0.05935037 0.9525775 0.4556764 -0.02411252 0.7114863 0.6956104 -0.4467599 1.001556 0.3570289 -0.4327315 0.7852054 0.7389006 -0.4710925 1.002518 0.385044 -0.4341163 0.8162741 0.718024 -0.8643301 0.9947357 0.3955113 -0.8502619 0.7613645 0.7062396 0.8132936 1.116324 0.4553991 0.842508 0.8001603 0.7708848 0.4070798 0.9899114 0.4748715 0.4275139 0.730031 0.8050832 0.3775431 0.9566238 0.4137523 0.4275735 0.8206538 0.6721858 -0.002275049 1.078712 0.3704154 0.01348531 0.8168545 0.6893696 -0.2930828 -0.1982854 0.592271 -0.6217179 0.2559753 0.6773064 0.1795886 0.1211329 0.4433485 -0.1179698 0.5720044 0.834864 0.3652185 0.5314793 0.5544701 0.09913295 0.4693151 0.690468 0.4240502 0.9337648 0.574297 0.008506953 0.8714224 -0.02291297 -1.114484 -0.073897 -0.004025459 -0.8541274 0.4384223 0.5391778 -1.037157 -0.1455738 0.6021187 -0.9326458 0.4208156 -0.1194535 -1.015387 0.5204637 -0.01099276 -1.034531 1.103875 0.427366 -0.8997922 0.4113325 0.5817318 -1.118157 0.919429 0.4482029 -1.249577 0.6084815 0.5319792 -0.996002 1.130595 0.4737636 -0.7253649 0.3611291 0.7940166 -0.542566 0.8118327 0.202659 -0.9514337 -0.157118 0.4243136 -1.255617 0.282946 0.6569989 -0.5925081 -0.1338247 0.8621167 -0.8523628 0.3351349 -0.6976727 -0.5951285 0.0851798 -0.6675493 -1.055484 0.4254847 -0.2509047 -0.7857353 -0.2234721 -0.1163734 -1.172564 0.1952642 -0.5575516 1.163731 0.4299793 -0.8691341 0.8117324 0.1119508 -0.07334858 1.042823 0.1285761 -0.4079852 0.7676079 -0.2665187 0.5354472 1.029474 0.5218042 0.2202864 1.201709 0.0740534 0.8717128 0.7041974 0.156425 0.4896684 0.7893139 -0.2696986 -0.5466304 0.7238052 -0.2058169 -0.5886818 0.722532 0.1905348 -0.4471429 0.3304582 -0.2143589 -0.3675273 0.3191426 0.1769093 -0.4835851 0.4388983 -0.1576608 -0.4678731 0.4426643 0.2339636 -0.5307596 0.04412186 -0.1654338 -0.4004375 0.03402328 0.224854 -0.6248406 1.158725 0.8164153 -0.4163435 0.8229736 0.8516333 -0.6326457 1.125529 0.4242821 -0.4061443 0.7953867 0.4374606 -0.6747031 1.105218 0.4057769 -0.4076563 0.8143394 0.4339184 -0.6748118 1.07377 0.010091 -0.2967929 0.8783305 0.01669472 -0.6681986 -0.04205483 1.115101 -0.3486724 -0.01089477 0.8826135 -0.6818973 -0.4411517 1.060743 -0.3315421 -0.4271197 0.8569175 -0.7223693 -0.4682589 1.02435 -0.4300123 -0.4359226 0.7649415 -0.6253996 -0.8550441 1.108283 -0.3702428 -0.8465027 0.7940359 -0.7303381 0.8022113 1.02749 -0.4465867 0.8389913 0.7580931 -0.6315507 0.4165157 1.104434 -0.3268665 0.432143 0.8453565 -0.7191103 0.3844223 1.011767 -0.4482454 0.4197061 0.726542 -0.6391193 -0.003336966 1.065825 -0.4174224 0.009450078 0.7163246 -0.3014883 -1.12463 0.4648277 -0.363285 -1.141063 1.031151 -0.6631774 -0.6807239 0.4251942 -0.7087389 -0.6525039 0.9983479 0.06209623 -0.174389 -0.1439565 0.3849907 -0.3538341 -0.003975391 0.2270691 0.1808884 -0.0941168 0.5480381 0.01919949 0.0759353 -0.01575505 -0.3420469 -0.09711587 -0.05485749 -0.7159494 0.03427273 0.3797952 -0.3921073 -0.1216964 0.3582783 -0.7603116 0.03043949 0.4569872 0.6656851 -0.09543132 0.2209005 0.9760857 0.003879785 0.159479 0.4051082 -0.04004502 -0.1136257 0.7323934 -0.01922869 -0.01566535 0.4646747 -0.2273918 0.044824 0.124334 -0.04529762 0.3531679 0.5918064 -0.1376069 0.4400596 0.2415379 0.02936553 0.05119919 -0.6356542 -0.1854755 0.09672892 -0.2879221 -0.009691238 -0.3411909 -0.6148568 -0.1503211 -0.3189954 -0.2801702 0.07743066 0.01726067 0.01057916 0.9969676 0.2771738 -0.2939142 0.9248682 0.3403438 0.2390659 1.099365 0.6012569 -0.03408247 0.9711158 -0.006746351 -0.3393962 1.018785 -0.05628359 -0.760232 1.009341 0.3778459 -0.4484545 1.191671 0.3524311 -0.8075 1.031436 0.4524278 0.6492918 0.9946913 0.2152548 0.9819989 0.9619079 0.1083854 0.4588648 1.148043 -0.09944641 0.7146915 0.9131627 -0.02141845 0.5141839 1.135995 0.03998368 0.1785973 0.9379281 0.355902 0.6230517 1.082198 0.458428 0.2317622 1.00248 0.05047559 -0.6154296 1.172913 0.09588354 -0.3082563 0.9141057 -0.3410509 -0.6116845 1.105407 -0.3175327 -0.2692114 0.8972233 -0.5785939 -1.041244 -0.1469045 -0.4932492 -1.152179 0.3239682 -0.4693079 -0.557626 -0.03287565 -0.12089 -0.7803937 0.2959152</float_array>
          <technique_common>
            <accessor source="#Cube_005-mesh-positions-array" count="164" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_005-mesh-normals">
          <float_array id="Cube_005-mesh-normals-array" count="246">0.9736199 0.1369299 -0.1825225 0.9997249 -0.0106557 -0.02089905 0.9809849 0.09198021 0.1709042 0.8113752 -0.5843238 -0.01536405 0.7117261 -0.6726956 -0.2023034 0.5690632 0.8203177 0.05697399 -0.7610924 -0.6300774 -0.1540814 -0.7447041 -0.6664705 0.03511303 0.5103971 -0.2704859 -0.8162918 0.5853976 0.1205124 -0.8017396 0.657774 0.1554102 -0.7370082 0.6523297 -0.06133866 -0.7554492 0.8924962 -0.01887637 0.4506599 -0.6120408 0.7209074 -0.3251134 -0.1205293 -0.9737533 0.1930732 -0.2303639 -0.9174711 -0.3243136 0.7371773 -0.6180296 -0.2731465 0.5911231 -0.5676012 -0.573064 -0.3881089 -0.7357594 -0.5550042 -0.2473059 0.8806912 -0.4040088 0.5899911 0.7083637 -0.387468 -0.8630043 -0.4785983 0.1617636 -0.9373056 -0.161562 0.3087978 0.8507995 0.5219245 -0.06111758 0.7381585 0.6724906 -0.05365079 -0.591628 0.1287071 -0.7958711 -0.6112645 -0.311159 -0.7276921 -0.6348375 -0.3042821 -0.710207 -0.6895086 -0.2376217 -0.6841885 -0.7667162 -0.6338226 -0.1020557 0.4631597 -0.01260292 -0.8861852 -0.04926514 -0.3789468 -0.9241062 0.01410472 0.07518643 -0.9970698 0.2746171 -0.3574063 -0.8926624 -0.1595097 0.5625937 -0.8111999 -0.08852344 -0.3004338 0.9496859 -0.4034871 0.02697205 0.9145878 0.2595812 0.2762265 0.9253738 0.2533966 -0.4568907 0.852667 -0.1230762 0.6500117 0.7498915 -0.9624799 0.167084 0.2138115 0.8419688 -0.4040909 0.35749 0.991495 0.03494769 -0.1253647 0.9656388 -6.89977e-4 0.2598871 0.8944702 -0.4245826 0.1401886 0.891794 -0.450117 0.04580825 0.3155876 0.9377992 -0.1446968 -0.490993 -0.8575457 0.1534317 -0.5365446 -0.806061 0.2497708 0.5935831 -0.1983224 -0.7799534 0.4651551 -0.007175087 -0.8852003 0.7605708 0.3001911 -0.5756887 0.2606416 -0.3769018 -0.8888257 0.9849922 -0.07867681 0.1536241 -0.9617558 0.2531964 0.1044866 0.1772534 -0.8799947 0.4406707 0.1933251 -0.9787715 -0.06805789 0.9888702 -0.1001386 -0.1100359 0.4918363 -0.5804665 -0.6489652 -0.627332 -0.4890485 -0.6060415 -0.2045592 0.7497011 -0.6293678 0.612138 0.7798433 -0.1308876 -0.9648312 -0.2417904 -0.1031416 -0.9922824 0.1178128 0.03867751 0.8247898 0.5651738 -0.0173338 0.4412976 0.8619878 0.2494667 -0.5035683 0.03257417 -0.8633412 -0.764784 -0.1556355 -0.6252064 -0.6009203 -0.3327112 -0.7267724 -0.8425587 -0.0499463 -0.5362839 -0.8148168 -0.578582 -0.03628474 0.3762474 -0.044887 -0.9254313 -0.09911942 -0.3206501 -0.9419973 -0.2681384 0.1025353 -0.9579083 0.3435266 -0.3948502 -0.8521049 -0.05538845 0.4562221 -0.8881406 0.1242314 -0.3220047 0.9385519 -0.09553778 -0.400027 0.9115102 -0.4477795 0.3795363 0.809596 -0.1200401 -0.2282923 0.9661641 0.01407784 0.5186838 0.8548504 -0.4205154 0.479291 0.7703551</float_array>
          <technique_common>
            <accessor source="#Cube_005-mesh-normals-array" count="82" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_005-mesh-map-0">
          <float_array id="Cube_005-mesh-map-0-array" count="492">0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.80731 1.10331 0.9392474 0.2630026 0.09894013 0.1310653 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.9840079 0.05287802 0.9840079 1.034196 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.9840079 0.05287802 0.9840079 1.034196 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 -0.03299725 0.9713724 0.80731 1.10331 0.09894013 0.1310653 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.002689719 1.034196 0.002689719 0.05287802 0.9840079 1.034196 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.002689719 1.034196 0.002689719 0.05287802 0.9840079 1.034196</float_array>
          <technique_common>
            <accessor source="#Cube_005-mesh-map-0-array" count="246" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_005-mesh-vertices">
          <input semantic="POSITION" source="#Cube_005-mesh-positions"/>
        </vertices>
        <triangles material="haybale3-material" count="82">
          <input semantic="VERTEX" source="#Cube_005-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_005-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_005-mesh-map-0" offset="2" set="0"/>
          <p>2 0 0 0 0 1 1 0 2 6 1 3 4 1 4 5 1 5 10 2 6 8 2 7 9 2 8 14 3 9 12 3 10 13 3 11 18 4 12 16 4 13 17 4 14 22 5 15 23 5 16 21 5 17 26 6 18 24 6 19 25 6 20 30 7 21 28 7 22 29 7 23 34 8 24 35 8 25 33 8 26 38 9 27 39 9 28 37 9 29 42 10 30 43 10 31 41 10 32 46 11 33 47 11 34 45 11 35 50 12 36 51 12 37 49 12 38 54 13 39 52 13 40 53 13 41 58 14 42 59 14 43 57 14 44 62 15 45 63 15 46 61 15 47 66 16 48 67 16 49 65 16 50 70 17 51 71 17 52 69 17 53 74 18 54 75 18 55 73 18 56 78 19 57 79 19 58 77 19 59 82 20 60 83 20 61 81 20 62 86 21 63 87 21 64 85 21 65 90 22 66 91 22 67 89 22 68 94 23 69 92 23 70 93 23 71 98 24 72 96 24 73 97 24 74 102 25 75 100 25 76 101 25 77 106 26 78 104 26 79 105 26 80 110 27 81 108 27 82 109 27 83 114 28 84 112 28 85 113 28 86 118 29 87 116 29 88 117 29 89 122 30 90 123 30 91 121 30 92 126 31 93 127 31 94 125 31 95 130 32 96 131 32 97 129 32 98 134 33 99 135 33 100 133 33 101 138 34 102 139 34 103 137 34 104 142 35 105 140 35 106 141 35 107 146 36 108 144 36 109 145 36 110 150 37 111 148 37 112 149 37 113 154 38 114 152 38 115 153 38 116 158 39 117 156 39 118 157 39 119 162 40 120 160 40 121 161 40 122 3 41 123 2 41 124 1 41 125 7 42 126 6 42 127 5 42 128 11 43 129 10 43 130 9 43 131 15 44 132 14 44 133 13 44 134 19 45 135 18 45 136 17 45 137 20 46 138 22 46 139 21 46 140 27 47 141 26 47 142 25 47 143 31 48 144 30 48 145 29 48 146 32 49 147 34 49 148 33 49 149 36 50 150 38 50 151 37 50 152 40 51 153 42 51 154 41 51 155 44 52 156 46 52 157 45 52 158 48 53 159 50 53 160 49 53 161 55 54 162 54 54 163 53 54 164 56 55 165 58 55 166 57 55 167 60 56 168 62 56 169 61 56 170 64 57 171 66 57 172 65 57 173 68 58 174 70 58 175 69 58 176 72 59 177 74 59 178 73 59 179 76 60 180 78 60 181 77 60 182 80 61 183 82 61 184 81 61 185 84 62 186 86 62 187 85 62 188 88 63 189 90 63 190 89 63 191 95 64 192 94 64 193 93 64 194 99 65 195 98 65 196 97 65 197 103 66 198 102 66 199 101 66 200 107 67 201 106 67 202 105 67 203 111 68 204 110 68 205 109 68 206 115 69 207 114 69 208 113 69 209 119 70 210 118 70 211 117 70 212 120 71 213 122 71 214 121 71 215 124 72 216 126 72 217 125 72 218 128 73 219 130 73 220 129 73 221 132 74 222 134 74 223 133 74 224 136 75 225 138 75 226 137 75 227 143 76 228 142 76 229 141 76 230 147 77 231 146 77 232 145 77 233 151 78 234 150 78 235 149 78 236 155 79 237 154 79 238 153 79 239 159 80 240 158 80 241 157 80 242 163 81 243 162 81 244 161 81 245</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Cube_004-mesh" name="Cube.004">
      <mesh>
        <source id="Cube_004-mesh-positions">
          <float_array id="Cube_004-mesh-positions-array" count="492">-0.5176281 -0.7232998 -0.204998 -0.4521469 -0.7281794 0.1857443 -0.4682019 -0.3311955 -0.2149879 -0.5849389 -0.3070996 0.1880508 -0.6337732 -0.3422985 -0.1819664 -0.6516907 -0.4883428 0.1859288 -0.6292305 0.0284084 -0.03906798 -0.5510469 -0.1060369 0.3347423 -0.4736608 0.5080431 -0.180859 -0.5512304 0.5061212 0.2121786 -0.4408381 0.8991207 -0.1919368 -0.49386 0.9179396 0.2061656 -0.6849862 -1.146017 0.8182671 -0.4281964 -0.8500031 0.8493702 -0.6640596 -1.13604 0.423419 -0.4848374 -0.7634046 0.4383578 -0.7195089 -1.104447 0.4058888 -0.3897038 -0.8665739 0.4295452 -0.7162347 -1.075797 0.009924113 -0.4540156 -0.7743387 0.01961308 -0.8785516 -0.7397456 1.01284 -0.3098059 -1.131194 1.018265 -0.818477 -0.6137505 0.06522673 -0.2019051 -0.9481908 0.02279376 -0.7366915 1.122794 0.826819 -0.4775304 0.8156619 0.851015 -0.7530381 1.064032 0.4260393 -0.5151181 0.7341747 0.4354079 -0.8133701 1.003743 0.3970821 -0.4590495 0.8187744 0.4341261 -0.7551581 1.042439 0.007415175 -0.422689 0.8173652 0.01958072 -0.6701617 0.04632341 1.07065 -0.3566573 0.01604521 0.8302835 -0.6567508 0.4436587 1.048069 -0.40597 0.4339824 0.7310684 -0.7201588 0.4683954 1.026962 -0.4554964 0.4398558 0.7317484 -0.6625439 0.8599026 1.061142 -0.3956941 0.8502662 0.7611618 -0.7062386 -0.8132941 1.116324 -0.4535117 -0.8426979 0.8024275 -0.6619395 -0.4178425 1.120531 -0.3691416 -0.4322118 0.8470607 -0.7049754 -0.3938789 1.087568 -0.4791315 -0.4210053 0.7420797 -0.7446114 0.009429395 0.9918761 -0.4575707 -0.009613275 0.720383 -0.6465343 0.2902504 -0.2100167 -0.4567035 0.6114819 0.2264611 -0.5508604 -0.1891691 0.0938031 -0.4433478 0.1179695 0.5720046 -0.7181269 -0.4743335 0.5399547 -0.4632515 -0.1772812 0.4795527 -0.6904671 -0.4240505 0.9337649 -0.5742961 -0.008507311 0.8714226 0.02291351 1.114484 -0.07389664 0.004026174 0.8541272 0.4384226 -0.5391773 1.037157 -0.1455733 -0.602118 0.9326456 0.420816 0.1194543 1.015387 0.520464 0.01099371 1.034531 1.103876 -0.4188102 0.7989372 0.448895 -0.5766842 0.9853329 0.9730386 -0.3097898 1.19849 0.6438203 -0.6294913 1.038003 1.103455 -0.4737629 0.7253647 0.3611295 -0.6883009 0.4902217 0.8437984 -0.2026585 0.9514336 -0.1571177 -0.3294138 1.175324 0.3548114 -0.573373 0.5216033 -0.07026147 -0.7535004 0.7532311 0.4287018 0.7356708 0.6433773 0.04021441 0.66755 1.055484 0.4254848 0.2509053 0.7857353 -0.2234719 0.116374 1.172564 0.1952645 0.5199083 -1.090145 0.4855175 0.8691347 -0.8117325 0.1119503 0.07334917 -1.042823 0.1285757 0.4079856 -0.7676079 -0.2665191 -0.5354465 -1.029474 0.5218041 -0.2202858 -1.201709 0.07405304 -0.7379712 -0.5811983 0.2228908 -0.4738927 -0.7745599 -0.2630279 0.4351592 -0.7230314 -0.204563 0.4019293 -0.7283799 0.1855742 0.5399457 -0.3274518 -0.2117949 0.5663635 -0.3053488 0.1896707 0.5410047 -0.4392969 -0.1583071 0.5655382 -0.4396063 0.2365573 0.4991289 -0.04514664 -0.166308 0.5762533 -0.0218265 0.236138 0.6248415 -1.158725 0.8164147 0.3760161 -0.857078 0.848778 0.6326465 -1.125529 0.4242815 0.406145 -0.7953869 0.4374602 0.6747038 -1.105218 0.4057764 0.407657 -0.8143396 0.433918 0.6748124 -1.07377 0.01009047 0.2967934 -0.8783307 0.0166943 0.7461952 0.05327755 1.013374 0.4472025 0.01892161 0.7644032 0.6540049 0.4389255 1.094129 0.414998 0.429253 0.7646034 0.6917615 0.4638541 1.064271 0.3655998 0.4306745 0.8422212 0.7102398 0.8618135 1.006735 0.3760344 0.8466504 0.7876305 0.7081174 -0.8058378 1.056556 0.3746938 -0.8462145 0.844495 0.7136432 -0.4084065 1.006009 0.4295166 -0.4275827 0.7317355 0.703318 -0.3869997 1.032425 0.3570083 -0.4288727 0.8361919 0.6900478 0.008367478 1.004764 0.3229337 -0.01364862 0.8209132 0.3242344 1.138802 0.463579 0.3281335 1.117166 1.034677 0.5632552 0.6126299 0.435351 0.7087399 0.6525035 0.9983479 -0.07730954 0.1760625 -0.1705175 -0.3547919 0.349016 0.05559307 -0.2234945 -0.1814642 -0.08704137 -0.5532706 -0.01804983 0.06417214 0.01207989 0.4234087 -0.2549059 0.05580961 0.745488 -0.02935302 -0.3787521 0.4222381 -0.1867976 -0.3550396 0.7864422 -0.03249144 -0.4089106 -0.7091534 -0.2203189 -0.2314444 -0.9650422 0.03667128 -0.1023282 -0.4652366 -0.218751 0.09112876 -0.7043064 0.06692057 0.04224473 -0.5293003 -0.09274625 -0.02643686 -0.1804209 0.08146083 -0.3514401 -0.5971159 -0.1255793 -0.4415273 -0.2352631 0.01393824 -0.05119872 0.6356541 -0.1854753 -0.09580188 0.3101982 0.0383085 0.3415139 0.6221882 -0.1344745 0.318996 0.28017 0.07743066 -0.0759359 0.05076158 1.174232 -0.2771729 0.2939138 0.9248684 -0.3462637 -0.2332041 1.117357 -0.5926268 0.02387195 0.9391654 -0.003835439 0.3383103 0.9917311 0.05628448 0.7602316 1.009341 -0.3778449 0.448454 1.191671 -0.3608872 0.7392535 0.8670761 -0.4084709 -0.7095516 1.16969 -0.2152539 -0.9819993 0.9619076 -0.1083844 -0.4588653 1.148043 0.09944725 -0.7146919 0.9131624 0.0214194 -0.5141844 1.135995 -0.04412984 -0.1659475 0.9665181 -0.3615587 -0.6056607 1.121596 -0.4400318 -0.2890135 0.8731305 -0.05270993 0.6778665 1.051871 -0.09588271 0.3082559 0.9141058 0.3410518 0.611684 1.105407 0.3130861 0.2358806 0.9775907 0.6541705 1.005582 -0.1078448 0.4932499 1.152179 0.3239684 0.3123444 0.6391698 -0.1250979 0.1208906 0.7803935 0.2959154</float_array>
          <technique_common>
            <accessor source="#Cube_004-mesh-positions-array" count="164" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_004-mesh-normals">
          <float_array id="Cube_004-mesh-normals-array" count="246">-0.9779388 0.1274883 0.1654765 -0.9989169 0.02688091 -0.037979 -0.9782609 0.07664585 -0.1926942 -0.7539572 0.6565078 -0.02337104 -0.58666 0.8080567 0.05361521 -0.4773561 -0.8535764 -0.2086588 0.763158 0.634182 -0.12411 0.4445471 0.8824114 0.1540387 -0.7661899 -0.2326102 -0.5990372 -0.7357246 0.1544653 -0.6594315 -0.6611197 0.2140733 -0.7190921 -0.687236 -0.002294123 -0.7264307 -0.9824724 0.08834213 0.1641461 0.761531 -0.6474711 0.0291841 0.1205294 0.9737533 0.1930736 -0.004009723 0.9418063 -0.3361322 -0.9251841 0.2390519 -0.2947688 -0.6531899 0.5687741 -0.499839 0.3881085 0.7357597 -0.555004 0.2473058 -0.8806912 -0.4040086 -0.7760876 -0.60396 -0.1814402 0.9313998 -0.3616386 -0.04137873 0.9820826 -0.02537554 -0.1867349 -0.7731981 -0.6304261 -0.0687586 -0.7381585 -0.6724907 -0.05365079 0.5903207 0.2981843 -0.7500718 0.5668679 -0.1436022 -0.8111962 0.5429425 -0.1133766 -0.8320814 0.496538 -0.04398328 -0.8668999 0.9106132 0.4131522 0.009434938 -0.3429477 0.1638194 -0.9249595 0.04562962 0.3871847 -0.9208725 0.4687062 -0.4949848 -0.7316451 0.09950077 0.3794345 -0.9198529 0.0417509 -0.5282031 -0.8480911 -0.5311408 0.3511916 0.7710732 0.4458385 -0.1006518 0.8894365 0.3766851 -0.3778814 0.8457625 -0.1326211 0.4134412 0.9008207 -0.1808201 -0.3248869 0.9283063 0.6423277 -0.6204167 0.4499981 -0.9237534 -0.2899448 -0.2502232 -0.9549928 0.152331 0.2545275 -0.9813584 0.1347115 -0.1370717 -0.8844013 0.4169518 0.2097278 -0.7332925 0.6295925 0.256701 -0.5601175 -0.8158056 -0.1439789 0.7996146 0.5715098 -0.1843723 0.5588383 0.8279887 0.04620206 -0.6097562 -0.02444308 -0.792212 -0.7263729 0.1651406 -0.6671664 -0.7712564 0.09309607 -0.6296799 -0.8264636 -0.2055773 -0.5241144 -0.9284383 0.04815304 0.3683528 0.9153822 -0.2038226 0.347177 -0.1772536 0.8799945 0.440671 -0.3601416 0.9277995 -0.09739732 -0.7847921 0.4948955 -0.373068 -0.7280168 0.5434387 -0.4179306 0.5051429 0.6324689 -0.5872084 0.2447009 -0.8730065 -0.421878 -0.7635125 -0.559728 -0.3221079 0.9640223 -0.2539258 0.07862937 0.9926462 0.1042112 -0.06159287 -0.8187336 -0.5559881 -0.1433615 -0.4412974 -0.861988 0.2494663 0.8069645 0.06362044 -0.5871634 0.5480327 -0.1223394 -0.827462 0.6776632 -0.2726969 -0.6829414 0.4475999 0.003827273 -0.8942257 0.7425677 0.6267121 -0.2362738 -0.6065638 0.0629673 -0.7925375 -0.1414566 0.5806752 -0.8017521 0.3992763 -0.4969035 -0.770497 -0.002263188 0.4463787 -0.8948414 0.08626115 -0.5724735 -0.8153731 -0.2688069 0.3612838 0.89287 -0.2541162 0.7269272 0.637967 0.4477801 -0.3795366 0.8095955 -0.3464831 0.5244293 0.7777683 -0.1982396 -0.3023562 0.9323529 0.598948 -0.6365342 0.4858863</float_array>
          <technique_common>
            <accessor source="#Cube_004-mesh-normals-array" count="82" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_004-mesh-map-0">
          <float_array id="Cube_004-mesh-map-0-array" count="492">0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.80731 1.10331 0.9392474 0.2630026 0.09894013 0.1310653 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.9840079 0.05287802 0.9840079 1.034196 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.9840079 0.05287802 0.9840079 1.034196 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 -0.03299725 0.9713724 0.80731 1.10331 0.09894013 0.1310653 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.002689719 1.034196 0.002689719 0.05287802 0.9840079 1.034196 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.002689719 1.034196 0.002689719 0.05287802 0.9840079 1.034196</float_array>
          <technique_common>
            <accessor source="#Cube_004-mesh-map-0-array" count="246" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_004-mesh-vertices">
          <input semantic="POSITION" source="#Cube_004-mesh-positions"/>
        </vertices>
        <triangles material="haybale3-material" count="82">
          <input semantic="VERTEX" source="#Cube_004-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_004-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_004-mesh-map-0" offset="2" set="0"/>
          <p>2 0 0 0 0 1 1 0 2 6 1 3 4 1 4 5 1 5 10 2 6 8 2 7 9 2 8 14 3 9 12 3 10 13 3 11 18 4 12 16 4 13 17 4 14 22 5 15 23 5 16 21 5 17 26 6 18 24 6 19 25 6 20 30 7 21 28 7 22 29 7 23 34 8 24 35 8 25 33 8 26 38 9 27 39 9 28 37 9 29 42 10 30 43 10 31 41 10 32 46 11 33 47 11 34 45 11 35 50 12 36 51 12 37 49 12 38 54 13 39 52 13 40 53 13 41 58 14 42 59 14 43 57 14 44 62 15 45 63 15 46 61 15 47 66 16 48 67 16 49 65 16 50 70 17 51 71 17 52 69 17 53 74 18 54 75 18 55 73 18 56 78 19 57 79 19 58 77 19 59 82 20 60 83 20 61 81 20 62 86 21 63 87 21 64 85 21 65 90 22 66 91 22 67 89 22 68 94 23 69 92 23 70 93 23 71 98 24 72 96 24 73 97 24 74 102 25 75 100 25 76 101 25 77 106 26 78 104 26 79 105 26 80 110 27 81 108 27 82 109 27 83 114 28 84 112 28 85 113 28 86 118 29 87 116 29 88 117 29 89 122 30 90 123 30 91 121 30 92 126 31 93 127 31 94 125 31 95 130 32 96 131 32 97 129 32 98 134 33 99 135 33 100 133 33 101 138 34 102 139 34 103 137 34 104 142 35 105 140 35 106 141 35 107 146 36 108 144 36 109 145 36 110 150 37 111 148 37 112 149 37 113 154 38 114 152 38 115 153 38 116 158 39 117 156 39 118 157 39 119 162 40 120 160 40 121 161 40 122 3 41 123 2 41 124 1 41 125 7 42 126 6 42 127 5 42 128 11 43 129 10 43 130 9 43 131 15 44 132 14 44 133 13 44 134 19 45 135 18 45 136 17 45 137 20 46 138 22 46 139 21 46 140 27 47 141 26 47 142 25 47 143 31 48 144 30 48 145 29 48 146 32 49 147 34 49 148 33 49 149 36 50 150 38 50 151 37 50 152 40 51 153 42 51 154 41 51 155 44 52 156 46 52 157 45 52 158 48 53 159 50 53 160 49 53 161 55 54 162 54 54 163 53 54 164 56 55 165 58 55 166 57 55 167 60 56 168 62 56 169 61 56 170 64 57 171 66 57 172 65 57 173 68 58 174 70 58 175 69 58 176 72 59 177 74 59 178 73 59 179 76 60 180 78 60 181 77 60 182 80 61 183 82 61 184 81 61 185 84 62 186 86 62 187 85 62 188 88 63 189 90 63 190 89 63 191 95 64 192 94 64 193 93 64 194 99 65 195 98 65 196 97 65 197 103 66 198 102 66 199 101 66 200 107 67 201 106 67 202 105 67 203 111 68 204 110 68 205 109 68 206 115 69 207 114 69 208 113 69 209 119 70 210 118 70 211 117 70 212 120 71 213 122 71 214 121 71 215 124 72 216 126 72 217 125 72 218 128 73 219 130 73 220 129 73 221 132 74 222 134 74 223 133 74 224 136 75 225 138 75 226 137 75 227 143 76 228 142 76 229 141 76 230 147 77 231 146 77 232 145 77 233 151 78 234 150 78 235 149 78 236 155 79 237 154 79 238 153 79 239 159 80 240 158 80 241 157 80 242 163 81 243 162 81 244 161 81 245</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Cube_003-mesh" name="Cube.003">
      <mesh>
        <source id="Cube_003-mesh-positions">
          <float_array id="Cube_003-mesh-positions-array" count="1032">-0.5168936 -0.8606321 -0.01616585 -0.5181704 -1.0311 0.07172358 -0.6213147 -0.8591408 0.07153946 -0.5172621 -0.9818672 0.009568512 -0.5770607 -0.9593096 0.02098011 -0.5912617 -0.9806032 0.0718128 -0.5902084 -0.8602598 0.009450554 -0.6300022 -0.8444517 0.9521695 -0.5315055 -1.011731 0.9560913 -0.5261254 -0.8452426 1.037355 -0.6036115 -0.9620903 0.9556761 -0.5893852 -0.9406279 1.004614 -0.5300995 -0.962731 1.015819 -0.5994784 -0.8447673 1.012723 -0.6206022 0.860302 0.07073491 -0.5171562 1.031878 0.07048398 -0.5167156 0.8604555 -0.01634657 -0.5902912 0.9815958 0.07065337 -0.5765441 0.9594818 0.02037876 -0.5168088 0.9818773 0.009030163 -0.5899055 0.8603383 0.009134054 -0.630084 0.8704369 0.9470321 -0.5265718 0.8712453 1.031486 -0.5260856 1.04263 0.9444761 -0.5997377 0.8704826 1.007576 -0.5861392 0.9698091 0.9958295 -0.5263656 0.9927998 1.005651 -0.5994682 0.9918834 0.9462072 0.6199124 -0.8611466 0.06938034 0.5168449 -1.033248 0.06934273 0.5167728 -0.861127 -0.0167098 0.5897368 -0.9828511 0.06936717 0.5763316 -0.9605165 0.01967805 0.5168133 -0.9828363 0.00849092 0.5896798 -0.8611443 0.008519887 0.6201623 -0.8611114 0.9304053 0.51712 -0.8610782 1.016428 0.5171175 -1.033209 0.9303635 0.5899967 -0.8610996 0.9912477 0.5766418 -0.9604729 0.9800719 0.5171409 -0.9827901 0.9912168 0.5899963 -0.9828145 0.9303904 0.5020862 1.03044 0.06935596 0.6036983 0.8580024 0.06935596 0.5040535 0.8586712 -0.01670926 0.5741145 0.9798398 0.06935596 0.5623699 0.9578095 0.01966613 0.5757703 0.8584297 0.008498549 0.5036672 0.9803149 0.008498549 0.5017601 1.03209 0.9293022 0.5037777 0.8600665 1.015541 0.6036931 0.8580286 0.9304077 0.5033176 0.9820834 0.9900788 0.5622579 0.9583761 0.9797297 0.5757232 0.858668 0.9911207 0.5740425 0.9802036 0.9301778 -0.5063708 -0.00615561 1.009028 -0.5793142 -0.006052792 0.9838123 -0.609725 -0.005940616 0.9231042 -0.5165131 -5.79358e-5 -0.01670926 -0.589542 -5.79358e-5 0.008498609 -0.6197803 -6.91129e-5 0.06934738 0.5167393 -6.4077e-5 1.016489 0.5896707 -8.1319e-5 0.9912841 0.6196703 -1.26309e-4 0.9304309 0.5167095 -6.82563e-5 -0.01670587 0.5896238 -8.7939e-5 0.008508086 0.6195983 -1.36461e-4 0.06937408 -0.5273925 -1.018272 0.517171 -0.5998144 -0.9683879 0.5168517 -0.6275441 -0.8493589 0.5145107 -0.5223609 1.036055 0.5094469 -0.5957654 0.9864965 0.5100282 -0.626248 0.8658384 0.5103632 0.5938374 0.8561877 0.4998908 0.5646527 0.9780985 0.4998908 0.4931427 1.028794 0.4998908 0.5168444 -1.033248 0.4998775 0.5898116 -0.9828405 0.4998885 0.6200488 -0.8611274 0.4998908 -0.6145175 -0.003726661 0.4958366 0.6179726 -4.3381e-4 0.4999165 -0.5212739 0.4375648 1.025095 -0.5821499 -0.4347347 0.9927302 -0.6126036 -0.4345325 0.9316733 -0.5164885 -0.430617 -0.01672822 -0.5895481 0.4304999 0.008507549 -0.6199242 0.4307324 0.06956362 0.5165299 -0.4299818 1.019196 0.5817995 0.4290046 0.9912829 0.6104169 0.4287042 0.9304255 0.5096879 0.4291734 -0.01670926 0.5895526 -0.4306274 0.008542597 0.6197627 -0.430633 0.06940704 -0.5094656 -0.4343942 1.01987 -0.5944792 0.4376778 1.000215 -0.6248482 0.4376514 0.9395253 -0.5165131 0.4304768 -0.01670926 -0.5894212 -0.4307121 0.008406221 -0.6192383 -0.4311373 0.06893742 0.5096879 0.4291734 1.016491 0.5897874 -0.4304623 0.9918304 0.6200432 -0.4305747 0.9305037 0.5166534 -0.4306091 -0.01668852 0.5817995 0.4290046 0.008498609 0.6104169 0.4287042 0.06935602 0.6036983 0.4274677 0.4998908 -0.5837035 -0.4526876 0.5010133 -0.5906001 0.4150416 0.505306 0.6199387 -0.4306082 0.4999104 -0.009402573 0.8776106 1.012256 -0.009791135 0.9998584 0.9858683 -0.0102747 1.04887 0.9253244 -0.003091394 0.8604189 -0.01670926 -0.003264844 0.9821019 0.008498609 -0.003988683 1.032384 0.06935602 -0.00455147 -0.8524818 1.032271 -0.005639255 -0.9731363 1.003603 -0.006020247 -1.02312 0.9419685 8.89028e-4 -0.8610203 -0.01684498 9.38856e-4 -0.9827283 0.008354008 0.001029074 -1.033131 0.06919533 0.002635598 -0.002228856 1.020299 1.28617e-4 -5.79358e-5 -0.01670926 -0.01033008 1.032575 0.501333 -0.003219962 -1.02705 0.5063371 5.23293e-4 -0.4305371 -0.01677978 6.48291e-4 -0.4285076 1.04071 -0.002705276 0.4347544 1.018552 -8.27853e-4 0.4303008 -0.01670926 -0.5261254 -0.8452426 0.0144158 -0.5094656 -0.4343942 0.03190028 -0.00455147 -0.8524818 0.01949954 6.4829e-4 -0.4285076 0.01106071 -0.5004085 -0.832921 5.26041e-4 -0.5016443 -0.9979116 0.08559131 -0.6014742 -0.8314776 0.08541303 -0.5007652 -0.9502604 0.02543348 -0.5586423 -0.9284276 0.0364784 -0.5723869 -0.949037 0.08567768 -0.5713675 -0.8325607 0.02531927 -0.6098825 -0.8172605 0.9377455 -0.5145509 -0.979165 0.9415412 -0.5093437 -0.818026 1.020193 -0.5843399 -0.931119 0.9411394 -0.5705707 -0.9103463 0.9885047 -0.51319 -0.931739 0.9993494 -0.5803396 -0.8175659 0.9963529 -0.6007846 0.8327137 0.08463436 -0.5006626 0.9987767 0.08439153 -0.5002362 0.8328621 3.51131e-4 -0.5714476 0.9501098 0.0845555 -0.5581422 0.9287065 0.03589642 -0.5003264 0.9503823 0.02491235 -0.5710743 0.8327488 0.02501291 -0.6099618 0.8425229 0.9327731 -0.5097757 0.8433052 1.014513 -0.5093052 1.009183 0.9302994 -0.5805906 0.842567 0.9913713 -0.567429 0.9387019 0.9800025 -0.5095762 0.9609538 0.989509 -0.5803298 0.9600669 0.9319747 0.599868 -0.8334189 0.08332335 0.5001124 -0.9999898 0.08328694 0.5000426 -0.8334 -4.17232e-7 0.570662 -0.9512127 0.0833106 0.5576876 -0.9295958 0.03521806 0.5000818 -0.9511983 0.02439045 0.570607 -0.8334167 0.02441853 0.6001099 -0.8333849 0.9166806 0.5003786 -0.8333527 0.9999397 0.5003763 -0.9999527 0.9166402 0.5709136 -0.8333734 0.9755679 0.5579878 -0.9295535 0.9647513 0.5003989 -0.9511536 0.9755381 0.5709133 -0.9511772 0.9166662 0.4858279 0.9973844 0.08329975 0.5841749 0.830488 0.08329975 0.4877321 0.8311352 0 0.5555417 0.9484103 0.08329975 0.5441746 0.9270879 0.03520655 0.5571444 0.8309015 0.02439785 0.4873582 0.9488701 0.02439785 0.4855123 0.9989812 0.9156129 0.4874652 0.8324857 0.9990804 0.5841699 0.8305132 0.916683 0.4870198 0.9505818 0.9744367 0.5440661 0.9276362 0.96442 0.5570988 0.8311321 0.975445 0.5554721 0.9487624 0.9164604 -0.4902238 -0.005901694 0.9927769 -0.5608234 -0.005802214 0.9683715 -0.5902569 -0.005693674 0.9096141 -0.5000402 0 0 -0.5707225 0 0.02439785 -0.5999891 -1.08179e-5 0.08329141 0.5000102 -5.94381e-6 0.9999983 0.5705981 -2.26318e-5 0.9756032 0.5996336 -6.61759e-5 0.9167054 0.4999814 -9.98875e-6 3.42727e-6 0.5705527 -2.90389e-5 0.02440702 0.599564 -7.60023e-5 0.08331727 -0.51057 -0.9854957 0.5167249 -0.5806648 -0.9372141 0.5164159 -0.6075035 -0.82201 0.5141502 -0.5057001 1.00282 0.5092491 -0.5767459 0.954853 0.5098116 -0.6062489 0.8380721 0.5101359 0.5746309 0.8287315 0.5 0.546384 0.9467249 0.5 0.4771718 0.9957913 0.5 0.5001119 -0.9999899 0.4999872 0.5707345 -0.9512025 0.4999979 0.6 -0.8334004 0.5 -0.5948954 -0.003550887 0.4960761 0.5979905 -3.63796e-4 0.5000249 -0.5046481 0.4235604 1.008328 -0.5635679 -0.4207091 0.9770027 -0.593043 -0.4205134 0.9179078 -0.5000164 -0.4167237 -1.8239e-5 -0.5707284 0.4167225 0.02440661 -0.6001284 0.4169476 0.08350074 0.4998075 -0.4161089 1.002618 0.5629798 0.4152752 0.975602 0.5906776 0.4149845 0.9167002 0.4931854 0.4154386 0 0.5704838 -0.4167338 0.02444046 0.5997231 -0.4167392 0.08334922 -0.4932191 -0.4203796 1.003271 -0.575501 0.4236698 0.9842472 -0.6048942 0.4236443 0.9255076 -0.5000402 0.4167002 0 -0.5706056 -0.4168157 0.02430856 -0.5994645 -0.4172273 0.08289468 0.4931854 0.4154386 1 0.570711 -0.416574 0.9761319 0.5999946 -0.4166828 0.9167758 0.4999271 -0.4167161 2.01762e-5 0.5629798 0.4152752 0.02439785 0.5906776 0.4149845 0.08329981 0.5841749 0.4137877 0.5 -0.5650716 -0.4380851 0.5010865 -0.5717465 0.401761 0.5052412 0.5998935 -0.4167152 0.500019 -0.009224951 0.8494661 0.9959011 -0.009600996 0.9677857 0.9703614 -0.01006907 1.015222 0.911763 -0.003116548 0.8328268 0 -0.003284335 0.9505997 0.02439785 -0.003984928 0.9992666 0.08329981 -0.004529774 -0.8250325 1.015273 -0.005582571 -0.94181 0.9875264 -0.005951285 -0.9901874 0.9278723 7.35976e-4 -0.8332967 -1.31309e-4 7.84203e-4 -0.9510937 0.02425789 8.7156e-4 -0.9998773 0.08314424 0.002426445 -0.002101123 1.003686 0 0 0 -0.01012259 0.9994514 0.5013959 -0.003240942 -0.9939914 0.5062392 3.81993e-4 -0.4166464 -6.81877e-5 5.02975e-4 -0.4146821 1.023441 -0.002742826 0.4208403 1.001994 -9.25736e-4 0.4165298 0 -0.5181704 -1.0311 0.3251777 -0.6213147 -0.8591408 0.3250539 -0.5912617 -0.9806032 0.3252378 -0.6206022 0.860302 0.3245128 -0.5171562 1.031878 0.324344 -0.5902912 0.9815958 0.324458 0.6199124 -0.8611466 0.3236017 0.5168449 -1.033248 0.3235765 0.5897368 -0.9828511 0.3235929 0.5020862 1.03044 0.3235853 0.6036983 0.8580024 0.3235853 0.5741145 0.9798398 0.3235853 -0.6197803 -6.91129e-5 0.3235796 0.6195983 -1.36461e-4 0.3235976 -0.6199242 0.4307324 0.323725 0.6197627 -0.430633 0.3236197 -0.6192383 -0.4311373 0.3233038 0.6104169 0.4287042 0.3235854 -0.003988683 1.032384 0.3235854 0.001029074 -1.033131 0.3234773 -0.5181704 -1.0311 0.3493177 -0.6213147 -0.8591408 0.3491938 -0.5912617 -0.9806032 0.3493777 -0.6206022 0.860302 0.3486527 -0.5171562 1.031878 0.348484 -0.5902912 0.9815958 0.3485979 0.6199124 -0.8611466 0.3477417 0.5168449 -1.033248 0.3477163 0.5897368 -0.9828511 0.3477329 0.5020862 1.03044 0.3477253 0.6036983 0.8580024 0.3477253 0.5741145 0.9798398 0.3477253 -0.6197803 -6.91129e-5 0.3477195 0.6195983 -1.36461e-4 0.3477374 -0.6199242 0.4307324 0.3478649 0.6197627 -0.430633 0.3477597 -0.6192383 -0.4311373 0.3474438 0.6104169 0.4287042 0.3477253 -0.003988683 1.032384 0.3477253 0.001029074 -1.033131 0.3476172 -0.5181704 -1.0311 0.7086318 -0.6213147 -0.8591408 0.7085079 -0.5912617 -0.9806032 0.7086918 -0.6206022 0.860302 0.7079668 -0.5171562 1.031878 0.7077981 -0.5902912 0.9815958 0.707912 0.6199124 -0.8611466 0.7070558 0.5168449 -1.033248 0.7070305 0.5897368 -0.9828511 0.7070469 0.5020862 1.03044 0.7070394 0.6036983 0.8580024 0.7070394 0.5741145 0.9798398 0.7070394 -0.6197803 -6.91129e-5 0.7070336 0.6195983 -1.36461e-4 0.7070516 -0.6199242 0.4307324 0.7071791 0.6197627 -0.430633 0.7070738 -0.6192383 -0.4311373 0.7067579 0.6104169 0.4287042 0.7070394 -0.003988683 1.032384 0.7070394 0.001029074 -1.033131 0.7069314 -0.5181704 -1.0311 0.7327717 -0.6213147 -0.8591408 0.7326478 -0.5912617 -0.9806032 0.7328317 -0.6206022 0.860302 0.7321068 -0.5171562 1.031878 0.731938 -0.5902912 0.9815958 0.7320519 0.6199124 -0.8611466 0.7311957 0.5168449 -1.033248 0.7311704 0.5897368 -0.9828511 0.7311869 0.5020862 1.03044 0.7311794 0.6036983 0.8580024 0.7311794 0.5741145 0.9798398 0.7311794 -0.6197803 -6.91129e-5 0.7311735 0.6195983 -1.36461e-4 0.7311915 -0.6199242 0.4307324 0.731319 0.6197627 -0.430633 0.7312137 -0.6192383 -0.4311373 0.7308979 0.6104169 0.4287042 0.7311794 -0.003988683 1.032384 0.7311794 0.001029074 -1.033131 0.7310712</float_array>
          <technique_common>
            <accessor source="#Cube_003-mesh-positions-array" count="344" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_003-mesh-normals">
          <float_array id="Cube_003-mesh-normals-array" count="1005">0 0 -1 -6.91448e-5 0.1021597 -0.9947681 0.1595522 0.09407806 -0.9826966 0.9926174 -0.1212869 -3.26694e-4 0.9700883 -0.1085064 -0.2171521 0.9736593 8.67418e-4 -0.2280061 0.01310449 0.1112514 0.9937059 -0.1325826 0.0867511 0.9873684 -0.1545205 -0.02555203 0.9876591 -0.01339077 -0.9998375 0.01207643 -0.3068383 -0.9516375 0.0153737 -0.2752212 -0.9149838 -0.295056 0.3067563 0.9517699 -0.005882322 0.2777662 0.911164 -0.3043455 0.00179702 0.9411279 -0.338046 -0.1596593 -0.09554523 -0.9825376 -0.1914761 -0.5418925 -0.8183456 -0.5981812 -0.4368733 -0.6718043 -0.7822661 -0.5796652 -0.2281408 -0.9727035 -0.09424543 -0.2120513 -0.660775 -0.08526682 -0.7457252 -0.9734252 -0.06866103 0.2184699 -0.790933 -0.5642925 0.2366412 -0.5992603 -0.4117507 0.6865482 -0.1814058 -0.5208866 0.8341277 -0.1475449 -0.05554276 0.9874946 -0.6566341 -0.03309434 0.7534829 -0.9684889 0.1083776 -0.2242403 -0.7806557 0.5773196 -0.2393301 -0.5974606 0.4325085 -0.6752609 -0.1889351 0.5364545 -0.8225084 -0.1588094 0.09414303 -0.9828106 -0.6606707 0.08469152 -0.7458831 -0.9710662 0.1035898 0.2151734 -0.6529901 0.07605296 0.7535384 -0.5907039 0.4340736 0.6801831 -0.1705908 0.5391305 0.8247649 -0.7808556 0.582333 0.2261701 0.7824763 -0.577407 -0.2330926 0.5975942 -0.4299312 -0.6767868 0.1877305 -0.5329784 -0.8250402 0.1576467 -0.09279108 -0.9831263 0.6603419 -0.08380246 -0.7462747 0.9702196 -0.1085525 0.2165418 0.660543 -0.08428043 0.7460428 0.5976366 -0.4300583 0.6766687 0.1706157 -0.09644287 0.9806066 0.1923679 -0.5354177 0.8223884 0.7824911 -0.5775119 0.232783 0.7890687 0.5762385 -0.2128846 0.6103171 0.4313796 -0.664398 0.1939038 0.5361781 -0.8215318 0.974875 0.1142168 -0.1912413 0.672324 0.08898431 -0.7348893 0.1987861 0.5352709 0.8209562 0.6087983 0.429319 0.6671205 0.7913018 0.5731702 0.2128788 0.1546673 0.09658622 0.9832341 0.6685083 0.08994388 0.7382458 0.9747285 0.1143134 0.1919295 -0.2784615 -0.9053676 0.3205756 -0.8272393 -0.5618477 0.001559674 -0.6569544 -0.03491151 0.7531216 -0.2927824 0.9560119 -0.0178824 -0.2694311 0.9103854 -0.3140149 -0.9947282 0.1019789 -0.01077961 -0.6609742 -2.69378e-4 -0.7504088 -0.9804699 -0.008135318 -0.1965011 0.01819854 0.5295681 0.8480722 0.2928984 0.9071326 0.3021936 0.9919025 0.1270018 -8.71032e-6 0.8273585 0.561674 -6.79923e-4 5.14204e-4 0.5164323 -0.856328 0.6698958 -8.33882e-4 0.7424547 0.9739748 9.53201e-4 0.2266544 0.2935246 -0.9559503 0.001563608 0.2713646 -0.9103465 -0.3124591 0.8225802 -0.5686492 -2.74472e-4 0.1672382 0.002528071 -0.9859133 0.6694576 0.01100218 -0.7427688 0.008911907 -0.5298746 0.8480293 -0.001104176 -0.515625 -0.8568137 -0.00362575 -0.9426144 -0.3338639 -0.978403 -0.01101803 -0.206413 -0.9998683 0.01188629 0.01105958 -0.9764827 0.006732881 -0.2154906 0.6606495 1.50103e-4 -0.7506945 0.6632511 0.006668388 -0.7483673 0.9746882 0.01200801 -0.2232459 0.1659039 1.71283e-5 -0.986142 0.1666626 0.001495301 -0.9860129 0.6636088 0.008443653 0.7480322 0.9746685 0.01244097 0.2233082 0.977538 0.01847207 0.2099486 0.1702507 0.004605054 0.98539 0.6694047 0.01115745 0.7428143 -0.659907 1.48061e-4 -0.7513474 -0.660146 -3.36548e-5 -0.7511374 -0.1655265 2.85413e-4 -0.9862053 -0.1654255 1.6933e-5 -0.9862223 -0.6601729 -0.01404875 0.7509822 -0.975229 -0.00322324 0.2211744 -0.9768391 0.029563 0.2119231 -0.1883232 0.02952921 0.9816631 -0.1730111 -2.21354e-4 0.9849199 0.003126382 0.01157933 0.9999281 -0.003026843 0.020787 0.9997794 0.9996821 0.0252158 -2.10151e-5 0.9998387 0.01796472 -1.44569e-4 -1.54542e-4 -2.61069e-5 -1 -1.15093e-5 6.76989e-5 -1 -0.9786214 -0.03709751 0.2022967 0.2672832 -0.9121499 0.3107125 -0.8187658 0.5739098 -0.01582491 -0.2700282 0.9135212 0.304243 -0.9975614 -0.06762778 -0.01725554 0.01348382 0.9489329 0.3151899 -0.009856045 -0.9408475 0.338687 0.999997 0.002415657 -3.28535e-4 -0.9996055 -0.02807253 -7.92316e-4 -0.9999371 0.009261906 -0.006331682 -5.826e-4 -0.1018515 -0.9947994 0.9775422 0.01839071 -0.2099364 0.01256453 -0.118011 0.9929329 -0.6668496 0.04105937 0.7440603 0.1694156 0.003732502 0.9855377 0.1910927 0.002099871 0.9815698 -0.165165 -5.16674e-4 -0.9862658 0.002138912 0.003187835 0.9999927 0.005862772 0.9997757 -0.02035385 0.04058784 0.01938629 0.998988 -0.01032215 -0.04209858 0.9990602 0 0 -1 -6.91521e-5 0.1021597 -0.994768 0.1595524 0.09407812 -0.9826965 0.9926175 -0.1212866 -3.26778e-4 0.9700885 -0.1085067 -0.2171514 0.9736595 8.67483e-4 -0.2280055 0.01310515 0.1112502 0.9937061 -0.1325838 0.08674901 0.9873684 -0.1545217 -0.02555185 0.9876589 -0.01339054 -0.9998375 0.01207667 -0.3068394 -0.9516372 0.01537376 -0.2752228 -0.9149829 -0.2950571 0.306756 0.9517701 -0.005882084 0.2777661 0.9111639 -0.3043462 0.001797497 0.9411274 -0.3380478 -0.1596593 -0.09554523 -0.9825376 -0.1914764 -0.5418916 -0.8183461 -0.5981814 -0.4368737 -0.6718038 -0.7822674 -0.5796634 -0.2281407 -0.9727035 -0.09424507 -0.2120515 -0.660775 -0.08526706 -0.7457252 -0.9734252 -0.06866079 0.2184701 -0.7909337 -0.5642912 0.2366423 -0.5992582 -0.4117514 0.6865497 -0.1814041 -0.5208823 0.8341308 -0.1475434 -0.05554217 0.9874948 -0.6566327 -0.03309357 0.7534841 -0.9684889 0.1083777 -0.2242404 -0.7806565 0.5773182 -0.2393311 -0.5974596 0.4325085 -0.6752617 -0.1889353 0.5364526 -0.8225096 -0.1588094 0.09414303 -0.9828106 -0.6606706 0.08469176 -0.7458832 -0.9710659 0.10359 0.2151745 -0.6529895 0.07605266 0.7535389 -0.5907067 0.4340714 0.680182 -0.1705936 0.5391276 0.8247662 -0.7808566 0.5823318 0.2261697 0.7824757 -0.5774079 -0.2330925 0.5975936 -0.4299312 -0.6767873 0.18773 -0.5329792 -0.8250398 0.1576462 -0.09279096 -0.9831265 0.6603422 -0.08380305 -0.7462743 0.9702194 -0.1085529 0.2165427 0.6605446 -0.08428025 0.7460414 0.5976369 -0.4300579 0.6766686 0.1706178 -0.09644365 0.980606 0.1923682 -0.5354174 0.8223885 0.7824919 -0.5775104 0.2327836 0.7890685 0.5762389 -0.2128841 0.6103174 0.4313796 -0.6643978 0.1939033 0.5361777 -0.8215321 0.9748751 0.1142169 -0.1912409 0.6723242 0.08898425 -0.7348891 0.1987869 0.5352713 0.8209558 0.6087981 0.4293176 0.6671217 0.7913016 0.5731706 0.2128791 0.1546675 0.09658557 0.9832341 0.668508 0.08994406 0.7382461 0.9747284 0.1143131 0.1919303 -0.2784627 -0.9053661 0.3205791 -0.8272398 -0.561847 0.001559495 -0.6569553 -0.03491121 0.7531208 -0.2927862 0.9560108 -0.01788216 -0.2694322 0.9103844 -0.3140169 -0.9947282 0.1019788 -0.01077973 -0.6609745 -2.69291e-4 -0.7504084 -0.9804701 -0.008135259 -0.1965006 0.01819908 0.5295689 0.8480717 0.2928978 0.9071333 0.3021921 0.9919025 0.1270025 -8.77672e-6 0.8273574 0.5616755 -6.79925e-4 5.14402e-4 0.5164306 -0.8563289 0.669895 -8.33373e-4 0.7424554 0.9739748 9.53207e-4 0.2266545 0.2935234 -0.9559507 0.001562952 0.2713642 -0.9103469 -0.312458 0.8225797 -0.56865 -2.74838e-4 0.1672382 0.002528011 -0.9859133 0.6694576 0.01100218 -0.7427688 0.008911609 -0.5298745 0.8480293 -0.001104176 -0.5156229 -0.8568149 -0.003625392 -0.9426137 -0.3338657 -0.978403 -0.01101809 -0.2064129 -0.9998682 0.01188635 0.01105964 -0.9764828 0.006732881 -0.2154901 0.6606499 1.50051e-4 -0.7506943 0.6632508 0.006668388 -0.7483676 0.9746883 0.01200807 -0.2232456 0.1659038 1.70914e-5 -0.9861419 0.1666621 0.001495242 -0.986013 0.6636099 0.008443415 0.7480312 0.9746687 0.01244091 0.2233076 0.9775379 0.01847195 0.2099488 0.1702517 0.004604816 0.9853898 0.6694048 0.01115757 0.7428142 -0.6599071 1.48281e-4 -0.7513473 -0.6601462 -3.37488e-5 -0.7511372 -0.1655266 2.85508e-4 -0.9862053 -0.1654253 1.68375e-5 -0.9862223 -0.6601739 -0.01404887 0.7509815 -0.9752292 -0.003223359 0.2211735 -0.9768391 0.02956312 0.2119231 -0.1883264 0.02952837 0.9816625 -0.1730111 -2.20961e-4 0.9849199 0.003127098 0.01157909 0.9999282 -0.003026962 0.02078783 0.9997794 0.9996821 0.0252158 -2.09923e-5 0.9998387 0.01796478 -1.44509e-4 -1.54522e-4 -2.61364e-5 -1 -1.14576e-5 6.76534e-5 -1 -0.9786214 -0.03709751 0.2022971 0.2672842 -0.9121491 0.3107143 -0.8187685 0.5739059 -0.01582467 -0.2700289 0.9135211 0.304243 -0.9975615 -0.06762832 -0.01725554 0.01348447 0.948933 0.3151893 -0.009855985 -0.9408477 0.3386864 0.9999971 0.002415657 -3.28584e-4 -0.9996055 -0.02807247 -7.92344e-4 -0.9999371 0.009261906 -0.006331682 -5.82576e-4 -0.1018514 -0.9947994 0.9775421 0.01839065 -0.2099362 0.012564 -0.1180102 0.9929329 -0.6668522 0.04105865 0.744058 0.1694154 0.003732979 0.9855378 0.1910917 0.002100408 0.98157 -0.1651648 -5.16723e-4 -0.9862658 0.002139151 0.003188073 0.9999927 0.005863726 0.9997757 -0.02035319 0.0038414 0.9999926 0 -9.86115e-4 0.9999996 0 0.9717634 0.2359576 0 -0.9999883 0.004851162 0 0.5687013 -0.8225442 0 -0.5665418 0.8240331 0 -1 -3.34039e-4 0 -0.9707277 -0.2401829 0 -0.9999993 -0.001257419 0 1 3.47562e-4 0 -2.26717e-4 -1 0 -0.9999988 -0.00157833 0 0.9998776 0.015648 0 0.5748372 0.8182678 0 0.970611 -0.2406537 0 1 3.81953e-4 0 -0.9701659 0.2424421 0 0.9997709 0.02140522 0 -0.5684111 -0.8227447 0 -0.003911912 -0.9999924 0 0.0038414 0.9999927 0 -9.86115e-4 0.9999995 0 0.9717635 0.235957 0 -0.9999883 0.004851281 0 0.5686989 -0.8225459 0 -0.5665422 0.8240328 0 -1 -3.34038e-4 0 -0.9707276 -0.2401834 0 -0.9999993 -0.001257419 0 1 3.47385e-4 0 -2.26718e-4 -1 0 -0.9999988 -0.001578509 0 0.9998776 0.01564794 0 0.574836 0.8182687 0 0.9706109 -0.2406545 0 1 3.81773e-4 0 -0.9701662 0.2424407 0 0.9997709 0.02140533 0 -0.5684127 -0.8227437 0 -0.003911912 -0.9999924 0 0.0038414 0.9999927 0 -9.86117e-4 0.9999996 0 0.9717632 0.2359582 0 -0.9999883 0.0048514 0 0.5687003 -0.8225449 0 -0.5665427 0.8240325 0 -0.9707275 -0.2401837 0 -0.9999993 -0.001257061 0 1 3.47702e-4 0 -2.26717e-4 -1 0 -0.9999988 -0.00157833 0 0.9998776 0.01564824 0 0.9706107 -0.2406552 0 1 3.81771e-4 0 -0.970166 0.2424415 0 0.9997709 0.02140486 0 -0.5684115 -0.8227444 0 -0.003911912 -0.9999924 0 -9.86111e-4 0.9999996 0 0.9717631 0.2359588 0 -0.9999883 0.004851222 0 0.5687015 -0.8225441 0 -0.5665447 0.8240311 0 -0.9707275 -0.2401837 0 1 3.47883e-4 0 -2.26718e-4 -1 0 -0.9999988 -0.001578152 0 0.9998776 0.0156483 0 0.9706105 -0.2406563 0 1 3.8195e-4 0 0.9997709 0.02140498 0 -0.5684103 -0.8227452 0 -0.003911912 -0.9999924 0</float_array>
          <technique_common>
            <accessor source="#Cube_003-mesh-normals-array" count="335" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_003-mesh-map-0">
          <float_array id="Cube_003-mesh-map-0-array" count="3564">0.4842762 0.304527 0.5249489 0.4670838 0.3616918 0.4559773 0.5278167 1.3599 0.3071991 1.36076 0.3405654 1.162791 -0.1152601 -0.3549512 0.1724025 -0.3531774 0.1042028 -0.1342491 0.02240085 0.8147085 0.05736315 0.5527183 0.2655781 0.5552102 1.078147 0.4692564 1.344453 0.4751487 1.369777 0.749041 0.7965644 -0.2826948 0.8092992 -0.352505 0.8418068 -0.3375182 0.2630647 0.5206422 0.2819204 0.5267479 0.2867138 0.554942 0.8719852 -0.2815397 0.8388808 -0.2821729 0.8572006 -0.3380878 1.383245 -0.338934 1.387719 -0.3991709 1.414725 -0.3916385 -0.2785934 0.5736611 -0.2745011 0.5389567 -0.2402995 0.5293768 0.3082253 0.4524115 0.333508 0.4541562 0.3396655 0.495855 0.8070722 0.4232905 0.8246179 0.4697966 0.7979834 0.4665369 1.417166 1.009773 1.411161 1.037534 1.383679 1.044003 0.7417859 0.4398387 0.7770166 0.4315857 0.7826576 0.4706705 1.328031 0.3767729 1.370044 0.3817757 1.368684 0.4306091 0.1724025 -0.3531774 0.1853479 -0.4115424 0.2184197 -0.400864 0.9132171 1.048713 0.8891405 1.042648 0.8843333 1.014536 0.3071991 1.36076 0.2996157 1.416591 0.2756267 1.406277 0.217364 1.036412 0.2085553 1.063808 0.1783317 1.069919 0.2583107 -0.3542003 0.2164457 -0.3560236 0.234786 -0.4044533 0.7373907 1.358192 0.7696277 1.357871 0.762377 1.403942 -0.2523282 0.4635205 -0.2314767 0.5288634 -0.2766264 0.5183783 -0.2451356 1.150494 -0.271204 1.146475 -0.2700172 1.124329 1.340996 0.4379335 1.373053 0.4483172 1.38229 0.4800631 0.3652765 0.5628952 0.3471534 0.562165 0.3492906 0.517209 0.3616918 0.4559773 0.3705763 0.5083967 0.3515627 0.4968501 0.809227 0.5061461 0.8070569 0.4814947 0.8303303 0.4715162 -0.3617053 -0.3503695 -0.4007734 -0.3499315 -0.396229 -0.3964088 0.6858423 0.5484235 0.6706228 0.5012096 0.6954544 0.5058544 -0.2333217 0.5705984 -0.2402995 0.5293768 0.04964488 0.5193265 1.145323 -0.3626976 1.387719 -0.3991709 1.383245 -0.338934 0.1724025 -0.3531774 0.2154684 -0.3522157 0.1465513 -0.1303212 1.353667 0.1876761 1.370044 0.3817757 1.328031 0.3767729 1.143731 1.010845 1.383408 1.010082 1.383679 1.044003 0.8246179 0.4697966 0.8070722 0.4232905 1.048863 0.375975 0.7965644 -0.2826948 0.8386602 -0.2811818 0.7727239 -0.0694698 0.8388808 -0.2821729 0.8719852 -0.2815397 0.8053228 -0.07326024 -0.1152601 -0.3549512 -0.3617053 -0.3503695 -0.3640009 -0.407433 0.8710384 0.7433254 0.809227 0.5061461 0.839628 0.5008405 0.5120571 0.5653628 0.3652765 0.5628952 0.3630568 0.5085978 1.070208 0.4354504 1.340996 0.4379335 1.344453 0.4751487 0.7417859 0.4398387 0.7568681 0.4853407 0.5395537 0.5139247 1.417166 1.009773 1.383408 1.010082 1.369777 0.749041 -0.2523282 0.4635205 -0.3006207 0.464172 -0.3778758 0.2573955 0.7696277 1.357871 0.7373907 1.358192 0.7523012 1.158789 -0.03262078 1.071869 0.1817641 1.036485 0.1783317 1.069919 0.5218298 1.415858 0.2996157 1.416591 0.3071991 1.36076 0.2708449 0.276335 0.3616918 0.4559773 0.3378825 0.4531735 0.3355473 0.7633116 0.3471534 0.562165 0.3652765 0.5628952 0.3082253 0.4524115 0.3219789 0.5048312 0.1047271 0.5128614 -0.249036 0.8798831 -0.2785934 0.5736611 -0.2333217 0.5705984 0.2583107 -0.3542003 0.2703159 -0.4145299 0.5304064 -0.3875589 0.217364 1.036412 0.1817641 1.036485 0.2271146 0.7980079 1.048863 0.375975 0.8070722 0.4232905 0.7625638 0.2665069 1.022662 0.05525022 0.7569423 0.1064674 0.8053228 -0.07326024 0.3098707 1.16267 0.3251953 0.9633036 0.3559363 0.9634471 0.1925451 -0.130246 0.1816835 0.0859633 0.139953 0.08026468 0.7873814 0.9569609 0.7585753 0.9581357 0.7383804 0.7392652 -0.4042286 0.05921518 -0.4246501 0.05663013 -0.4121535 -0.1466693 0.7345188 0.2744295 0.7269799 0.1128844 0.7569423 0.1064674 0.6980761 0.2849982 0.6885844 0.1209945 0.7257173 0.1138489 1.358323 0.01616013 1.315271 0.01971185 1.351922 -0.1493442 0.1974475 0.2755106 0.08819508 0.08480513 0.1303796 0.08962094 -0.08017086 0.2674952 -0.2186614 0.07257401 0.08819508 0.08480513 0.554579 0.7592127 0.5675109 0.9620214 0.3559363 0.9634471 0.4792816 -0.08303219 0.4548501 0.1273057 0.1816835 0.0859633 0.7585753 0.9581357 0.5675109 0.9620214 0.554579 0.7592127 1.351922 -0.1493442 1.315271 0.01971185 1.022662 0.05525022 1.309803 0.1871761 1.328031 0.3767729 1.048863 0.375975 0.7357222 1.414478 0.5218298 1.415858 0.5278167 1.3599 -0.2356137 1.123389 -0.03262078 1.071869 -0.04139631 1.103139 1.070208 0.4354504 1.078147 0.4692564 0.839628 0.5008405 0.5120571 0.5653628 0.5002894 0.5156605 0.6706228 0.5012096 1.065071 0.429667 1.048863 0.375975 1.328031 0.3767729 0.913661 1.014023 1.143731 1.010845 1.144705 1.046333 1.145323 -0.3626976 1.133715 -0.2979122 0.8719852 -0.2815397 0.05736315 0.5527183 0.04964488 0.5193265 0.2630647 0.5206422 0.8996943 0.7411515 0.839628 0.5008405 1.078147 0.4692564 -0.2071204 0.8771493 -0.2333217 0.5705984 0.05736315 0.5527183 0.7373907 1.358192 0.5278167 1.3599 0.5553715 1.161482 0.7523012 1.158789 0.5553715 1.161482 0.5675109 0.9620214 1.315271 0.01971185 1.309803 0.1871761 1.018915 0.2006311 1.351922 -0.1493442 1.082862 -0.1130297 1.133715 -0.2979122 0.7383804 0.7392652 0.554579 0.7592127 0.5120571 0.5653628 0.5213645 -0.3197541 0.4792816 -0.08303219 0.1925451 -0.130246 0.5120571 0.5653628 0.554579 0.7592127 0.3588068 0.7632392 0.07899737 0.4579698 -0.08017086 0.2674952 0.1974475 0.2755106 0.3082253 0.4524115 0.1974475 0.2755106 0.2318354 0.2795746 1.391763 -0.1550627 1.351922 -0.1493442 1.383245 -0.338934 0.7417859 0.4398387 0.6980761 0.2849982 0.7329499 0.2755556 0.779179 0.4303248 0.7345188 0.2744295 0.7625638 0.2665069 -0.3862053 -0.1450047 -0.4121535 -0.1466693 -0.4007734 -0.3499315 0.7660426 0.7355929 0.7383804 0.7392652 0.6858423 0.5484235 0.2583107 -0.3542003 0.1925451 -0.130246 0.150947 -0.1351315 0.2766269 1.360651 0.3098707 1.16267 0.3405654 1.162791 1.082862 -0.1130297 0.8053228 -0.07326024 0.8719852 -0.2815397 1.018915 0.2006311 0.7625638 0.2665069 0.7569423 0.1064674 0.3251953 0.9633036 0.3355473 0.7633116 0.3588068 0.7632392 0.1816835 0.0859633 0.2708449 0.276335 0.2373852 0.2715275 0.7825812 1.15825 0.7523012 1.158789 0.7585753 0.9581357 -0.346766 0.2601526 -0.3778758 0.2573955 -0.4246501 0.05663013 0.7736073 -0.07032644 0.8053228 -0.07326024 0.7569423 0.1064674 0.7328045 -0.0668413 0.7727239 -0.0694698 0.7257173 0.1138489 1.358323 0.01616013 1.353667 0.1876761 1.309803 0.1871761 0.1042028 -0.1342491 0.1465513 -0.1303212 0.1303796 0.08962094 -0.1957928 -0.137696 0.1042028 -0.1342491 0.08819508 0.08480513 0.5675109 0.9620214 0.5553715 1.161482 0.3405654 1.162791 0.4548501 0.1273057 0.4842762 0.304527 0.2708449 0.276335 0.6885844 0.1209945 0.6980761 0.2849982 0.4842762 0.304527 -0.3862053 -0.1450047 -0.1957928 -0.137696 -0.2186614 0.07257401 -0.346766 0.2601526 -0.08017086 0.2674952 0.07899737 0.4579698 0.7965644 -0.2826948 0.7328045 -0.0668413 0.4792816 -0.08303219 -0.2356137 1.123389 -0.2071204 0.8771493 0.02240085 0.8147085 0.8996943 0.7411515 1.124235 0.7353435 1.143731 1.010845 0.7328045 -0.0668413 0.6885844 0.1209945 0.4548501 0.1273057 -0.346766 0.2601526 -0.4042286 0.05921518 -0.2186614 0.07257401 0.2867138 0.554942 0.2537391 0.7975072 0.2271146 0.7980079 0.7965644 -0.2826948 0.5213645 -0.3197541 0.5304064 -0.3875589 -0.2700172 1.124329 -0.249036 0.8798831 -0.2071204 0.8771493 -0.2523282 0.4635205 0.07899737 0.4579698 0.1047271 0.5128614 1.405377 0.7518975 1.369777 0.749041 1.344453 0.4751487 0.5249489 0.4670838 0.5395537 0.5139247 0.3705763 0.5083967 0.8710384 0.7433254 0.8996943 0.7411515 0.913661 1.014023 -0.1152601 -0.3549512 -0.09899801 -0.4141257 0.1853479 -0.4115424 1.124235 0.7353435 1.369777 0.749041 1.383408 1.010082 -0.03262078 1.071869 0.02240085 0.8147085 0.2271146 0.7980079 -0.3617053 -0.3503695 -0.1152601 -0.3549512 -0.1957928 -0.137696 0.6980761 0.2849982 0.7417859 0.4398387 0.5249489 0.4670838 0.07899737 0.4579698 -0.08017086 0.2674952 0.1974475 0.2755106 0.2708449 0.276335 0.4842762 0.304527 0.3616918 0.4559773 0.5553715 1.161482 0.5278167 1.3599 0.3405654 1.162791 -0.1957928 -0.137696 -0.1152601 -0.3549512 0.1042028 -0.1342491 0.2271146 0.7980079 0.02240085 0.8147085 0.2655781 0.5552102 1.124235 0.7353435 1.078147 0.4692564 1.369777 0.749041 0.8386602 -0.2811818 0.7965644 -0.2826948 0.8418068 -0.3375182 0.2655781 0.5552102 0.2630647 0.5206422 0.2867138 0.554942 0.8872715 -0.3501904 0.8719852 -0.2815397 0.8572006 -0.3380878 1.418391 -0.3441779 1.383245 -0.338934 1.414725 -0.3916385 -0.2333217 0.5705984 -0.2785934 0.5736611 -0.2402995 0.5293768 0.3219789 0.5048312 0.3082253 0.4524115 0.3396655 0.495855 0.779179 0.4303248 0.8070722 0.4232905 0.7979834 0.4665369 1.383408 1.010082 1.417166 1.009773 1.383679 1.044003 0.7568681 0.4853407 0.7417859 0.4398387 0.7826576 0.4706705 1.337521 0.437491 1.328031 0.3767729 1.368684 0.4306091 0.2154684 -0.3522157 0.1724025 -0.3531774 0.2184197 -0.400864 0.913661 1.014023 0.9132171 1.048713 0.8843333 1.014536 0.2766269 1.360651 0.3071991 1.36076 0.2756267 1.406277 0.1817641 1.036485 0.217364 1.036412 0.1783317 1.069919 0.2703159 -0.4145299 0.2583107 -0.3542003 0.234786 -0.4044533 0.7357222 1.414478 0.7373907 1.358192 0.762377 1.403942 -0.3006207 0.464172 -0.2523282 0.4635205 -0.2766264 0.5183783 -0.2356137 1.123389 -0.2451356 1.150494 -0.2700172 1.124329 1.344453 0.4751487 1.340996 0.4379335 1.38229 0.4800631 0.3630568 0.5085978 0.3652765 0.5628952 0.3492906 0.517209 0.3378825 0.4531735 0.3616918 0.4559773 0.3515627 0.4968501 0.839628 0.5008405 0.809227 0.5061461 0.8303303 0.4715162 -0.3640009 -0.407433 -0.3617053 -0.3503695 -0.396229 -0.3964088 0.7132661 0.5437181 0.6858423 0.5484235 0.6954544 0.5058544 0.05736315 0.5527183 -0.2333217 0.5705984 0.04964488 0.5193265 1.133715 -0.2979122 1.145323 -0.3626976 1.383245 -0.338934 0.1042028 -0.1342491 0.1724025 -0.3531774 0.1465513 -0.1303212 1.309803 0.1871761 1.353667 0.1876761 1.328031 0.3767729 1.144705 1.046333 1.143731 1.010845 1.383679 1.044003 1.065071 0.429667 0.8246179 0.4697966 1.048863 0.375975 0.7328045 -0.0668413 0.7965644 -0.2826948 0.7727239 -0.0694698 0.7736073 -0.07032644 0.8388808 -0.2821729 0.8053228 -0.07326024 -0.09899801 -0.4141257 -0.1152601 -0.3549512 -0.3640009 -0.407433 0.8996943 0.7411515 0.8710384 0.7433254 0.839628 0.5008405 0.5002894 0.5156605 0.5120571 0.5653628 0.3630568 0.5085978 1.078147 0.4692564 1.070208 0.4354504 1.344453 0.4751487 0.5249489 0.4670838 0.7417859 0.4398387 0.5395537 0.5139247 1.405377 0.7518975 1.417166 1.009773 1.369777 0.749041 -0.346766 0.2601526 -0.2523282 0.4635205 -0.3778758 0.2573955 0.7825812 1.15825 0.7696277 1.357871 0.7523012 1.158789 -0.04139631 1.103139 -0.03262078 1.071869 0.1783317 1.069919 0.5278167 1.3599 0.5218298 1.415858 0.3071991 1.36076 0.2373852 0.2715275 0.2708449 0.276335 0.3378825 0.4531735 0.3588068 0.7632392 0.3355473 0.7633116 0.3652765 0.5628952 0.07899737 0.4579698 0.3082253 0.4524115 0.1047271 0.5128614 -0.2071204 0.8771493 -0.249036 0.8798831 -0.2333217 0.5705984 0.5213645 -0.3197541 0.2583107 -0.3542003 0.5304064 -0.3875589 0.2537391 0.7975072 0.217364 1.036412 0.2271146 0.7980079 1.018915 0.2006311 1.048863 0.375975 0.7625638 0.2665069 1.082862 -0.1130297 1.022662 0.05525022 0.8053228 -0.07326024 0.3405654 1.162791 0.3098707 1.16267 0.3559363 0.9634471 0.150947 -0.1351315 0.1925451 -0.130246 0.139953 0.08026468 0.7660426 0.7355929 0.7873814 0.9569609 0.7383804 0.7392652 -0.3862053 -0.1450047 -0.4042286 0.05921518 -0.4121535 -0.1466693 0.7625638 0.2665069 0.7345188 0.2744295 0.7569423 0.1064674 0.7329499 0.2755556 0.6980761 0.2849982 0.7257173 0.1138489 1.391763 -0.1550627 1.358323 0.01616013 1.351922 -0.1493442 0.2318354 0.2795746 0.1974475 0.2755106 0.1303796 0.08962094 0.1974475 0.2755106 -0.08017086 0.2674952 0.08819508 0.08480513 0.3588068 0.7632392 0.554579 0.7592127 0.3559363 0.9634471 0.1925451 -0.130246 0.4792816 -0.08303219 0.1816835 0.0859633 0.7383804 0.7392652 0.7585753 0.9581357 0.554579 0.7592127 1.082862 -0.1130297 1.351922 -0.1493442 1.022662 0.05525022 1.018915 0.2006311 1.309803 0.1871761 1.048863 0.375975 0.7373907 1.358192 0.7357222 1.414478 0.5278167 1.3599 -0.2451356 1.150494 -0.2356137 1.123389 -0.04139631 1.103139 0.8303303 0.4715162 1.070208 0.4354504 0.839628 0.5008405 0.6858423 0.5484235 0.5120571 0.5653628 0.6706228 0.5012096 1.337521 0.437491 1.065071 0.429667 1.328031 0.3767729 0.9132171 1.048713 0.913661 1.014023 1.144705 1.046333 0.8872715 -0.3501904 1.145323 -0.3626976 0.8719852 -0.2815397 0.2655781 0.5552102 0.05736315 0.5527183 0.2630647 0.5206422 1.124235 0.7353435 0.8996943 0.7411515 1.078147 0.4692564 0.02240085 0.8147085 -0.2071204 0.8771493 0.05736315 0.5527183 0.7523012 1.158789 0.7373907 1.358192 0.5553715 1.161482 0.7585753 0.9581357 0.7523012 1.158789 0.5675109 0.9620214 1.022662 0.05525022 1.315271 0.01971185 1.018915 0.2006311 1.383245 -0.338934 1.351922 -0.1493442 1.133715 -0.2979122 0.6858423 0.5484235 0.7383804 0.7392652 0.5120571 0.5653628 0.2583107 -0.3542003 0.5213645 -0.3197541 0.1925451 -0.130246 0.3652765 0.5628952 0.5120571 0.5653628 0.3588068 0.7632392 0.3082253 0.4524115 0.07899737 0.4579698 0.1974475 0.2755106 0.333508 0.4541562 0.3082253 0.4524115 0.2318354 0.2795746 1.418391 -0.3441779 1.391763 -0.1550627 1.383245 -0.338934 0.7770166 0.4315857 0.7417859 0.4398387 0.7329499 0.2755556 0.8070722 0.4232905 0.779179 0.4303248 0.7625638 0.2665069 -0.3617053 -0.3503695 -0.3862053 -0.1450047 -0.4007734 -0.3499315 0.7132661 0.5437181 0.7660426 0.7355929 0.6858423 0.5484235 0.2164457 -0.3560236 0.2583107 -0.3542003 0.150947 -0.1351315 0.3071991 1.36076 0.2766269 1.360651 0.3405654 1.162791 1.133715 -0.2979122 1.082862 -0.1130297 0.8719852 -0.2815397 1.022662 0.05525022 1.018915 0.2006311 0.7569423 0.1064674 0.3559363 0.9634471 0.3251953 0.9633036 0.3588068 0.7632392 0.139953 0.08026468 0.1816835 0.0859633 0.2373852 0.2715275 0.7873814 0.9569609 0.7825812 1.15825 0.7585753 0.9581357 -0.4042286 0.05921518 -0.346766 0.2601526 -0.4246501 0.05663013 0.7269799 0.1128844 0.7736073 -0.07032644 0.7569423 0.1064674 0.6885844 0.1209945 0.7328045 -0.0668413 0.7257173 0.1138489 1.315271 0.01971185 1.358323 0.01616013 1.309803 0.1871761 0.08819508 0.08480513 0.1042028 -0.1342491 0.1303796 0.08962094 -0.2186614 0.07257401 -0.1957928 -0.137696 0.08819508 0.08480513 0.3559363 0.9634471 0.5675109 0.9620214 0.3405654 1.162791 0.1816835 0.0859633 0.4548501 0.1273057 0.2708449 0.276335 0.4548501 0.1273057 0.6885844 0.1209945 0.4842762 0.304527 -0.4042286 0.05921518 -0.3862053 -0.1450047 -0.2186614 0.07257401 -0.2523282 0.4635205 -0.346766 0.2601526 0.07899737 0.4579698 0.5213645 -0.3197541 0.7965644 -0.2826948 0.4792816 -0.08303219 -0.03262078 1.071869 -0.2356137 1.123389 0.02240085 0.8147085 0.913661 1.014023 0.8996943 0.7411515 1.143731 1.010845 0.4792816 -0.08303219 0.7328045 -0.0668413 0.4548501 0.1273057 -0.08017086 0.2674952 -0.346766 0.2601526 -0.2186614 0.07257401 0.2655781 0.5552102 0.2867138 0.554942 0.2271146 0.7980079 0.8092992 -0.352505 0.7965644 -0.2826948 0.5304064 -0.3875589 -0.2356137 1.123389 -0.2700172 1.124329 -0.2071204 0.8771493 -0.2314767 0.5288634 -0.2523282 0.4635205 0.1047271 0.5128614 1.38229 0.4800631 1.405377 0.7518975 1.344453 0.4751487 0.3616918 0.4559773 0.5249489 0.4670838 0.3705763 0.5083967 0.8843333 1.014536 0.8710384 0.7433254 0.913661 1.014023 0.1724025 -0.3531774 -0.1152601 -0.3549512 0.1853479 -0.4115424 1.143731 1.010845 1.124235 0.7353435 1.383408 1.010082 0.1817641 1.036485 -0.03262078 1.071869 0.2271146 0.7980079 -0.3862053 -0.1450047 -0.3617053 -0.3503695 -0.1957928 -0.137696 0.4842762 0.304527 0.6980761 0.2849982 0.5249489 0.4670838 0.3082253 0.4524115 0.07899737 0.4579698 0.1974475 0.2755106 0.4893428 0.3404628 0.5181221 0.4711406 0.3833823 0.4600637 0.4952956 1.182288 0.3112109 1.182302 0.3255044 1.023993 0.03163373 -0.1819749 0.2551285 -0.1796132 0.2467517 -0.01834702 0.07554084 0.7884767 0.06315571 0.5561335 0.2939237 0.5437985 0.9970523 0.5046795 1.200365 0.5281493 1.200714 0.7160972 0.7647781 -0.182107 0.7649276 -0.227002 0.7907655 -0.2186524 0.2906728 0.5167351 0.3118407 0.5216338 0.3175848 0.5437718 0.8210261 -0.1823697 0.7964537 -0.182641 0.8020848 -0.2193115 1.196882 -0.1781699 1.199897 -0.2216888 1.221741 -0.2137832 -0.2263531 0.5348603 -0.222344 0.512241 -0.1997213 0.5076293 0.3329322 0.4606367 0.3573846 0.4600806 0.3673719 0.4949368 0.7626846 0.4238336 0.7761768 0.4646028 0.7523158 0.4605563 1.227075 0.9058789 1.222302 0.9279462 1.200456 0.9330881 0.702908 0.4377328 0.734748 0.43013 0.7385045 0.4638139 1.195567 0.4548141 1.224485 0.4565367 1.220528 0.4934761 0.2551285 -0.1796132 0.2549622 -0.224674 0.2807618 -0.2161823 0.8221417 0.9364985 0.8008298 0.93151 0.7964537 0.9094295 0.3112109 1.182302 0.3131233 1.227293 0.2926938 1.21899 0.2302113 0.9241315 0.2213777 0.9454697 0.1978071 0.9509524 0.3181754 -0.1822229 0.2866747 -0.1822246 0.2924342 -0.2190242 0.6793833 1.182276 0.7072644 1.182195 0.7033541 1.21896 -0.1937662 0.4619147 -0.1935878 0.5079024 -0.2214552 0.4986571 -0.1885608 0.9504676 -0.2112625 0.9445626 -0.2147073 0.9235724 1.200367 0.5009163 1.222544 0.5061162 1.227293 0.5279878 0.3892328 0.5493227 0.3739811 0.54939 0.3775387 0.5120949 0.3833823 0.4600637 0.3960902 0.5030787 0.3784518 0.4947685 0.7611779 0.49553 0.7600009 0.4739025 0.781265 0.466469 -0.1900748 -0.1754408 -0.2212251 -0.1749641 -0.2154443 -0.2119088 0.6496428 0.5318986 0.638094 0.4931313 0.6595056 0.4958519 -0.1976852 0.5363736 -0.1997213 0.5076293 0.05892199 0.5223895 1.012573 -0.223404 1.199897 -0.2216888 1.196882 -0.1781699 0.2551285 -0.1796132 0.2866747 -0.1793799 0.2793028 -0.01857727 1.221173 0.2963806 1.224485 0.4565367 1.195567 0.4548141 1.010576 0.9080554 1.20024 0.9061242 1.200456 0.9330881 0.7761768 0.4646028 0.7626846 0.4238336 0.9854299 0.4206815 0.7647781 -0.182107 0.7964537 -0.1819739 0.7922745 0.007212877 0.7964537 -0.182641 0.8210261 -0.1823697 0.8186711 0.003692865 0.03163373 -0.1819749 -0.1900748 -0.1754408 -0.1899523 -0.2206721 0.7991632 0.7092134 0.7611779 0.49553 0.7884528 0.4928009 0.5077938 0.5493341 0.3892328 0.5493227 0.3895644 0.5037512 0.9951516 0.4747492 1.200367 0.5009163 1.200365 0.5281493 0.702908 0.4377328 0.7150862 0.4764756 0.5299369 0.5078433 1.227075 0.9058789 1.20024 0.9061242 1.200714 0.7160972 -0.1937662 0.4619147 -0.2270753 0.46188 -0.226701 0.3024128 0.7072644 1.182195 0.6793833 1.182276 0.6793779 1.022857 0.02304387 0.9425402 0.2033467 0.9259917 0.1978071 0.9509524 0.4972164 1.227283 0.3131233 1.227293 0.3112109 1.182302 0.2866021 0.3125461 0.3833823 0.4600637 0.3613414 0.4589657 0.3452653 0.7085897 0.3739811 0.54939 0.3892328 0.5493227 0.3329322 0.4606367 0.3512574 0.5035793 0.1119757 0.5133793 -0.2040811 0.7418922 -0.2263531 0.5348603 -0.1976852 0.5363736 0.3181754 -0.1822229 0.318148 -0.2272933 0.5410352 -0.2272869 0.2302113 0.9241315 0.2033467 0.9259917 0.2455365 0.7446505 0.9854299 0.4206815 0.7626846 0.4238336 0.7453555 0.2980011 0.9962999 0.1377817 0.7792775 0.1718366 0.8186711 0.003692865 0.3003844 1.023842 0.3251237 0.8663308 0.3512483 0.8665733 0.3155152 -0.02252531 0.2691581 0.1466634 0.2346394 0.1447464 0.7103991 0.8636232 0.6854465 0.8639411 0.683406 0.6986207 -0.1956233 0.1430746 -0.2272017 0.1430383 -0.2238256 -0.0158559 0.7169333 0.3038266 0.7502433 0.1752957 0.7792775 0.1718366 0.6801496 0.3129003 0.7120623 0.1800683 0.7488622 0.1760158 1.214694 0.1323423 1.186825 0.1324126 1.189752 -0.02631425 0.2143639 0.3148645 0.186996 0.1533946 0.2225087 0.153558 0.01301312 0.3082816 0.01198798 0.1471575 0.186996 0.1533946 0.5340434 0.7080219 0.5282642 0.8661195 0.3512483 0.8665733 0.529246 -0.008432924 0.4987084 0.180584 0.2691581 0.1466634 0.6854465 0.8639411 0.5282642 0.8661195 0.5340434 0.7080219 1.189752 -0.02631425 1.186825 0.1324126 0.9962999 0.1377817 1.192306 0.2948417 1.195567 0.4548141 0.9854299 0.4206815 0.6812943 1.227267 0.4972164 1.227283 0.4952956 1.182288 -0.1850888 0.9261206 0.02304387 0.9425402 0.01360088 0.9625675 0.9951516 0.4747492 0.9970523 0.5046795 0.7884528 0.4928009 0.5077938 0.5493341 0.4984256 0.5094422 0.638094 0.4931313 0.9917928 0.4688562 0.9854299 0.4206815 1.195567 0.4548141 0.8225976 0.9094441 1.010576 0.9080554 1.01061 0.9351206 1.012573 -0.223404 1.009974 -0.1793742 0.8210261 -0.1823697 0.06315571 0.5561335 0.05892199 0.5223895 0.2906728 0.5167351 0.8250266 0.7094901 0.7884528 0.4928009 0.9970523 0.5046795 -0.1682044 0.7487481 -0.1976852 0.5363736 0.06315571 0.5561335 0.6793833 1.182276 0.4952956 1.182288 0.5060065 1.023703 0.6793779 1.022857 0.5060065 1.023703 0.5282642 0.8661195 1.186825 0.1324126 1.192306 0.2948417 0.9834827 0.2687105 1.189752 -0.02631425 1.008954 -0.03062736 1.009974 -0.1793742 0.683406 0.6986207 0.5340434 0.7080219 0.5077938 0.5493341 0.5410666 -0.182217 0.529246 -0.008432924 0.3155152 -0.02252531 0.5077938 0.5493341 0.5340434 0.7080219 0.3670235 0.7087154 0.09198504 0.4640964 0.01301312 0.3082816 0.2143639 0.3148645 0.3329322 0.4606367 0.2143639 0.3148645 0.2479267 0.3166556 1.217773 -0.02641832 1.189752 -0.02631425 1.196882 -0.1781699 0.702908 0.4377328 0.6801496 0.3129003 0.7151944 0.3047168 0.7367733 0.4290757 0.7169333 0.3038266 0.7453555 0.2980011 -0.1926012 -0.01587557 -0.2238256 -0.0158559 -0.2212251 -0.1749641 0.7068443 0.6967341 0.683406 0.6986207 0.6496428 0.5318986 0.3181754 -0.1822229 0.3155152 -0.02252531 0.2824933 -0.0223512 0.2870541 1.182226 0.3003844 1.023842 0.3255044 1.023993 1.008954 -0.03062736 0.8186711 0.003692865 0.8210261 -0.1823697 0.9834827 0.2687105 0.7453555 0.2980011 0.7792775 0.1718366 0.3251237 0.8663308 0.3452653 0.7085897 0.3670235 0.7087154 0.2691581 0.1466634 0.2866021 0.3125461 0.2544096 0.3094249 0.7073376 1.022728 0.6793779 1.022857 0.6854465 0.8639411 -0.1927012 0.3022815 -0.226701 0.3024128 -0.2272017 0.1430383 0.7928526 0.006327748 0.8186711 0.003692865 0.7792775 0.1718366 0.7582128 0.009117662 0.7922745 0.007212877 0.7488622 0.1760158 1.214694 0.1323423 1.221173 0.2963806 1.192306 0.2948417 0.2467517 -0.01834702 0.2793028 -0.01857727 0.2225087 0.153558 0.02916336 -0.01796954 0.2467517 -0.01834702 0.186996 0.1533946 0.5282642 0.8661195 0.5060065 1.023703 0.3255044 1.023993 0.4987084 0.180584 0.4893428 0.3404628 0.2866021 0.3125461 0.7120623 0.1800683 0.6801496 0.3129003 0.4893428 0.3404628 -0.1926012 -0.01587557 0.02916336 -0.01796954 0.01198798 0.1471575 -0.1927012 0.3022815 0.01301312 0.3082816 0.09198504 0.4640964 0.7647781 -0.182107 0.7582128 0.009117662 0.529246 -0.008432924 -0.1850888 0.9261206 -0.1682044 0.7487481 0.07554084 0.7884767 0.8250266 0.7094901 1.012681 0.7162615 1.010576 0.9080554 0.7582128 0.009117662 0.7120623 0.1800683 0.4987084 0.180584 -0.1927012 0.3022815 -0.1956233 0.1430746 0.01198798 0.1471575 0.3175848 0.5437718 0.2669795 0.7392823 0.2455365 0.7446505 0.7647781 -0.182107 0.5410666 -0.182217 0.5410352 -0.2272869 -0.2147073 0.9235724 -0.2040811 0.7418922 -0.1682044 0.7487481 -0.1937662 0.4619147 0.09198504 0.4640964 0.1119757 0.5133793 1.227293 0.7157115 1.200714 0.7160972 1.200365 0.5281493 0.5181221 0.4711406 0.5299369 0.5078433 0.3960902 0.5030787 0.7991632 0.7092134 0.8250266 0.7094901 0.8225976 0.9094441 0.03163373 -0.1819749 0.0317229 -0.2272933 0.2549622 -0.224674 1.012681 0.7162615 1.200714 0.7160972 1.20024 0.9061242 0.02304387 0.9425402 0.07554084 0.7884767 0.2455365 0.7446505 -0.1900748 -0.1754408 0.03163373 -0.1819749 0.02916336 -0.01796954 0.6801496 0.3129003 0.702908 0.4377328 0.5181221 0.4711406 0.2774499 -0.6290105 0.2893673 -0.6290102 0.2893651 -0.3791686 0.2778262 -0.8823288 0.2897426 -0.8823286 0.2893673 -0.6290102 0.2774473 -0.3357118 0.2893647 -0.3357118 0.2893642 -0.2738155 0.2781614 1.297215 0.2900785 1.297215 0.2892128 1.508508 0.2774438 0.6368773 0.2893612 0.6368774 0.2893527 0.680626 0.2778826 -0.9261406 0.289799 -0.9261401 0.2897426 -0.8823286 0.2774993 1.933995 0.2774299 1.72131 0.2893474 1.72131 0.2782526 1.235445 0.2901697 1.235445 0.2900785 1.297215 0.2774299 1.72131 0.2772954 1.508508 0.2892128 1.508508 0.2774589 0.3624398 0.2893763 0.3624398 0.289366 0.5749751 0.2774354 0.6806259 0.2893527 0.680626 0.2893021 0.9352724 0.2779099 -0.9878568 0.277522 -1.19991 0.2894383 -1.19991 0.2774468 -0.2738155 0.2893642 -0.2738155 0.2893624 -0.06185328 0.2774476 -0.3791686 0.2893651 -0.3791686 0.2893647 -0.3357118 0.2774486 0.5749751 0.289366 0.5749751 0.2893612 0.6368774 0.2774589 0.3624398 0.2774493 0.1499125 0.2893667 0.1499125 0.2779099 -0.9878568 0.2898263 -0.9878568 0.289799 -0.9261401 0.2774493 0.1499125 0.277445 -0.06185328 0.2893624 -0.06185328 0.2782526 1.235445 0.2782234 1.191588 0.2901405 1.191588 0.2782234 1.191588 0.2773851 0.9352724 0.2893021 0.9352724 0.2904033 -0.6290097 0.3023207 -0.6290097 0.3023185 -0.3791679 0.2907795 -0.8823283 0.3026958 -0.882328 0.3023207 -0.6290097 0.2904008 -0.3357117 0.3023182 -0.3357117 0.3023177 -0.2738151 0.2911158 1.297215 0.3030329 1.297215 0.3021675 1.508508 0.2903978 0.6368774 0.3023152 0.6368778 0.3023068 0.6806262 0.2908358 -0.9261398 0.3027523 -0.9261398 0.3026958 -0.882328 0.2904542 1.933995 0.2903847 1.72131 0.3023021 1.72131 0.2912071 1.235445 0.3031241 1.235445 0.3030329 1.297215 0.2903847 1.72131 0.29025 1.508508 0.3021675 1.508508 0.2904127 0.3624401 0.3023302 0.3624401 0.30232 0.5749755 0.2903894 0.680626 0.3023068 0.6806262 0.3022563 0.9352724 0.2908631 -0.9878563 0.290475 -1.19991 0.3023915 -1.19991 0.2904003 -0.2738151 0.3023177 -0.2738151 0.3023161 -0.06185293 0.2904012 -0.3791681 0.3023185 -0.3791679 0.3023182 -0.3357117 0.2904025 0.5749753 0.30232 0.5749755 0.3023152 0.6368778 0.2904127 0.3624401 0.2904031 0.1499127 0.3023205 0.149913 0.2908631 -0.9878563 0.3027796 -0.9878563 0.3027523 -0.9261398 0.2904031 0.1499127 0.2903987 -0.06185317 0.3023161 -0.06185293 0.2912071 1.235445 0.2911778 1.191588 0.3030948 1.191589 0.2911778 1.191588 0.2903393 0.9352724 0.3022563 0.9352724 0.2866021 0.3125461 0.4893428 0.3404628 0.3833823 0.4600637 0.5060065 1.023703 0.4952956 1.182288 0.3255044 1.023993 0.02916336 -0.01796954 0.03163373 -0.1819749 0.2467517 -0.01834702 0.2455365 0.7446505 0.07554084 0.7884767 0.2939237 0.5437985 1.012681 0.7162615 0.9970523 0.5046795 1.200714 0.7160972 0.7964537 -0.1819739 0.7647781 -0.182107 0.7907655 -0.2186524 0.2939237 0.5437985 0.2906728 0.5167351 0.3175848 0.5437718 0.8228856 -0.2272933 0.8210261 -0.1823697 0.8020848 -0.2193115 1.224692 -0.1783152 1.196882 -0.1781699 1.221741 -0.2137832 -0.1976852 0.5363736 -0.2263531 0.5348603 -0.1997213 0.5076293 0.3512574 0.5035793 0.3329322 0.4606367 0.3673719 0.4949368 0.7367733 0.4290757 0.7626846 0.4238336 0.7523158 0.4605563 1.20024 0.9061242 1.227075 0.9058789 1.200456 0.9330881 0.7150862 0.4764756 0.702908 0.4377328 0.7385045 0.4638139 1.197906 0.5007688 1.195567 0.4548141 1.220528 0.4934761 0.2866747 -0.1793799 0.2551285 -0.1796132 0.2807618 -0.2161823 0.8225976 0.9094441 0.8221417 0.9364985 0.7964537 0.9094295 0.2870541 1.182226 0.3112109 1.182302 0.2926938 1.21899 0.2033467 0.9259917 0.2302113 0.9241315 0.1978071 0.9509524 0.318148 -0.2272933 0.3181754 -0.1822229 0.2924342 -0.2190242 0.6812943 1.227267 0.6793833 1.182276 0.7033541 1.21896 -0.2270753 0.46188 -0.1937662 0.4619147 -0.2214552 0.4986571 -0.1850888 0.9261206 -0.1885608 0.9504676 -0.2147073 0.9235724 1.200365 0.5281493 1.200367 0.5009163 1.227293 0.5279878 0.3895644 0.5037512 0.3892328 0.5493227 0.3775387 0.5120949 0.3613414 0.4589657 0.3833823 0.4600637 0.3784518 0.4947685 0.7884528 0.4928009 0.7611779 0.49553 0.781265 0.466469 -0.1899523 -0.2206721 -0.1900748 -0.1754408 -0.2154443 -0.2119088 0.6734368 0.5273286 0.6496428 0.5318986 0.6595056 0.4958519 0.06315571 0.5561335 -0.1976852 0.5363736 0.05892199 0.5223895 1.009974 -0.1793742 1.012573 -0.223404 1.196882 -0.1781699 0.2467517 -0.01834702 0.2551285 -0.1796132 0.2793028 -0.01857727 1.192306 0.2948417 1.221173 0.2963806 1.195567 0.4548141 1.01061 0.9351206 1.010576 0.9080554 1.200456 0.9330881 0.9917928 0.4688562 0.7761768 0.4646028 0.9854299 0.4206815 0.7582128 0.009117662 0.7647781 -0.182107 0.7922745 0.007212877 0.7928526 0.006327748 0.7964537 -0.182641 0.8186711 0.003692865 0.0317229 -0.2272933 0.03163373 -0.1819749 -0.1899523 -0.2206721 0.8250266 0.7094901 0.7991632 0.7092134 0.7884528 0.4928009 0.4984256 0.5094422 0.5077938 0.5493341 0.3895644 0.5037512 0.9970523 0.5046795 0.9951516 0.4747492 1.200365 0.5281493 0.5181221 0.4711406 0.702908 0.4377328 0.5299369 0.5078433 1.227293 0.7157115 1.227075 0.9058789 1.200714 0.7160972 -0.1927012 0.3022815 -0.1937662 0.4619147 -0.226701 0.3024128 0.7073376 1.022728 0.7072644 1.182195 0.6793779 1.022857 0.01360088 0.9625675 0.02304387 0.9425402 0.1978071 0.9509524 0.4952956 1.182288 0.4972164 1.227283 0.3112109 1.182302 0.2544096 0.3094249 0.2866021 0.3125461 0.3613414 0.4589657 0.3670235 0.7087154 0.3452653 0.7085897 0.3892328 0.5493227 0.09198504 0.4640964 0.3329322 0.4606367 0.1119757 0.5133793 -0.1682044 0.7487481 -0.2040811 0.7418922 -0.1976852 0.5363736 0.5410666 -0.182217 0.3181754 -0.1822229 0.5410352 -0.2272869 0.2669795 0.7392823 0.2302113 0.9241315 0.2455365 0.7446505 0.9834827 0.2687105 0.9854299 0.4206815 0.7453555 0.2980011 1.008954 -0.03062736 0.9962999 0.1377817 0.8186711 0.003692865 0.3255044 1.023993 0.3003844 1.023842 0.3512483 0.8665733 0.2824933 -0.0223512 0.3155152 -0.02252531 0.2346394 0.1447464 0.7068443 0.6967341 0.7103991 0.8636232 0.683406 0.6986207 -0.1926012 -0.01587557 -0.1956233 0.1430746 -0.2238256 -0.0158559 0.7453555 0.2980011 0.7169333 0.3038266 0.7792775 0.1718366 0.7151944 0.3047168 0.6801496 0.3129003 0.7488622 0.1760158 1.217773 -0.02641832 1.214694 0.1323423 1.189752 -0.02631425 0.2479267 0.3166556 0.2143639 0.3148645 0.2225087 0.153558 0.2143639 0.3148645 0.01301312 0.3082816 0.186996 0.1533946 0.3670235 0.7087154 0.5340434 0.7080219 0.3512483 0.8665733 0.3155152 -0.02252531 0.529246 -0.008432924 0.2691581 0.1466634 0.683406 0.6986207 0.6854465 0.8639411 0.5340434 0.7080219 1.008954 -0.03062736 1.189752 -0.02631425 0.9962999 0.1377817 0.9834827 0.2687105 1.192306 0.2948417 0.9854299 0.4206815 0.6793833 1.182276 0.6812943 1.227267 0.4952956 1.182288 -0.1885608 0.9504676 -0.1850888 0.9261206 0.01360088 0.9625675 0.781265 0.466469 0.9951516 0.4747492 0.7884528 0.4928009 0.6496428 0.5318986 0.5077938 0.5493341 0.638094 0.4931313 1.197906 0.5007688 0.9917928 0.4688562 1.195567 0.4548141 0.8221417 0.9364985 0.8225976 0.9094441 1.01061 0.9351206 0.8228856 -0.2272933 1.012573 -0.223404 0.8210261 -0.1823697 0.2939237 0.5437985 0.06315571 0.5561335 0.2906728 0.5167351 1.012681 0.7162615 0.8250266 0.7094901 0.9970523 0.5046795 0.07554084 0.7884767 -0.1682044 0.7487481 0.06315571 0.5561335 0.6793779 1.022857 0.6793833 1.182276 0.5060065 1.023703 0.6854465 0.8639411 0.6793779 1.022857 0.5282642 0.8661195 0.9962999 0.1377817 1.186825 0.1324126 0.9834827 0.2687105 1.196882 -0.1781699 1.189752 -0.02631425 1.009974 -0.1793742 0.6496428 0.5318986 0.683406 0.6986207 0.5077938 0.5493341 0.3181754 -0.1822229 0.5410666 -0.182217 0.3155152 -0.02252531 0.3892328 0.5493227 0.5077938 0.5493341 0.3670235 0.7087154 0.3329322 0.4606367 0.09198504 0.4640964 0.2143639 0.3148645 0.3573846 0.4600806 0.3329322 0.4606367 0.2479267 0.3166556 1.224692 -0.1783152 1.217773 -0.02641832 1.196882 -0.1781699 0.734748 0.43013 0.702908 0.4377328 0.7151944 0.3047168 0.7626846 0.4238336 0.7367733 0.4290757 0.7453555 0.2980011 -0.1900748 -0.1754408 -0.1926012 -0.01587557 -0.2212251 -0.1749641 0.6734368 0.5273286 0.7068443 0.6967341 0.6496428 0.5318986 0.2866747 -0.1822246 0.3181754 -0.1822229 0.2824933 -0.0223512 0.3112109 1.182302 0.2870541 1.182226 0.3255044 1.023993 1.009974 -0.1793742 1.008954 -0.03062736 0.8210261 -0.1823697 0.9962999 0.1377817 0.9834827 0.2687105 0.7792775 0.1718366 0.3512483 0.8665733 0.3251237 0.8663308 0.3670235 0.7087154 0.2346394 0.1447464 0.2691581 0.1466634 0.2544096 0.3094249 0.7103991 0.8636232 0.7073376 1.022728 0.6854465 0.8639411 -0.1956233 0.1430746 -0.1927012 0.3022815 -0.2272017 0.1430383 0.7502433 0.1752957 0.7928526 0.006327748 0.7792775 0.1718366 0.7120623 0.1800683 0.7582128 0.009117662 0.7488622 0.1760158 1.186825 0.1324126 1.214694 0.1323423 1.192306 0.2948417 0.186996 0.1533946 0.2467517 -0.01834702 0.2225087 0.153558 0.01198798 0.1471575 0.02916336 -0.01796954 0.186996 0.1533946 0.3512483 0.8665733 0.5282642 0.8661195 0.3255044 1.023993 0.2691581 0.1466634 0.4987084 0.180584 0.2866021 0.3125461 0.4987084 0.180584 0.7120623 0.1800683 0.4893428 0.3404628 -0.1956233 0.1430746 -0.1926012 -0.01587557 0.01198798 0.1471575 -0.1937662 0.4619147 -0.1927012 0.3022815 0.09198504 0.4640964 0.5410666 -0.182217 0.7647781 -0.182107 0.529246 -0.008432924 0.02304387 0.9425402 -0.1850888 0.9261206 0.07554084 0.7884767 0.8225976 0.9094441 0.8250266 0.7094901 1.010576 0.9080554 0.529246 -0.008432924 0.7582128 0.009117662 0.4987084 0.180584 0.01301312 0.3082816 -0.1927012 0.3022815 0.01198798 0.1471575 0.2939237 0.5437985 0.3175848 0.5437718 0.2455365 0.7446505 0.7649276 -0.227002 0.7647781 -0.182107 0.5410352 -0.2272869 -0.1850888 0.9261206 -0.2147073 0.9235724 -0.1682044 0.7487481 -0.1935878 0.5079024 -0.1937662 0.4619147 0.1119757 0.5133793 1.227293 0.5279878 1.227293 0.7157115 1.200365 0.5281493 0.3833823 0.4600637 0.5181221 0.4711406 0.3960902 0.5030787 0.7964537 0.9094295 0.7991632 0.7092134 0.8225976 0.9094441 0.2551285 -0.1796132 0.03163373 -0.1819749 0.2549622 -0.224674 1.010576 0.9080554 1.012681 0.7162615 1.20024 0.9061242 0.2033467 0.9259917 0.02304387 0.9425402 0.2455365 0.7446505 -0.1926012 -0.01587557 -0.1900748 -0.1754408 0.02916336 -0.01796954 0.4893428 0.3404628 0.6801496 0.3129003 0.5181221 0.4711406 0.2774476 -0.3791686 0.2774499 -0.6290105 0.2893651 -0.3791686 0.2774499 -0.6290105 0.2778262 -0.8823288 0.2893673 -0.6290102 0.2774468 -0.2738155 0.2774473 -0.3357118 0.2893642 -0.2738155 0.2772954 1.508508 0.2781614 1.297215 0.2892128 1.508508 0.2774354 0.6806259 0.2774438 0.6368773 0.2893527 0.680626 0.2778262 -0.8823288 0.2778826 -0.9261406 0.2897426 -0.8823286 0.2894167 1.933995 0.2774993 1.933995 0.2893474 1.72131 0.2781614 1.297215 0.2782526 1.235445 0.2900785 1.297215 0.2893474 1.72131 0.2774299 1.72131 0.2892128 1.508508 0.2774486 0.5749751 0.2774589 0.3624398 0.289366 0.5749751 0.2773851 0.9352724 0.2774354 0.6806259 0.2893021 0.9352724 0.2898263 -0.9878568 0.2779099 -0.9878568 0.2894383 -1.19991 0.277445 -0.06185328 0.2774468 -0.2738155 0.2893624 -0.06185328 0.2774473 -0.3357118 0.2774476 -0.3791686 0.2893647 -0.3357118 0.2774438 0.6368773 0.2774486 0.5749751 0.2893612 0.6368774 0.2893763 0.3624398 0.2774589 0.3624398 0.2893667 0.1499125 0.2778826 -0.9261406 0.2779099 -0.9878568 0.289799 -0.9261401 0.2893667 0.1499125 0.2774493 0.1499125 0.2893624 -0.06185328 0.2901697 1.235445 0.2782526 1.235445 0.2901405 1.191588 0.2901405 1.191588 0.2782234 1.191588 0.2893021 0.9352724 0.2904012 -0.3791681 0.2904033 -0.6290097 0.3023185 -0.3791679 0.2904033 -0.6290097 0.2907795 -0.8823283 0.3023207 -0.6290097 0.2904003 -0.2738151 0.2904008 -0.3357117 0.3023177 -0.2738151 0.29025 1.508508 0.2911158 1.297215 0.3021675 1.508508 0.2903894 0.680626 0.2903978 0.6368774 0.3023068 0.6806262 0.2907795 -0.8823283 0.2908358 -0.9261398 0.3026958 -0.882328 0.3023717 1.933995 0.2904542 1.933995 0.3023021 1.72131 0.2911158 1.297215 0.2912071 1.235445 0.3030329 1.297215 0.3023021 1.72131 0.2903847 1.72131 0.3021675 1.508508 0.2904025 0.5749753 0.2904127 0.3624401 0.30232 0.5749755 0.2903393 0.9352724 0.2903894 0.680626 0.3022563 0.9352724 0.3027796 -0.9878563 0.2908631 -0.9878563 0.3023915 -1.19991 0.2903987 -0.06185317 0.2904003 -0.2738151 0.3023161 -0.06185293 0.2904008 -0.3357117 0.2904012 -0.3791681 0.3023182 -0.3357117 0.2903978 0.6368774 0.2904025 0.5749753 0.3023152 0.6368778 0.3023302 0.3624401 0.2904127 0.3624401 0.3023205 0.149913 0.2908358 -0.9261398 0.2908631 -0.9878563 0.3027523 -0.9261398 0.3023205 0.149913 0.2904031 0.1499127 0.3023161 -0.06185293 0.3031241 1.235445 0.2912071 1.235445 0.3030948 1.191589 0.3030948 1.191589 0.2911778 1.191588 0.3022563 0.9352724</float_array>
          <technique_common>
            <accessor source="#Cube_003-mesh-map-0-array" count="1782" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_003-mesh-vertices">
          <input semantic="POSITION" source="#Cube_003-mesh-positions"/>
        </vertices>
        <triangles material="haybale2-material" count="258">
          <input semantic="VERTEX" source="#Cube_003-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_003-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_003-mesh-map-0" offset="2" set="0"/>
          <p>129 0 0 113 1 1 44 2 2 79 3 3 28 4 4 93 5 5 110 6 6 22 7 7 82 8 8 125 9 9 68 10 10 1 11 11 76 12 12 42 13 13 115 14 14 0 15 15 3 16 16 4 17 17 5 18 18 4 17 19 3 16 20 2 19 21 6 20 22 4 17 23 7 21 24 10 22 25 11 23 26 12 24 27 11 23 28 10 22 29 9 25 30 13 26 31 11 23 32 14 27 33 17 28 34 18 29 35 19 30 36 18 29 37 17 28 38 16 31 39 20 32 40 18 29 41 21 33 42 24 34 43 25 35 44 22 7 45 26 36 46 25 35 47 27 37 48 25 35 49 26 36 50 28 4 51 31 38 52 32 39 53 33 40 54 32 39 55 31 38 56 30 41 57 34 42 58 32 39 59 35 43 60 38 44 61 39 45 62 36 46 63 40 47 64 39 45 65 41 48 66 39 45 67 40 47 68 45 49 69 46 50 70 48 51 71 43 52 72 47 53 73 46 50 74 44 2 75 48 51 76 46 50 77 52 54 78 53 55 79 55 56 80 50 57 81 54 58 82 53 55 83 51 59 84 55 56 85 53 55 86 8 60 87 10 22 88 69 61 89 69 61 90 10 22 91 7 21 92 22 7 93 24 34 94 95 62 95 95 62 96 24 34 97 21 33 98 71 63 99 15 64 100 17 28 101 17 28 102 14 27 103 73 65 104 0 15 105 6 20 106 98 66 107 6 20 108 2 19 109 99 67 110 110 6 111 50 57 112 52 54 113 111 68 114 52 54 115 49 69 116 74 70 117 43 52 118 45 49 119 75 71 120 45 49 121 42 13 122 16 31 123 19 30 124 114 72 125 19 30 126 15 64 127 115 14 128 36 46 129 38 44 130 101 73 131 38 44 132 35 43 133 102 74 134 77 75 135 29 76 136 31 38 137 78 77 138 31 38 139 28 4 140 91 78 141 44 2 142 47 53 143 104 79 144 47 53 145 43 52 146 9 25 147 12 24 148 117 80 149 117 80 150 12 24 151 8 60 152 30 41 153 33 40 154 120 81 155 33 40 156 29 76 157 121 82 158 73 65 159 14 27 160 87 83 161 80 84 162 61 85 163 99 67 164 92 86 165 66 87 166 67 88 167 103 89 168 65 90 169 66 87 170 63 91 171 64 92 172 90 93 173 62 94 174 63 91 175 89 95 176 86 96 177 60 97 178 61 85 179 97 98 180 59 99 181 60 97 182 57 100 183 58 101 184 84 102 185 94 103 186 56 104 187 57 100 188 127 105 189 122 106 190 56 104 191 106 107 192 81 108 193 67 88 194 126 109 195 123 110 196 65 90 197 64 92 198 81 108 199 106 107 200 84 102 201 58 101 202 80 84 203 96 111 204 21 33 205 73 65 206 41 48 207 78 77 208 79 3 209 37 112 210 77 75 211 78 77 212 75 71 213 76 12 214 49 69 215 74 70 216 75 71 217 55 56 218 72 113 219 73 65 220 21 33 221 23 114 222 71 63 223 72 113 224 69 61 225 70 115 226 2 19 227 68 10 228 69 61 229 5 18 230 112 116 231 49 69 232 76 12 233 118 117 234 8 60 235 68 10 236 35 43 237 79 3 238 109 118 239 102 74 240 109 118 241 81 108 242 58 101 243 96 111 244 108 119 245 84 102 246 107 120 247 70 115 248 90 93 249 106 107 250 74 70 251 119 121 252 126 109 253 103 89 254 74 70 255 106 107 256 105 122 257 116 123 258 127 105 259 94 103 260 9 25 261 94 103 262 83 124 263 83 124 264 84 102 265 7 21 266 16 31 267 97 98 268 86 96 269 20 32 270 86 96 271 87 83 272 100 125 273 89 95 274 54 58 275 89 95 276 90 93 277 51 59 278 30 41 279 103 89 280 92 86 281 34 42 282 92 86 283 93 5 284 107 120 285 99 67 286 2 19 287 108 119 288 87 83 289 61 85 290 66 87 291 104 79 292 105 122 293 65 90 294 91 78 295 104 79 296 101 73 297 102 74 298 64 92 299 88 126 300 101 73 301 63 91 302 98 66 303 99 67 304 61 85 305 85 127 306 98 66 307 60 97 308 57 100 309 95 62 310 96 111 311 82 8 312 95 62 313 57 100 314 128 128 315 82 8 316 56 104 317 81 108 318 109 118 319 93 5 320 123 110 321 129 0 322 91 78 323 59 99 324 97 98 325 129 0 326 100 125 327 128 128 328 122 106 329 88 126 330 127 105 331 116 123 332 0 15 333 85 127 334 126 109 335 37 112 336 118 117 337 125 9 338 112 116 339 124 129 340 71 63 341 85 127 342 59 99 343 123 110 344 88 126 345 62 94 346 122 106 347 3 16 348 120 81 349 121 82 350 0 15 351 119 121 352 120 81 353 40 47 354 117 80 355 118 117 356 36 46 357 116 123 358 117 80 359 114 72 360 115 14 361 42 13 362 113 1 363 114 72 364 48 51 365 111 68 366 112 116 367 23 114 368 110 6 369 111 68 370 26 36 371 124 129 372 115 14 373 15 64 374 77 75 375 125 9 376 121 82 377 50 57 378 110 6 379 128 128 380 97 98 381 16 31 382 113 1 383 132 130 384 133 130 385 131 130 386 91 78 387 129 0 388 44 2 389 109 118 390 79 3 391 93 5 392 128 128 393 110 6 394 82 8 395 121 82 396 125 9 397 1 11 398 124 129 399 76 12 400 115 14 401 6 20 402 0 15 403 4 17 404 1 11 405 5 18 406 3 16 407 5 18 408 2 19 409 4 17 410 13 26 411 7 21 412 11 23 413 8 60 414 12 24 415 10 22 416 12 24 417 9 25 418 11 23 419 20 32 420 14 27 421 18 29 422 15 64 423 19 30 424 17 28 425 19 30 426 16 31 427 18 29 428 27 37 429 21 33 430 25 35 431 24 34 432 22 7 433 25 35 434 23 114 435 27 37 436 26 36 437 34 42 438 28 4 439 32 39 440 29 76 441 33 40 442 31 38 443 33 40 444 30 41 445 32 39 446 41 48 447 35 43 448 39 45 449 38 44 450 36 46 451 39 45 452 37 112 453 41 48 454 40 47 455 42 13 456 45 49 457 48 51 458 45 49 459 43 52 460 46 50 461 47 53 462 44 2 463 46 50 464 49 69 465 52 54 466 55 56 467 52 54 468 50 57 469 53 55 470 54 58 471 51 59 472 53 55 473 68 10 474 8 60 475 69 61 476 70 115 477 69 61 478 7 21 479 82 8 480 22 7 481 95 62 482 96 111 483 95 62 484 21 33 485 72 113 486 71 63 487 17 28 488 72 113 489 17 28 490 73 65 491 85 127 492 0 15 493 98 66 494 98 66 495 6 20 496 99 67 497 111 68 498 110 6 499 52 54 500 112 116 501 111 68 502 49 69 503 75 71 504 74 70 505 45 49 506 76 12 507 75 71 508 42 13 509 113 1 510 16 31 511 114 72 512 114 72 513 19 30 514 115 14 515 88 126 516 36 46 517 101 73 518 101 73 519 38 44 520 102 74 521 78 77 522 77 75 523 31 38 524 79 3 525 78 77 526 28 4 527 104 79 528 91 78 529 47 53 530 105 122 531 104 79 532 43 52 533 116 123 534 9 25 535 117 80 536 118 117 537 117 80 538 8 60 539 119 121 540 30 41 541 120 81 542 120 81 543 33 40 544 121 82 545 108 119 546 73 65 547 87 83 548 107 120 549 80 84 550 99 67 551 93 5 552 92 86 553 67 88 554 92 86 555 103 89 556 66 87 557 89 95 558 63 91 559 90 93 560 100 125 561 62 94 562 89 95 563 87 83 564 86 96 565 61 85 566 86 96 567 97 98 568 60 97 569 83 124 570 57 100 571 84 102 572 83 124 573 94 103 574 57 100 575 94 103 576 127 105 577 56 104 578 105 122 579 106 107 580 67 88 581 103 89 582 126 109 583 65 90 584 90 93 585 64 92 586 106 107 587 107 120 588 84 102 589 80 84 590 108 119 591 96 111 592 73 65 593 35 43 594 41 48 595 79 3 596 41 48 597 37 112 598 78 77 599 55 56 600 75 71 601 49 69 602 51 59 603 74 70 604 55 56 605 27 37 606 72 113 607 21 33 608 27 37 609 23 114 610 72 113 611 5 18 612 69 61 613 2 19 614 1 11 615 68 10 616 5 18 617 124 129 618 112 116 619 76 12 620 125 9 621 118 117 622 68 10 623 102 74 624 35 43 625 109 118 626 64 92 627 102 74 628 81 108 629 80 84 630 58 101 631 108 119 632 7 21 633 84 102 634 70 115 635 51 59 636 90 93 637 74 70 638 30 41 639 119 121 640 103 89 641 43 52 642 74 70 643 105 122 644 9 25 645 116 123 646 94 103 647 13 26 648 9 25 649 83 124 650 13 26 651 83 124 652 7 21 653 20 32 654 16 31 655 86 96 656 14 27 657 20 32 658 87 83 659 50 57 660 100 125 661 54 58 662 54 58 663 89 95 664 51 59 665 34 42 666 30 41 667 92 86 668 28 4 669 34 42 670 93 5 671 70 115 672 107 120 673 2 19 674 80 84 675 108 119 676 61 85 677 67 88 678 66 87 679 105 122 680 66 87 681 65 90 682 104 79 683 63 91 684 101 73 685 64 92 686 62 94 687 88 126 688 63 91 689 60 97 690 98 66 691 61 85 692 59 99 693 85 127 694 60 97 695 58 101 696 57 100 697 96 111 698 56 104 699 82 8 700 57 100 701 122 106 702 128 128 703 56 104 704 67 88 705 81 108 706 93 5 707 65 90 708 123 110 709 91 78 710 123 110 711 59 99 712 129 0 713 62 94 714 100 125 715 122 106 716 36 46 717 88 126 718 116 123 719 119 121 720 0 15 721 126 109 722 77 75 723 37 112 724 125 9 725 23 114 726 112 116 727 71 63 728 126 109 729 85 127 730 123 110 731 127 105 732 88 126 733 122 106 734 1 11 735 3 16 736 121 82 737 3 16 738 0 15 739 120 81 740 37 112 741 40 47 742 118 117 743 40 47 744 36 46 745 117 80 746 48 51 747 114 72 748 42 13 749 44 2 750 113 1 751 48 51 752 26 36 753 111 68 754 23 114 755 22 7 756 110 6 757 26 36 758 71 63 759 124 129 760 15 64 761 29 76 762 77 75 763 121 82 764 100 125 765 50 57 766 128 128 767 129 0 768 97 98 769 113 1 770 130 131 771 132 131 772 131 131 773</p>
        </triangles>
        <triangles material="haybale-material" count="336">
          <input semantic="VERTEX" source="#Cube_003-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_003-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_003-mesh-map-0" offset="2" set="0"/>
          <p>263 132 774 247 133 775 178 134 776 213 135 777 162 136 778 227 137 779 244 138 780 156 139 781 216 140 782 259 141 783 202 142 784 135 143 785 210 144 786 176 145 787 249 146 788 134 147 789 137 148 790 138 149 791 139 150 792 138 149 793 137 148 794 136 151 795 140 152 796 138 149 797 141 153 798 144 154 799 145 155 800 146 156 801 145 155 802 144 154 803 143 157 804 147 158 805 145 155 806 148 159 807 151 160 808 152 161 809 153 162 810 152 161 811 151 160 812 150 163 813 154 164 814 152 161 815 155 165 816 158 166 817 159 167 818 156 139 819 160 168 820 159 167 821 161 169 822 159 167 823 160 168 824 162 136 825 165 170 826 166 171 827 167 172 828 166 171 829 165 170 830 164 173 831 168 174 832 166 171 833 169 175 834 172 176 835 173 177 836 170 178 837 174 179 838 173 177 839 175 180 840 173 177 841 174 179 842 179 181 843 180 182 844 182 183 845 177 184 846 181 185 847 180 182 848 178 134 849 182 183 850 180 182 851 186 186 852 187 187 853 189 188 854 184 189 855 188 190 856 187 187 857 185 191 858 189 188 859 187 187 860 142 192 861 144 154 862 203 193 863 203 193 864 144 154 865 141 153 866 156 139 867 158 166 868 229 194 869 229 194 870 158 166 871 155 165 872 205 195 873 149 196 874 151 160 875 151 160 876 148 159 877 207 197 878 134 147 879 140 152 880 232 198 881 140 152 882 136 151 883 233 199 884 244 138 885 184 189 886 186 186 887 245 200 888 186 186 889 183 201 890 208 202 891 177 184 892 179 181 893 209 203 894 179 181 895 176 145 896 150 163 897 153 162 898 248 204 899 153 162 900 149 196 901 249 146 902 170 178 903 172 176 904 235 205 905 172 176 906 169 175 907 236 206 908 211 207 909 163 208 910 165 170 911 212 209 912 165 170 913 162 136 914 225 210 915 178 134 916 181 185 917 238 211 918 181 185 919 177 184 920 143 157 921 146 156 922 251 212 923 251 212 924 146 156 925 142 192 926 164 173 927 167 172 928 254 213 929 167 172 930 163 208 931 255 214 932 207 197 933 148 159 934 221 215 935 214 216 936 195 217 937 233 199 938 226 218 939 200 219 940 201 220 941 237 221 942 199 222 943 200 219 944 197 223 945 198 224 946 224 225 947 196 226 948 197 223 949 223 227 950 220 228 951 194 229 952 195 217 953 231 230 954 193 231 955 194 229 956 191 232 957 192 233 958 218 234 959 228 235 960 190 236 961 191 232 962 261 237 963 256 238 964 190 236 965 240 239 966 215 240 967 201 220 968 260 241 969 257 242 970 199 222 971 198 224 972 215 240 973 240 239 974 218 234 975 192 233 976 214 216 977 230 243 978 155 165 979 207 197 980 175 180 981 212 209 982 213 135 983 171 244 984 211 207 985 212 209 986 209 203 987 210 144 988 183 201 989 208 202 990 209 203 991 189 188 992 206 245 993 207 197 994 155 165 995 157 246 996 205 195 997 206 245 998 203 193 999 204 247 1000 136 151 1001 202 142 1002 203 193 1003 139 150 1004 246 248 1005 183 201 1006 210 144 1007 252 249 1008 142 192 1009 202 142 1010 169 175 1011 213 135 1012 243 250 1013 236 206 1014 243 250 1015 215 240 1016 192 233 1017 230 243 1018 242 251 1019 218 234 1020 241 252 1021 204 247 1022 224 225 1023 240 239 1024 208 202 1025 253 253 1026 260 241 1027 237 221 1028 208 202 1029 240 239 1030 239 254 1031 250 255 1032 261 237 1033 228 235 1034 143 157 1035 228 235 1036 217 256 1037 217 256 1038 218 234 1039 141 153 1040 150 163 1041 231 230 1042 220 228 1043 154 164 1044 220 228 1045 221 215 1046 234 257 1047 223 227 1048 188 190 1049 223 227 1050 224 225 1051 185 191 1052 164 173 1053 237 221 1054 226 218 1055 168 174 1056 226 218 1057 227 137 1058 241 252 1059 233 199 1060 136 151 1061 242 251 1062 221 215 1063 195 217 1064 200 219 1065 238 211 1066 239 254 1067 199 222 1068 225 210 1069 238 211 1070 235 205 1071 236 206 1072 198 224 1073 222 258 1074 235 205 1075 197 223 1076 232 198 1077 233 199 1078 195 217 1079 219 259 1080 232 198 1081 194 229 1082 191 232 1083 229 194 1084 230 243 1085 216 140 1086 229 194 1087 191 232 1088 262 260 1089 216 140 1090 190 236 1091 215 240 1092 243 250 1093 227 137 1094 257 242 1095 263 132 1096 225 210 1097 193 231 1098 231 230 1099 263 132 1100 234 257 1101 262 260 1102 256 238 1103 222 258 1104 261 237 1105 250 255 1106 134 147 1107 219 259 1108 260 241 1109 171 244 1110 252 249 1111 259 141 1112 246 248 1113 258 261 1114 205 195 1115 219 259 1116 193 231 1117 257 242 1118 222 258 1119 196 226 1120 256 238 1121 137 148 1122 254 213 1123 255 214 1124 134 147 1125 253 253 1126 254 213 1127 174 179 1128 251 212 1129 252 249 1130 170 178 1131 250 255 1132 251 212 1133 248 204 1134 249 146 1135 176 145 1136 247 133 1137 248 204 1138 182 183 1139 245 200 1140 246 248 1141 157 246 1142 244 138 1143 245 200 1144 160 168 1145 258 261 1146 249 146 1147 149 196 1148 211 207 1149 259 141 1150 255 214 1151 184 189 1152 244 138 1153 262 260 1154 231 230 1155 150 163 1156 247 133 1157 282 262 1158 302 262 1159 293 262 1160 268 263 1161 288 263 1162 302 263 1163 275 264 1164 295 264 1165 294 264 1166 265 265 1167 285 265 1168 300 265 1169 272 266 1170 292 266 1171 291 266 1172 269 267 1173 289 267 1174 288 267 1175 278 268 1176 276 268 1177 296 268 1178 266 269 1179 286 269 1180 285 269 1181 276 270 1182 280 270 1183 300 270 1184 279 271 1185 299 271 1186 290 271 1187 271 272 1188 291 272 1189 303 272 1190 267 273 1191 278 273 1192 298 273 1193 274 274 1194 294 274 1195 301 274 1196 273 275 1197 293 275 1198 295 275 1199 270 276 1200 290 276 1201 292 276 1202 279 277 1203 277 277 1204 297 277 1205 267 278 1206 287 278 1207 289 278 1208 277 279 1209 281 279 1210 301 279 1211 266 280 1212 264 280 1213 284 280 1214 264 281 1215 283 281 1216 303 281 1217 322 282 1218 342 282 1219 333 282 1220 308 283 1221 328 283 1222 342 283 1223 315 284 1224 335 284 1225 334 284 1226 305 285 1227 325 285 1228 340 285 1229 312 286 1230 332 286 1231 331 286 1232 309 287 1233 329 287 1234 328 287 1235 318 288 1236 316 288 1237 336 288 1238 306 289 1239 326 289 1240 325 289 1241 316 290 1242 320 290 1243 340 290 1244 319 291 1245 339 291 1246 330 291 1247 311 292 1248 331 292 1249 343 292 1250 307 293 1251 318 293 1252 338 293 1253 314 294 1254 334 294 1255 341 294 1256 313 295 1257 333 295 1258 335 295 1259 310 296 1260 330 296 1261 332 296 1262 319 297 1263 317 297 1264 337 297 1265 307 298 1266 327 298 1267 329 298 1268 317 299 1269 321 299 1270 341 299 1271 306 300 1272 304 300 1273 324 300 1274 304 301 1275 323 301 1276 343 301 1277 225 210 1278 263 132 1279 178 134 1280 243 250 1281 213 135 1282 227 137 1283 262 260 1284 244 138 1285 216 140 1286 255 214 1287 259 141 1288 135 143 1289 258 261 1290 210 144 1291 249 146 1292 140 152 1293 134 147 1294 138 149 1295 135 143 1296 139 150 1297 137 148 1298 139 150 1299 136 151 1300 138 149 1301 147 158 1302 141 153 1303 145 155 1304 142 192 1305 146 156 1306 144 154 1307 146 156 1308 143 157 1309 145 155 1310 154 164 1311 148 159 1312 152 161 1313 149 196 1314 153 162 1315 151 160 1316 153 162 1317 150 163 1318 152 161 1319 161 169 1320 155 165 1321 159 167 1322 158 166 1323 156 139 1324 159 167 1325 157 246 1326 161 169 1327 160 168 1328 168 174 1329 162 136 1330 166 171 1331 163 208 1332 167 172 1333 165 170 1334 167 172 1335 164 173 1336 166 171 1337 175 180 1338 169 175 1339 173 177 1340 172 176 1341 170 178 1342 173 177 1343 171 244 1344 175 180 1345 174 179 1346 176 145 1347 179 181 1348 182 183 1349 179 181 1350 177 184 1351 180 182 1352 181 185 1353 178 134 1354 180 182 1355 183 201 1356 186 186 1357 189 188 1358 186 186 1359 184 189 1360 187 187 1361 188 190 1362 185 191 1363 187 187 1364 202 142 1365 142 192 1366 203 193 1367 204 247 1368 203 193 1369 141 153 1370 216 140 1371 156 139 1372 229 194 1373 230 243 1374 229 194 1375 155 165 1376 206 245 1377 205 195 1378 151 160 1379 206 245 1380 151 160 1381 207 197 1382 219 259 1383 134 147 1384 232 198 1385 232 198 1386 140 152 1387 233 199 1388 245 200 1389 244 138 1390 186 186 1391 246 248 1392 245 200 1393 183 201 1394 209 203 1395 208 202 1396 179 181 1397 210 144 1398 209 203 1399 176 145 1400 247 133 1401 150 163 1402 248 204 1403 248 204 1404 153 162 1405 249 146 1406 222 258 1407 170 178 1408 235 205 1409 235 205 1410 172 176 1411 236 206 1412 212 209 1413 211 207 1414 165 170 1415 213 135 1416 212 209 1417 162 136 1418 238 211 1419 225 210 1420 181 185 1421 239 254 1422 238 211 1423 177 184 1424 250 255 1425 143 157 1426 251 212 1427 252 249 1428 251 212 1429 142 192 1430 253 253 1431 164 173 1432 254 213 1433 254 213 1434 167 172 1435 255 214 1436 242 251 1437 207 197 1438 221 215 1439 241 252 1440 214 216 1441 233 199 1442 227 137 1443 226 218 1444 201 220 1445 226 218 1446 237 221 1447 200 219 1448 223 227 1449 197 223 1450 224 225 1451 234 257 1452 196 226 1453 223 227 1454 221 215 1455 220 228 1456 195 217 1457 220 228 1458 231 230 1459 194 229 1460 217 256 1461 191 232 1462 218 234 1463 217 256 1464 228 235 1465 191 232 1466 228 235 1467 261 237 1468 190 236 1469 239 254 1470 240 239 1471 201 220 1472 237 221 1473 260 241 1474 199 222 1475 224 225 1476 198 224 1477 240 239 1478 241 252 1479 218 234 1480 214 216 1481 242 251 1482 230 243 1483 207 197 1484 169 175 1485 175 180 1486 213 135 1487 175 180 1488 171 244 1489 212 209 1490 189 188 1491 209 203 1492 183 201 1493 185 191 1494 208 202 1495 189 188 1496 161 169 1497 206 245 1498 155 165 1499 161 169 1500 157 246 1501 206 245 1502 139 150 1503 203 193 1504 136 151 1505 135 143 1506 202 142 1507 139 150 1508 258 261 1509 246 248 1510 210 144 1511 259 141 1512 252 249 1513 202 142 1514 236 206 1515 169 175 1516 243 250 1517 198 224 1518 236 206 1519 215 240 1520 214 216 1521 192 233 1522 242 251 1523 141 153 1524 218 234 1525 204 247 1526 185 191 1527 224 225 1528 208 202 1529 164 173 1530 253 253 1531 237 221 1532 177 184 1533 208 202 1534 239 254 1535 143 157 1536 250 255 1537 228 235 1538 147 158 1539 143 157 1540 217 256 1541 147 158 1542 217 256 1543 141 153 1544 154 164 1545 150 163 1546 220 228 1547 148 159 1548 154 164 1549 221 215 1550 184 189 1551 234 257 1552 188 190 1553 188 190 1554 223 227 1555 185 191 1556 168 174 1557 164 173 1558 226 218 1559 162 136 1560 168 174 1561 227 137 1562 204 247 1563 241 252 1564 136 151 1565 214 216 1566 242 251 1567 195 217 1568 201 220 1569 200 219 1570 239 254 1571 200 219 1572 199 222 1573 238 211 1574 197 223 1575 235 205 1576 198 224 1577 196 226 1578 222 258 1579 197 223 1580 194 229 1581 232 198 1582 195 217 1583 193 231 1584 219 259 1585 194 229 1586 192 233 1587 191 232 1588 230 243 1589 190 236 1590 216 140 1591 191 232 1592 256 238 1593 262 260 1594 190 236 1595 201 220 1596 215 240 1597 227 137 1598 199 222 1599 257 242 1600 225 210 1601 257 242 1602 193 231 1603 263 132 1604 196 226 1605 234 257 1606 256 238 1607 170 178 1608 222 258 1609 250 255 1610 253 253 1611 134 147 1612 260 241 1613 211 207 1614 171 244 1615 259 141 1616 157 246 1617 246 248 1618 205 195 1619 260 241 1620 219 259 1621 257 242 1622 261 237 1623 222 258 1624 256 238 1625 135 143 1626 137 148 1627 255 214 1628 137 148 1629 134 147 1630 254 213 1631 171 244 1632 174 179 1633 252 249 1634 174 179 1635 170 178 1636 251 212 1637 182 183 1638 248 204 1639 176 145 1640 178 134 1641 247 133 1642 182 183 1643 160 168 1644 245 200 1645 157 246 1646 156 139 1647 244 138 1648 160 168 1649 205 195 1650 258 261 1651 149 196 1652 163 208 1653 211 207 1654 255 214 1655 234 257 1656 184 189 1657 262 260 1658 263 132 1659 231 230 1660 247 133 1661 273 302 1662 282 302 1663 293 302 1664 282 303 1665 268 303 1666 302 303 1667 274 304 1668 275 304 1669 294 304 1670 280 305 1671 265 305 1672 300 305 1673 271 306 1674 272 306 1675 291 306 1676 268 307 1677 269 307 1678 288 307 1679 298 288 1680 278 288 1681 296 288 1682 265 308 1683 266 308 1684 285 308 1685 296 309 1686 276 309 1687 300 309 1688 270 310 1689 279 310 1690 290 310 1691 283 311 1692 271 311 1693 303 311 1694 287 312 1695 267 312 1696 298 312 1697 281 313 1698 274 313 1699 301 313 1700 275 295 1701 273 295 1702 295 295 1703 272 314 1704 270 314 1705 292 314 1706 299 315 1707 279 315 1708 297 315 1709 269 316 1710 267 316 1711 289 316 1712 297 317 1713 277 317 1714 301 317 1715 286 318 1716 266 318 1717 284 318 1718 284 319 1719 264 319 1720 303 319 1721 313 282 1722 322 282 1723 333 282 1724 322 320 1725 308 320 1726 342 320 1727 314 321 1728 315 321 1729 334 321 1730 320 322 1731 305 322 1732 340 322 1733 311 323 1734 312 323 1735 331 323 1736 308 324 1737 309 324 1738 328 324 1739 338 288 1740 318 288 1741 336 288 1742 305 325 1743 306 325 1744 325 325 1745 336 309 1746 316 309 1747 340 309 1748 310 326 1749 319 326 1750 330 326 1751 323 327 1752 311 327 1753 343 327 1754 327 328 1755 307 328 1756 338 328 1757 321 329 1758 314 329 1759 341 329 1760 315 295 1761 313 295 1762 335 295 1763 312 330 1764 310 330 1765 332 330 1766 339 331 1767 319 331 1768 337 331 1769 309 316 1770 307 316 1771 329 316 1772 337 332 1773 317 332 1774 341 332 1775 326 333 1776 306 333 1777 324 333 1778 324 334 1779 304 334 1780 343 334 1781</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Cube_001-mesh" name="Cube.001">
      <mesh>
        <source id="Cube_001-mesh-positions">
          <float_array id="Cube_001-mesh-positions-array" count="492">-0.5347093 0.7234184 1.20519 -0.5391202 0.7254559 0.8119454 -0.5373588 0.3289551 1.213077 -0.51076 0.3122457 0.81671 -0.560566 0.3436716 1.179805 -0.5740898 0.4914641 0.8156084 -0.5577523 -0.02548968 1.040584 -0.5400988 0.1067109 0.6660115 -0.5305144 -0.5076484 1.181499 -0.5349252 -0.5056108 0.7882546 -0.5331639 -0.9021117 1.189386 -0.5065651 -0.918821 0.7930192 -0.6829759 1.147581 0.1819606 -0.4399945 0.8400257 0.1497943 -0.6968619 1.108236 0.5742993 -0.4280887 0.8154415 0.5631025 -0.6640766 1.147583 0.6003986 -0.4210953 0.8400273 0.5682322 -0.6779626 1.108238 0.9927372 -0.4091894 0.815443 0.9815403 -0.855359 0.7000843 -0.01890236 -0.2590939 1.050264 -0.02872455 -0.8008709 0.5866637 0.9316296 -0.2244927 0.9808461 0.9802273 -0.796373 -1.044976 0.1819606 -0.4871134 -0.8041669 0.1497943 -0.7571272 -1.059139 0.5742993 -0.462446 -0.7924348 0.5631025 -0.7962415 -1.026077 0.6003986 -0.4869821 -0.7852683 0.5682322 -0.7569958 -1.04024 0.9927372 -0.4623146 -0.773536 0.9815403 -0.715426 -0.05283707 -0.01161396 -0.4061664 -0.02007901 0.229115 -0.6761801 -0.4452095 -0.02481245 -0.3814991 -0.4333571 0.2418631 -0.7295293 -0.4697442 -0.0147401 -0.4202699 -0.4369862 0.2259888 -0.6902835 -0.8621165 -0.02793872 -0.3956024 -0.8502641 0.2387369 -0.7630425 0.8040244 -0.04202306 -0.454455 0.8426028 0.1987059 -0.7164117 0.4124609 -0.05522155 -0.4220061 0.4298627 0.211454 -0.7550289 0.3857108 -0.02209579 -0.4464415 0.4242892 0.2186331 -0.7083981 -0.005852341 -0.03529441 -0.4139927 0.01154923 0.2313812 -0.6679517 -0.2916665 1.204151 -0.5244869 -0.6165999 0.7587818 -0.6140831 0.1843788 0.8925318 -0.5104007 -0.1236422 0.4170488 -0.7764951 0.419776 0.4642828 -0.5088605 0.138207 0.525566 -0.7562782 0.3678086 0.07369208 -0.5082954 0.06029009 0.1187511 0.02478754 -1.102212 1.078441 -0.001042604 -0.9011358 0.5460696 -0.5395322 -1.040487 1.144479 -0.5985028 -0.876819 0.5954378 0.126859 -0.9602069 0.4607985 0.003275692 -1.124075 -0.0706073 -0.4230877 -0.8493648 0.5698863 -0.5792075 -1.051745 0.05376631 -0.378996 -1.224034 0.3738492 -0.5807347 -1.017003 -0.1170248 -0.4913305 -0.7329633 0.643772 -0.7411583 -0.516394 0.1721844 -0.2347415 -0.9766507 1.178398 -0.3768634 -1.215471 0.6811214 -0.6151856 -0.5570557 1.102043 -0.8078082 -0.802797 0.6180817 0.7166721 -0.6192529 0.9373031 0.6473222 -1.033013 0.5519325 0.2282768 -0.7606917 1.198249 0.1579506 -1.213031 0.8487427 0.5387304 1.126938 0.5422515 0.8362383 0.7466163 0.8445129 0.09449619 1.084696 0.8993219 0.3905637 0.732716 1.24597 -0.5876795 1.076622 0.5083848 -0.2264907 1.207413 0.9290438 -0.8048416 0.6426978 0.8103418 -0.4817804 0.781937 1.266363 0.490895 0.7234184 1.20519 0.4953058 0.7254559 0.8119454 0.4935445 0.3289551 1.213077 0.4669457 0.3122457 0.81671 0.5122951 0.4390977 1.157984 0.516706 0.4411352 0.7647395 0.5149446 0.04463434 1.165871 0.4883458 0.02792489 0.769504 0.6391616 1.147581 0.1819606 0.3961802 0.8400257 0.1497943 0.6530477 1.108236 0.5742993 0.3842744 0.8154415 0.5631025 0.6202624 1.147583 0.6003986 0.377281 0.8400273 0.5682322 0.6341484 1.108238 0.9927372 0.3653752 0.815443 0.9815403 0.7071973 -0.04766637 -0.06423723 0.3979378 -0.01490831 0.1764917 0.6679516 -0.4400388 -0.07743585 0.3732705 -0.4281864 0.1892397 0.7070658 -0.4660567 -0.04431009 0.3978064 -0.4332987 0.1964188 0.6678201 -0.858429 -0.05750852 0.373139 -0.8465766 0.209167 0.7192282 0.8040244 -0.04202306 0.4106407 0.8426028 0.1987059 0.6725974 0.4124609 -0.05522149 0.378192 0.4298627 0.211454 0.7112146 0.3857108 -0.02209579 0.4026272 0.4242892 0.2186331 0.6645839 -0.005852341 -0.03529441 0.3701784 0.01154923 0.2313812 0.3128618 -1.131716 0.5357969 0.3457098 -1.129115 -0.03291374 0.6132166 -0.646677 0.5697276 0.6987308 -0.6451023 1.69963e-4 -0.06970262 -0.1752257 1.157237 -0.3698911 -0.3514251 0.9741911 -0.2252815 0.1811763 1.090579 -0.5506541 0.01862472 0.9299462 0.01391774 -0.3827278 1.176011 0.05533385 -0.7307187 0.9975402 -0.3792734 -0.4071726 1.154247 -0.3566586 -0.7733768 1.001026 -0.4329487 0.6874193 1.157875 -0.2261721 0.970564 0.9797243 -0.1309033 0.4351724 1.129398 0.1023775 0.7183499 0.976154 0.02895528 0.4969875 1.160069 -0.03563016 0.1523774 0.9819183 -0.3523038 0.5944612 1.131593 -0.4407932 0.2384005 0.978348 -0.05159229 -0.6466482 1.164162 -0.09626513 -0.2990601 0.9856914 0.3413527 -0.6185225 1.142398 0.3153098 -0.2525461 0.9891775 -0.05853348 -0.03608363 -0.1301257 -0.2860354 -0.3026471 0.04834526 -0.3433033 0.2361348 -0.108361 -0.5969414 -0.02897739 0.04485929 0.01391774 -0.3827278 -0.1242407 0.05533385 -0.7307187 0.05423033 -0.3792734 -0.4071726 -0.1024761 -0.3566586 -0.7733768 0.05074417 -0.4329487 0.6874193 -0.1061041 -0.2261721 0.9705639 0.07204627 -0.1309033 0.4351724 -0.07762789 0.1023775 0.7183499 0.07561659 0.02252918 0.5168824 -0.1303734 -0.04205632 0.1722722 0.04777681 -0.3587298 0.614356 -0.1018972 -0.4472193 0.2582955 0.05134719 -0.05159229 -0.6466482 -0.1123919 -0.09626513 -0.2990601 0.06607913 0.3413525 -0.6185225 -0.09062731 0.3153098 -0.2525461 0.0625931 0.6163825 -1.023413 1.127375 0.4613366 -1.168715 0.6947177 0.3908264 -0.5983979 1.078987 0.1926338 -0.7396479 0.656776</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-positions-array" count="164" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-normals">
          <float_array id="Cube_001-mesh-normals-array" count="246">-0.9999127 0.00694096 0.01125156 -0.9993889 -0.01874387 0.02950412 -0.9999127 0.006941318 0.01125156 -0.7860559 -0.6116912 -0.08916306 -0.7860564 -0.6116907 -0.0891633 -0.5672304 0.8200567 0.07587444 0.606139 -0.7903453 -0.08916288 0.6061385 -0.7903457 -0.08916217 -0.6705178 -0.01714253 0.7416955 -0.6705176 -0.01714277 0.7416956 -0.6700757 -0.0297718 0.7416955 -0.6700758 -0.0297715 0.7416954 -0.9835126 -0.08320271 -0.1605624 0.7295279 0.6818973 -0.05296462 -0.062056 -0.9582847 -0.2789974 -0.03521144 -0.9267424 0.3740437 -0.8649682 -0.4282822 0.2615428 -0.6231682 -0.5687534 0.536825 0.57095 -0.5557059 0.6043238 0.3952144 0.7915233 0.4661507 -0.6978301 0.6526526 0.2950897 0.9955637 -0.06906598 -0.06389743 0.9955637 -0.0690661 -0.06389725 -0.7860561 0.6116909 0.08916348 -0.7860563 0.6116907 0.0891633 0.6061385 -0.08721995 0.7905623 0.6061387 -0.08721977 0.7905623 0.604388 -0.09862321 0.7905623 0.6043881 -0.09862351 0.7905622 0.84781 -0.5282531 0.04655236 -0.4048432 -0.08894932 0.9100494 0.04746603 -0.3830489 0.9225078 0.2409666 0.3008364 0.9227311 -0.08781611 -0.3753097 0.9227302 -0.05105209 0.3825865 0.9225082 -0.3506026 -0.2957208 -0.8886097 -0.02070152 -0.4581971 -0.8886095 0.3416798 0.3089339 -0.8875893 -0.1752091 -0.4260128 -0.8875893 0.01640999 0.4583708 -0.8886097 0.8192977 0.3865944 -0.4234338 -0.9955637 -0.0690664 -0.06389725 -0.9957608 -0.06131011 -0.06856846 -0.9955638 -0.0690661 -0.06389725 -0.7369087 -0.6757262 -0.01896566 -0.7369093 -0.6757256 -0.01896566 -0.5004595 0.8558211 0.1308074 0.6705182 -0.7416507 -0.01896566 0.6705175 -0.7416514 -0.01896566 -0.6061385 -0.08721995 0.7905623 -0.6061387 -0.08721977 0.7905622 -0.604388 -0.09862321 0.7905623 -0.6043879 -0.09862369 0.7905622 -0.962525 -0.06364649 -0.2636184 0.7814078 0.6130803 -0.1163374 -0.1417624 -0.9283026 -0.343741 -0.1260525 -0.9393425 0.3189773 -0.9123349 -0.3367328 0.2329297 -0.6971177 -0.5479308 0.4623841 0.499205 -0.6338859 0.590748 0.3780199 0.7389538 0.557717 -0.682178 0.6157732 0.3942801 0.9999127 0.00694096 0.01125138 0.9999127 0.006940722 0.01125156 -0.7369087 0.6757262 0.01896566 -0.7369093 0.6757256 0.01896566 0.6705176 -0.01714289 0.7416956 0.6705178 -0.01714235 0.7416954 0.6700757 -0.02977138 0.7416955 0.6700758 -0.02977138 0.7416954 0.7983991 -0.5904051 0.1182402 -0.4962862 -0.05459135 0.866441 -0.02069973 -0.4581964 0.88861 0.3416815 0.3089333 0.8875889 -0.1752091 -0.4260137 0.887589 0.01640874 0.4583697 0.8886102 -0.2491459 -0.2947964 -0.9225082 0.04746699 -0.3830474 -0.9225084 0.2409674 0.3008371 -0.9227306 -0.08781594 -0.3753088 -0.9227306 -0.05105143 0.3825864 -0.9225082 0.7543391 0.4284172 -0.4974248</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-normals-array" count="82" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-map-0">
          <float_array id="Cube_001-mesh-map-0-array" count="492">0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.80731 1.10331 0.9392474 0.2630026 0.09894013 0.1310653 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.002689719 0.05287802 0.002689719 1.034196 0.9840079 1.034196 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.9840079 0.05287802 0.9840079 1.034196 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.1335377 0.6867747 0.8273997 0.6867747 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.7657069 1.141389 0.8796698 0.4569493 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.1335377 -0.007087171 0.8273997 -0.007087171 0.8273997 0.6867747 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.7657069 1.141389 0.08126765 1.027426 0.1952306 0.3429865 0.002689719 0.05287802 0.9840079 0.05287802 0.9840079 1.034196 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 -0.03299725 0.9713724 0.80731 1.10331 0.09894013 0.1310653 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.9840079 0.05287802 0.002689719 0.05287802 0.9840079 1.034196 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.002689719 1.034196 0.002689719 0.05287802 0.9840079 1.034196 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.8273997 -0.007087171 0.1335377 -0.007087171 0.8273997 0.6867747 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.08126765 1.027426 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.1335377 0.6867747 0.1335377 -0.007087171 0.8273997 0.6867747 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.8796698 0.4569493 0.7657069 1.141389 0.1952306 0.3429865 0.002689719 1.034196 0.002689719 0.05287802 0.9840079 1.034196</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-map-0-array" count="246" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_001-mesh-vertices">
          <input semantic="POSITION" source="#Cube_001-mesh-positions"/>
        </vertices>
        <triangles material="haybale3-material" count="82">
          <input semantic="VERTEX" source="#Cube_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_001-mesh-map-0" offset="2" set="0"/>
          <p>2 0 0 0 0 1 1 0 2 6 1 3 4 1 4 5 1 5 10 2 6 8 2 7 9 2 8 14 3 9 12 3 10 13 3 11 18 4 12 16 4 13 17 4 14 22 5 15 23 5 16 21 5 17 26 6 18 24 6 19 25 6 20 30 7 21 28 7 22 29 7 23 34 8 24 35 8 25 33 8 26 38 9 27 39 9 28 37 9 29 42 10 30 43 10 31 41 10 32 46 11 33 47 11 34 45 11 35 50 12 36 51 12 37 49 12 38 54 13 39 52 13 40 53 13 41 58 14 42 59 14 43 57 14 44 62 15 45 63 15 46 61 15 47 66 16 48 67 16 49 65 16 50 70 17 51 71 17 52 69 17 53 74 18 54 75 18 55 73 18 56 78 19 57 79 19 58 77 19 59 82 20 60 83 20 61 81 20 62 86 21 63 87 21 64 85 21 65 90 22 66 91 22 67 89 22 68 94 23 69 92 23 70 93 23 71 98 24 72 96 24 73 97 24 74 102 25 75 100 25 76 101 25 77 106 26 78 104 26 79 105 26 80 110 27 81 108 27 82 109 27 83 114 28 84 112 28 85 113 28 86 118 29 87 116 29 88 117 29 89 122 30 90 123 30 91 121 30 92 126 31 93 127 31 94 125 31 95 130 32 96 131 32 97 129 32 98 134 33 99 135 33 100 133 33 101 138 34 102 139 34 103 137 34 104 142 35 105 140 35 106 141 35 107 146 36 108 144 36 109 145 36 110 150 37 111 148 37 112 149 37 113 154 38 114 152 38 115 153 38 116 158 39 117 156 39 118 157 39 119 162 40 120 160 40 121 161 40 122 3 41 123 2 41 124 1 41 125 7 42 126 6 42 127 5 42 128 11 43 129 10 43 130 9 43 131 15 44 132 14 44 133 13 44 134 19 45 135 18 45 136 17 45 137 20 46 138 22 46 139 21 46 140 27 47 141 26 47 142 25 47 143 31 48 144 30 48 145 29 48 146 32 49 147 34 49 148 33 49 149 36 50 150 38 50 151 37 50 152 40 51 153 42 51 154 41 51 155 44 52 156 46 52 157 45 52 158 48 53 159 50 53 160 49 53 161 55 54 162 54 54 163 53 54 164 56 55 165 58 55 166 57 55 167 60 56 168 62 56 169 61 56 170 64 57 171 66 57 172 65 57 173 68 58 174 70 58 175 69 58 176 72 59 177 74 59 178 73 59 179 76 60 180 78 60 181 77 60 182 80 61 183 82 61 184 81 61 185 84 62 186 86 62 187 85 62 188 88 63 189 90 63 190 89 63 191 95 64 192 94 64 193 93 64 194 99 65 195 98 65 196 97 65 197 103 66 198 102 66 199 101 66 200 107 67 201 106 67 202 105 67 203 111 68 204 110 68 205 109 68 206 115 69 207 114 69 208 113 69 209 119 70 210 118 70 211 117 70 212 120 71 213 122 71 214 121 71 215 124 72 216 126 72 217 125 72 218 128 73 219 130 73 220 129 73 221 132 74 222 134 74 223 133 74 224 136 75 225 138 75 226 137 75 227 143 76 228 142 76 229 141 76 230 147 77 231 146 77 232 145 77 233 151 78 234 150 78 235 149 78 236 155 79 237 154 79 238 153 79 239 159 80 240 158 80 241 157 80 242 163 81 243 162 81 244 161 81 245</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="haybale_square_haybits4" name="haybale_square_haybits4" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_006-mesh" name="haybale_square_haybits4">
          <bind_material>
            <technique_common>
              <instance_material symbol="haybale3-material" target="#haybale3-material">
                <bind_vertex_input semantic="Cube-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="haybale_square_haybits3" name="haybale_square_haybits3" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_005-mesh" name="haybale_square_haybits3">
          <bind_material>
            <technique_common>
              <instance_material symbol="haybale3-material" target="#haybale3-material">
                <bind_vertex_input semantic="Cube-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="haybale_square_haybits2" name="haybale_square_haybits2" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_004-mesh" name="haybale_square_haybits2">
          <bind_material>
            <technique_common>
              <instance_material symbol="haybale3-material" target="#haybale3-material">
                <bind_vertex_input semantic="Cube-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="haybale_square" name="haybale_square" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_003-mesh" name="haybale_square">
          <bind_material>
            <technique_common>
              <instance_material symbol="haybale2-material" target="#haybale2-material">
                <bind_vertex_input semantic="Cube-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="haybale-material" target="#haybale-material">
                <bind_vertex_input semantic="Cube-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="haybale_square_haybits1" name="haybale_square_haybits1" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_001-mesh" name="haybale_square_haybits1">
          <bind_material>
            <technique_common>
              <instance_material symbol="haybale3-material" target="#haybale3-material">
                <bind_vertex_input semantic="Cube-mesh-map-0" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>